'use strict';

var express = require('express');
var controller = require('./service.controller');

var router = express.Router();


router.post('/createInstanceByProperties', controller.createInstanceByProperties);
router.post('/graphql', controller.graphql);
router.post('/instance', controller.instance);
router.post('/classic', controller.classic);
router.post('/typeConfig', controller.typeConfig);
router.post('/createEdge', controller.createEdge);

router.post('/listReportingSite', controller.listReportingSite);
router.post('/listCommonRefEnum', controller.listCommonRefEnum);
router.post('/listReportingSiteFunctionalLocation', controller.listReportingSiteFunctionalLocation);
router.post('/listReportingSiteCursor', controller.listReportingSiteCursor);
router.post('/listReportingUnitFunctionalLocation', controller.listReportingUnitFunctionalLocation);
router.post('/listReportingUnitCursor', controller.listReportingUnitCursor);
router.post('/listReportingSiteById', controller.listReportingSiteById);
router.post('/listReportingUnitById', controller.listReportingUnitById);
router.post('/listGeoRegion', controller.listGeoRegion);
router.post('/listCountry', controller.listCountry);
router.post('/listBusinessLineByUnit', controller.listBusinessLineByUnit);
router.post('/listReportingSiteByCountry', controller.listReportingSiteByCountry);
router.post('/listReportingLocation', controller.listReportingLocation);
router.post('/listEquipmentByFunctionalLocation', controller.listEquipmentByFunctionalLocation);

router.post('/aggregateFunctionalLocation', controller.aggregateFunctionalLocation);
router.post('/listFunctionalLocation', controller.listFunctionalLocation);
router.post('/listFunctionalLocationCursor', controller.listFunctionalLocationCursor);
router.post('/aggregateEquipment', controller.aggregateEquipment);
router.post('/listEquipment', controller.listEquipment);
router.post('/listEquipmentCursor', controller.listEquipmentCursor);

router.post('/listAuditSummary', controller.listAuditSummary)

router.post('/space', controller.createSpace);
router.post('/listProcess', controller.listProcess);
router.post('/listProcessBySite', controller.listProcessBySite);
router.post('/listProcessConfigurationByProcess', controller.listProcessConfigurationByProcess);
router.post('/listSubCategory', controller.listSubCategory);
router.post('/listQuestionBankByCategory', controller.listQuestionBankByCategory);
router.post('/listQuestionList', controller.listQuestionList);

router.post('/aggregateObservation', controller.aggregateObservation);
router.post('/aggregateFieldWalk', controller.aggregateFieldWalk);
router.post('/aggregateAudit', controller.aggregateAudit);
router.post('/aggregateAction', controller.aggregateAction);

router.post('/aggregateChecklist', controller.aggregateChecklist);

router.post('/listObservation', controller.listObservation);
router.post('/listDepartment', controller.listDepartment);
router.post('/listObservationById', controller.listObservationById);
router.post('/listChecklistByCategory', controller.listChecklistByCategory);
router.post('/listFieldWalk', controller.listFieldWalk);
router.post('/priviewImage', controller.priviewImage);
router.post('/listVendor', controller.listVendor);
router.post('/listSupplier', controller.listSupplier);
router.post('/listSchedule', controller.listSchedule);
router.post('/listScheduleDetail', controller.listScheduleDetail);
router.post('/listOFWALog', controller.listOFWALog);
router.post('/listChecklist', controller.listChecklist);
router.post('/listAudit', controller.listAudit);
router.post('/listExternalIdsByTableName', controller.listExternalIdsByTableName);
router.post('/deleteInstance', controller.deleteInstance);

//user
router.post('/listUser', controller.listUser);
router.post('/searchUser', controller.searchUser);
router.post('/listUserAzureAttribute', controller.listUserAzureAttribute);
router.post('/searchUserAzureAttribute', controller.searchUserAzureAttribute);
router.post('/listScoreCard', controller.listScoreCard);

//Application
router.post('/listApplication', controller.listApplication);
router.post('/listAction', controller.listAction);

router.post('/aggregateObservationOFWA', controller.aggregateObservationOFWA);

router.post('/listSetting', controller.listSetting);
router.post('/listWorkOrderHeader', controller.listWorkOrderHeader);
router.post('/processData', controller.getProcessData);
router.post('/listUserRoleSite', controller.listUserRoleSite);
router.post('/getDataSetId', controller.getDataSetId);
router.post('/listCompExAsset', controller.listCompExAsset);
module.exports = router;
