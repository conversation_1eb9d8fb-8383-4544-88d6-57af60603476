# Docker
# Build and push an image to Azure Container Registry
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- dev

resources:
- repo: self

variables:
  # Container registry service connection established during pipeline creation
  dockerRegistryServiceConnection: 'Digital Plant Docker Images Deploy'
  imageRepository: 'digitalbbso/dev/react'
  containerRegistry: 'acrcdsanalyticsussc01.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: 'latest'

  # Agent VM image name
  # vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      # vmImage: $(vmImageName)
      name: 'GST-Backend-Linux'
    steps:
    - task: npmAuthenticate@0
      inputs:
        workingFile: './.npmrc'
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
