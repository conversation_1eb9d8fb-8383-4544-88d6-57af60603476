// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import { translate, DynamicTranslationArea, TranslationContextProvider, TranslationContext } from '@celanese/celanese-sdk'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let allListData = []
let colSummary = {}
let displayedColumns = [
  'externalId',
  'title',
  'auditNumber',
  // 'corePrinciple',
  // 'process',
  // 'observationType',
  'observationStartDate',
  'observationEndDate',
  'occurrence',
  'year',
  'quarter',
  // 'priority',
  'observer',
  'status',
  'createdTime',
  'createdBy',
  // "lastUpdatedTime",
  // "modifiedBy",
  'actions',
]

let headersKeyVal = {
  externalId: 'ID',
  title: 'Title',
  auditNumber: 'Audit Number',
  corePrinciple:'Core Principle',
  process:'Process',
  observationType:'Observation Type',
  occurrence:'Occurrence',
  observationStartDate:'Start Date',
  observationEndDate:'Due Date',
  year: 'Year',
  quarter: 'Quarter',
  priority: 'Priority',
  observer:'Observer',
  status: 'Status',
  createdTime: 'Created Time',
  createdBy: 'Created By',
  lastUpdatedTime: 'Updated On',
  modifiedBy: 'Updated By',
  actions: 'Actions',
}

var paginationCursor = []
let site
let unit
let search
let startDate
let endDate
let initFlag
let token
let userAccessMenu
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
let corePrincipleId
let processId
let subProcessId
let idToken
function AuditPlan() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')

  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const { locale, updateLocale } = useContext(TranslationContext)

  const [t, i18n] = useTranslation('global')
  const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

    const getIdTokenFromMsal = (token) => {      
      return Promise.resolve(token);
    };
    
    const getAuthToken = () => getIdTokenFromMsal(idToken);

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if(i18n){
    i18n.changeLanguage(newValue)
    console.log('Selected Language: ', selectedLanguage)
    console.log('i18n.language: ', i18n)
    }
  }

  function rowDroDownChange(e) {
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value
    filterData()
  }

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })
    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        // remove prev language data
        // const prevLang = localStorage.getItem('LocaleData')
        // localStorage.removeItem('LocaleData')
        // localStorage.removeItem('APP-OFWATranslationData'+prevLang)
        // localStorage.setItem('LocaleData', e.data.LanguageCode)
        // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        token = e.data.data
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || shouldTranslateDynamic)
      }
      if (e.data.type && e.data.type == 'AuditPlan') {
        if (e.data.action == 'Column') {
          window.parent.postMessage(
            {
              type: 'AuditPlan',
              action: 'Loading',
              data: true,
            },
            '*'
          )
          displayedColumns = e.data.data
          console.log('e.data.data',e.data.data);
          colFun()
        } else if (e.data.action == 'Filter') {
          site = e.data.sites
          corePrincipleId = e.data.corePrincipleId
          processId = e.data.processId
          subProcessId = e.data.subProcessId
          setting();
          console.log('Filter: ', e.data)
          // language
          handleLanguageChange(e.data.LanguageCode)
          updateLocale(e.data.LanguageCode)
          localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
          headersKeyVal = {
            externalId: translate('stLabel.id'),
            title: translate('stLabel.title'),
            auditNumber: translate('stLabel.tablecolsAuditnumber'),
            corePrinciple:translate('stLabel.corePrinciple'),
            process:translate('stLabel.tablecolsProcess'),
            observationType:translate('stLabel.observationType'),
            occurrence:translate('stLabel.occurrence'),
            observationStartDate:translate('stLabel.startDate'),
            observationEndDate:translate('stLabel.tablecolsDuedate'),
            year: translate('stLabel.year'),
            quarter: translate('stLabel.quarter'),
            observer:translate('stLabel.observer'),
            priority: translate('stLabel.priority'),
            status: translate('stLabel.status'),
            createdTime: translate('stLabel.tablecolsCreatedtime'),
            createdBy: translate('stLabel.createdBy'),
            lastUpdatedTime: translate('stLabel.updatedOn'),
            modifiedBy: translate('stLabel.updatedBy'),
            actions: translate('stLabel.actions'),
          }
          console.log('OBSList headersKeyVal', headersKeyVal)
          colFun()
          getData()
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu scheduleList', userAccessMenu)
          colFun()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          console.log('e.data.data',e.data.data);
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        console.log('Language', e.data)
        // remove prev language data
        const prevLang = localStorage.getItem('LocaleData')
        localStorage.removeItem('LocaleData')
        localStorage.removeItem('APP-OFWATranslationData'+prevLang)
        localStorage.setItem('LocaleData', e.data.LanguageCode)
        localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        handleLanguageChange(e.data.LanguageCode)
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
              window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
              console.log('DynamicTranslation', e.data)

        headersKeyVal = {
          externalId: translate('stLabel.id'),
          title: translate('stLabel.title'),
          auditNumber: translate('stLabel.tablecolsAuditnumber'),
          corePrinciple:translate('stLabel.corePrinciple'),
          process:translate('stLabel.tablecolsProcess'),
          observationType:translate('stLabel.observationType'),
          occurrence:translate('stLabel.occurrence'),
          observationStartDate:translate('stLabel.startDate'),
          observationEndDate:translate('stLabel.tablecolsDuedate'),
          year: translate('stLabel.year'),
          quarter: translate('stLabel.quarter'),
          observer:translate('stLabel.observer'),
          priority: translate('stLabel.priority'),
          status: translate('stLabel.status'),
          createdTime: translate('stLabel.tablecolsCreatedtime'),
          createdBy: translate('stLabel.createdBy'),
          lastUpdatedTime: translate('stLabel.updatedOn'),
          modifiedBy: translate('stLabel.updatedBy'),
          actions: translate('stLabel.actions'),
        }

        console.log('OBSLIst headersKeyVal', headersKeyVal)
        colFun()
        getData()
        console.log('Get data called')
      }

      if (e.data.action == 'DynamicTranslation' || e.data.type == 'DynamicTranslation') {
        console.log('App.js DynamicTranslation', e.data)
        idToken = e.data.idToken
        // getAuthToken()
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
        window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
        console.log('DynamicTranslation', e.data)
        updateLocale(e.data.LanguageCode)
        // localStorage.setItem('LocaleData', e.data.LanguageCode)
        // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, e.data.labels)
      }
    }
    setDataCount(1)
    colFun()
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose()
  }, [])

  const [searchParams, setSearchParams] = useSearchParams()

  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    return html`<div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
      ${userAccessMenu &&
      userAccessMenu.ScheduleView &&
      userAccessMenu.ScheduleView.featureAccessLevelCode
        ? html`${userAccessMenu.ScheduleView.featureAccessLevelCode ==
          'ViewAccess'
            ? html`<div
                onClick=${() => viewClick(cInd)}
                title=${translate('stLabel.buttonView')}
                style="height:18px;margin-right:8px;cursor: pointer;"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="20"
                  height="20"
                  x="0"
                  y="0"
                  viewBox="0 0 488.85 488.85"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                  class=""
                >
                  <g>
                    <path
                      d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
      ${userAccessMenu &&
      userAccessMenu.ScheduleEdit &&
      userAccessMenu.ScheduleEdit.featureAccessLevelCode
        ? html`${userAccessMenu.ScheduleEdit.featureAccessLevelCode ==
          'EditAccess'
            ? html`<div
                onClick=${() => editClick(cInd)}
                title=${translate('stLabel.edit')}
                style="height:18px;margin-right:8px;cursor: pointer;"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}

       <div   onClick=${() => detailsClick(cInd)}
                title=${translate('stLabel.schDetails')}
                style="height:18px;margin-right:8px;cursor: pointer;"
                >
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"  width="18"
                  height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M384 277.333c-58.91 0-106.667 47.756-106.667 106.667S325.09 490.667 384 490.667 490.667 442.91 490.667 384 442.91 277.333 384 277.333zM377.6 441.6c-8.295 8.131-21.571 8.131-29.867 0l-42.667-42.667 29.867-29.867 27.733 27.733 70.4-70.4 29.867 29.867L377.6 441.6z" fill="#1A2254" opacity="1" data-original="#000000" class=""></path><path d="M256 384c0-70.692 57.308-128 128-128h21.333V21.333C405.333 9.551 395.782 0 384 0H42.667C30.885 0 21.333 9.551 21.333 21.333v469.333c0 11.782 9.551 21.333 21.333 21.333H384C313.308 512 256 454.693 256 384zM170.667 85.333H320V128H170.667V85.333zm0 106.667h106.667v42.667H170.667V192zm-64 149.333c-11.782 0-21.333-9.551-21.333-21.333s9.551-21.333 21.333-21.333c11.782 0 21.333 9.551 21.333 21.333s-9.551 21.333-21.333 21.333zm0-106.666c-11.782 0-21.333-9.551-21.333-21.333 0-11.782 9.551-21.333 21.333-21.333 11.782 0 21.333 9.551 21.333 21.333 0 11.781-9.551 21.333-21.333 21.333zm0-106.667c-11.782 0-21.333-9.551-21.333-21.333 0-11.782 9.551-21.333 21.333-21.333 11.782 0 21.333 9.551 21.333 21.333 0 11.782-9.551 21.333-21.333 21.333zm128 213.333h-64v-42.667h64v42.667z" fill="#1A2254" opacity="1" data-original="#000000" class=""></path></g></svg>
       </div>
         <div    onClick=${() => disableClick(cInd)}
                  title=${translate('stLabel.disable')}
                  style="height:18px; margin-right:12px;cursor: pointer;" >
       <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"   width="15"
                    height="15" x="0" y="0" viewBox="0 0 384 384" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M192 384c105.863 0 192-86.129 192-192 0-51.328-19.96-99.55-56.207-135.793C291.543 19.961 243.328 0 192 0 86.129 0 0 86.129 0 192c0 51.328 19.96 99.55 56.207 135.793C92.449 364.039 140.672 384 192 384zm0-32c-37.39 0-72.8-12.71-101.297-36.078L315.922 90.703C339.289 119.2 352 154.61 352 192c0 88.223-71.777 160-160 160zm0-320c37.383 0 72.8 12.71 101.29 36.078L68.077 293.297C44.711 264.8 32 229.39 32 192c0-88.223 71.777-160 160-160zm0 0" fill="#1A2254" opacity="1" data-original="#000000" class=""></path></g></svg>
       </div>

       <div
                  onClick=${() => sendClick(cInd)}
                  title=${translate('stLabel.sendAction')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="20"
                    height="20"
                    x="0"
                    y="0"
                    viewBox="0 0 32 32"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="m28.91 4.417-11 24a1 1 0 0 1-1.907-.334l-.93-11.157-11.156-.93a1 1 0 0 1-.334-1.906l24-11a1 1 0 0 1 1.326 1.326z"
                        fill="#1A2254"
                        data-original="#000000"
                      ></path>
                    </g>
                  </svg>
                </div>

    </div> `
  }
  function disableClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'disableClick', data: listData[parseInt(index)] },
      '*'
    )
  }
  function sendClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'createAction', data: listData[parseInt(index)] },
      '*'
    )
  }
  function detailsClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'scheduleDetails', data: listData[parseInt(index)] },
      '*'
    )
  }

  function editClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'Edit', data: listData[parseInt(index)] },
      '*'
    )
  }
  function viewClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'View', data: listData[parseInt(index)] },
      '*'
    )
  }
  function openNewTab(externalId) {
    const url = Constants.AIM_URL+"/action-item/details/"+externalId;
    window.open(url, '_blank'); // Open URL in a new tab
  }
  
  
  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    console.log('displayedColumns',displayedColumns)
    console.log('colSummary',colSummary);
    main.redefine('configuration', {
      columns: displayedColumns,
      header: headersKeyVal,
      headerSummary: colSummary,
      format: {
        // externalId: (x, i) => {
        //   return html`<div style="cursor: pointer;" onClick=${() => openNewTab(x)}>
        //     ${x}
        //   </div>`;
        // },
        
        createdTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        observationStartDate: (x) => format(new Date(x), dateFormat),
        observationEndDate: (x) => format(new Date(x), dateFormat),
        status: (x, i, j) => {
          if (j[i].status == 'Disabled') {
            return html`<div>
          <span style="color:red" > ${j[i].status}</span>
         
           </div>`
          }else{
            return html`<div>
            <span style="color:blsck" > ${j[i].status}</span>
           
             </div>`
          } 
        },
        id: (x) => {
          return x.toString()
        },
        site: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        unit: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        location: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        actions: (x, i, j) => {
          if(j[i].status == "Disabled"){
            return html`<div></div>`;
          }else{
            return action1(x, i);
          }
        },
      },
      align: {
        externalId: 'left',
        title: 'left',
        auditNumber: 'left',
        corePrinciple:'left',
        process:'left',
        observationType:'left',
        occurrence:'left',
        observationStartDate:'left',
        observationEndDate:'left',
        year: 'left',
        quarter: 'left',
        observer:'Observer',
        priority: 'left',
        status: 'left',
        createdTime: 'left',
        createdBy: 'left',
        lastUpdatedTime: 'left',
        modifiedBy: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        externalId: 200,
        createdOn: 200,
        createdBy: 200,
        updatedOn: 200,
        modifiedBy: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }
  function getQuarter(dateString) {

    const date = new Date(dateString);
    
    // Ensure the date is valid
    if (isNaN(date)) {
        return 'Invalid date';
    }

    // Get the month (0-based index)
    const monthIndex = date.getMonth();

    // Determine the quarter
    let quarter;
    if (monthIndex >= 0 && monthIndex <= 2) {
        quarter = 'Q1';
    } else if (monthIndex >= 3 && monthIndex <= 5) {
        quarter = 'Q2';
    } else if (monthIndex >= 6 && monthIndex <= 8) {
        quarter = 'Q3';
    } else if (monthIndex >= 9 && monthIndex <= 11) {
        quarter = 'Q4';
    } else {
        return 'Invalid date';
    }

    return quarter;
}

  async function getData() {
    console.log('getData called')
    console.log(startDate, endDate)
    fetch(Constants.NODE_API + '/api/service/listSchedule', {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + token,
        Accept: 'application/json',
        'Content-type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify({
        sites: site,
        startDate: startDate,
        endDate: endDate,
        corePrincipleId:corePrincipleId,
        processId:processId,
        subProcessId:subProcessId
      }),
    })
      .then((res) => res.json())
      .then((result) => {
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   console.log('temp before', temp[1].childNodes[0].nodeValue)
        //   temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //   console.log('temp after', temp[1].childNodes[0].nodeValue)
        // }
        var listProcess =
          result['data']['list' + Constants.typeSchedule]['items']
        listData = []
        listProcess.forEach((element) => {
          element['actions'] = ''
          // element["site"] = element["refSite"] ? element["refSite"]["description"] : "";
          var sDate = new Date(element.observationStartDate)
          element['auditNumber'] = element['auditNumber'] ? element['auditNumber'] : '';
       
          element['corePrinciple'] =  element['refOFWACorePrinciple']?element['refOFWACorePrinciple'].name:'';
          element['process'] =  element['refOFWAProcess']?element['refOFWAProcess'].name:'';
          element['observationType'] =  element['refOFWAObservationType']?element['refOFWAObservationType'].name:'';

          element['year'] = element['year']? element['year'] + '':sDate.getFullYear()+ '';
          element['quarter'] =  element['quarter']? element['quarter']: getQuarter(element.observationStartDate);
          element['observer'] = element['performerAzureDirectoryUserID']? element['performerAzureDirectoryUserID']['firstName']+' '+element['performerAzureDirectoryUserID']['lastName']:'';
          listData.push(element)
        })
        window.parent.postMessage(
          {
            type: 'AuditPlan',
            action: 'Loading',
            data: true,
          },
          '*'
        )

        //  main.redefine("data", listData);
        setCurrentPage(1)
        initFlag = true
        filterData()
        // colFun();
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  function filterData() {
    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }


  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
    <DynamicTranslationArea
      getAuthToken={getAuthToken}
      dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
      shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
    >
      <TranslationContextProvider
        getAuthToken={getAuthToken}
      >
      <div ref={viewofSelectionRef} />
      </TranslationContextProvider>
      </DynamicTranslationArea>
      <div className='tableBottom no-translate'>
        <div></div>
        <Pagination
          className='pagination-bar'
          //assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default AuditPlan
