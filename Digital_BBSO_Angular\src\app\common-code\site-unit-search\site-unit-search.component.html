<div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">

    <div fxLayoutAlign="start center">
        <span>
            <svg width="25" height="25" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" fill="#083D5B">
                <path
                    d="M13 5C11.35503 5 10 6.3550302 10 8L10 9.1425781C8.279832 9.5899132 7 11.141562 7 13L7 18L13 18L13 20L7 20L7 26.349609C4.673 27.175609 3 29.393 3 32C3 34.607 4.673 36.824391 7 37.650391L7 43L1 43L1 45L47 45L49 45L49 44L49 43L46 43L46 24L44.935547 24L43.935547 9L40 9L39.132812 22L42 22L42 24L23 24L23 8C23 6.3550302 21.64497 5 20 5L13 5 z M 13 7L20 7C20.56503 7 21 7.4349698 21 8L21 24L17 24L17 43L15 43L15 13C15 11.141562 13.720168 9.5899132 12 9.1425781L12 8C12 7.4349698 12.43497 7 13 7 z M 9 28C11.206 28 13 29.794 13 32C13 34.206 11.206 36 9 36C6.794 36 5 34.206 5 32C5 29.794 6.794 28 9 28 z M 20 28L24 28L24 30L20 30L20 28 z M 26 28L30 28L30 30L26 30L26 28 z M 33 28L37 28L37 30L33 30L33 28 z M 39 28L43 28L43 30L39 30L39 28 z M 20 33L24 33L24 35L20 35L20 33 z M 26 33L30 33L30 35L26 35L26 33 z M 33 33L37 33L37 35L33 35L33 33 z M 39 33L43 33L43 35L39 35L39 33 z" />
            </svg>
        </span>
        <span class="site-unit-search-control-label">{{ 'SITE_UNIT.SITE' | translate }}</span>
        <mat-form-field appearance="outline" class="set-back-color">
            <mat-select placeholder="Choose site" [formControl]="siteControl" disableOptionCentering>
                <mat-select-filter [placeholder]="'COMMONFILTER.SEARCH' | translate" [displayMember]="'name'" [array]="siteList"
                    (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                <br>
                <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                    {{item.name}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <!-- <div fxLayoutAlign="start center" class="ml-20">
        <span>
            <svg width="25" height="25" viewBox="0 0 24 24"  fill="#083D5B"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M2.92444 4.12856C2.98896 2.93485 3.9757 2 5.17116 2H6.32894C7.52439 2 8.51114 2.93485 8.57566 4.12856L9.4135 19.6286C9.48315 20.917 8.45714 22 7.16678 22H4.33332C3.04296 22 2.01695 20.917 2.0866 19.6286L2.92444 4.12856Z" />
                <path
                    d="M9.51184 22C10.1114 21.3751 10.4626 20.5125 10.4119 19.5746L9.85566 9.28385L14.5218 5.42222C14.7456 5.23698 15.0563 5.19768 15.3192 5.32137C15.5821 5.44506 15.75 5.70947 15.75 6.00002V10.2404L20.7306 5.45898C20.9474 5.25083 21.2675 5.19223 21.544 5.31007C21.8205 5.42792 22 5.69946 22 6.00002V19.75C22 20.9927 20.9926 22 19.75 22H9.51184ZM13 20.5H18V16C18 15.4477 17.5523 15 17 15H14C13.4477 15 13 15.4477 13 16V20.5Z" />
            </svg>
        </span>
        <span class="site-unit-search-control-label">{{ 'SITE_UNIT.UNIT' | translate }}</span>

        <mat-form-field appearance="outline" class="set-back-color">
            <mat-select placeholder="Choose unit" [formControl]="unitControl">
                <mat-select-filter [placeholder]="'Serach'" [displayMember]="'name'" [array]="unitList"
                    (filteredReturn)="filteredUnitList =$event"></mat-select-filter>
                <mat-option *ngFor="let item of filteredUnitList" [value]="item.externalId">
                    {{item.description}}
                </mat-option>
            </mat-select>
        </mat-form-field>

    </div> -->
</div>