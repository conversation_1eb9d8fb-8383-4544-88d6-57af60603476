import { SelectionModel } from "@angular/cdk/collections";
import { DatePipe } from "@angular/common";
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { MatDatepicker } from "@angular/material/datepicker";
import { MatPaginator } from "@angular/material/paginator";
import {MatSort, Sort, MatSortModule, SortDirection} from '@angular/material/sort';
import {MatTableDataSource, MatTableModule} from '@angular/material/table';
import { Router } from "@angular/router";
import { TableColumnsSchemaDtl } from "../../common.interface";

@Component({
    selector: 'commom-mat-table',
    templateUrl: './mat-table.component.html',
    styleUrls: ['./mat-table.component.scss'],
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class CommonMatTableComponent implements OnInit {
  @ViewChild(MatPaginator, { static: false }) matPaginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) matSort: MatSort;
  @ViewChild("paginator") paginator: MatPaginator;
  @ViewChild("rangePicker", { read: undefined, static: false }) datePicker: MatDatepicker<Date>;
  
  @Input() tableColumnSchemaDtls: TableColumnsSchemaDtl[];

  @Output() rowAction: EventEmitter<any> = new EventEmitter<any>();
  @Input() className:string = 'default';
  @Input() callbackFunction: (args: any) => void; // function to call on action
  @Input() tableMaxWidth: number;
  @Input() tableMaxHeight: number;
  @Input() hasTableFooter: boolean = false;
  @Input() tableFooterData: string[] = [];
  @Input() hasPagination: boolean = true;
  @Input() isSearchable: boolean = false;
  
  @Input() rowActions: any[] = [];
  @Input() actionColumnName: string = "Actions";
  @Input() formDefaultNavigateURL = true;
  @Input() paginationSizes: number[] = [5, 10, 15, 20, 25, 30, 50];
  @Input() defaultPageSize = this.paginationSizes[1];
  @Input() sortField: string;
  @Input() sortDirection: SortDirection;
  @Input() hasDateRangePicker: boolean = false;
  @Input() filterAddEmpty: boolean = false;
  @Input() hasBorder: boolean = false;
  @Input() set tableDataSource(data: any[]) {
    this.setTableDataSource(data);
    this.getColumnFilterDropdownList(data);
  }
  //TableRowAction
  @Input() noContentMessage: string = "No match found";
  @Input() showCheckBox: boolean = false;
  @Output() checkBoxEvent: EventEmitter<any> = new EventEmitter<any>();

  @Input() initialSelection: number[] = [];
  @Input() allowMutliSelect: boolean = true;
  selection = new SelectionModel<any>(this.allowMutliSelect, this.initialSelection);
  checkboxCloumnName: string = "checkBox";

  placeHolder: string = "Search";
  filterValues = {};
  columnDataType: string;
  filterOrder: Set<string> = new Set();
  selectedDateRange: any[] = [];
  globalFilterArr: any[] = [];
  uuidValue: string;
  public tableObj = {
    "displayedColumns": [],
    "columnsToDisplay": [],
    "dataSource": new MatTableDataSource([]),
  };

  constructor(private router: Router, private datePipe: DatePipe) {
    
  }

  ngOnInit(): void {
      this.tableObj.displayedColumns = this.tableColumnSchemaDtls.map((c) => c.name);
        // if (this.rowActions.length > 0) {
        //     this.tableObj.displayedColumns = [...this.tableObj.displayedColumns, this.actionColumnName];
        // }
        // if (this.showCheckBox) {
        //   this.tableObj.displayedColumns = [this.checkboxCloumnName, ...this.tableObj.displayedColumns];
        // }
    }
     
    
      ngAfterViewInit() {
        this.tableObj.dataSource.paginator = this.matPaginator;
        this.tableObj.dataSource.sort = this.matSort;
      }

      getSortIcon(sortKey): string {
        if (sortKey == this.sortField) {
          if (this.sortDirection === "asc") {
            return "arrow_upward";
          }
          return "arrow_downward";
        }
        return "swap_vert";
      }
    
      sortColumn(columnName: string, columnType: string, hasSort?: boolean) {
        if (hasSort) {
          this.sortField = columnName;
          this.sortDirection = this.sortDirection === "asc" ? "desc" : "asc";
          this.columnDataType = columnType;
          this.matSort.active = columnName;
          this.matSort.direction = this.sortDirection;
          this.tableObj.dataSource.sort = this.matSort;
        }
      }

      onFilterIconClick(columnOpen) {
        this.columnDataType = columnOpen;
      }

    
      clearFilters(column) {
        this.filterValues[column] = [];
        this.tableObj.dataSource.filter = JSON.stringify(this.filterValues);
        this.filterOrder.delete(column);
        if (this.columnDataType === "date" && this.hasDateRangePicker) {
        //   this.dateRange.get("startDate").setValue("");
        //   this.dateRange.get("endDate").setValue("");
          this.selectedDateRange = [];
        }
        this.resetFilterDropdown();
      }

      selectFilterCheckBox(event, cb, col, item) {
        if (this.selectedDateRange.length > 0 && col.type === "date") {
          return;
        }
        event.stopPropagation();
        cb.toggle();
        this.updateFilter(col.dataKey, item);
      }

      updateFilter(column: string, filter: string) {
        if (!this.filterValues.hasOwnProperty(column)) {
          this.filterValues[column] = [filter];
          this.filterOrder.add(column);
        } else {
          if (this.filterValues[column].includes(filter)) {
            this.filterValues[column] = this.filterValues[column].filter((filterValue) => filterValue !== filter);
          } else {
            this.filterValues[column].push(filter);
          }
          this.filterValues[column].length === 0 ? this.filterOrder.delete(column) : this.filterOrder.add(column);
        }
        this.tableObj.dataSource.filter = JSON.stringify(this.filterValues);
        this.resetFilterDropdown();
      }

      isChecked(key, value): boolean {
        if (this.filterValues.hasOwnProperty(key)) {
          if (this.filterValues[key].includes(value)) {
            return true;
          }
        }
        return false;
      }

      resetFilterDropdown() {
        for (let i = 0; i < this.tableColumnSchemaDtls.length; i++) {
          const columnSchema = this.tableColumnSchemaDtls[i];
          const searchText = '';
          //const searchText = this.searchForm.get("searchText").value;
          let filteredDataSet = [...columnSchema.dropdownFilterOptions];
          if (this.filterOrder.values().next().value === columnSchema.dataKey) {
            filteredDataSet = searchText ? this.globalFilterArr.map((item) => item[columnSchema.dataKey]) : this.tableObj.dataSource.data.map((item) => item[columnSchema.dataKey]);
          }
          if (!this.filterOrder.has(columnSchema.dataKey)) {
            filteredDataSet = this.tableObj.dataSource.filteredData.map((item) => item[columnSchema.dataKey]);
          }
          this.sortFilterList(filteredDataSet, columnSchema.type);
          columnSchema.dropdownFilterOptions = new Set(filteredDataSet);
          if (columnSchema.type === "date") {
            this.getDateColumnValue(columnSchema, filteredDataSet);
          }
        }
      }

      getDateColumnValue(col, list) {
        const tranformedDate = list.map((element) => {
          let d = new Date(element);
          let formattedDate = this.datePipe.transform(d, "MM/dd/yyyy");
          return formattedDate;
        });
        col.dropdownFilterOptions = new Set(tranformedDate);
      }
      
     
      setTableDataSource(data: any) {
        this.tableObj.dataSource = new MatTableDataSource<any>(data);
        this.tableObj.dataSource.paginator = this.matPaginator;
        this.tableObj.dataSource.filterPredicate = this.getColumnFilteredList;
        this.matSort.active = this.sortField;
        this.matSort.direction = this.sortDirection;
        this.tableObj.dataSource.sort = this.matSort;
        this.tableObj.dataSource.sortData = (data: any[], sort: MatSort): any[] => {
          let sortKey = sort.active;
          const direction = this.sortDirection === "asc" ? 1 : -1;
          if (!sort.active || sort.direction === "") {
            return data;
          }
          return data.sort((a, b) => this.sortFunction(a[sortKey], b[sortKey]) * direction);
        };
        // Retain filter when data change
        this.tableObj.dataSource.filter = JSON.stringify(this.filterValues);
        this.matSort.sortChange.emit(this.matSort);
      }
    
      getColumnFilterDropdownList(data) {
        if (data) {
          this.tableColumnSchemaDtls.filter((o) => {
            let dropdownList = data.map((item) => {
              return item[o.dataKey];
            });
            this.sortFilterList(dropdownList, o.type);
            o.dropdownFilterOptions = new Set(dropdownList);
            if (o.type === "date") {
              this.getDateColumnValue(o, dropdownList);
            }
          });
        }
      }
    
      
    
      // **** Action rule section ****//
    
      getRuleField(field: string, rules: any[]): string {
        if (!rules) return "";
        const filteredRules = rules.filter((e) => !!e[field] && this.isRuleApplicable(e));
        const values = filteredRules.map((e) => e[field]);
        return values ? values.join(" ") : "";
      }
    
      isRuleApplicable(rule: any): boolean {
        switch (rule.name) {
          case "one_row":
            return this.tableObj.dataSource.data?.length == 1;
          default:
            return false;
        }
      }
    
    
      onSearch(event: Event): void {
        this.filterValues["globalFilter"] = (event.target as HTMLInputElement).value;
        this.tableObj.dataSource.filter = JSON.stringify(this.filterValues);
        this.globalFilterArr = this.tableObj.dataSource.filteredData;
        this.getColumnFilterDropdownList(this.globalFilterArr);
        this.resetFilterDropdown();
      }
    
     
    
      sortFunction(a: string, b: string, columnType?: string) {
        let columnDataType = columnType ? columnType : this.columnDataType;
        if (columnDataType === "date") {
          const aVal: number = Date.parse(a) || 0;
          const bVal: number = Date.parse(b) || 0;
          return aVal - bVal;
        } else {
          const aVal = typeof a === "string" ? a.trim().toLowerCase() : a || "";
          const bVal = typeof b === "string" ? b.trim().toLowerCase() : b || "";
          if (aVal < bVal) return -1;
          if (aVal > bVal) return 1;
          return 0;
        }
      }
    
      sortFilterList(list, columnType): (a: string, b: string) => number {
        if (list) {
          return list.sort((a, b) => {
            return this.sortFunction(a, b, columnType);
          });
        }
      }
    
     
    
     

      getColumnFilteredList(data: any, filters: string): boolean {
        const filterValues = JSON.parse(filters);
        let colMatch = true;
        if (filterValues.globalFilter) {
          const search = filterValues.globalFilter.toString().trim().toLowerCase();
          colMatch = Object.keys(data).findIndex((prop) => data[prop] && data[prop].toString().trim().toLowerCase().indexOf(search) !== -1) !== -1;
        }
        for (let filterKey in filterValues) {
          if (filterValues[filterKey].length && filterKey !== "globalFilter") {
            let searchValue = data[filterKey];
            // Added for history log date column
            if (filterKey === "lastUpdatedDate" || filterKey === "createdDate") {
              const tdate = searchValue != null ? searchValue.split(" ")[0] : "";
              searchValue = tdate;
            }
            // ends
            colMatch = colMatch && filterValues[filterKey].includes(searchValue);
          }
        }
    
        return colMatch;
      }
    
      
    
      onActionClick(columnSchema: any, elementData: any) {
        console.log("onActionClick ", elementData, columnSchema);
    
        if (columnSchema.owner && this.callbackFunction) {
          this.callbackFunction.apply(columnSchema.owner, [elementData]);
        } else if (columnSchema && columnSchema.navigateURL) {
          console.log("in onClickEdit", elementData, columnSchema.navigateURL);
          const id: number = Number(elementData?.id || elementData?.documentTypeId || elementData?.documentID);
          this.router.navigate([columnSchema.navigateURL, id], { state: { prevPath: this.router.url } });
        } else if (columnSchema && columnSchema.actionType) {
          console.log("columnSchema.actionType ", columnSchema.actionType);
          this.rowAction.emit({ action: columnSchema, rowData: elementData });
        }
      }
    
     
    
      
    
     
    
      onDateRangeChange(evt, column: string) {
        // const startDate = this.dateRange.get("startDate").value;
        // const endDate = this.dateRange.get("endDate").value;
        // let allDates = [];
        // const theDate = new Date(startDate);
        // while (theDate < endDate) {
        //   const transformedDate = this.datePipe.transform(theDate, "MM/dd/yyyy");
        //   allDates = [...allDates, transformedDate];
        //   theDate.setDate(theDate.getDate() + 1);
        // }
        // allDates = [...allDates, this.datePipe.transform(this.dateRange.get("endDate").value, "MM/dd/yyyy")];
        // if (!this.filterValues.hasOwnProperty(column)) {
        //   this.filterValues[column] = allDates;
        //   this.filterOrder.add(column);
        // } else {
        //   this.filterValues[column] = [];
        //   const isDateIncluded = this.filterValues[column].some((d) => this.selectedDateRange.includes(d));
        //   if (isDateIncluded) {
        //     this.filterValues[column] = this.filterValues[column].filter((filterValue) => !this.selectedDateRange.includes(filterValue));
        //     this.filterValues[column].push(...allDates);
        //   } else {
        //     this.filterValues[column].push(...allDates);
        //   }
        //   this.filterValues[column].length === 0 ? this.filterOrder.delete(column) : this.filterOrder.add(column);
        // }
        // this.selectedDateRange = allDates;
        // this.tableObj.dataSource.filter = JSON.stringify(this.filterValues);
        // this.resetFilterDropdown();
      }
      formNavigationUrl(element, col) {
        if (this.formDefaultNavigateURL) {
          this.navigateToURL(col.navigateURL, element[col.dataKey]);
        }
      }
      navigateToURL(path, id) {
        this.router.navigate([path, id], { state: { prevPath: this.router.url } });
      }
     
    
      openDatePicker() {
        this.datePicker.open();
      }
    
      
    
      isAllSelected() {
        return this.selection.selected.length == this.tableObj.dataSource.data.length;
      }
    
      onSingleSelect(row: any) {
        this.selection.toggle(row);
        this.checkBoxEvent.emit(this.selection.selected);
      }
    
      toggleAllRows() {
        this.isAllSelected() ? this.selection.clear() : this.tableObj.dataSource.data.forEach((row) => this.selection.select(row));
        this.checkBoxEvent.emit(this.selection.selected);
      }
    
      onLinkClick(url: string) {
        window.open(url, "_blank");
      }
}