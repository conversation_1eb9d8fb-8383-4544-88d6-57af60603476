import { animation, trigger, animateChild, group, transition, animate, style, query, state, keyframes } from '@angular/animations';

export const Animations = {
  openClose:  trigger('openClose', [
    state('open', style({
      width: '190px'
    })),
    state('closed', style({
      width: '80px'
    })),
    transition('open => closed', animate('400ms ease-in-out')),
    transition('closed => open', animate('400ms ease-in-out'))
  ]),

 hideShow: trigger('hideShow', [
  state('show', style({
    opacity: 1,
    visibility: 'visible',
    overflow: 'visible',
    display: 'block'
  })),
  state('hide', style({
    opacity: 0,
    visibility: 'hidden',
    overflow: 'hidden',
    display: 'none'
  })),
  transition('show => hide', animate('400ms ease-in-out')),
  transition('hide => show', animate('400ms ease-in-out'))
 ]),

 paddingIncreaseReduce: trigger('paddingIncreaseReduce', [
  state('increase', style({
    padding: '85px 20px 20px 210px'
  })),
  state('reduce', style({
    padding: '85px 20px 20px 100px'
  })),
  transition('increase => reduce', animate('400ms ease-in-out')),
  transition('reduce => increase', animate('400ms ease-in-out'))
 ]),
}