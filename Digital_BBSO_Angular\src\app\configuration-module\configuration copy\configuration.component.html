<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">

        <div class="ovservation-list-section configuration-section">
            <div class="audit-plan-unit-section">
                <commom-label [labelText]="'CONFIGURATION.TITLE'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>
                <div class="audit-head-icon">
                    <div class="icon-bg-box" (click)="formConfig();">
                        <mat-icon class="configuration_node_add">note_add</mat-icon>
                    </div>
                    <div class="icon-bg-box" (click)="goPage('configuration/config-list')">
                        <mat-icon class="configuration_list">list</mat-icon>
                    </div>
                </div>
            </div>


            <div class="marginTop">

                <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                    <div fxLayout="row auto" fxLayoutAlign="start center">
                       
                        <div class="treat-md">
                            <span class="semi-bold">{{ 'CONFIGURATION.UNIT_SITE.SITE' | translate }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()" placeholder="Choose site" [formControl]="siteControl"
                                    disableOptionCentering>
                                    <mat-select-filter [placeholder]="'Search'" [displayMember]="'description'" [array]="siteList"
                                        (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                        </div>

                    </div>
                    <div style="display: inline-grid !important;">
                        <div fxLayoutAlign="start center">

                            <span class="subheading-1 marginLeft-5 marginRight-5">{{ 'CONFIGURATION.UNIT_SITE.PROCESS' |
                                translate }}</span>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()" placeholder="Choose process" [formControl]="processControl"
                                    disableOptionCentering>
                                    <mat-select-filter [placeholder]="'Search'" [displayMember]="'name'" [array]="processList"
                                        (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredProcessList" [value]="item.externalId">
                                        {{item.name}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <div class="marginTop">
                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.CORE_PRINCIPLE'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
            </div>
            <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                <div *ngFor="let item of corePrincipleList" (click)="selectCorePriciple(item)" fxLayout="row"
                    [ngClass]="(selectedCorePrinciple && selectedCorePrinciple.externalId) == item.externalId ? 'category_box' : 'category_box_light'"
                    fxLayoutAlign="center center">
                    <div fxFlexAlign="center">
                        <span>
                            {{item.name}}
                        </span>
                    </div>
                </div>
            </div>

            <div *ngIf="auditBool == false" class="marginTop">
                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.OBSERVATION_TYPE'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>

            </div>
            <div *ngIf="auditBool == false" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                <div *ngFor="let item of observationList" (click)="selectSubProcess(item)" fxLayout="row"
                    [ngClass]="(selectedSubProcess && selectedSubProcess.externalId) == item.externalId ? 'category_box' : 'category_box_light'"
                    fxLayoutAlign="center center">
                    <div fxFlexAlign="center">
                        <span>
                            {{item.name}}
                        </span>
                    </div>
                </div>
                <div fxLayout="row" class="category_box_light" (click)="addObservation()" fxLayoutAlign="center center">
                    <div fxLayout="row" style="align-items: center;">
                        <mat-icon class="add_Icon">add_circle</mat-icon>
                        <span>
                            {{ 'CONFIGURATION.FORM_CONTROLS.ADD_OBSERVATION_TYPE' | translate }}
                        </span>
                    </div>
                </div>

            </div>
            <div *ngIf="auditBool == true" class="marginTop">
                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.AUDIT_TYPE'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
            </div>
            <div *ngIf="auditBool == true" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">

                <div *ngFor="let item of auditList" (click)="audithighlightColor(item)" fxLayout="row"
                    [ngClass]="auditSelected == item.id ? 'category_box' : 'category_box_light'"
                    fxLayoutAlign="center center">
                    <div fxFlexAlign="center">
                        <span>
                            {{item.name}}
                        </span>
                    </div>

                </div>
                <div fxLayout="row" class="category_box_light" (click)="addAudit()" fxLayoutAlign="center center">
                    <div fxLayout="row" style="align-items: center;">

                        <mat-icon class="add_Icon">add_circle</mat-icon>

                        <span>
                            {{ 'CONFIGURATION.FORM_CONTROLS.ADD_AUDIT_TYPE' | translate }}
                        </span>
                    </div>

                </div>



            </div>

            <div class="marginTop">
                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.CATEGORY'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
            </div>
            <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">

                <div *ngFor="let item of categoriesList" (click)="categorieshighlightColor(item)" fxLayout="row"
                    [ngClass]="categoriesSelected == item.id ? 'category_box' : 'category_box_light'"
                    fxLayoutAlign="center center">
                    <div fxFlexAlign="center">
                        <span>
                            {{item.name}}
                        </span>
                    </div>

                </div>
                <div fxLayout="row" class="category_box_light" (click)="addCategories()" fxLayoutAlign="center center">
                    <div fxLayout="row" style="align-items: center;">

                        <mat-icon class="add_Icon">add_circle</mat-icon>

                        <span>
                            {{ 'CONFIGURATION.FORM_CONTROLS.ADD_CATEGORY' | translate }}
                        </span>
                    </div>

                </div>

            </div>

            <div class="marginTop">
                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.SUB_CATEGORY'" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
            </div>
            <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">

                <div *ngFor="let item of subCategoriesList" (click)="subighlightColor(item)" fxLayout="row"
                    [ngClass]="subSelected == item.id ? 'category_box' : 'category_box_light'"
                    fxLayoutAlign="center center">
                    <div fxFlexAlign="center">
                        <span>
                            {{item.name}}
                        </span>
                    </div>

                </div>
                <div fxLayout="row" class="category_box_light" (click)="addSubCatrgory()" fxLayoutAlign="center center">
                    <div fxLayout="row" style="align-items: center;">

                        <mat-icon class="add_Icon">add_circle</mat-icon>

                        <span>
                            {{ 'CONFIGURATION.FORM_CONTROLS.ADD_SUB_CATEGORY' | translate }}
                        </span>
                    </div>

                </div>

            </div>

            <div fxLayout="row wrap" fxLayoutGap="20px">
                <div *ngIf="auditBool == false" class="marginTop">
                    <div fxLayout="row" fxLayoutAlign="start center">
                        <span class="body-2">
                            {{ 'CONFIGURATION.FORM_CONTROLS.FEEDBACK' | translate }}
                        </span>
                        <span class="body-2 bold">
                            &nbsp; {{ 'CONFIGURATION.FORM_CONTROLS.UNSAFE' | translate }}
                        </span>
                        <div>
                            &nbsp; &nbsp; &nbsp;
                            <mat-form-field appearance="outline" style="width: 110px;">

                                <mat-select [formControl]="feebackControl">
                                    <mat-option value="Yes">Yes</mat-option>
                                    <mat-option value="No">No</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>
                <div *ngIf="auditBool == false" class="marginTop">
                    <div fxLayout="row" fxLayoutAlign="start center">
                        <span class="body-2">
                            {{ 'CONFIGURATION.FORM_CONTROLS.SIGNATURE' | translate }}
                        </span>

                        <div>
                            &nbsp; &nbsp; &nbsp;
                            <mat-form-field appearance="outline" style="width: 110px;">

                                <mat-select value="Yes">
                                    <mat-option value="Yes">Yes</mat-option>
                                    <mat-option value="No">No</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>

            </div>



            <div *ngIf="auditBool == true" style="margin-top: 37px;">

            </div>
            <div class="create-schedular-fotter">
                <common-lib-button [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'"
                    (buttonAction)="goPage('config-list')"></common-lib-button>
                <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.SAVE'"
                    (buttonAction)="saveConfig();"></common-lib-button>
            </div>

        </div>
    </div>
</div>