<div class="create-observation outerbox">
    <commom-label labelText="{{ labels['cards'+corePrinciple.name] }}"  [tagName]="'h4'" [cstClassName]="'heading unit-heading mb-50'"></commom-label>

    <!-- <div fxLayout="row" class="mb-20" class="justify-center">
        <div class="arrow-box arrow-right">
            <div fxLayout="row" fxLayoutAlign="center center">
                <div fxFlex="15" style="padding-left: 10px;">
                    <span class="semi-bold-28">1</span>
                </div>
                <div fxFlex="85" fxLayout="column">
                    <span class="regular title">
                        {{ 'OBSERVATION.CREATE.CARD.TITLE' | translate }}
                    </span>
                    <span class="semi-regular-12" style="margin-top: 5px;">
                        {{ 'OBSERVATION.CREATE.CARD.DESCRIPTION' | translate }}
                    </span>

                </div>

            </div>
        </div>
        <div class="observe-box">
            <div fxLayout="row" fxLayoutAlign="center center">
                <div fxFlex="15" style="padding-left: 10px;height: 10px;">
                    <span class="semi-bold-28">2</span>
                </div>
                <div fxFlex="85" fxLayout="column">
                    <span class="regular title">
                        {{'Observation type' | translate }} 
                    </span>
                    <span class="semi-regular-12" style="margin-top: 5px;">
                        {{'Choose the Observation type' | translate }}
                    </span>

                </div>

            </div>
        </div>
    </div> -->
    <div fxLayout="row" *ngFor="let item of processList">
        <div fxFlex="100" style="margin-bottom: 20px;" *ngIf="((selectedProcess && selectedProcess.name == item.name) || !selectedProcess) && item.subProcessList.length>0 ">
            <div fxLayout="row" class="process-stl-heading">
                <div class="process-stl-ico" style="margin-right: 10px;">
                    <i [innerHTML]="item.iconImage | safeHtml"></i>
                </div>
                <div fxFlex="auto">
                    <span class="process-subheading-2" *ngIf="item.name != 'Field Walk' || item.name != 'Audit' || item.name != 'Observation' " > 
                        <!-- {{ 'OBSERVATION.MAIN.CARDS.' + item.name | translate}}  -->
                          {{ labels['cards'+item.name] }} 
                    </span>
                    <span class="process-subheading-2" *ngIf="item.name == 'Observation'" > 
                        <!-- {{ 'OBSERVATION.MAIN.CARDS.' + item.name | translate}}  -->
                        {{ labels[item.name.toLowerCase()] }}
                    </span>
                    <span class="process-subheading-2" *ngIf="item.name == 'Field Walk'" > 
                        <!-- {{ 'OBSERVATION.MAIN.CARDS.' + item.name | translate}}  -->
                          {{ labels['fieldWalk'] }} 
                    </span>
                    <span class="process-subheading-2" *ngIf="item.name == 'Audit'" > 
                        <!-- {{ 'OBSERVATION.MAIN.CARDS.' + item.name | translate}}  -->
                          {{ labels['audit'] }} 
                    </span>
                </div>
            </div>
            <div fxLayout="row" >
                <div fxFlex="100" class="d-flex flex-flow-wrap ">
                    <mat-card (click)="selectSubProcess(item,subProcess)"
                        [ngClass]="{'selectedItem':(selectedSubProcess && selectedSubProcess.externalId) == subProcess.externalId,'active-class grid-stl-disable': subProcess.isActive == false, 'grid-stl': (subProcess.isActive == null || subProcess.isActive == true) }"
                        
                        *ngFor="let subProcess of item.subProcessList">
                        <div class="grid-stl-ico">
                            <i [innerHTML]="subProcess.iconImage ? subProcess.iconImage : defaultIcon | safeHtml"></i>
                        </div>
                        <div style="width: 78%; height: 88%; overflow: hidden;">
                            <div class="grid-stl-heading">
                                <span style="font-size: 16px !important;" class="subheading-2">{{ subProcess.name}} </span>
                            </div>
                            <div class="grid-stl-heading" *ngIf="subProcess.description && subProcess.description.length>0">
                                <span [matTooltip]="subProcess.description" class="subtitle" >{{ subProcess.description}} </span>
                            </div>
                        </div>
                       
                        
                    </mat-card>
                </div>
            </div>
        </div>
    </div>
    <!-- <div class="create-observation-section">
        <div fxLayout="row">
            <div fxFlex="100" class="d-flex flex-flow-wrap justify-center">
                <mat-card class="grid-stl" (click)="selectProcess(item)"
                    [ngClass]="(selectedProcess && selectedProcess.externalId) == item.externalId ? 'selectedItem' : ''" *ngFor="let item of processList">
                    <div class="grid-stl-ico">
                        <i [innerHTML]="item.iconImage | safeHtml"></i>
                    </div>
                    <div class="grid-stl-heading">
                        <span class="subheading-2">{{ 'OBSERVATION.MAIN.CARDS.' + item.name |  translate}} </span>
                    </div>
                </mat-card>
            </div>
        </div>
    </div> -->

    <div class="create-obervation-fotter" fxLayout="column" fxLayoutAlign="end end">
            <div fxLayout="row"  fxLayoutGap="10px">
                <common-lib-button [className]="'cancel cst-btn'" text="{{labels['buttonBack']}}"
                (buttonAction)="goBack()"></common-lib-button>
            <!-- <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.NEXT'"
                (buttonAction)="goSubPage()"></common-lib-button>
            </div> -->
           
    </div>

</div>
