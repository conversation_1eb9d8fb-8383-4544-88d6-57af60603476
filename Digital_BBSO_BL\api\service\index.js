'use strict';

var express = require('express');
var controller = require('./service.controller');

var router = express.Router();


router.post('/createInstanceByProperties', controller.createInstanceByProperties);
router.post('/graphql', controller.graphql);
router.post('/instance', controller.instance);
router.post('/classic', controller.classic);
router.post('/space', controller.space);
router.post('/createEdge', controller.createEdge);
router.post('/deleteInstance', controller.deleteInstance);

router.post('/listCommonRefEnum', controller.listCommonRefEnum);
router.post('/listGeoRegion', controller.listGeoRegion);
router.post('/listCountry', controller.listCountry);
router.post('/listReportingLocation', controller.listReportingLocation);
router.post('/listBusinessLineByUnit', controller.listBusinessLineByUnit);
router.post('/listReportingSiteByCountry', controller.listReportingSiteByCountry);
router.post('/listReportingSiteFunctionalLocation', controller.listReportingSiteFunctionalLocation);
router.post('/listReportingSiteCursor', controller.listReportingSiteCursor);
router.post('/listReportingUnitFunctionalLocation', controller.listReportingUnitFunctionalLocation);
router.post('/listReportingUnitCursor', controller.listReportingUnitCursor);
router.post('/listReportingSiteById', controller.listReportingSiteById);
router.post('/listReportingUnitById', controller.listReportingUnitById);
router.post('/listEquipmentByFunctionalLocation', controller.listEquipmentByFunctionalLocation);
router.post('/listFunctionalLocation', controller.listFunctionalLocation);


router.post('/listProcess', controller.listProcess);
router.post('/listProcessBySite', controller.listProcessBySite);
router.post('/listProcessConfigurationByProcess', controller.listProcessConfigurationByProcess);
router.post('/listSubCategory', controller.listSubCategory);
router.post('/listQuestionBankByCategory', controller.listQuestionBankByCategory);
router.post('/listQuestionList', controller.listQuestionList);
router.post('/listObservation', controller.listObservation);
router.post('/listDepartment', controller.listDepartment);

router.post('/listObservationById', controller.listObservationById);
router.post('/listChecklistByCategory', controller.listChecklistByCategory);
router.post('/listFieldWalk', controller.listFieldWalk);
router.post('/priviewImage', controller.priviewImage);
router.post('/listVendor', controller.listVendor);
router.post('/listSupplier', controller.listSupplier);
router.post('/listSchedule', controller.listSchedule);
router.post('/listScheduleDetail', controller.listScheduleDetail);
router.post('/listOFWALog', controller.listOFWALog);
router.post('/scheduleSendToVedor', controller.scheduleSendToVedor);
router.post('/listChecklist', controller.listChecklist);
router.post('/listAudit', controller.listAudit);
router.post('/listAuditSummary', controller.listAuditSummary)
// router.post('/listEvidenceChecklist', controller.listEvidenceChecklist);
// router.get('/fetchAndDelete', controller.fetchAndDelete);

router.post('/aggregateObservation', controller.aggregateObservation);
router.post('/aggregateFieldWalk', controller.aggregateFieldWalk);
router.post('/aggregateAudit', controller.aggregateAudit);
router.post('/aggregateAction', controller.aggregateAction);
router.post('/aggregateChecklist', controller.aggregateChecklist);
router.post('/createInstance', controller.createInstance);
router.post('/listUser', controller.listUser);
router.post('/searchUser', controller.searchUser);
router.post('/listAllUser', controller.listAllUser);
router.post('/listUserAzureAttribute', controller.listUserAzureAttribute);
router.post('/listUserAzureAttributeAllData', controller.listUserAzureAttributeAllData);
router.post('/listScoreCard', controller.listScoreCard);
router.post('/listApplication', controller.listApplication);
router.post('/listAction', controller.listAction);
router.post("/notificationevent", controller.notificationevent);
router.post('/notificationgroup', controller.notificationgroup);
//Python 
router.post('/integrationUser', controller.integrationUser);
router.post('/updateFavouriteLan', controller.updateFavouriteLan);
router.post('/unreadNotificationCount', controller.unreadNotificationCount);
router.post('/aggregateObservationOFWA', controller.aggregateObservationOFWA);
router.post('/listReportingLocationAll', controller.listReportingLocationAll);

router.post('/listSetting', controller.listSetting);
router.post('/processData', controller.processData);
router.post('/getUnitInfo', controller.getUnitInfo);
router.post('/getRolesInfo', controller.getRolesInfo);
router.post('/listUserRoleSite', controller.listUserRoleSite);
router.post('/getDataSetId', controller.getDataSetId);
router.post('/staticTranslation', controller.staticTranslation);
router.post('/dynamicTranslation', controller.dynamicTranslation);
router.post('/listWorkOrderHeader', controller.listWorkOrderHeader);

//field walk transformation API
router.post('/fieldWalkAttendee', controller.fieldWalkAttendee);
router.post('/listCompExAsset', controller.listCompExAsset);

module.exports = router;
