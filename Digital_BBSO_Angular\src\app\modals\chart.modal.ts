import { ApexAxisChartSeries, ApexChart, ApexDataLabels, ApexFill, ApexForecastDataPoints, ApexGrid, ApexLegend, ApexMarkers, ApexPlotOptions, ApexStroke, ApexTitleSubtitle, ApexTooltip, ApexXAxis, ApexYAxis } from "ng-apexcharts";

export type ChartOptions = {
    series?: ApexAxisChartSeries;
    chart?: ApexChart;
    dataLabels?: ApexDataLabels;
    plotOptions?: ApexPlotOptions;
    yaxis?: ApexYAxis;
    xaxis?: ApexXAxis;
    fill?: ApexFill;
    tooltip?: ApexTooltip;
    stroke?: ApexStroke;
    legend?: ApexLegend;
    colors?: string[];
    title?: ApexTitleSubtitle;
    grid?: ApexGrid;
    markers?: ApexMarkers;
    labels?:string[],
    responsive?:any[],
    forecastDataPoints?:ApexForecastDataPoints
};

 

