import { Component, Inject, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, map, startWith } from 'rxjs';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { CommonService } from 'src/app/services/common.service';

@Component({
  selector: 'app-question-list',
  templateUrl: './question-list.component.html',
  styleUrls: ['./question-list.component.scss'],
})
export class QuestionListComponent implements OnInit {
  displayedColumns: any = [];

  allColumns = [
    {
      key: 'externalId',
      displayName: 'ID',
      name: 'externalId',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'name',
      displayName: 'Question #',
      name: 'name',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'description',
      displayName: 'Description',
      name: 'description',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'createdBy',
      displayName: 'Created By',
      name: 'createdBy',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'createdTime',
      displayName: 'Created Time',
      name: 'createdTime',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'date',
      displayName: 'Date',
      name: 'date',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'actions',
      displayName: 'Actions',
      name: 'actions',
      activeFlag: true,
      summary: false,
    },
  ];

  url: any = '';
  configListRow: any;
  loaderFlag: boolean;
  userAccessMenu: any;
  configurationAddQuestion: any;
  constructor(
    private router: Router,
    private tokenService: TokenService,
    private commonService: CommonService,
    private dataService: DataService,
    private dialog: MatDialog
  ) {
    //this.url =this.dataService.React_API+ "/questionList";
    this.url = this.dataService.React_API + '/questionList';

    console.log(history.state);
    this.configListRow = history.state;

    this.loaderFlag = true;
  }

  ngOnInit(): void {
    var _this = this;
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == 'QuestionList') {
        console.log('e.data', e);
        console.log('e.data', e.data.data);
        if (e.data.action == 'FormView') {
          console.log('FormView-->');
          _this.router.navigateByUrl('configuration/add-question', {
            state: {
              data: e.data.data,
              config: _this.configListRow,
              formType: 'View',
            },
          });
        } else if (e.data.action == 'FormEdit') {
          console.log('FormEdit-->');
          _this.router.navigateByUrl('configuration/add-question', {
            state: {
              data: e.data.data,
              config: _this.configListRow,
              formType: 'Edit',
            },
          });
        }
      }
    };
    if (_this.commonService.siteList.length > 0) {
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == 'userAccess') {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu);
          _this.getUserMenuConfig();
        }
      }
    });
  }
  ngAfterViewInit(): void {
    var _this = this;
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { type: 'AuthToken', data: _this.tokenService.getToken() },
      '*'
    );
    iWindow?.postMessage(
      { type: 'FormConfig', action: 'Column', data: this.displayedColumns },
      '*'
    );
    // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');
    _this.getUserMenuConfig();
    setTimeout(() => {
      _this.applyFilter();
    }, 1000);

    if (this.configListRow.externalId == undefined) {
      this.router.navigateByUrl('configuration/config-list', {
        state: {},
      });
    }
  }
  applyFilter() {
    var _this = this;
    var iframe = document.getElementById('iFrameFormConfig');
    console.log('iframe', iframe);
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { type: 'AuthToken', data: _this.tokenService.getToken() },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'FormConfig',
        action: 'Filter',
        subCategory: this.configListRow.externalId,
        LanguageCode: `${this.commonService.selectedLanguage}`,
      },
      '*'
    );
    console.log('applyFilter');
    _this.getUserMenuConfig();
    setTimeout(function () {
      _this.loaderFlag = false;
    }, 2000);
  }
  getUserMenuConfig() {
    var _this = this;

    if (_this.commonService.menuFeatureUserIn.length > 0) {
      _this.configurationAddQuestion =
        _this.commonService.menuFeatureUserIn.find(
          (item) =>
            item.featureCode ==
            _this.dataService.appMenuCode.configurationAddQuestion
        );
      var configurationQuestionList =
        _this.commonService.menuFeatureUserIn.find(
          (item) =>
            item.featureCode ==
            _this.dataService.appMenuCode.configurationQuestionList
        );
      var iframe = document.getElementById('iFrameFormConfig');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { type: 'AuthToken', data: _this.tokenService.getToken() },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'Configuration',
          action: 'AccessMenu',
          data: {
            configurationQuestionList: configurationQuestionList,
          },
        },
        '*'
      );
    } else {
      _this.configurationAddQuestion = {};
      var iframe = document.getElementById('iFrameFormConfig');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { type: 'AuthToken', data: _this.tokenService.getToken() },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'Configuration',
          action: 'AccessMenu',
          data: {
            configurationQuestionList: {},
          },
        },
        '*'
      );
    }
  }

  ngOnDestroy(): void {}
  addQuestion() {
    this.router.navigateByUrl('configuration/add-question', {
      state: { config: this.configListRow, formType: 'Add' },
    });
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result == 'object') {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach((element) => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }
    });

    console.log('_this.displayedColumns,', _this.displayedColumns);
    setTimeout(function () {
      _this.ngAfterViewInit();
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);
  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) {
        myColumns.push(eData);
      }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };
    dialogConfig.height = 'auto';
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result == 'object') {
        this.setSummary();
      }
    });
  }
  setSummary() {
    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.key] = {
          enable: 'Y',
          colorRange: eData['summaryColor'] ? eData['summaryColor'] : [],
        };
      }
    });
    console.log('summaryCol', summaryCol);
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { type: 'AuthToken', data: _this.tokenService.getToken() },
      '*'
    );
    iWindow?.postMessage(
      { type: 'FormConfig', action: 'Summary', data: summaryCol },
      '*'
    );
    _this.getUserMenuConfig();
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  siteSelected(region) {}
  processSelected(process) {}
}
