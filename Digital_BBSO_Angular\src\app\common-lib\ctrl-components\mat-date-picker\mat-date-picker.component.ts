import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
    selector: 'mat-date-picker',
    templateUrl: './mat-date-picker.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class MatDatePickerComponent implements OnInit {
 @Input() className:string= '';
 @Input() SelectedDate:Date;
 @Output() dateInput: EventEmitter<any> = new EventEmitter<any>();
  constructor(){}

   ngOnInit(): void {
       
   }

   onDateChange(event):void {
      this.dateInput.emit(event.value);
   }
}