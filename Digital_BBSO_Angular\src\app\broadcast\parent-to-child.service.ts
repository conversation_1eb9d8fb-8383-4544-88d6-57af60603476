import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { SelectedCountryData } from '../modals/home.modal';

@Injectable({
  providedIn: 'root'
})
export class ParentToChildService {

  private readonly searchChanges$ = new Subject<string>();
  private readonly actionFlexLayOutChange$ = new Subject<string>();
  private readonly confirmPopup$ = new Subject<string>();
  private readonly siteUnitObservable$ = new Subject<any>();

  private readonly expandCollapseActionBroadcast = new BehaviorSubject<boolean>(true);
  public expandCollapseActionBroadcast$ = this.expandCollapseActionBroadcast.asObservable();

  private readonly subHeaderDataBroadcast = new BehaviorSubject<string>('OBSERVATION.SUB_HEADER.TITLE');
  public subHeaderDataBroadcast$ = this.subHeaderDataBroadcast.asObservable();

  private readonly selectedCountryBroadcast = new BehaviorSubject<any>(new SelectedCountryData());
  public selectedCountryBroadcast$ = this.selectedCountryBroadcast.asObservable();

  private readonly showMobileSideBar = new Subject<boolean>();
  public showMobileSideBar$ = this.showMobileSideBar.asObservable();

  private readonly addLinkData =  new Subject<any>();
  public addLinkData$ = this.addLinkData.asObservable();

  private readonly occurrenceData =  new Subject<any>();
  public occurrenceData$ = this.occurrenceData.asObservable();

  broadcastOccurrenceData(data:any):void {
    this.occurrenceData.next(data);
}

  broadcastNewLinkData(data:any):void {
      this.addLinkData.next(data);
  }

  broadcastSidebarAction(state:boolean):void{
     this.expandCollapseActionBroadcast.next(state);
  }

  broadcastSubHeaderdata(data:string):void {
     this.subHeaderDataBroadcast.next(data);
  }

  broadcastSelectedCountry(data:any):void {
    this.selectedCountryBroadcast.next(data);
 }

 broadcastShowMobileSideBar(state:boolean):void {
  this.showMobileSideBar.next(state);
}

  search(text: string): void {
      this.searchChanges$.next(text);
  }


  searchChanges(): Observable<string> { 
      return this.searchChanges$.asObservable();
  }

  actionLayout(text: string): void {
    this.actionFlexLayOutChange$.next(text);
  }
  actionFlexLayOutChange(): Observable<string> { 
    return this.actionFlexLayOutChange$.asObservable();
  }
  confirmPopSetval(text: string): void {
    this.confirmPopup$.next(text);
  }
  confirmPopup(): Observable<string> { 
    return this.confirmPopup$.asObservable();
  }

  siteUnitFunc(text: any): void {
    this.siteUnitObservable$.next(text);
  }
  siteUnitObservable(): Observable<any> { 
    return this.siteUnitObservable$.asObservable();
  }

  


}
