import { HTTP_INTERCEPTORS, HttpEvent, HttpErrorResponse, HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';

// import { AuthService } from '../_services/auth.service';

import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { TokenService } from './services/token.service';
import { DataService } from './services/data.service';
import { Router } from '@angular/router';

const TOKEN_HEADER_KEY = 'Authorization';  // for Spring Boot back-end
// const TOKEN_HEADER_KEY = 'x-access-token';    // for Node.js Express back-end
const IDTOKEN_HEADER_KEY = 'idtoken';   
const httpOptions = {
    headers: new HttpHeaders({ 'Content-Type': 'application/json' })
};

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    //   private isRefreshing = false;
    private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

    constructor(private router: Router, private http: HttpClient, private tokenService: TokenService, private dataService: DataService) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<Object>> {
        let authReq = req;
        const token = this.tokenService.getToken();
        // const token = localStorage.getItem('dataSourceTkn');
        if (token != null && this.isHeaderNeeded(req)) {
            authReq = this.addTokenHeader(req, token);
        }
        return next.handle(authReq).pipe(catchError(error => {
            if (error instanceof HttpErrorResponse && !authReq.url.includes('auth/token') && error.status === 401) {
                return this.handle401Error(authReq, next);
            }

            return throwError(error);
        }));
    }

    private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
        if (!this.dataService.isRefreshing) {
            this.dataService.isRefreshing = true;
            this.refreshTokenSubject.next(null);

            // this.tokenService.saveToken("response.access_token");
            // this.tokenService.saveRefreshToken("response.refresh_token");
            const token = this.tokenService.getRefreshToken();
            if (token){
                // this.router.navigate(['/login']);
                this.dataService.login()
               
            }else{
                this.dataService.isRefreshing = false;
                // this.router.navigate(['/login']);
                this.dataService.login()
            }
        }

        return this.refreshTokenSubject.pipe(
            filter(token => token !== null),
            take(1),
            switchMap((token) => next.handle(this.addTokenHeader(request, token)))
        );
    }

    private addTokenHeader(request: HttpRequest<any>, token: string) {
        /* for Spring Boot back-end */
        const tokenID = this.tokenService.getIDToken();
        if(request.url.includes('cognitedata.com')){
            return request.clone({
                headers: request.headers.set(TOKEN_HEADER_KEY, 'Bearer ' + token)});
        }
        else if(request.url.includes('/user')){
            return request.clone({
                headers: request.headers.set(TOKEN_HEADER_KEY, 'Bearer ' + tokenID)});
        }
        else{
            return request.clone({
                headers: request.headers.set(TOKEN_HEADER_KEY, 'Bearer ' + token)
                    .set(IDTOKEN_HEADER_KEY, 'Bearer ' + tokenID)
                    .set('Cache-Control', 'no-cache, no-store, must-revalidate, post-check=0, pre-check=0')
                    .set('Pragma', 'no-cache')
                    .set('Expires', '0')
            });
        }

        /* for Node.js Express back-end */
        // return request.clone({ headers: request.headers.set(TOKEN_HEADER_KEY, token) });
    }

    isHeaderNeeded(req:any) {
        if (req.url.indexOf('cdn') !=-1  || req.url.indexOf('unpkg') !=-1) { // this condition is up to you, it could be an exact match or how ever you like
            return false;
        } else {
            return true;
        }
    }
}

export const authInterceptorProviders = [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
];
