import { ViewportRuler } from '@angular/cdk/overlay';
import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { MsalService } from '@azure/msal-angular';
import { TranslateService } from '@ngx-translate/core';
import { ParentToChildService } from 'src/app/broadcast/parent-to-child.service';
import { DataService } from 'src/app/services/data.service';
import { ProjectService } from 'src/app/services/project.service';
import { environment } from 'src/environments/environment';
import { MatDialog } from '@angular/material/dialog';
import { UserInterDailogComponent } from '../user-integration-dialog/userInter-dailog.component';
import { CommonService } from 'src/app/services/common.service';
import { TokenService } from 'src/app/services/token.service';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { NgZone } from '@angular/core';
import _ from "lodash";
import { LanguageService } from 'src/app/services/language.service';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderComponent implements OnInit, AfterViewInit {
  title = '';
  assets: any;
  assetsById: any;
  timeseries: any;
  events: any;
  files: any;
  sequences: any;
  models3D: any;
  currentItem: any = 'tele';
  modelTypeList: any = [];
  projectList: any = [];
  modelControl = new FormControl();
  @ViewChild('mySelect') mySelect;
  projectName = "";
  
  userAsSite: any = [];
  defaultSite: any;
  
  languageList = [
    {
      name: 'English',
      languageCode: 'en',
      displayLanguageCode: 'EN',
    },
    {
      name: 'German',
      languageCode: 'de',
      displayLanguageCode: 'DE',
    },
    {
      name: 'Italian',
      languageCode: 'it',
      displayLanguageCode: 'IT',
    },
    {
      name: 'Spanish',
      languageCode: 'es',
      displayLanguageCode: 'ES',
    },
    {
      name: 'Dutch',
      languageCode: 'nl',
      displayLanguageCode: 'NL',
    },
    {
      name: 'French',
      languageCode: 'fr',
      displayLanguageCode: 'FR',
    },
    {
      name: 'Portugese',
      languageCode: 'pt',
      displayLanguageCode: 'PT',
    },
    {
      name: 'Korean',
      languageCode: 'ko',
      displayLanguageCode: 'KO',
    },
    {
      name: 'Japanese',
      languageCode: 'ja',
      displayLanguageCode: 'JA',
    },
    {
      name: 'Mandarin',
      languageCode: 'zh',
      displayLanguageCode: 'ZH',
    },
    {
      name: 'Swedish',
      languageCode: 'sv',
      displayLanguageCode: 'SV',
    },
  ];
  selectedLanguage = this.languageList[0];
  labels = {};

  userInfo: any;
  docReact: any;
  vendorFlag: boolean;
  // showLanguageLoading:boolean = false;
  
  @ViewChild('languageId') languageId: ElementRef;
  applicationName: any= '';
  private shouldCallApi: boolean = true;
  constructor(
    private location: Location,
    private projectService: ProjectService,
    public dataService: DataService,
    private viewportRuler: ViewportRuler,
    private parentToChildService: ParentToChildService,
    private translate: TranslateService,
    private http: HttpClient,
    private router: Router,private ref: ChangeDetectorRef,
    private authService: MsalService,
    public commonService: CommonService,
    public dialog: MatDialog,
    public tokenService: TokenService,
    public translationService: TranslationService,
    private ngZone: NgZone,
    private languageService: LanguageService,
    private changeDetector: ChangeDetectorRef,

  ) {
    this.docReact = this.viewportRuler.getViewportRect();
    this.labels = {
      'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
      'mobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'mobtitle'] || 'mobtitle',
      'menuChooselang': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuChooselang'] || 'menuChooselang',
      'menuSignout': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSignout'] || 'menuSignout',
    }

    this.languageList.sort((a, b) => {
      if (a.name === 'English') return -1
      if (b.name === 'English') return 1

      return a.name.localeCompare(b.name)
    })

  }
  shortName ="";
  unreadNotification:any
  notificationWebsite(){
    window.open(environment.Notification_Portal, '_blank');
  }
  ngOnInit(): void {

    // this.ngZone.run(() => {
    //   console.log('Out subscribe', this.labels)
    //   this.languageService.language$.subscribe((language) => {
    //     console.log('obs labels language', this.commonService.selectedLanguage)
    //     console.log('obs labels', this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()])
    //     this.labels = {
    //       'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
    //       'mobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'mobtitle'] || 'mobtitle',
    //       'menuChooselang': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuChooselang'] || 'menuChooselang',
    //       'menuSignout': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSignout'] || 'menuSignout',
    //     }
    //     console.log('commonService label', this.labels)
    //     this.changeDetector.detectChanges();
    //   })
    //   this.changeDetector.detectChanges();
    // })

    this.dataService
      .postData({},
        this.dataService.NODE_API + '/api/service/unreadNotificationCount'
      )
      .subscribe((data) => {
        var a:any=data
        console.log(data)
        this.unreadNotification=0
        if (a && a.message){
          console.log(a.message)
          this.unreadNotification = a.message
        }
        this.ref.detectChanges();
      });

    this.dataService.getData("https://graph.microsoft.com/v1.0/me").subscribe(
      (res: any) => {
        var name = res.displayName + "";
        this.shortName = name.slice(0, 2).toLocaleUpperCase();
        this.userInfo = res;
        this.ref.detectChanges();
      });

    var _this = this;
    this.router.events.subscribe((val) => {
      if (_this.router.url) {
        // var menuSelected = _this.menuList.find(({ url }) => "/"+url == _this.router.url);
        // currentMenu: any = this.menuList[0];
      }
    });
    var _this = this;
    _this.userAsSite = _this.commonService.userAssignedSite;
   _this.defaultSite = _this.userAsSite.find(item => item.siteId == _this.dataService.siteId)
   console.log('userAsSite===>',_this.userAsSite)
  
  //  _this.selectedLanguage =_this.languageList.find(item => item.languageCode == _this.commonService.selectedLanguage);
  //  console.log(' _this.selectedLanguage ', _this.selectedLanguage )
  // _this.languageChange();
   _this.ref.detectChanges();
    //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
        if(fiterType == "userAccess"){
          _this.userAsSite = _this.commonService.userAssignedSite;
          _this.defaultSite = _this.userAsSite.find(item => item.siteId == _this.dataService.siteId)
          if (_this.defaultSite.length > 0 && _this.shouldCallApi){
            _this.getSetting(_this.defaultSite.siteId,0)
          }
          else if (_this.userAsSite.length > 0 && _this.shouldCallApi){
            _this.getSetting(_this.userAsSite[0].siteId,1)
          }
          console.log('userAsSite===>',_this.userAsSite)
          _this.selectedLanguage =_this.languageList.find(item => item.displayLanguageCode == _this.commonService.selectedLanguage);
          if( _this.selectedLanguage){
            _this.languageChange('callFromOnInit');
          }
       
          _this.ref.detectChanges();
        }
    })


    var _this = this;
    var path = this.location.path();
    const myArray = path.split("?");
    let word = myArray[0];
    var vendorPage1 = _this.dataService.vendorPage1 || "/observations/quality-assessment";
    var vendorPage2 = _this.dataService.vendorPage2 ||"/observations/upload-doc";
    var vendorPage3 = _this.dataService.vendorPage3 ||"/observations/completed";
    if (word == vendorPage1 || word == vendorPage2 || word == vendorPage3) {
      _this.vendorFlag = true;
    } else {
      _this.vendorFlag = false;
    }
  }

  getSetting(site,i) {
    var _this = this;
    
    _this.commonService.loaderFlag = true;
    _this.dataService.postData({ sites: [site] }, _this.dataService.NODE_API + '/api/service/listSetting')
      .subscribe((resData: any) => {
        var settingList = resData['data']['list' + _this.commonService.configuration['typeSetting']]['items'];

        _this.applicationName = settingList[0].applicationName;
        if (_this.applicationName==null){
          _this.applicationName = 'Observations, Field Walks and Audits';
        }
        
        _this.ref.detectChanges();
        
        _this.commonService.loaderFlag = false;
      })}
  ngAfterViewInit(): void {
    this.ref.detectChanges();
    this.ngZone.run(() => {
        if (this.commonService.labelObject && _.isEmpty(this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()])) {
          this.ref.detectChanges();
        }
        this.ref.detectChanges();
      })
  }
  
  clickUserSite(site){
    console.log(site)
    var _this =this
 
    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {
        title:'confirmationmessageUserintergrationtitle'
       }
   });
   dialogRef.afterClosed().subscribe(result => {
    console.log('result',result)
      if(result == 'YES'){
        _this.shouldCallApi = false;
    _this.getSetting(site.siteId,2)
        _this.commonService.loaderFlag = true;
        _this.defaultSite = site
        _this.dataService.siteId = site.siteId
      
        _this.commonService.getIntegrationUser('header-click',function (data) {
          _this.commonService.loaderFlag = false;
        })
        _this.ref.detectChanges();
      }
  });


  }


  isLoggedIn(): boolean {
    return true;
  }

  logout() {

  }

  logoutAccount() {
    localStorage.clear();
    this.authService.logoutRedirect();
  }


  languageClick(item) {
    console.log(item)
    this.selectedLanguage = item;
    this.languageChange();
  }
  profileClick() {

  }
  languageDropdown() {
    console.log(this.languageId)
    window.setTimeout(() => document.getElementById('languageId').click(), 100);
  }

  projectSelected(item) {

  }
  pageSelected(item) {

  }

  showSideBar(showSidebar){
    this.parentToChildService.broadcastShowMobileSideBar(showSidebar);
}

languageChange(callFromOnInit = null) {
  
  console.log("kjjkjjjjjj")
  this.translate.addLangs(['en', 'de', 'it', 'es', 'nl', 'fr', 'pt', 'ko', 'ja', 'zh',]);
  // this.translate.setDefaultLang('en');
  console.log('selectedLanguage', this.selectedLanguage, this.selectedLanguage.languageCode)
  // this.translate.use(this.selectedLanguage.languageCode);
  this.commonService.selectedLanguage = this.selectedLanguage.displayLanguageCode;
  console.log('callFromOnInit', callFromOnInit)
  console.log('commonService LabelObject', this.commonService.labelObject, this.commonService.selectedLanguage)
  var _this = this;

  if (this.commonService.labelObject && this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()] && Object.values(this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()]).length > 0 ){
    console.log('Inside Lang if')
    this.labels = {
      'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
      'mobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'mobtitle'] || 'mobtitle',
      'menuChooselang': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuChooselang'] || 'menuChooselang',
      'menuSignout': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSignout'] || 'menuSignout',
    }
  
    this.commonService.updateLabelObject(this.commonService.selectedLanguage.toUpperCase())
        console.log('this.labels in lang change', this.labels,this.commonService.labelObject,this.labels,this.commonService.labelObject[this.commonService.selectedLanguage], this.commonService.selectedLanguage)
  
    this.changeDetector.detectChanges();
    this.ngZone.run(() => {
      console.log('header selectedLanguage', this.selectedLanguage)
      this.languageService.selectedLanguage(this.selectedLanguage.languageCode.toUpperCase());
    })
    var iframe = document.getElementsByTagName('iframe');
    console.log('iframe', iframe);
    if (iframe == null) return;
     if(iframe){
      var iWindow = iframe[0].contentWindow;
   
      if(iWindow){
        iWindow?.postMessage(
          { type: 'AuthToken', data: _this.tokenService.getToken() },
          '*'
        );
        iWindow?.postMessage(
          {
            type: 'Language',
            action: 'Language',
            LanguageCode: `${_this.selectedLanguage.languageCode.toUpperCase()}`,
            idToken: _this.tokenService.getIDToken(),
            labels: _this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()], // APP-OFWATranslationDataEN
          },
          '*'
        );
      }
     
    }
  }
  else {
    console.log('Inside else')
    if (callFromOnInit === null ){
      this.commonService.showLanguageLoading = true;
    }
    console.log('lang loading: ', this.commonService.showLanguageLoading)
    this.translationService.getLabels(
      this.commonService.selectedLanguage.toUpperCase(),
      function (data) {
        console.log('Inside Lang Chnage getLabels: ', data, _this.commonService.selectedLanguage, _this.commonService.showLanguageLoading)
        _this.labels = {
          'title': _this.commonService.labelObject[_this.commonService.selectedLanguage][environment.applicationId+'.'+_this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
          'mobtitle': _this.commonService.labelObject[_this.commonService.selectedLanguage][environment.applicationId+'.'+_this.commonService.selectedLanguage+'.stLabel.'+'mobtitle'] || 'mobtitle',
          'menuChooselang': _this.commonService.labelObject[_this.commonService.selectedLanguage][environment.applicationId+'.'+_this.commonService.selectedLanguage+'.stLabel.'+'menuChooselang'] || 'menuChooselang',
          'menuSignout': _this.commonService.labelObject[_this.commonService.selectedLanguage][environment.applicationId+'.'+_this.commonService.selectedLanguage+'.stLabel.'+'menuSignout'] || 'menuSignout',
        }
        _this.commonService.updateLabelObject(_this.commonService.selectedLanguage.toUpperCase())
        console.log('_this.labels in lang change', _this.labels,_this.commonService.labelObject,_this.labels,_this.commonService.labelObject[_this.commonService.selectedLanguage], _this.commonService.selectedLanguage)
        _this.changeDetector.detectChanges(); 
        // angular lang chang
        _this.ngZone.run(() => {
          console.log('header selectedLanguage', _this.selectedLanguage)
          _this.languageService.selectedLanguage(_this.selectedLanguage.languageCode.toUpperCase());
        })
        _this.commonService.showLanguageLoading = false;
        console.log('lang loading: ', _this.commonService.showLanguageLoading)
        _this.changeDetector.detectChanges();
        var iframe = document.getElementsByTagName('iframe');
    console.log('iframe', iframe);
    if (iframe == null) return;
     if(iframe){
      var iWindow = iframe[0].contentWindow;
   
      if(iWindow){
        iWindow?.postMessage(
          { type: 'AuthToken', data: _this.tokenService.getToken() },
          '*'
        );
        iWindow?.postMessage(
          {
            type: 'Language',
            action: 'Language',
            LanguageCode: `${_this.selectedLanguage.languageCode.toUpperCase()}`,
            idToken: _this.tokenService.getIDToken(),
            labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], // APP-OFWATranslationDataEN
          },
          '*'
        );
      }
     
    }
      }
    )
  }


  

  //updateFavouriteLan
  this.dataService
  .postData({"languageCode": this.selectedLanguage.displayLanguageCode},
    this.dataService.NODE_API + '/api/service/updateFavouriteLan'
  )
  .subscribe((data) => {
    console.log('data',data)
    this.ngZone.run(() => {
      console.log('header selectedLanguage', this.selectedLanguage)
      this.languageService.selectedLanguage(this.selectedLanguage.languageCode.toUpperCase());
    })
  })
    

    // this.changeDetector.detectChanges();   
   
}
}
