import { ChangeDetectorRef, Component, Inject, NgZone, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'app-field-send-mail',
  templateUrl: './field-send-mail.component.html'
})
export class fieldSendMailComponent implements OnInit {

  attendeeControl: FormControl = new FormControl("");
  externalAttendeeControl: FormControl = new FormControl("");
  attendeeList = [];
  filteredattendeeList = this.attendeeList.slice();
  selectedAttendee: any = [];
  externalAttendee: any = [];
  fl_searchVal: any;
  fieldDetail: any;
  labels = {}
  constructor(
    public dialogRef: MatDialogRef<fieldSendMailComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public router: Router,
    private dataService: DataService,
    private commonService: CommonService,
    private cdRef: ChangeDetectorRef,
    private translate: TranslateService,
    private ngZone: NgZone,
    private languageService: LanguageService
  ) { 
    this.fieldDetail = data

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
      'observationSendmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationSendmail'] || 'observationSendmail',
      'commonfilterUsers': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterUsers'] || 'commonfilterUsers',
      'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
      }
    }

  }

  ngOnInit(): void {
    var _this =this

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'observationSendmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationSendmail'] || 'observationSendmail',
          'commonfilterUsers': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterUsers'] || 'commonfilterUsers',
          'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        }
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })

    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      var tempAttend = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"]
      tempAttend.forEach(element => {
        element.fullName = element.firstName+' '+element.lastName
      });
      _this.attendeeList = tempAttend;
      _this.filteredattendeeList = _this.attendeeList.slice();
    })
    _this.attendeeControl.valueChanges.subscribe(value => {
      console.log('value',value)
       function push(array, item) {
        if (!array.find((ele) => ele.externalId === item.externalId)) {
          array.push(item);
        }
      }
      push(_this.selectedAttendee, value); 
      
    })

  }
  getUserMail(){
    var _this = this;
    function validateEmail(email) {
      // Regular expression for validating an email
      const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return re.test(String(email).toLowerCase());
    }
  
  // Example usage
  const email = this.externalAttendeeControl.value;
  if (validateEmail(email)) {
    var obj = {
      externalId:this.externalAttendeeControl.value,
      fullName:this.externalAttendeeControl.value
    }
    const isDuplicate = this.externalAttendee.some(item => item.externalId === obj.externalId);

    if (isDuplicate) {
      this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterDontenterduplicatemail']});
        
    } else {
      this.externalAttendee.push(obj);
      this.externalAttendeeControl.reset();
       
    }
  
  } else {
    this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterFillvalidemail'] });
 
  }
  
  }
  onEnterPress(event: Event) {
    const keyboardEvent = event as KeyboardEvent;
    if (keyboardEvent.key === 'Enter') {
      this.getUserMail()
    }
  }

  removeSelectedUser(item){
    const index = this.selectedAttendee.findIndex(prop => prop.externalId === item.externalId)
    this.selectedAttendee.splice(index,1)
  }
  removeexternalSelectedUser(item){
    const index = this.externalAttendee.findIndex(prop => prop.externalId === item.externalId)
    this.externalAttendee.splice(index,1)
  }
  async onAttendeeChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.attendeeList = [];
    _this.filteredattendeeList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.attendeeList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.attendeeList.forEach((element)=>{
          element.fullName =  element.firstName+' '+element.lastName
        })
        _this.filteredattendeeList = _this.attendeeList.slice();
      
  
      })
      _this.cdRef.detectChanges()
      
  }
  }

   
  cancelClick(): void {
    this.dialogRef.close('NO');
  }
  submitClick(): void {
  
   var _this =this
 
  //Site, Unit, Location, Date, Craft, Department, Short Description, Describe the field, and Urgency.
  if(this.selectedAttendee.length > 0){



    var instanceNotification = [
      {
        application: this.commonService.applicationInfo.name,
        description: 'field Details',
        users: [],
        severity: this.fieldDetail.urgency?this.fieldDetail.urgency:"Medium",
        properties:[
          {
            "name": "Site",
            "value":this.fieldDetail.refSite && this.fieldDetail.refSite.description?this.fieldDetail.refSite.description:"",
            "type": "text"
          },
          {
            "name": "Location",
            "value": this.fieldDetail.refReportingLocation && this.fieldDetail.refReportingLocation.description?this.fieldDetail.refReportingLocation.description:"",
            "type": "text"
          },
          {
            "name": "Date",
            "value": this.fieldDetail.date?this.fieldDetail.date:"",
            "type": "text"
          },
          
          {
            "name": "whatWentWell",
            "value": this.fieldDetail.whatWentWell?this.fieldDetail.whatWentWell:"",
            "type": "text"
          },
          {
            "name": "whatNeedsAttention",
            "value": this.fieldDetail.whatNeedsAttention?this.fieldDetail.whatNeedsAttention:"",
            "type": "text"
          },
          {
            "name": "whatDidYouDoAboutIt",
            "value": this.fieldDetail.whatDidYouDoAboutIt?this.fieldDetail.whatDidYouDoAboutIt:"",
            "type": "text"
          },
          {
            "name": "overallSummary",
            "value": this.fieldDetail.overallSummary?this.fieldDetail.overallSummary:"",
            "type": "text"
          },
          
      ]
      
        }
    ];

    this.selectedAttendee.forEach((ele)=>{
      instanceNotification[0].users.push(ele.externalId)
    })
 
   console.log('instanceNotification',instanceNotification)
  
     let notificationType = 'field Details';
     _this.commonService.notification(
      instanceNotification,
      notificationType
    );
    
    var logObj = {
      objectExternalId:this.fieldDetail.externalId,
      objectType:_this.commonService.configuration["typeFieldWalk"],
      refUser:{
        "space": _this.dataService.userInfo.user.space,
        "externalId": _this.dataService.userInfo.user.externalId
      },
      logType:"Email",
      dateTime:new Date(),
      beforeJSON:this.fieldDetail,
      afterJSON:instanceNotification[0]
    }
   
    var mainLog = {
      "type": _this.commonService.configuration["typeOFWALog"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        logObj
      ]
    }
    _this.dataService.postData(mainLog, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(logdata => {
 
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterEmailsent'] });
      _this.dialogRef.close('YES');
    })
  }else{
    _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
  }
  
  }
}
