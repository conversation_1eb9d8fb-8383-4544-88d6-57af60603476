import { ComponentFixture, TestBed, async, fakeAsync } from '@angular/core/testing';

import { MainWorkflowComponent } from './main-workflow.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { AppModule } from 'src/app/app.module';




fdescribe('Workflow test', () => {
  let component: MainWorkflowComponent;
  let fixture: ComponentFixture<MainWorkflowComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AppModule]
    })
    .compileComponents();
  });

  beforeEach(async(() => {
    fixture = TestBed.createComponent(MainWorkflowComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should not valid', () => {
    expect(component).toBeTruthy();
  });
  it('should true false', () => {
    expect(true).toBe(true);
  });
  it('should match span text', fakeAsync (() => {
    const span = fixture.debugElement.nativeElement.querySelector('#workflow_text');
    fixture.detectChanges();
    expect(component.wTitle).toBe(span.textContent);
  }));
  it("Check Worlflow array length", () => {

    expect(component.workFlowArray.length).toBeGreaterThanOrEqual(1);
  });

  // it('should valid', () => {
  //   const inputElement = document.getElementById("first");
  //   inputElement.value="asdf";
  //   inputElement.dispatchEvent(new Event('input'));
  //   // console.log(component.ngForm.form.controls.first.value);
  //   expect(component.ngForm.form.valid).toBeTruthy();
  //   inputElement.value="";
  //   inputElement.dispatchEvent(new Event('input'));
  //   expect(component.ngForm.form.valid).not.toBeTruthy();
  // });
});
