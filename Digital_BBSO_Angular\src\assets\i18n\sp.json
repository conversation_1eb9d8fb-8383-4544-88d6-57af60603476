{"MENU": {"HOME": "Home", "DASHBOARD": "Dashboards", "ObSERVATION": "Observation", "FIELDWALK": "Field Walk", "AUDIT": "Audit", "SCHEDULE": "Schedule", "CONFIGURATION": "Configuration", "ACTION": "Actions", "ALERT": "<PERSON><PERSON><PERSON>", "RULE": "Rules", "WORKFLOW": "Workflow"}, "All Sites": "All Sites", "TITLE": "Observations, Field Walks and Audits", "MOB_TITLE": "OFA", "CREATE_ACTION": "Create Action", "SITE_UNIT": {"SITE": "Site", "UNIT": "Unit"}, "CONFIRMATION_MESSAGE": {"TITLE": "Do you want to assign this RCA?", "USERINTERGRATION_TITLE": "Are you sure do you want to switch to a different site."}, "COMMONFILTER": {"CHOOSE_SITE": "Choose Site", "CHOOSE_UNIT": "Choose Unit", "CHOOSE_PROCESS": "Choose Process", "CHOOSE_LOCATION": "Choose Location", "CHOOSE_ATTENDEE": "<PERSON><PERSON>", "CHOOSE_AUDIT_TYPE": "<PERSON><PERSON>", "CHOOSE_CATEGORY": "Choose Category", "CHOOSE_CRAFT": "Choose Craft", "CHOOSE_ASSETS": "<PERSON><PERSON>", "CHOOSE_CONTRACTOR": "<PERSON>ose Contractor", "CHOOSE_BEHALF": "<PERSON><PERSON>", "CHOOSE_ASSIGNED": "<PERSON><PERSON> Assigned To", "SEARCH": "Search", "START_DATE": "Start Date", "END_DATE": "End Date"}, "OBSERVATION": {"COMMON": {"CREATE_AN": "Create an"}, "SUB_HEADER": {"TITLE": "Welcome To OBSERVATIONS, FIELD WALKS AND AUDITS"}, "MAIN": {"TITLE": "What would you like to do today?", "CARDS": {"OBSERVATION": "Observation", "Observation": "Observation", "Field Walk": "Field Walk", "Audit": "Audit", "Stewardship": "Stewardship", "Quality": "Quality", "Reliability": "Reliability", "Behaviour": "Behaviour", "Final Product": "Final Product", "HAZARD": "Hazard", "OFFICE_SAFETY": "Office Safety", "INCIDENT": "Incident", "SUPPLIER_AUDIT": "Supplier Audit", "CUSTOMER_AUDIT": "Customer <PERSON><PERSON>", "All": "All"}}, "CREATE": {"CARD": {"TITLE": "Process", "DESCRIPTION": "Choose the process under which you want to report", "CHOOSE_THE": "Choose the", "TYPE": "type"}}, "SUB_OBSERVATION": {"CARD": {"TITLE": "Core Principles", "DESCRIPTION": "Choose the core principle under which you want to report", "CHOOSE_THE": "Choose the", "TYPE": "type"}}, "BEHAVIOUR_CHECKLIST": {"CREATE_AN_OBERVATION": "Create an Observation", "CREATE_AN_HAZARDS": "Create an Hazards", "CREATE_AN_AUDIT": "Create an Audit", "FORM_CONTROLS": {"PROCESS": "Process", "CORE_PRINCIPLE": "Core Principles", "OBSERVATION_TYPE": "Observation Type", "CHOOSE_CONTRACTOR": "<PERSON>ose Contractor", "CATEGORY": "Category", "SUB_CATEGORY": "Sub-Category", "LOCATION_OBSERVED": "Location Observed", "OBSERVED_BEHALF": "Observed on behalf of", "PLANT": "Plant", "CRAFT": "Crafts", "DATE_TIME": "Date and Time", "START_TIME": "Start Time", "END_TIME": "End Time", "ASSETS": "Assets", "VIEW_IN": "view_in_ar", "CONTRACTOR": "Contractor", "CONTRACTOR_DETAIL": "Contractor <PERSON><PERSON>", "DESCRIBE_OBSERVATION": "Describe the Observations", "SHIFT": "Shift", "SHORT_DESCRIPTION": "Short Description", "FLOOR": "Floor", "URGENCY": "Urgency", "ADDITIONAL_COMMENTS": "Additional comments on Observation", "UPLOAD_PHOTOS": "Upload Photos or Videos (If applicable)", "BEHAVIOUR_CHECKLIST": "Checklist", "ADD_CIRCLE": "add_circle", "CHECKLIST": "Checklist", "SAFE": "Safe", "UNSAFE": "Unsafe", "NOT_OBSERVED": "Not Observed", "INJURY_POTENTIAL": "Injury <PERSON>", "NOTE": "Notes", "FEEDBACK": "<PERSON><PERSON><PERSON>", "ACTIVITY": "Activity", "RISKY_ACTION": "Risky Action", "RISK": "Risk", "RISK_AGREEMENT": "Risk Agreement", "REASON_ACTION": "Reason for Action", "SAFE_BEHAVIOUR": "Safe Behaviour", "POSSIBLE": "Possible", "DIFFICULT": "<PERSON><PERSON><PERSON><PERSON>", "IMPOSIBLE": "Imposible", "SUGGESTED_SOLUTION": "Suggested Solution", "SIGNATURE": "Signature", "High": "High", "Medium": "Medium", "Low": "Low", "YES": "Yes", "NO": "No"}, "CHIPS": {"STOP_WORK": "Stop Work and Report", "USE_CAUTION": "Use Caution and Report", "CONTINUE_REPORT": "Continue and Report", "TEST_CATEGORY": "Test Category", "SCAFFOLDS": "ScaffoldS", "PPE": "PPE", "NEW_CATEGORY": "New Category", "LINE_FIRE": "Line of Fire"}}, "FIELD_VISIT": {"TITLE": "Field Visit", "FORM_CONTROLS": {"LOCATION": "Location", "OPTION_1": "Option 1", "ATTENDEES": "Attendees", "DATE_TIME": "Date and Time", "START_TIME": "Start Time", "END_TIME": "End Time", "ASSETS": "Assets", "VIEW_IN": "view_in_ar", "WHAT_WENT_WELL": "What went well?", "WHAT_NEED_ATTENTION": "What needs attention?", "WHAT_YOU_ABOUT_IT": "What did you do about it?", "OVERALL_SUMMARY": "Overall Summary", "SIGNATURE": "Signature"}}, "OBSERVATION_LIST": {"TITLE_OBSERVATION": "Observations", "TITLE_FIELDWALK": "Field Walk", "TITLE_AUDIT": "Audit"}, "QUALITY_ASSESSMENT": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"CHECKLIST": "Checklist", "SUPPLIER_SELF_COMMENTS": "Supplier self assessment Comments"}}, "QUALITY_UPLOAD": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"DOCUMENT": "Evidence/Document", "UPLOAD_DOCUMENT": "Upload Documents", "CHOSSE_FILE": "Choose file", "UPLOADED": "Uploaded", "VIEW": "View"}}, "AUDIT_PLAN": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"DOCUMENT": "Evidence/Document", "UPLOAD_DOCUMENT": "Documents Uploaded", "VERIFICATION_COMMENTS": "Verification Comments"}}, "SCORECARD": {"TITLE": "Scorecard", "TABLE_HEADER": {"GENERAL_INFORMATION": " GENERAL INFORMATION", "PRODUCT_RELATED_INFORMATION": "PRODUCT RELATED INFORMATION", "AUDIT_RELATED_INFORMATION": "AUDIT RELATED INFORMATION", "AUDIT_SUMMARY": "AUDIT SUMMARY", "AUDIT_RESULT": "AUDIT RESULT"}, "FORM_CONTROLS": {"SIGNATURE": "Signature", "LEAD_AUDITOR": "LEAD AUDITOR", "MANAGER_ORGANIZATION": "MANAGER OF AUDITED ORGANIZATION"}, "TABLE_HEADING": {"COMPANY_NAME": "COMPANY NAME", "HQ_STREET_ADDRESS": "HQ STREET ADDRESS", "CITY": "CITY", "STATE": "REGION / STATE", "POSTAL_CODE": "POSTAL (ZIP) CODE", "COUNTRY": "COUNTRY", "MOBILE": "MOBILE", "E_MAIL": "E-MAIL", "CORPORATE_WEB_SITE": "CORPORATE WEB SITE", "PRODUCT_FAMILY": "PRODUCT FAMILY", "LOCATION_MANUFACTURING_SITE": "LOCATION MANUFACTURING SITE", "PRODUCT": "PRODUCT", "MAIN_CONTACT_NAME": "MAIN CONTACT NAME", "JOB_TITLE": "JOB TITLE", "TELEPHONE": "TELEPHONE", "AUDIT_REASON": "AUDIT REASON", "AUDIT_DATE": "AUDIT DATE", "AUDITOR": "AUDITOR", "DEPARTMENT": "PARTICIPANTS/DEPARTMENT", "CONCLUSION": "CONCLUSION", "PATH_FORWARD": "PATH FORWARD", "NEED_IMPROVEMENT": "NEED IMPROVEMENT", "ACCEPTABLE": "ACCEPTABLE", "EXCELLENT": "EXCELLENT"}}, "AUDITSUMMARY": {"TITLE": "<PERSON>t <PERSON>"}}, "DASHBOARD": {"TITLE": "Dashboard", "SUB_HEADING": {"chartTitle1": "Trends", "chartTitle2": "Observers Points", "chartTitle3": "By Location", "chartTitle4": "By Categories", "chartTitle5": "By Department", "chartTitle6": "Risk Behaviours at last 7 days", "chartTitle7": "At Risk by Section", "chartTitle8": "By Department", "chartTitle9": "Sitewide participation last 7 days", "chartTitle10": "By Categories", "chartTitle11": "Percentage Participation"}, "CARDS": {"TITLE_OBSERVATION": "Observations", "BEHAVIOUR": "Behaviour", "HAZARD": "Hazards", "INCIDENT": "Incidents", "AUDIT": "Audits", "ACTION": "Actions"}}, "SCHEDULE": {"SCHEDULE_LIST": {"TITLE": "Schedule"}, "CREATE_SCHEDULE": {"TITLE": "Create Schedule", "FORM_CONTROLS": {"PROCESS": "Process", "OBSERVATION": "Observation", "FIELD_WALK": "Field Walk", "AUDIT": "Audit", "TITLE": "Title", "OBSERVATION_TYPE": "Observation Type", "BEHAVIOUR": "Behaviour", "HAZARDS": "Hazards", "INCIDENTS": "Incidents", "OBSERVER": "Observer", "LOCATION_OBSERVED": "Location to be Observed", "UTILITY_CHILLING_PLANT": "Utility II Chilling plant area", "UTILITY_CHILLING_SECOND": "Utility I Chilling plant area", "DATE_TIME": "Date and Time", "OBSERVATION_POINTS": "Observation Points", "AUDIT_TYPE": "Audit Type", "CATEGORY": "Category", "CORE_PRINCIPLE": "Core Principles", "SUPPLIER_AUDIT": "Supplier Audit", "VENDOR_NUMBER": "Vendor Number", "VENDOR_NAME": "Vendor Name", "BUSINESS": "Business", "REGION": "Region", "EUROPE": "Europe", "COUNTRY": "Country", "GERMANY": "Germany", "YEAR": "Year", "QUARTER": "Quarter", "PRIORITY": "Priority", "HIGH": "High", "LOW": "Low", "LEAD_AUDITOR": "Lead Auditor", "PROCUREMENT_PPR": "Procurement PPR", "SITE_MANAGER": "Site Manager", "START_DATE": "Start Date", "END_DATE": "End Date", "FIRST_TIME_AUDIT": "First Time Audit", "LEGAL_ENTITY": "Legal Entity", "SITE_CERTIFICATE_NUMBER": "Site Certificate Number", "CO_AUDITOR": "Co-Auditor", "EMAIL": "E-Mail", "CHOOSE_LEAD_AUDITOR": "<PERSON><PERSON> Lead Auditor"}}, "TRACKER": {"TITLE": "Tracker", "COMPLETED": "Completed", "NOT_COMPLETED": "Not Completed", "SCHEDULED": "Scheduled"}}, "CONFIGURATION": {"TITLE": "Configuration", "UNIT_SITE": {"PROCESS": "Process", "SITE": "Site"}, "FORM_CONTROLS": {"CORE_PRINCIPLE": "Core Principles", "OBSERVATION_TYPE": "Observation Types", "ADD_OBSERVATION_TYPE": "Add Observation Type", "AUDIT_TYPE": "Audit Type", "ADD_AUDIT_TYPE": "Add Audit Type", "CATEGORY": "Categories", "ADD_CATEGORY": "Add Category", "SUB_CATEGORY": "Sub-Categories", "ADD_SUB_CATEGORY": "Add Sub-Category", "FEEDBACK": "Feedback to be enabled when selected", "TIME_CAPTURE": "Time Capture Required", "UNSAFE": "Unsafe", "SIGNATURE": "Signature Required"}, "FORM_CONFIG": {"TITLE": "Form Configuration"}, "QUESTION_CONFIG": {"TITLE": "Form Configuration", "FORM_CONTROLS": {"OBSERVATION_TYPE": "Observation Type", "COLUMN_NAME": "Column Name", "CHECK_BOX": "Check Box", "RADIO": "Radio", "DROP_DOWN": "Dropdown", "INPUT_BOX": "Input Box"}}, "QUESTION_LIST": {"TITLE": "Form for "}, "ADD_QUESTION": {"TITLE": "Questions for ", "FORM_CONTROLS": {"QUESTION": "Question", "QUESTION_DESCRIPTION": "Question Description", "FIELD_REQUIRED": "Fields Required", "SAFE": "Safe", "UNSAFE": "Unsafe", "N_A": "N/A", "INJURY_POTENTIAL": "Injury <PERSON>"}}}, "ACTION": {"ACTION_LIST": {"TITLE": "Actions"}, "CREATE_ACTION": {"TITLE": "Create Action for Observation", "DESC_MESSAGE": "Minimum length of 10 and maximum of 200 characters", "FORM_CONTROLS": {"OBSERVATION_DESCRIPTION": "Action description", "SUGGESTION_FEEDBACK": "Suggestions / Feedback", "UPLOAD_FILES": "Upload files", "DRAG_DROP": "Drag and drop file here or", "CHOOSE_FILE": "Choose file", "OBSERVED_BY": "Observed by", "ASSIGNED_TO": "Assigned to", "STATUS": "Status", "ASSIGNES": "Assigned", "START_DATE": "Start date", "DUE_DATE": "Due date", "PRIORITY": "Priority"}}}, "ALERT": {"TITLE": "<PERSON><PERSON><PERSON>"}, "RULE": {"RULE_LIST": {"TITLE": "Rule List"}, "CREATE_RULE": {"TITLE": "Create Rule", "FORM_CONTROLS": {"RULE_NAME": "Rule Name", "ADD": "Add", "WHEN": "When", "SCHEDULE": "Schedule", "OPERATOR": "Operators", "GREATER_THAN": "Greater than", "VALUE": "Value", "DUE_DATE": "Due date", "THEN": "Then", "ALERT": "<PERSON><PERSON>", "WORKFLOW": "Workflow", "CHOOSE_WORKFLOW": "Choose Workflow", "EMAIL": "Email", "TEXT": "Text", "TEAMS": "Teams", "EMAIL_TEXT": "Email"}}}, "WORKFLOW": {"MAIN_WORKFLOW": {"TITLE": "Workflows", "CARDS": {"CREATED_AT": "Created at", "CREATED_BY": "Created by", "SCHEDULE_OVERDUE": "Schedule Overdue", "OBSERVATION_POINTS": "Observation Points", "AUDIT_OVERDUE": "Audit Overdue"}}, "CREATE_WORKFLOW": {"TITLE": "Create Workflow", "WORKFLOW_NAME": "Workflow name", "CARDS": {"EMAIL": "Email", "TEAMS": "Teams", "SMS": "SMS"}}}, "BUTTON": {"SUBSCRIBE": "Subscribe", "UNSUBSCRIBE": "Unsubscribe", "KNOW_MORE": "Know more", "READ_MORE": "Read More", "ADD_LINKS": "Add Links", "ADD_KPI": "Add KPI's", "CANCEL": "Cancel", "NEXT": "Next", "SAVE": "Save", "SAVE_ACTION": "Save Action", "VIEW": "View", "YES": "Yes", "NO": "No", "ASSIGN": "Assign", "SUBMIT": "Submit", "BACK": "Back", "UPLOAD_PHOTO_VIDEO": "Upload Photo or Video", "CREATE_OBSERVATION": "Create Observation", "CREATE_FIELDWALK": "Create Field Walk", "CREATE_AUDIT": "Create <PERSON><PERSON>", "CREATE_SCHEDULE": "Create Schedule", "TRACKER": "Tracker", "ADD_FORM_CONFIGURATION": "Add Form Configuration", "REMOVE": "Remove", "ADD_MORE": "Add More", "ADD_RULE": "Add Rule", "CREATE_WORKFLOW": "Create Workflow", "ADD_QUESTION": "Add Question", "ADD_ANOTHER_QUESTION": "Add another Question", "APPLY": "Apply", "CLEAR": "Clear"}, "AUDIT_LIST": {"CORE_PRINCIPLE": "Core Principle", "AUDIT_TYPE": "Audit Type", "START_DATE": "Start Date", "END_DATE": "End Date"}, "REACT_TABLE": {"COL_SUMMARY": "Column Summary", "COL_SELECTION": "Column Selection", "LIST_COLS": "List of Columns", "EXPORT": "Export"}, "TABLE_COLS": {"ID": "ID", "Title": "Title", "Year": "Year", "Quarter": "Quarter", "Priority": "Priority", "Start Date": "Start Date", "End Date": "End Date", "Status": "Status", "Created On": "Created On", "Created By": "Created By", "Updated On": "Updated On", "Updated By": "Updated By", "Actions": "Actions", "Description": "Description", "Application Name": "Application Name", "Assigned To": "Assigned To", "Site": "Site", "Unit": "Unit", "Reporting Location": "Reporting Location", "Assign Date": "Assign Date", "Due Date": "Due Date", "Object Type": "Object Type", "Object Id": "Object Id", "Observation Date": "Observation Date", "Craft": "Craft", "Process": "Process", "Core Principle": "Core Principle", "Sub Process": "Sub Process", "Category": "Category", "Sub Category": "Sub Category", "Observation type": "Observation Type", "Question #": "Question", "Created At": "Created At", "Date": "Date", "Name": "Name", "Role": "Role", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Nov": "Nov", "Dec": "Dec", "Total": "Total", "Complete": "Complete", "Point": "Point", "Assigned Date": "Assigned Date"}}