
  
  <section class="table-container" [class]="className">
    <!-- Table -->
    <table mat-table matSort 
      [dataSource]="tableObj.dataSource" [ngStyle]="{ 'max-width': +tableMaxWidth + 'px' }">

      <ng-container *ngFor="let col of tableColumnSchemaDtls; let colIndex = index" matColumnDef="{{ col.name }}">
        <th mat-header-cell *matHeaderCellDef [ngStyle]="{ 'width': +col.width + '%' }">
          <div class="header-cell-content" (click)="sortColumn(col.dataKey, col?.type, col.hasSort)">
            {{ col.name }}
          </div>
  
          <button mat-icon-button *ngIf="col.hasSort && getSortIcon(col.dataKey) != 'swap_vert'" aria-label="Sort" (click)="sortColumn(col.dataKey, col?.type, col.hasSort)">
              <mat-icon>{{ getSortIcon(col.dataKey) }} </mat-icon>
          </button>
         
          <button mat-icon-button *ngIf="col.hasFilter" aria-label="Filter icon" (click)="onFilterIconClick(col?.type)" [matMenuTriggerFor]="menu" hideDelay="200">
            <mat-icon>filter_alt</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <ng-template matMenuContent>
              <ng-container *ngIf="!filterValues[col.dataKey] || filterValues[col.dataKey].length === 0; else clearFilter">
                <div class="filterMenuCaption mb-3 text-center">
                  <div class="chooseFilters">Choose filter</div>
                </div>
              </ng-container>
              <ng-template #clearFilter>
                <div mat-menu-item class="filterMenuCaption mb-3 text-center" (click)="clearFilters(col.dataKey)" (keydown.enter)="clearFilters(col.dataKey)" (keydown.space)="clearFilters(col.dataKey)">
                  <a href="javascript:void(0)" (click)="clearFilters(col.dataKey)">Clear filter</a>
                </div>
              </ng-template>
  
               <!-- Date range added for history log starts -->
             <!-- <div mat-menu-item *ngIf="col.type === 'date' && hasDateRangePicker && col.dropdownFilterOptions.size > 0" (click)="openDatePicker()" (keydown.enter)="openDatePicker()" (keydown.space)="openDatePicker()">
                <mat-form-field appearance="fill" class="example-form-field" *ngIf="col.type === 'date' && hasDateRangePicker && col.dropdownFilterOptions.size > 0">
                  <mat-label class="px-0">Enter a date range</mat-label>
                  <mat-date-range-input [formGroup]="dateRange" [rangePicker]="rangePicker" (click)="$event.stopPropagation()">
                    <input matStartDate placeholder="Start date" formControlName="startDate" readonly />
                    <input matEndDate placeholder="End date" formControlName="endDate" (dateChange)="onDateRangeChange($event, col.dataKey)" readonly />
                  </mat-date-range-input>
                  <mat-hint class="px-0">MM/DD/YYYY - MM/DD/YYYY</mat-hint>
                  <mat-datepicker-toggle #rangePickerToggle matSuffix [for]="rangePicker"></mat-datepicker-toggle>
                  <mat-date-range-picker #rangePicker></mat-date-range-picker>
                </mat-form-field>
              </div> -->
              <!-- date range ends -->
              <ng-container *ngFor="let item of col.dropdownFilterOptions">
                <div
                  mat-menu-item
                  *ngIf="item || filterAddEmpty"
                  (click)="selectFilterCheckBox($event, cb, col, item)"
                  (keydown.enter)="selectFilterCheckBox($event, cb, col, item)"
                  (keydown.space)="selectFilterCheckBox($event, cb, col, item)"
                >
                  <mat-checkbox
                    class="mat-menu__checkbox"
                    [value]="item"
                    [disabled]="selectedDateRange.length > 0 && col.type === 'date'"
                    [checked]="isChecked(col.dataKey, item)"
                    (click)="$event.stopPropagation()"
                    (change)="updateFilter(col.dataKey, item)"
                    #cb
                    >{{ item || "(Blank)" }}</mat-checkbox
                  >
                </div>
              </ng-container>
            </ng-template>
          </mat-menu>  
        </th>
        <td mat-cell *matCellDef="let element" [ngStyle]="{ 'width': +col.width + '%' }">
          <ng-container *ngIf="col.dataKey == 'status'; then status; else other_content"></ng-container>
          <ng-template #status> 
               <span [class]="element[col.dataKey]">{{element[col.dataKey]}}</span>
          </ng-template>
          <ng-template #other_content>
             <span [ngClass]="col.dataKey == 'type' ? 'type' : ''">{{element[col.dataKey]}}</span>
          </ng-template>
          
          <!-- <ng-template #showNavigaion> -->
            <!-- <common-lib-button [buttonType]="EnumButtonType.Link" [buttonText]="element[col.dataKey]" (click)="formNavigationUrl(element, col)"></common-lib-button> -->
          <!-- </ng-template> -->
          <!-- <ng-template #showLink> -->
            <!-- <common-lib-button [buttonType]="EnumButtonType.Link_No_Underline" [buttonText]="element[col.dataKey]" (click)="onLinkClick(element.url)"></common-lib-button> -->
          <!-- </ng-template> -->
          <!-- <ng-template #showHTML>
            <div class="scroll-section pr-4">
              <div class="content-sec inner-html" [innerHTML]="element[col.dataKey] | safe : 'html'"></div>
            </div>
          </ng-template> -->
          <!-- <ng-template #showLineClamp>
            <common-lib-line-clamp [row]="1">
              <p class="mb-0" #content [innerHTML]="element[col.dataKey] | safe : 'html'"></p>
            </common-lib-line-clamp>
          </ng-template> -->
          <!-- <ng-template #showCommon>
            <span [innerHTML]="element[col.dataKey] | highlightText : filterValues.globalFilter">{{ element[col.dataKey] }}</span>
          </ng-template> -->
        </td>
        <!-- <ng-container *ngIf="hasTableFooter">
          <td mat-footer-cell *matFooterCellDef [ngStyle]="{ 'width': +col.width + '%' }">{{ tableFooterData[colIndex] }}</td>
        </ng-container> -->
      </ng-container>
      <!-- Action column -->
      <ng-container *ngIf="rowActions.length >= 0" [matColumnDef]="actionColumnName">
        <th mat-header-cell *matHeaderCellDef class="" [ngClass]="hasBorder ? 'action-column action-padding' : 'action-column'">{{ actionColumnName }}</th>
        <td mat-cell *matCellDef="let element" class="text-right">
          <div class="btn-group">
            <div *ngFor="let action of rowActions; let i = index">
              <!-- <common-lib-button
                class="edit-column"
                [buttonType]="EnumButtonType.Link"
                [ngClass]="{ 'mat-row-next': i > 0 }"
                (mouseover)="displayHoverInfo(action?.rules)"
                [buttonText]="action.icon"
                [isDisabled]="getRuleField('attr', action?.rules).includes('disabled')"
                (click)="onActionClick(action, element)"
              ></common-lib-button> -->
            </div>
          </div>
        </td>
      </ng-container>
  
      <!-- CheckBox Selection option: Begin -->
      <ng-container *ngIf="showCheckBox" [matColumnDef]="checkboxCloumnName">
        <th mat-header-cell *matHeaderCellDef class="checkbox-outer-content">
          <mat-checkbox
            class="checkbox-alignment-header"
            (change)="$event ? toggleAllRows() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()"
          ></mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox class="checkbox-alignment-content" (click)="$event.stopPropagation()" (change)="$event ? onSingleSelect(row) : null" [checked]="selection.isSelected(row)"></mat-checkbox>
        </td>
      </ng-container>
      <!-- CheckBox Selection option: End -->
  
      <tr mat-header-row *matHeaderRowDef="tableObj.displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: tableObj.displayedColumns"></tr>
      <tr class="mat-row text-center" *matNoDataRow>
        <td class="mat-cell" [attr.colspan]="tableObj.displayedColumns.length">{{ noContentMessage }}</td>
      </tr>
      <ng-container *ngIf="hasTableFooter">
        <tr mat-footer-row *matFooterRowDef="tableObj.displayedColumns"></tr>
      </ng-container>
    </table>
    <!-- Pagination -->
    <div class="row">
      <div class="col-12">
        <mat-paginator *ngIf="hasPagination" [id]="uuidValue" #paginator [pageSizeOptions]="paginationSizes" [pageSize]="defaultPageSize" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </section>
  