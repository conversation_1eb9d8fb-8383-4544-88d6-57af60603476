import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from "@angular/core";
import { TranslationService } from "src/app/services/TranslationService/translation.services";
import { Subject } from 'rxjs';
import { TranslateService } from "@ngx-translate/core";

@Component({
    selector: 'commom-label',
    templateUrl: './label.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class CommonLabelComponent implements OnInit, AfterViewInit {
    @Input() labelText:string = '';
    @Input() labelTextNoLan:string = '';
    @Input() firstlabelText:string = '';
    @Input() cstClassName:string ='';
    @Input() tagName:string = 'h6';
    @Input() icon:string = '';
    @Input() strict:boolean = false;

    // Create a Subject for the observable
    private labelChangeSubject = new Subject<string>();
    labelChange$ = this.labelChangeSubject.asObservable();
    constructor(
        public translationService: TranslationService,
        public changeDetector: ChangeDetectorRef,
        public ngZone: NgZone,
        public translate: TranslateService
    ){}
    ngOnInit(): void {
        this.labelChangeSubject.subscribe(() => {
            // this.changeDetector.detectChanges();
            this.labelText = this.translationService.translate(this.labelText)
        });

        this.changeDetector.detectChanges();
        
    }
    ngAfterViewInit(): void {
        this.labelChangeSubject.next(this.labelText);
        this.ngZone.run(() => {
            // this.translate.onLangChange.subscribe(() => {})
            this.changeDetector.detectChanges()
        })
    }

    updateLabel(newLabel: string): void {
        this.labelText = newLabel;
        this.labelChangeSubject.next(newLabel); // Emit new value
    }
}
