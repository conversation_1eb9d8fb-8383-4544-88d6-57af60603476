import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { ActionRoutingModule } from "./action-routing.module";
import { ActionListComponent } from "./action-list/action-list.component";
import { CreateActionComponent } from "./create-action/create-action.component";


@NgModule({
    declarations: [
        ActionListComponent,
        CreateActionComponent
    ],
    imports: [
        CommonModule,
        ActionRoutingModule,
        SharedModule
    ],
    providers: [],
  })
  export class ActionModule { }