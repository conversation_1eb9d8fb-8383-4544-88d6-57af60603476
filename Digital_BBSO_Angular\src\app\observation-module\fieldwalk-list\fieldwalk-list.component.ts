import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { UserInterDailogComponent } from 'src/app/shared/user-integration-dialog/userInter-dailog.component';
import { fieldSendMailComponent } from '../fieldwalk-mail/field-send-mail.component';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { TemplateRef, ViewChild } from '@angular/core';

import { MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import * as _ from 'lodash';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-fieldwalk-list',
  templateUrl: './fieldwalk-list.component.html',
  styleUrls: ['./fieldwalk-list.component.scss'],
  providers:[ { provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS },]
})
export class FieldWalkListComponent implements OnInit {

  siteControl: FormControl = new FormControl("");
  @ViewChild('filterDialog') filterDialog!: TemplateRef<any>;
  siteList = [];
  filteredSiteList = [];
  site: any;
  selectedSite: any;
  showhide: boolean = false;
  OperationalLearning: boolean;
  projectName: FormControl = new FormControl("");
  workOrderNumber: FormControl = new FormControl("");
  logList = [
  ]
  url: string = "";
  locationObserve: FormControl = new FormControl('');
  behalf: FormControl = new FormControl('');
  unitList = [];
  filteredUnitList = [];
  x: any = 60;
  y: any = 40;
  minSize: any = 100;
  errorSpinner: boolean = false;
  expandFlag: boolean = false;
  loaderFlag: boolean;
  listType: any = "Field Walk";
  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  processControl: FormControl = new FormControl("Observation");

  
  apply: boolean=true;
  observationControl: FormControl = new FormControl("Behaviour");
  observation = [
    {
      name: "Behaviour",
    },
    {
      name: "Hazards",
    },
    {
      name: "Incidents",
    },
  ]

  categoryControl: FormControl = new FormControl("PPE");
  category = [
    {
      name: "PPE"
    },
    {
      name: "Tools & Equipment"
    },
    {
      name: "Work Environment"
    },

  ]

  observeType: FormControl = new FormControl("");
  filteredObserveTypeOptions: Observable<any[]>

  // category: FormControl = new FormControl("");
  // filteredCategoryOptions: Observable<any[]>

  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });

  startDateControl: FormControl = new FormControl("");
  endDateControl: FormControl = new FormControl("");
  labels = {}
  
  url3: string = "";
  displayedColumns: any = [];
  allColumns = [

    { name: 'externalId',key2:'Id', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name: 'title', key2:'Title',displayName: "Title", key: "title", activeFlag: true, summary: false },
    { name: 'date', key2:'Observationdate',displayName: "Date", key: "date", activeFlag: true, summary: false },
    { name: 'corePrinciple', key2:'CorePrinciple',displayName: "Core Principle", key: "corePrinciple", activeFlag: true, summary: false },
    { name: 'subProcess', key2:'SubProcess',displayName: "Sub Process", key: "subProcess", activeFlag: true, summary: false },
    { name: 'site', key2:'Site',displayName: "Site", key: "site", activeFlag: true, summary: false },
    { name: 'unit', key2:'Unit',displayName: "Unit", key: "unit", activeFlag: true, summary: false },
    { key: 'workOrderNumber', key2:'WorkOrderNumber',displayName: "Work Order Number", name: "workOrderNumber", activeFlag: false, summary: false },
    { key: 'projectName', key2:'ProjectName',displayName: "Project Name", name: "projectName", activeFlag: false, summary: false },
    { name: 'createdTime', key2:'Createdon',displayName: "Created On", key: "createdOn", activeFlag: true, summary: false },
    { name: 'createdBy', key2:'Createdby',displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: "isOperationalLearning",key2:'OperationalLearning', displayName: "Operational Learning", key: "operationalLearning", activeFlag: true, summary: false },
    { name: "operationalLearningDescription",key2:'OperationalLearningDescription', displayName: "Describe the Operational Learning opportunities you found", key: "operationalLearningDescription",  activeFlag: false, summary: false },
    { name: 'lastUpdatedTime',key2:'Updatedon', displayName: "Updated On", key: "updatedOn", activeFlag: true, summary: false },
    { name: 'modifiedBy',key2:'Updatedby', displayName: "Updated By", key: "updatedBy", activeFlag: true, summary: false },
    { name: 'actions', key2:'Actions',displayName: "Actions", key: "actions", activeFlag: true, summary: false },
    ];

    allColumnsBackup= _.cloneDeep(this.allColumns)
  initialDataFlag = 0;
  userAccessMenu: any;
  createFW_obj: any;

  coreTypeControl: FormControl = new FormControl("");
  
  coreTypeList:any = []
  auditTypeControl: FormControl = new FormControl("");
  typeList: any;
  
  operationalLearningControl = new FormControl('All');
  enabledFields: string[];
  auditType: any;
  filterFlag: string;
  projectNameList: { name: string; externalIds: string[]; }[];
  workOrderNumberList: { name: string; externalIds: string[]; }[];
  filteredprojectNameList: any;
  filteredworkOrderNumberList: any;
  items: { name: string; count: number; externalIds: string[]; }[];
  createdByList: { name: string; externalIds: string[]; }[];
  filteredCreatedByList: any;
  createdBy: FormControl = new FormControl("");
  subProcessList: any[];
  filteredSubProcessList: any;
  subProcessControl: FormControl = new FormControl("");
  columnOrder: any[];

  constructor(private cd: ChangeDetectorRef,private router: Router, private commonService: CommonService, private tokenService: TokenService, private dataService: DataService, private dialog: MatDialog, private toastr: ToastrService,public translationService: TranslationService, private languageService: LanguageService, public ngZone: NgZone, private changeDetector: ChangeDetectorRef) {
    this.url3= this.dataService.React_API + "/fieldWalk";
    this.setDateFormat();
    console.log('commonService label', this.commonService.labelObject)
    console.log('commonService label', this.commonService.selectedLanguage)
    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'],
        'formcontrolsEnddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnddate'],
        'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
        'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'],
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'],
        'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'],
        'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'],
        'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'],
        'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'],
        'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'],
        'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'],
        'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
        'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'],
        'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'],
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'],
        'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'],
        'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'],
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'],
        'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'],
        'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'],
        'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'],
        'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'],
        'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'],
        'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'],
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'],
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'],
        'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'],
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'],
        // 'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
        'chooseProjectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseProjectName'] || 'chooseProjectName',
        'commonfilterChooseworkorder':this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseworkorder'] || 'Choose Work Order',
        'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
        'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',  
      'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
      'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters',
      'fieldWalkCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalkCount'] || 'fieldWalkCount',
      'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
      
      }
    }
    this.metricsbyperson = this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.metricsbyperson);
  }

  
  reportingLocationList: any;
  filteredReportingLocationList: any;
  tempLocation: any;
  behalfList: any;
  filteredBehalfList: any;
  fl_searchVal: any;

  openFilterDialog() {
    this.dialog.open(this.filterDialog, {
      disableClose: true, // Prevent closing on outside click
    });
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'],
          'formcontrolsEnddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnddate'],
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'],
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'],
          'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'],
          'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'],
          'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'],
          'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'],
          'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'],
          'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'],
          'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'],
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'],
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'],
          'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'],
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'],
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'],
          'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
          'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'],
          'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'],
          'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'],
          'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'],
          'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'],
          'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'],
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'],
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'],
          'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'],
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'],
        // 'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
        'chooseProjectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseProjectName'] || 'chooseProjectName',
        'chooseWorkOrderNumber':this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
        'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',  
        'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
        'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters',
        'fieldWalkCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalkCount'] || 'fieldWalkCount',
        'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
      
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo)
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo.externalId)
     
     
      // if(_this.observation && _this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }

    })

    console.log(_this.listType)
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if(_this.userAccessMenu){
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.siteControl.setValue(_this.dataService.siteId);
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.processLoad(() => { });
      _this.siteChanged();
    });

    if (_this.commonService.filterListFetched) {
      _this.filterInit('Site');
      _this.filterInit('Unit');
    }



    _this.auditTypeControl.valueChanges.subscribe(value => {
      var _this= this;
      _this.auditType = value["externalId"];
      console.log("auditType", _this.auditType)
      // _this.applyObservationFilter();
    });
    _this.loadProcessType()
  
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        _this.filterInit(fiterType);
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    
    
    _this.unitControl.valueChanges.subscribe(value => {
      console.log('Value:', value);
    
      _this.processLoad(() => { });
    
      let tempArray = [];
      
      // Check if value is an array or a single value
      if (Array.isArray(value)) {
        // If value is an array, filter for each element in the array
        tempArray = _this.tempLocation.filter(item => 
          value.includes(item.reportingUnit.externalId)
        );
      } else {
        // If value is a single value, filter normally
        tempArray = _this.tempLocation.filter(item => 
          item.reportingUnit.externalId === value
        );
      }
    
      console.log('tempArray:', tempArray);
    
      _this.reportingLocationList = tempArray;
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
    
      console.log('_this.reportingLocationList:', _this.reportingLocationList);
    });

    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);
    
    _this.loadProcessType()


    // setTimeout(function () {
    //   _this.applyObservationFilter();
    // }, 1500);

      window.onmessage = function (e) {
        if (e.data.type && e.data.action && e.data.type == "FieldWalkList") {
          console.log(e.data.data)
          var myData = e.data.data; 
          if (e.data.action == "FormView") {
            var processId = myData.refOFWAProcess["items"][0]["externalId"];
            var mySubProcess = _this.commonService.processList.find(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == processId)
            _this.router.navigateByUrl('observations/field-visit', {
              state: { 
                "process": e.data.data.refOFWAProcess["items"][0], 
                "subProcess": mySubProcess, 
                "fieldwalk": e.data.data,
                "pageFrom":"FieldWalk List",
                "action":"View" } 
            });
          } else if (e.data.action == "FormEdit") {
            var processId = myData.refOFWAProcess["items"][0]["externalId"];
            var mySubProcess = _this.commonService.processList.find(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == processId)
            _this.router.navigateByUrl('observations/field-visit', {
              state: { "process": e.data.data.refOFWAProcess["items"][0],"subProcess": mySubProcess,  "fieldwalk": e.data.data ,
              "pageFrom":"FieldWalk List",
              "action":"Edit"}
            });
          } else if (e.data.action == "FormDelete") {
            _this.deleteObservation(e.data.data.externalId,e.data.data.refSite.siteCode)
            // _this.router.navigateByUrl('observations', {
            //   state: { "externalId": e.data.data.externalId,
            //           "pageFrom":"Observation List",
            //           "action":"Delete" }
            // });
          }else if ( e.data.action == "Loading") {
            console.log("LoaderTrue Loading",e.data.data)
            _this.commonService.loaderFlag = e.data.data;
            // console.log("LoaderFalse Loading1",_this.loaderFlag)
          } else if ( e.data.action == "observationHistory") {
             console.log("e.data.datahistrory",e.data.data)
            _this.showhide = true;
            console.log('observationHistory',_this.showhide);
            _this.getLogList(e.data.data.externalId);
            _this.cd.detectChanges();
           } else if( e.data.action == "sendMail"){
            //sendMail
           console.log('sendMail',e.data.data);
           _this.sendMailUser(e.data.data);
           }else if (e.data.action == "createAction") {
           // _this.goPage('action/create-action')
            _this.router.navigateByUrl('action/create-action', {
              state: { "data": e.data.data,
                      "pageFrom":_this.commonService.configuration["typeFieldWalk"],
                      "action":"Create Action" }
            });
          }
        };


      }

    
    var _this = this;
    
    this.endDateControl.valueChanges.subscribe(async range => {
      // _this.applyObservationFilter();
      _this.setDateFormat();
    });

_this.fetchItems();
    _this.coreTypeControl.valueChanges.subscribe(value => {
      var _this= this;
    _this.typeList = _this.commonService.processList.filter(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == value["externalId"])
    _this.typeList = (_this.typeList.filter(e => e.name==_this.listType))[0].externalId
    console.log("typeList", _this.typeList)
    var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.typeList);
      })
      _this.subProcessList = childrenProcess.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      _this.filteredSubProcessList = _this.subProcessList.slice();


    });
    _this.subProcessControl.valueChanges.subscribe((value: any) => {
      if(value!=null){
        _this.apply=false
      _this.dataService.postData({ "externalId": value }, this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
          var processConfig;
          if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
            processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
          }else{
            processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.auditTypeControl.value.name)
          }
          var myColumn = [];
          Object.entries(processConfig.columnConfig).forEach(([key, value]) => {
            value["key"] = key;
            myColumn.push(value);
          })
          _this.columnOrder = myColumn.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
  if(processConfig.columnConfig!=null){
    _this.apply=true
    this.enabledFields = Object?.keys(processConfig?.columnConfig).filter(key => 
      typeof processConfig.columnConfig[key] === 'object' && processConfig.columnConfig[key].isEnabled
    ).map(key => processConfig.columnConfig[key].field);
    
    console.log(this.enabledFields);
  }
          
          // _this.applyObservationFilter();
  
        // }
      })
    }
        else{
          _this.apply=true
          // console.log(this.allColumnsBackup)
          // console.log(this.allColumnsBackup.filter(column => column.activeFlag).map(column => column.key2));
          this.enabledFields=this.allColumnsBackup.filter(column => column.activeFlag).map(column => column.key2)
        }
    });

  }

  filterInit(fiterType) {
    var _this = this;

    if (fiterType == 'Site') {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.commonService.siteList.slice();
      console.log('_this.siteList', _this.siteList);
      _this.commonService.observeListSubject.next(true);
      _this.setDateFormat();
    }
    if (fiterType == 'Unit') {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.commonService.unitList.slice();
    }
    //  _this.userData = _this.commonService.userInfo
  }
  onSelectOperationalLearning(value: boolean | string) {
    var _this = this
    if (value !== null) {
      console.log(value)
      if (value == "All"){
        _this.OperationalLearning = null
      }
      else{
        _this.OperationalLearning = !!value
      }
      // this.applyObservationFilter();
    }}

    fetchItems(): void {
      this.loaderFlag = true;
  
      let startD = '';
      let endD = '';
      if (this.startDateControl.value) {
          const myStartDate = new Date(this.startDateControl.value);
          startD = `${myStartDate.getFullYear()}-${("0" + (myStartDate.getMonth() + 1)).slice(-2)}-${("0" + myStartDate.getDate()).slice(-2)}`;
      }
      if (this.endDateControl.value) {
          const myEndDate = new Date(this.endDateControl.value);
          endD = `${myEndDate.getFullYear()}-${("0" + (myEndDate.getMonth() + 1)).slice(-2)}-${("0" + myEndDate.getDate()).slice(-2)}`;
      }
  
      const requestBody = {
          limit: 1000,
          sites: [this.siteControl.value],
          listType: this.listType ,
          isActive: true,
          startDate: startD,
          endDate: endD
      };
  
      this.dataService.postData(requestBody, this.dataService.NODE_API + "/api/service/listFieldWalk").subscribe((response: any) => {
          console.log("response---->", response);
  
          if (response && response.data) {
              const observationList = response['data']['list' + this.commonService.configuration['typeFieldWalk']]['items'];
              console.log("Raw FieldWalk:", observationList);
  
              const nameExternalIdMap = new Map<string, { count: number, externalIds: string[] }>();
              const projectMap = new Map<string, { name: string, externalIds: string[] }>();
              const workOrderMap = new Map<string, { name: string, externalIds: string[] }>();
              const createdByMap = new Map<string, { name: string, externalIds: string[] }>();
  
              observationList.forEach((item: any) => {
                const projectName = item.projectName;
                const workOrderNumber = item.workOrderNumber;
                const displayName = item.createdBy ? item.createdBy : 'N/A';
                  const externalId = item.externalId;
                  const createdBy = item.createdBy;
  
                  if (projectName) {
                    if (!projectMap.has(projectName)) {
                        projectMap.set(projectName, { name: projectName, externalIds: [] });
                    }
                    projectMap.get(projectName)!.externalIds.push(externalId);
                }
        
                if (workOrderNumber) {
                    if (!workOrderMap.has(workOrderNumber)) {
                        workOrderMap.set(workOrderNumber, { name: workOrderNumber, externalIds: [] });
                    }
                    workOrderMap.get(workOrderNumber)!.externalIds.push(externalId);
                }
                
                      // Handle createdBy data
        if (createdBy) {
          if (!createdByMap.has(createdBy)) {
              createdByMap.set(createdBy, { name: createdBy, externalIds: [] });
          }
          createdByMap.get(createdBy)!.externalIds.push(externalId);
      }
  
  
                  if (!nameExternalIdMap.has(displayName)) {
                      nameExternalIdMap.set(displayName, { count: 0, externalIds: [] });
                  }
  
                  const entry = nameExternalIdMap.get(displayName)!;
                  entry.externalIds.push(externalId);
                  entry.count = entry.externalIds.length; // Update count to reflect all occurrences
              });
  
  
      this.projectNameList = Array.from(projectMap.values());
      this.workOrderNumberList = Array.from(workOrderMap.values());
      this.createdByList = Array.from(createdByMap.values());
  
      this.filteredprojectNameList = this.projectNameList;
      this.filteredworkOrderNumberList = this.workOrderNumberList;
      this.filteredCreatedByList = this.createdByList;
  
              this.items = Array.from(nameExternalIdMap.entries()).map(([name, { count, externalIds }]) => {
                  return { name, count, externalIds };
              });
  
              // Log detailed information
              this.items.forEach(item => {
                  console.log(`DisplayName: ${item.name}, Count: ${item.count}, ExternalIds: ${item.externalIds.join(', ')}`);
              });
  
            console.log("All items:", this.items);
            this.filterItems();
            this.cd.detectChanges(); 
            this.loaderFlag = false;
        } else {
            console.error("Response structure is not as expected");
            this.loaderFlag = false;
        }
  
  
  
          
      });
  }
  // filterItems() {
  //   throw new Error('Method not implemented.');
  // }

  closeDialog() {
    this.dialog.closeAll();
  }

  
  clearFilters() {
    this.unitControl.reset();
    this.behalf.reset();
    this.locationObserve.reset();
    this.coreTypeControl.reset();
    this.projectName.reset();
    this.workOrderNumber.reset();
    this.createdBy.reset();
    this.subProcessControl.reset();
    this.operationalLearningControl.setValue('All');
    this.startDateControl.reset();
    this.endDateControl.reset();
    this.applyObservationFilter();
    if(this.enabledFields && this.enabledFields.length>0){
      this.updateActiveFlags(this.allColumns, this.enabledFields);
    }
    this.dialog.closeAll();
  }

  applyFilters() {
    // Logic to apply filters based on the selected values
    console.log('Applied filters:', {
      unit: this.unitControl.value,
      behalf: this.behalf.value,
      location: this.locationObserve.value,
      operationalLearning: this.operationalLearningControl.value,
      projectName: this.projectName.value,
      workOrderNumber: this.workOrderNumber.value,
      createdBy: this.createdBy.value,
      coreType: this.coreTypeControl.value,
    });
    this.applyObservationFilter();
    if(this.enabledFields && this.enabledFields.length>0){
      this.updateActiveFlags(this.allColumns, this.enabledFields);
    }
    this.dialog.closeAll();
  }

  async onBehalfChange(item: any) {
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.behalfList = [];
    _this.filteredBehalfList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.behalfList.forEach((element)=>{
          element.name =  element.firstName+' '+element.lastName
        })
        _this.filteredBehalfList = _this.behalfList.slice();
      
  
      })
      _this.cd.detectChanges()
      
  }
}

  updateActiveFlags(columns: any[], activeKeys: string[]) {
    var _this=this;
    columns.forEach(column => {
      column.activeFlag = activeKeys.includes(column.key2);
    });
    // Call your function with the updated array    
    _this.setColumn(columns);
  }

  siteChanged() {
    var _this = this;
    var selectedSite = _this.commonService.getSelectedValue(
      _this.siteControl.value
    );
    _this.unitList = [];
    _this.filteredUnitList = _this.unitList.slice();

    if (selectedSite.length > 0) {
      var myCountries = [];
      var myRegions = [];
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e['country']) {
            myCountries.push(e['country']['externalId']);
            if (e['country']['parent'])
              myRegions.push(e['country']['parent']['externalId']);
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
      _this.filterFlag = 'Site';
    } else {
      selectedSite = _this.siteList.map((eItem) => eItem.externalId);
    }

    if (selectedSite.length > 0) {
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (
            e['reportingUnits']['items'] &&
            e['reportingUnits']['items'].length > 0
          ) {
            _this.unitList = _this.unitList.concat(
              _.orderBy(e['reportingUnits']['items'], ['name'], ['asc'])
            );
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
    } else {
      if (_this.siteList.length > 0) {
        _this.unitList = _this.commonService.unitList;
        _this.filteredUnitList = _this.unitList.slice();
      }
    }
  }
  getUserMenuConfig(){
    var _this = this
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
      setTimeout(()=>{
        _this.siteChanged();
      },1000)
    
    }
 
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
      if(_this.commonService.menuFeatureUserIn.length>0){
      _this.createFW_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkCreate);
      var FWView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkView);
      var FWEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkEdit);
      var FW3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalk3dAssest);
      var FWCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkCreateAction);
      var  FWdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkDelete);
      var  FWHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkHistory);
      var   FWShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkShare);

       var iframe = document.getElementById('iFrameFieldWalk');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken",  "user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(),
        "data": _this.tokenService.getToken(),
        "idToken":_this.tokenService.getIDToken(),
        "LanguageCode":_this.commonService.selectedLanguage.toUpperCase(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], }, '*');
      iWindow?.postMessage(
        {
          type: 'Language',
          action: 'Language',
          LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
          idToken: this.tokenService.getIDToken(),
          labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
        },
        '*'
      );
      iWindow?.postMessage({ "type": "FieldWalk", "action": "AccessMenu", "data": {FWView:FWView,FWEdit:FWEdit,FW3Dview:FW3Dview,FWCreateAction:FWCreateAction,FWdelete:FWdelete,FWHistory:FWHistory,FWShare:FWShare} }, '*');
        
      }else{
        _this.createFW_obj = {}
         var iframe = document.getElementById('iFrameFieldWalk');
        if (iframe == null) return;
        var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
          "data": _this.tokenService.getToken() }, '*');
        iWindow?.postMessage(
          {
            type: 'Language',
            action: 'Language',
            LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
            idToken: this.tokenService.getIDToken(),
            labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
          },
          '*'
        );
        iWindow?.postMessage({ "type": "FieldWalk", "action": "AccessMenu", "data": {FWView:{},FWEdit:{},FW3Dview:{},FWCreateAction:{},FWdelete:{},FWHistory:{},FWShare:{}} }, '*');
          
      }
    
  }


 


  deleteObservation(id:string, sitecode:string) {
    var _this = this
    console.log("observation",_this.observation)

    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {
        title:'createactionAreyousure',
       }
   });
   dialogRef.afterClosed().subscribe(result => {
    console.log('result',result)
      if(result == 'YES'){
        _this.commonService.loaderFlag = true;
        var site = _this.commonService.siteList.find(e => e.externalId == _this.observation["refSite"])
        _this.selectedSite = site;
var softdata={
"type": _this.commonService.configuration["typeFieldWalk"],
      "siteCode":_this.commonService.siteList.find(e => e.externalId == _this.siteControl.value).siteCode,
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        {
          "externalId": id,
          "isActive" : false,
        }
      ]
}
console.log(softdata)
  _this.dataService.postData(softdata, _this.dataService.NODE_API + "/api/service/createInstanceByProperties")
  .subscribe(data => {
    console.log("data",data)
    this.toastr.success('', 'This Fieldwalk has been deleted', {
      timeOut: 3000,
    });
    this.applyObservationFilter();
    _this.commonService.loaderFlag = false;

  })
        _this.cd.detectChanges();
      }
  });

  

  }


  getLogList(objectId){
    console.log("getloglist called")
    var _this =this;
    _this.logList =[]
    _this.dataService.postData({"objectId":objectId}, _this.dataService.NODE_API + "/api/service/listOFWALog")
    .subscribe(data => {
      console.log("data",data)
      var listLog = data['data']['list' + _this.commonService.configuration["typeOFWALog"]]['items'];
      var temp = []
      listLog.forEach(element => {
        console.log("element",element)
        var obj =    {
          externalId:element.objectExternalId,
          logType:element.logType,
          name:element.refUser?element.refUser.firstName+' '+element.refUser.lastName:'',
          panelOpenState:false,
          createdTime:element.dateTime,
       
        }
        temp.push(obj)
       
      });
      _this.logList = temp
      _this.errorSpinner = true;
      _this.cd.detectChanges();
  
                     
    })

  }


  sendMailUser(obData){
    var _this =this;
    
    const dialogRef =  _this.dialog.open(fieldSendMailComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'mail-dialog', data: obData
   });
   dialogRef.afterClosed().subscribe(result => {
   })


  }


  closeClick(){
    var _this =this;
    _this.showhide = false;
  }
  
  expandClick() {
    if (this.expandFlag) {
      this.expandFlag = false;
      this.x = 60;
      this.y = 40;

    
    } else {
      this.expandFlag = true;
      this.x = 0;
      this.y = 100;


    }

  }
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  a:number=0
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    if (_this.site && _this.site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(_this.site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      console.log('reportingLocationList',_this.reportingLocationList)
    }    
    _this.cd.detectChanges();
    console.log(_this.a)
    if(_this.a==0)
      {
        console.log("applyfilter")
        setTimeout(() => {
          
      _this.applyObservationFilter();
        }, 3000);
    _this.a++
    }
    cb();
  }


  goSupplier() {
    //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
    this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier', {
      timeOut: 3000,
    });
  }
  ngAfterViewInit(): void {

    var _this = this;
    _this.setDateFormat();
    // _this.commonService.loaderFlag = true
    console.log("loaderFlag   1",_this.loaderFlag)
      var iframe = document.getElementById('iFrameFieldWalk');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "FieldWalk", "action": "Column", "data": _this.displayedColumns }, '*');
      _this.getUserMenuConfig()


  }

  applyObservationFilter() {
    var _this = this;
    _this.commonService.loaderFlag = true;
    var iframe = document.getElementById('iFrameFieldWalk');
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var startD;
    var endD;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken(),
      "columnOrder": _this.columnOrder
     }, '*');
    if(_this.endDateControl.value){
      var myStartDate = new Date(this.startDateControl.value);
      startD = myStartDate.getFullYear() +"-"+(("0" + (myStartDate.getMonth() + 1)).slice(-2))+"-"+(("0" + (myStartDate.getDate())).slice(-2));
      var myEndDate = new Date(this.endDateControl.value);
      endD = myEndDate.getFullYear() +"-"+(("0" + (myEndDate.getMonth() + 1)).slice(-2))+"-"+(("0" + (myEndDate.getDate())).slice(-2));
     
    }
    var sites = []
    if(this.siteControl.value){
      sites = [this.siteControl.value]
    }
    
    // unit: this.unitControl.value,
    // "behalf": this.behalf.value,
    // "location": this.locationObserve.value,
    // coreType: this.coreTypeControl.value,

    iWindow?.postMessage({ "type": "FieldWalk", "action": "Filter",
      "projectName": this.projectName?.value,"createdBy": this.createdBy?.value,"subProcess":this.subProcessControl?.value,
      "workOrderNumber": this.workOrderNumber?.value,"units":this.unitControl?.value,"OperLearning": _this.OperationalLearning , "listType":_this.listType,"behalf": this.behalf?.value, "location": this.locationObserve?.value,"processId": _this.typeList, "subCategory": "","date":{start:startD,end:endD},sites:sites, 'LanguageCode': `${this.commonService.selectedLanguage}`, }, '*');
    _this.getUserMenuConfig()
  }
  ngOnDestroy(): void {

  }

  loadProcessType(){
    var _this= this;
    console.log(_this.siteControl.value)
    _this.coreTypeList = _this.commonService.processList.filter(e => e.processType=="Core Principles" && e.refSite.externalId == _this.siteControl.value)
    console.log("coretypelist---->",_this.coreTypeList)
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);


  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameFieldWalk');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FieldWalk", "action": "Summary", "data": summaryCol }, '*');
    _this.getUserMenuConfig()
  }

  processSelected(process) {
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {

  }
  categorySelected(cate) {

  }

  createFieldWalk() {
    var _this = this;
    console.log("kkkkkkk")
    // observations
    var processList = _this.commonService.processList.filter(e => {
      return e.processType == "Process" && e.name == "Field Walk" && e.refSite["externalId"] == _this.siteControl.value;
    })
    console.log(processList)
    _this.router.navigateByUrl('observations', {
      state: { "process": processList[0] }
    })
    // _this.router.navigateByUrl('observations', {
    //   state: {
    //     "observation": processList[0],
    //     "pageFrom": "Adit List",
    //     "action": "Edit"
    //   }
    // });
  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
  
  searchQuery: string = '';
  searchTerm = '';
  isPopupOpen = false;
  metricsbyperson: any;
  filteredItems: { name: string, count: number, externalIds: string[] }[] = [];

  resetSearch(): void {
    this.searchQuery = ''; 
    this.searchTerm = '';
    this.items = [];  
  }

  cancelClick() {
    this.resetSearch()
    this.isPopupOpen = !this.isPopupOpen;
  }
  popupClick() {
    this.isPopupOpen = !this.isPopupOpen;
    if (this.isPopupOpen) {
      this.fetchItems();
    }
  }
  onSearchTermChange(): void {
    console.log("Search term changed:", this.searchTerm);
    this.filterItems();
  }
    filterItems(): void {
    console.log("Filtering with searchTerm:", this.searchTerm);
    this.filteredItems = this.items.filter(item => {
      const matches = item.name?.toLowerCase().includes(this.searchTerm.toLowerCase());
      if (matches) {
        console.log("Item matches:", item);
      }
      return matches;
    });
    console.log("Filtered items:", this.filteredItems);
    this.cd.detectChanges();  // Ensure Angular detects changes
  }

  excelExport(){
    var _this = this
    var iframe = document.getElementById('iFrameFieldWalk');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
      "data": _this.tokenService.getToken(),
      "idToken":_this.tokenService.getIDToken(),
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
    }, '*');
    iWindow?.postMessage({ "type": "FieldWalk", "action": "Excel", "data": {} }, '*');

  }
}
