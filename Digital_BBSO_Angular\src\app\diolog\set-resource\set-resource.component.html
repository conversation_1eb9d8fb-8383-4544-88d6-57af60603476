<div class="mail-popup">
    <commom-label labelText="{{ labels['setrecurrence'] }}" [tagName]="'label'" [cstClassName]="'mail-sub-heading resource-label'"></commom-label>
    <div *ngIf="eventData.event.name != 'Quarterly'" class="mail-body">
         <div class="half-width p-r-10">
            <commom-label labelText="{{labels['startDate']}}" [tagName]="'label'"
            [cstClassName]="'form-label d-flex mt-5'"></commom-label>
            <mat-date-picker [className]="'form-date-picker'" [SelectedDate]="currentDate" (dateInput)="getSelectedDate($event, true)"></mat-date-picker>
    
         </div>
        <div class="half-width p-l-10">
            <commom-label labelText="{{labels['endDate']}}" [tagName]="'label'"
           [cstClassName]="'form-label d-flex mt-5'"></commom-label>
           <mat-date-picker [className]="'form-date-picker'" [SelectedDate]="endDateData" (dateInput)="getSelectedDate($event, false)"></mat-date-picker>
           <!-- <a (click)="aremoveEndDate()" class="removeDate" *ngIf="endDate">{{ labels['remove'] }}</a> -->
        </div>
      
        <div class="weeks" *ngIf="eventData.event.name == 'Weekly'">
            <commom-label labelText="{{ labels['weekdays'] }}" [tagName]="'label'"
            [cstClassName]="'form-label d-flex mt-5'"></commom-label>
              <ul>
                 <li *ngFor="let day of weeksDays;"><a [ngClass]="selectedDays.includes(day.fullName) ? 'selected' : ''" (click)="selectedUnSelectedDay(day)">{{day.name}}</a></li>
              </ul>
        </div>

        <div class="monthly" *ngIf="eventData.event.name == 'Monthly'">
            <commom-radio-box [radioOptionList]="radioOptionList" (radioInput)="getSelectedRadio($event)"
            [className]="'cst-action-radio-btn'" [selectedItem]="selectedItem"></commom-radio-box>
            <common-lib-input [cstClassName]="'resource-month-input'" [value]="inputValue" 
            [disable]="(selectedItem != 'On day' ? true : false)"
             (inputValue)="getInputValue($event)"></common-lib-input>
            <!-- <commom-select-box  [options]="dayCount" [modeselect]="selectedDayCount.value" 
            [className]="'dashboard-announcement-select resource-month-drop-down'" 
            [disable]="(selectedItem == 'On day' ? true : false)"
            (selectedOption)="selectedDayCountOption($event)"></commom-select-box>
            <commom-select-box  [options]="fullWeekDays" [modeselect]="selectedWeekDay.value" 
            [disable]="(selectedItem == 'On day' ? true : false)"
            [className]="'dashboard-announcement-select resource-month-drop-down'" 
            (selectedOption)="selectedWeekDayOption($event)"></commom-select-box> -->
        </div>

        <div class="text-display">
            <span>{{ labels['occursevery'] }} </span> 
            <span *ngIf="eventData.event.name == 'Daily' || (eventData.event.name == 'Weekly' && selectedDays.length == weeksDays.length)" class="mlr-2">{{ labels['day'] }}</span> 
            <span *ngIf="eventData.event.name == 'Weekly' && selectedDays.length < weeksDays.length" class="mlr-2">
                <ng-container *ngFor="let day of selectedDays; let ind =index">
                    {{ labels[day.toLowerCase()] }}
                    <span *ngIf="selectedDays.length > 1 && ind == selectedDays.length - 2">{{ labels['and'] }}</span>
                    <span *ngIf="selectedDays.length > 2 && ind < selectedDays.length - 2">,</span>
                </ng-container>
            </span> 
            <span *ngIf="eventData.event.name == 'Monthly'">
                <span class="mlr-2">{{labels['month'] }}</span>
                <span *ngIf="selectedItem == 'On day'" class="mlr-2">{{labels['onday']  }}</span>
                <span *ngIf="selectedItem != 'On day'" class="mlr-2">{{labels['on'] }}</span>
                <span *ngIf="selectedItem == 'On day'" class="mlr-2">{{inputValue}}</span>
                <span *ngIf="selectedDayCount && selectedItem != 'On day'" class="mlr-2">{{selectedDayCount.value}}</span>
                <span *ngIf="selectedWeekDay && selectedItem != 'On day'" class="mlr-2">{{selectedWeekDay.name}}</span>
            </span>
            <span *ngIf="startDate" class="mlr-2">{{labels['starting'] }}</span>
            <span *ngIf="startDate" class="mlr-2">{{startDate}}</span>
            <span *ngIf="endDate" class="mlr-2">{{labels['until'] }}</span>
            <span *ngIf="endDate" class="mlr-2">{{endDate}}</span>
        </div>
    </div>
    <div class="weeks" *ngIf="eventData.event.name == 'Quarterly'" style="margin-top: 10px;">
        <mat-form-field appearance="outline" class="schedule-form-width">
            <mat-label>{{labels['selectQuarter']}}</mat-label>
            <mat-select placeholder="{{labels['chooseQuarter']}}"  [(value)]="selectedQuarter" (selectionChange)="onQuarterSelect()">
              <mat-option *ngFor="let quarter of quarters" [value]="quarter">
                {{ quarter }}
              </mat-option>
            </mat-select>
          </mat-form-field>
      
          <div class="half-width p-r-10" style="margin-top: 10px;">
            <commom-label labelText="{{labels['startDate']}}" [tagName]="'label'"
            [cstClassName]="'form-label d-flex mt-5'"></commom-label>
            <mat-date-picker [className]="'form-date-picker'" [SelectedDate]="currentDate" (dateInput)="getSelectedDate($event, true)"></mat-date-picker>
    
         </div>
        <div class="half-width" style="margin-top: 10px;">
            <commom-label labelText="{{labels['endDate']}}" [tagName]="'label'"
           [cstClassName]="'form-label d-flex mt-5'"></commom-label>
           <mat-date-picker [className]="'form-date-picker'" [SelectedDate]="endDateData" (dateInput)="getSelectedDate($event, false)"></mat-date-picker>
           <!-- <a (click)="aremoveEndDate()" class="removeDate" *ngIf="endDate">{{ labels['remove'] }}</a> -->
        </div>
      </div>
      
    <div class="mail-fotter">
        <common-lib-button [className]="'cst-btn cancel mail-cancel'" text="{{ labels['buttonCancel'] }}" (buttonAction)="cancel()"></common-lib-button>
        <common-lib-button [className]="'cst-btn mail-shared'" text="{{ labels['buttonSave'] }}" (buttonAction)="save()"></common-lib-button>
    </div>
</div>
