
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": false,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "es2020",
    "module": "es2020",
    "lib": [
      "es2020",
      "dom"
    ],
    "emitDecoratorMetadata": true,
    "noImplicitAny": false,
    "strictNullChecks":false,
    "esModuleInterop": true,
    "strictPropertyInitialization": false,
    "allowJs": true,
    "checkJs": false,
    "types": ["jquery","three","THREE"],
    "resolveJsonModule": true,
    "skipLibCheck": true
  },
  "exclude": [
    "node_modules/@cognite/reveal"
  ],
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true,
    "disableTypeScriptVersionCheck": true,
  }
}
