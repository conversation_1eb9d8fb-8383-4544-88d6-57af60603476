import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';

@Component({
  selector: 'app-audit-plan-list',
  templateUrl: './audit-plan-list.component.html',
  styleUrls: ['./audit-plan-list.component.scss']
})
export class AuditPlanListComponent implements OnInit {

  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl : FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  auditTypeControl: FormControl = new FormControl("Audit type");
  auditType = [{
    name:"Audit type",
  }]

  supplierTypeControl: FormControl = new FormControl("Supplier type");
  supplierType =[
    {
      name:"Supplier type",
    }
  ]

  displayedColumns2: string[] = ['id', 'vendorNo', 'vendarName', 'vendorContactPerson', 'email','region', 'country', 'yearQ','auditor','status','actions'];
  dataSource = [
    {
      id: 'CESQM-2023-01',
      vendorNo: '1013380',
      vendarName: 'GODDING UND DRESSLER GMBH',
      vendorContactPerson: 'Michael',
      email: '<EMAIL>',
      region:'Europe',
      country: 'Germany',
      yearQ: '2023-Q3',
      auditor:'M. Aurin',
      status:'Pending',
      actions: ''
    },
    {
      id: 'CESQM-2023-02',
      vendorNo: '1014101',
      vendarName: 'Geba Kunststoffcompounds GmbH',
      vendorContactPerson: 'Williams',
      email: '<EMAIL>',
      region:'Europe',
      country: 'Germany',
      yearQ: '2023-Q3',
      auditor:'M. Aurin',
      status:'Pending',
      actions: ''
    },
   
    {
      id: 'CESQM-2023-03',
      vendorNo: '1125825',
      vendarName: 'LANXESS SALES NETHERLANDS B.V.',
      vendorContactPerson: 'Albert',
      email: '<EMAIL>',
      region:'Europe',
      country: 'Netherland',
      yearQ: '2023-Q3',
      auditor:'S. Petry',
      status:'Pending',
      actions: ''
    },
   
    {
      id: 'CESQM-2023-04',
      vendorNo: '1010015',
      vendarName: 'CLARIANT SE',
      vendorContactPerson: 'Thomson',
      email: '<EMAIL>',
      region:'Europe',
      country: 'Germany',
      yearQ: '2023-Q3',
      auditor:'S. Petry',
      status:'Pending',
      actions: ''
    },
   
   

  ];

  displayedColumns: any = [];

  allColumns = [

    { key: 'id', displayName: "ID", name: "id", activeFlag: true, summary: false },
    { key: 'vendorNo', displayName: "Vendor No", name: "vendorNo", activeFlag: true, summary: false },
    { key: 'vendarName', displayName: "Vendar Name", name: "vendarName", activeFlag: true, summary: false },
    { key: 'vendorContactPerson', displayName: "vendor Contact Person", name: "vendorContactPerson", activeFlag: true, summary: false },
      
    { key: 'email', displayName: "Email", name: "email", activeFlag: true, summary: false },
    { key: 'region', displayName: "region", name: "region", activeFlag: true, summary: false },
    { key: 'country', displayName: "Country", name: "country", activeFlag: true, summary: false },
    { key: 'yearQ', displayName: "Year-Q", name: "yearQ", activeFlag: true, summary: false },
    { key: 'auditor', displayName: "Auditor", name: "auditor", activeFlag: true, summary: false },
     { key: 'status', displayName: "Status", name: "status", activeFlag: true, summary: false },
     { key: 'actions', displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  
  
  
  ];
  url: string;
  site: any;
  filteredSiteList: any;
  siteList: any;
  initialDataFlag: any;
  constructor(private tokenService:TokenService, private commonService:CommonService, private toastr: ToastrService,private router: Router,private dataService: DataService,private dialog: MatDialog) {

    this.url =this.dataService.React_API+ "/audit?token=" + tokenService.getToken();

    var _this =this;
 
   }

  
   ngOnInit(): void {
    var _this = this;
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.siteControl.setValue(_this.dataService.siteId);
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      _this.processLoad(() => { });
    });

    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);


    setTimeout(function () {
      _this.applyObservationFilter();
    }, 1500);

    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "Audit") {
        console.log('FieldWalkList')
        console.log(e.data)
        if (e.data.action == "EMailSend") {
          _this.scheduleSendToVedor(e.data.data);
        } else if (e.data.action == "AuditView") {
          _this.router.navigateByUrl('observations/audit-plan-quality', {
            state: { "externalId": e.data.data.externalId }
          });
          console.log('AuditView-->')
        } else if (e.data.action == "ScoreCard") {
          _this.goPage('observations/scorecard')
          console.log('ScoreCard-->')
        } else if (e.data.action == "Create") {
          _this.goPage('action/create-action')
          console.log('Create-->')
        } else if (e.data.action == "AuditSummary") {
          console.log('AuditSummary-->')
          _this.goPage('observations/audit-summary')
        }
      };


    }


    // var _this = this;

    // this.endDateControl.valueChanges.subscribe(async range => {
    //   // console.log(,this.endDateControl.value);
    //   _this.applyObservationFilter();
    // });



  }

  scheduleSendToVedor(audit) {

    var _this = this;
    _this.dataService.postData({ auditId: audit.externalId }, _this.dataService.NODE_API + "/api/service/scheduleSendToVedor").subscribe(data => {
      _this.commonService.triggerToast({ type: 'success', title: "", msg: "Email sent successfully" });
      // if (data["items"].length > 0) {
      //   _this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });

      // } else {
      //   _this.commonService.triggerToast({ type: 'error', title: "", msg: "Failed" });
      // }
    })
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    cb();
  }


  goSupplier() {
    //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
    this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier', {
      timeOut: 3000,
    });
  }
  ngAfterViewInit(): void {

    var _this = this;
    var iframe = document.getElementById('iFrameAuditPlan');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "FieldWalkPlanList", "action": "Column", "data": _this.displayedColumns }, '*');

  }

  applyObservationFilter() {
    var _this = this;
    var iframe = document.getElementById('iFrameAuditPlan');
    console.log('iframe', iframe)
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var startD;
    var endD;

    // if (_this.endDateControl.value) {
    //   var myStartDate = new Date(this.startDateControl.value);
    //   startD = myStartDate.getFullYear() + "-" + (("0" + (myStartDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myStartDate.getDate() + 1)).slice(-2));
    //   var myEndDate = new Date(this.endDateControl.value);
    //   endD = myEndDate.getFullYear() + "-" + (("0" + (myEndDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myEndDate.getDate() + 1)).slice(-2));

    // }
    console.log(startD, endD)
    console.log(this.siteControl.value)
    var sites = []
    if (this.siteControl.value) {
      sites = [this.siteControl.value]
    }
    iWindow?.postMessage({ "type": "FieldWalk", "action": "Filter", "subCategory": "", "date": { start: startD, end: endD }, sites: sites }, '*');
    console.log("applyFilter")
  }
  ngOnDestroy(): void {

  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);


  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setSummary();
      }
    });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.key] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol', summaryCol)
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameAuditPlan');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "Audit", "action": "Summary", "data": summaryCol }, '*');
  }

  processSelected(process) {
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {

  }
  categorySelected(cate) {

  }

  createObservation() {
    this.newItemEvent.emit({ "cancel": true });
  }
  // goSupplier(){
  // //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
  //   this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier', {
  //     timeOut: 3000,
  //   });
  // }
}
