import { ComponentFixture, TestBed, fakeAsync } from '@angular/core/testing';

import { CreateRuleComponent } from './create-rule.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AppModule } from 'src/app/app.module';
import { CommonService } from 'src/app/services/common.service';

fdescribe('CreateRuleComponent', () => {
  let component: CreateRuleComponent;
  let fixture: ComponentFixture<CreateRuleComponent>;


  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule,AppModule],
      providers:[CommonService]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CreateRuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });


  it('should match span text', fakeAsync (() => {
    const span = fixture.debugElement.nativeElement.querySelector('#create_rule_title');
    fixture.detectChanges();
    expect(component.creaRuTitle).toBe(span.textContent);
  }));
  it('Create Rule Form Validation', () => {
    //component.createSchedule
    component.saveRules();
    expect(component.createRuleForm.valid).toBeFalsy();
    component.createRuleForm.controls['ruleName'].setValue("Test rule");
    component.createRuleForm.controls['schedule'].setValue("Schedule");
    component.createRuleForm.controls['operators'].setValue("Greater than");
    component.createRuleForm.controls['dueDate'].setValue("Due date");
    component.createRuleForm.controls['thenAlert'].setValue("");
    component.createRuleForm.controls['chooseWorkflow'].setValue("");
    expect(component.createRuleForm.valid).toBeTruthy();
   // expect(true).toBe(true);
   component.saveRules();
  });
});
