<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
  <div fxFlex="100">

    <div class="question-list-section">
      <div class="action-section-unit-section justify-end">
        <div class="icon-bg-box" (click)="summaryClick()" matTooltip="{{ 'REACT_TABLE.COL_SUMMARY' | translate }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-stroke-svg-icon"
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="settingClick()" matTooltip="{{ 'REACT_TABLE.COL_SELECTION' | translate }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
              d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="goPage('configuration/config-list')">
          <mat-icon class="icon-arrow">arrow_back_ios</mat-icon>
        </div>
      </div>

      <div class="action-section-unit-section">
        <commom-label [labelText]="'CONFIGURATION.QUESTION_LIST.TITLE'"  [labelTextNoLan]="configListRow.name" [tagName]="'h4'"
          [cstClassName]="'heading unit-heading'"></commom-label>
          <!-- <span class="heading unit-heading">{{configListRow.name}}</span> -->
        <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.ADD_QUESTION'" [icon]="'add'"
        *ngIf="configurationAddQuestion && configurationAddQuestion.featureAccessLevelCode !='NoAccess'"
          (buttonAction)="addQuestion();"></common-lib-button>
      </div>

      <div class="marginTop d-flex">
        <iframe id="iFrameFormConfig" class="iFrameTable" [src]="url| safe" title="description">
        </iframe>
      </div>

    </div>

  </div>
</div>