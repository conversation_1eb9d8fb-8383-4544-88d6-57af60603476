import { ChangeDetectorRef, Component, Inject, NgZone, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CommonService } from 'src/app/services/common.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-craft',
  templateUrl: './add-craft.component.html',
  styleUrls: ['./add-craft.component.scss']
})
export class AddCraftComponent implements OnInit {
  categoryOption = []
  configureDataDialog: any;
  craftControl: FormControl = new FormControl('');
  labels = {}

  constructor(@Inject(MAT_DIALOG_DATA) private _data: any,
    public dialogRef: MatDialogRef<AddCraftComponent>,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone,
    private changeDetector: ChangeDetectorRef,
    private commonService: CommonService
  ) {
    this.labels = {
      'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
      'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
    }
  }

  ngOnInit(): void {
    this.configureDataDialog = this._data
    console.log(this.configureDataDialog)
    this.craftControl.setValue(this.configureDataDialog.name);
    var _this = this
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
          'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

  }

  savaData() {
    this.dialogRef.close({ name: this.craftControl.value })
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
