0 info it worked if it ends with ok
1 verbose cli [ 'C:\\Program Files\\nodejs\\node.exe',
1 verbose cli   'C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js',
1 verbose cli   'start' ]
2 info using npm@3.10.10
3 info using node@v6.11.4
4 verbose run-script [ 'prestart', 'start', 'poststart' ]
5 info lifecycle wbsct@1.0.0~prestart: wbsct@1.0.0
6 silly lifecycle wbsct@1.0.0~prestart: no script for prestart, continuing
7 info lifecycle wbsct@1.0.0~start: wbsct@1.0.0
8 verbose lifecycle wbsct@1.0.0~start: unsafe-perm in lifecycle true
9 verbose lifecycle wbsct@1.0.0~start: PATH: C:\Program Files\nodejs\node_modules\npm\bin\node-gyp-bin;D:\KarguvelNew\ICCC\SmartTechPlatform-ICCC_Node_V3\node_modules\.bin;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Git\cmd;C:\Program Files\Microsoft VS Code\bin;C:\ffmpeg\bin;C:\Users\<USER>\Music\flutter\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Roaming\npm
10 verbose lifecycle wbsct@1.0.0~start: CWD: D:\KarguvelNew\ICCC\SmartTechPlatform-ICCC_Node_V3
11 silly lifecycle wbsct@1.0.0~start: Args: [ '/d /s /c', 'node server.js' ]
12 silly lifecycle wbsct@1.0.0~start: Returned: code: 1  signal: null
13 info lifecycle wbsct@1.0.0~start: Failed to exec start script
14 verbose stack Error: wbsct@1.0.0 start: `node server.js`
14 verbose stack Exit status 1
14 verbose stack     at EventEmitter.<anonymous> (C:\Program Files\nodejs\node_modules\npm\lib\utils\lifecycle.js:255:16)
14 verbose stack     at emitTwo (events.js:106:13)
14 verbose stack     at EventEmitter.emit (events.js:191:7)
14 verbose stack     at ChildProcess.<anonymous> (C:\Program Files\nodejs\node_modules\npm\lib\utils\spawn.js:40:14)
14 verbose stack     at emitTwo (events.js:106:13)
14 verbose stack     at ChildProcess.emit (events.js:191:7)
14 verbose stack     at maybeClose (internal/child_process.js:920:16)
14 verbose stack     at Process.ChildProcess._handle.onexit (internal/child_process.js:230:5)
15 verbose pkgid wbsct@1.0.0
16 verbose cwd D:\KarguvelNew\ICCC\SmartTechPlatform-ICCC_Node_V3
17 error Windows_NT 6.3.9600
18 error argv "C:\\Program Files\\nodejs\\node.exe" "C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js" "start"
19 error node v6.11.4
20 error npm  v3.10.10
21 error code ELIFECYCLE
22 error wbsct@1.0.0 start: `node server.js`
22 error Exit status 1
23 error Failed at the wbsct@1.0.0 start script 'node server.js'.
23 error Make sure you have the latest version of node.js and npm installed.
23 error If you do, this is most likely a problem with the wbsct package,
23 error not with npm itself.
23 error Tell the author that this fails on your system:
23 error     node server.js
23 error You can get information on how to open an issue for this project with:
23 error     npm bugs wbsct
23 error Or if that isn't available, you can get their info via:
23 error     npm owner ls wbsct
23 error There is likely additional logging output above.
24 verbose exit [ 1, true ]
