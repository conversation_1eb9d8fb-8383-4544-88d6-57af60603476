<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
  <div fxFlex="100" style="width: 100%; margin-top: 10px;" >
    <div fxLayout="row wrap">
      <!-- <app-common-filter [siteControl]="siteControl"></app-common-filter> -->
      <!-- <mat-form-field  appearance="outline" class="set-back-color">
              
        <mat-select (click)="filterClick()"  [formControl]="siteControl" placeholder="{{ labels['commonfilterChoosesite'] }}"
            disableOptionCentering >
            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch'] " [displayMember]="'description'" [array]="siteList"
                (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
            
            <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                {{item.description}}
            </mat-option>
        </mat-select>
    </mat-form-field> -->
      <mat-form-field *ngIf="false" appearance="outline" class="set-back-color marginLeft-5 marginRight-5" >
              
        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChooseunit']}}" [formControl]="unitControl"
            disableOptionCentering >
            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="unitList"
                (filteredReturn)="filteredUnitList =$event"></mat-select-filter>
          
            <mat-option *ngFor="let item of filteredUnitList" [value]="item.externalId">
                {{item.description}}
            </mat-option>
        </mat-select>
    </mat-form-field>
   

      <mat-form-field appearance="outline" class="set-back-color marginLeft-5 marginRight-5">
        <mat-label>{{ labels['corePrinciple'] }}</mat-label>
        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosecoreprinciple'] }}"
          [formControl]="corePrinciplesControl" disableOptionCentering>
          <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'" [array]="corePrinciplesList"
            (filteredReturn)="filteredCorePrinciplesList =$event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredCorePrinciplesList" [value]="item.externalId">
            <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
            {{ labels['cards'+item.name] }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="set-back-color marginLeft-5 marginRight-5">
        <mat-label>{{ labels['commonfilterChooseunit'] }}</mat-label>
        <mat-select placeholder="{{ labels['commonfilterChooseunit'] }}" [formControl]="unitControl" disableOptionCentering multiple>
          <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="unitList"
            (filteredReturn)="filteredUnitList = $event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredUnitList" [value]="item.externalId">
            {{ item.description }}
          </mat-option>
        </mat-select>
        <button *ngIf="unitControl.value" mat-icon-button matSuffix (click)="unitControl.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="set-back-color marginLeft-5 marginRight-5">
        <mat-label>{{ labels['commonfilterChooselocation'] }}</mat-label>
        <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}" [formControl]="locationObserve" disableOptionCentering>
          <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'description'" [array]="reportingLocationList" (filteredReturn)="filteredReportingLocationList = $event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item.externalId">
            {{ item.description }}
          </mat-option>
        </mat-select>
        <button *ngIf="locationObserve.value" mat-icon-button matSuffix (click)="locationObserve.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

        <mat-form-field *ngIf="false" appearance="outline" class="set-back-color marginLeft-5 marginRight-5">
          <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChooseprocess'] }}"
              [formControl]="processControl" disableOptionCentering>
              <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                  [array]="processList"
                  (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
              <mat-option *ngFor="let item of filteredProcessList" [value]="item.externalId">

                  <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                  {{ labels['cards'+item.name] }}
              </mat-option>
          </mat-select>
      </mat-form-field>
 

        <!-- <mat-form-field class="marginLeft-5 marginRight-5" style="width: 155px;" appearance="outline">
          <mat-select [formControl]="processControl">
            <mat-option *ngFor="let item of process" value="{{item.name}}">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- <mat-form-field  class="set-back-color marginLeft-5 marginRight-5" appearance="outline">
          <mat-select [formControl]="observationControl">
            <mat-option *ngFor="let item of observation" value="{{item.name}}">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- <mat-form-field class="marginLeft-5 marginRight-5" style="width: 155px;" appearance="outline">

          <mat-select [formControl]="categoryControl">
            <mat-option *ngFor="let item of category" value="{{item.name}}">{{item.name}}</mat-option>

          </mat-select>
        </mat-form-field> -->

        <mat-form-field appearance="outline" class="dateRangePick set-back-color-action">

          <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
          <mat-date-range-input style="margin-left: 15px;" [rangePicker]="picker3" [max]="maxDate">
            <input matStartDate   [formControl]="startDateControl" placeholder="{{ labels['startDate'] }}">
            <input matEndDate [formControl]="endDateControl" placeholder="{{ labels['endDate'] }}">
          </mat-date-range-input>
          <mat-date-range-picker #picker3 (closed)="onDateRangeChange()"></mat-date-range-picker>

        </mat-form-field>
          <div style="margin-left: 5px !important;" fxLayout="row" fxLayoutAlign="center center">
           
            <span (click)="rengeFilter('Week')" [ngClass]="{'range-filter-select': rangeFlag == 'Week', 'range-filter' : rangeFlag != 'Week' }">{{labels['1w'] }}</span>
            <span (click)="rengeFilter('Month')" [ngClass]="{'range-filter-select': rangeFlag == 'Month', 'range-filter' : rangeFlag != 'Month'  }">{{labels['1m'] }}</span>
            <span (click)="rengeFilter('Quarter')" [ngClass]="{'range-filter-select': rangeFlag == 'Quarter', 'range-filter' : rangeFlag != 'Quarter' }">{{labels['3m'] }}</span>
            <span (click)="rengeFilter('Year')" [ngClass]="{'range-filter-select': rangeFlag == 'Year', 'range-filter' : rangeFlag != 'Year' }">{{labels['1y'] }}</span>
            <span (click)="rengeFilter('YTD')" [ngClass]="{'range-filter-select': rangeFlag == 'YTD', 'range-filter' : rangeFlag != 'YTD' }">{{labels['ytd'] }}</span>

            <!-- <span (click)="rengeFilter('Custom')" [ngClass]="{'range-filter-select': rangeFlag == 'Custom', 'range-filter' : rangeFlag != 'Custom' }">Custom</span>
            
            <mat-form-field *ngIf="rangeFlag == 'Custom'" fxLayout="row" style="height: 38px;    margin-left: 5px;
                      margin-top: -12px;" appearance="outline" class="dateRangePick set-back-color-action">
            
              <mat-date-range-input [rangePicker]="picker3">
                <input matStartDate [formControl]="startDateControl" placeholder="Start date">
                <input matEndDate [formControl]="endDateControl" placeholder="End date">
              </mat-date-range-input>
            
              <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
              <mat-date-range-picker #picker3></mat-date-range-picker>
            </mat-form-field> -->

          </div>
        <!-- <div class="marginLeft-5 marginRight-5">
          <common-lib-button [className]="'cst-btn '" [text]="'BUTTON.APPLY'"
          (buttonAction)="applyFilter()"></common-lib-button>
        </div> -->
        <div style="margin-left: 15px !important;" fxLayout="row" fxLayoutAlign="center center">
          <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonClear'] }}"
          (buttonAction)="clearFilter('All')"></common-lib-button>
          <common-lib-button [className]="'cst-btn'" *ngIf="showSecondButton" style="margin-left: 5px;" text ="{{ labels['buttonDownloadpdf'] }}" 
          (buttonAction)="downloadPdf()"></common-lib-button>
        </div>
      
   

      
    </div>

    <div id="dashboard" class="dashboard-section" *ngIf="widgetList.length>1 && corePrinciplesControl.value">
      <commom-label labelText="{{ labels['dashboardTitle'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>

      <div class="dashboard-card-section" style="flex-flow: wrap;">
        <div class="dashboard-cst-card" [ngStyle]="{'border': key.key == (selectedWidget && selectedWidget.key) ? '3px solid #083d5b;' : ''}"  (click)="widgetClick(key)" *ngFor="let key of widgetList">
          <div class="left-part" *ngIf="key.isEnable">
            <span class="unit">
              <span> {{key.value}}</span> <span *ngIf="key.unit" class="unit-key">{{key.unit}}</span>
            </span>
            <span *ngIf="key.key != 'Field Walk' && key.key != 'Audit' && key.key != 'Action' && key.key != 'Observation'" class="bt-text">
              <!-- {{ key.name | translate }} -->
                {{ labels['cards'+key.key] }}
            </span>
            <span *ngIf="key.key == 'Observation'" class="bt-text">
              <!-- {{ key.name | translate }} -->
                {{ labels['observations'] }}
            </span>
            <span *ngIf="key.key == 'Field Walk'" class="bt-text">
              <!-- {{ key.name | translate }} -->
                {{ labels['cardsFieldwalks'] }}
            </span>
            <span *ngIf="key.key == 'Action'" class="bt-text">
              <!-- {{ key.name | translate }} -->
                {{ labels['actions'] }}
            </span>
            <span *ngIf="key.key == 'Audit'" class="bt-text">
              <!-- {{ key.name | translate }} -->
                {{ labels['audit'] }}
            </span>
          </div>
          <div class="right-part" *ngIf="key.isEnable">
            <i>
              <svg xmlns="http://www.w3.org/2000/svg" width="64.562" height="64.481" viewBox="0 0 64.562 64.481">
                <path id="sell_FILL0_wght400_GRAD0_opsz48"
                  d="M118.56-816.968a4.771,4.771,0,0,1-3.5,1.449,4.771,4.771,0,0,1-3.5-1.449L81.369-847.156a4.223,4.223,0,0,1-1.087-1.69A6.07,6.07,0,0,1,80-850.7V-875.17a4.691,4.691,0,0,1,1.369-3.462A4.691,4.691,0,0,1,84.83-880H109.3a6.87,6.87,0,0,1,1.932.282,4.133,4.133,0,0,1,1.771,1.087L143.032-848.6a4.906,4.906,0,0,1,1.53,3.582,4.906,4.906,0,0,1-1.53,3.582Zm-3.3-3.3,24.472-24.472L109.3-875.17H84.83V-850.7ZM93.283-862.612a4.02,4.02,0,0,0,2.938-1.248,4.02,4.02,0,0,0,1.248-2.938,4.02,4.02,0,0,0-1.248-2.938,4.02,4.02,0,0,0-2.938-1.248,4.02,4.02,0,0,0-2.938,1.248A4.02,4.02,0,0,0,89.1-866.8a4.02,4.02,0,0,0,1.248,2.938A4.02,4.02,0,0,0,93.283-862.612ZM84.83-875.17Z"
                  transform="translate(-80 880)" fill="#0095c7" opacity="0.15" />
              </svg>
            </i>
          </div>
        </div>
      </div>

    
      <div *ngIf="selectedProcess && listType!='Action'" style="margin-top: 5px;" class="d-flex align-item-center mr-10">
        <span class="subheading-1 site-icon-text" *ngIf="selectedProcess.name == 'Observation'">{{
            labels['observationTypes'] }}</span>
        <span class="subheading-1 site-icon-text" *ngIf="selectedProcess.name == 'Audit'">{{
            labels['formcontrolsAudittype'] }}</span>
        <span class="subheading-1 site-icon-text"
            *ngIf="selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{
            labels['subProcess'] }}</span>

        <mat-form-field style="margin-left: 5px;" appearance="outline" class="set-back-color">
            <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
                [formControl]="subProcessControl" disableOptionCentering>
                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                    [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                    {{item.name }}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <span class="subheading-1 site-icon-text" style="margin-left: 5px;"
            *ngIf="selectedProcess.name == 'Field Walk' || selectedProcess.name == 'Observation' || selectedProcess.name == 'Hazards'">{{
            labels['chooseCategory'] }}</span>

        <mat-form-field style="margin-left: 5px;" appearance="outline" class="set-back-color"
        *ngIf="selectedProcess.name == 'Field Walk' || selectedProcess.name == 'Observation' || selectedProcess.name == 'Hazards'">
          <!-- <mat-label>{{ labels['chooseCategory'] }}</mat-label> -->
          <mat-select placeholder="{{ labels['chooseCategory'] }}" [formControl]="categoryOpt">
            <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
              [displayMember]="'name'" [array]="workOrderNumberList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter> -->
            <mat-option *ngFor="let item of filteredCategoryList" [value]="item.externalId">
              {{ item.name }}
            </mat-option>
          </mat-select>
          <button *ngIf="categoryOpt.value" mat-icon-button matSuffix (click)="categoryOpt.reset()" aria-label="Clear">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
    </div>

    <div *ngIf="(listType && listType=='Action') || (dashboardConfigDetail && subProcessControl.value)">
      <!-- <mat-card class="chart-Card-main mt-20">
        <div class="chart-filter">
          <commom-label [labelText]="'DASHBOARD.SUB_HEADING.chartTitle2'" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading'"></commom-label>

          <mat-form-field fxLayout="row" appearance="outline" class="dateRangePick set-back-color-action">
            <mat-date-range-input [rangePicker]="picker">
              <input matStartDate placeholder="Start date">
              <input matEndDate placeholder="End date">
            </mat-date-range-input>

            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
          </mat-form-field>
        </div>

        <div id="chart" class="marginTop">
          <apx-chart [series]="chartOptions.series" [chart]="chartOptions.chart" [yaxis]="chartOptions.yaxis"
            [xaxis]="chartOptions.xaxis" [labels]="chartOptions.labels" [stroke]="chartOptions.stroke"
            [title]="chartOptions.title" [dataLabels]="chartOptions.dataLabels" [fill]="chartOptions.fill"
            [tooltip]="chartOptions.tooltip" [colors]="chartOptions.colors"></apx-chart>
        </div>
      </mat-card> -->

      
      <mat-card id= "1" class="chart-Card2" *ngIf="dashboardConfigDetail && dashboardConfigDetail['ByCategory']?.isEnabled && selectedWidget && (selectedWidget.key != 'Action')" [ngClass]="{'hide-card': selectedWidget.key === 'Field Walk'}">
        <commom-label *ngIf="selectedWidget && (selectedWidget.key != 'Field Walk')" id="heading" labelText="{{labels['subheadingCharttitle4']}}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chartCategor2" *ngIf="chartOptionsCategory && chartOptionsCategory.series && selectedWidget && (selectedWidget.key != 'Field Walk')">
          <apx-chart
            [series]="chartOptionsCategory.series"
            [chart]="chartOptionsCategory.chart"
            [xaxis]="chartOptionsCategory.xaxis"
            [dataLabels]="chartOptionsCategory.dataLabels"
            [plotOptions]="chartOptionsCategory.plotOptions"
            [stroke]="chartOptionsCategory.stroke"
            [fill]="chartOptionsCategory.fill"
            [yaxis]="chartOptionsCategory.yaxis"
            [title]="chartOptionsCategory.title"
            [tooltip]="chartOptionsCategory.tooltip"
            [legend]="chartOptionsCategory.legend"></apx-chart>
        </div>
      </mat-card>
      <!-- chartOptionsUnit -->
      <!-- By Unit Chart -->
      <mat-card id= "3" class="chart-Card2" *ngIf="dashboardConfigDetail && dashboardConfigDetail['ByUnit']?.isEnabled && selectedWidget && (selectedWidget.key != 'Audit') && (selectedWidget.key != 'Action')">
        <commom-label labelText="{{labels['byUnit']}}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chartCategor2" *ngIf="chartOptionsUnit && chartOptionsUnit.series && selectedWidget && selectedWidget.key != 'Audit'">
            <apx-chart
                [series]="chartOptionsUnit.series"
                [chart]="chartOptionsUnit.chart"
                [colors]="chartOptionsUnit.colors"
                [dataLabels]="chartOptionsUnit.dataLabels"
                [plotOptions]="chartOptionsUnit.plotOptions"
                [xaxis]="chartOptionsUnit.xaxis"
                [stroke]="chartOptionsUnit.stroke"
                [fill]="chartOptionsUnit.fill"
                [yaxis]="chartOptionsUnit.yaxis"
                [title]="chartOptionsUnit.title"
                [tooltip]="chartOptionsUnit.tooltip"
                [legend]="chartOptionsUnit.legend"
            ></apx-chart>
        </div>
    </mat-card>
      <mat-card id= "2" class="chart-Card2" *ngIf="dashboardConfigDetail && dashboardConfigDetail['ByLocation']?.isEnabled && selectedWidget && (selectedWidget.key != 'Audit') && (selectedWidget.key != 'Action')">
        <commom-label labelText="{{labels['byLocation']}}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chartCategor2" *ngIf="chartOptions && chartOptions.series && selectedWidget && selectedWidget.key != 'Audit'">
            <apx-chart
                [series]="chartOptions.series"
                [chart]="chartOptions.chart"
                [colors]="chartOptions.colors"
                [dataLabels]="chartOptions.dataLabels"
                [plotOptions]="chartOptions.plotOptions"
                [xaxis]="chartOptions.xaxis"
                [stroke]="chartOptions.stroke"
                [fill]="chartOptions.fill"
                [yaxis]="chartOptions.yaxis"
                [title]="chartOptions.title"
                [tooltip]="chartOptions.tooltip"
                [legend]="chartOptions.legend"
            ></apx-chart>
        </div>
    </mat-card>

  <mat-card id= "4" class="chart-Card2" *ngIf="selectedWidget && (selectedWidget.key == 'Action')">
    <commom-label labelText="{{ labels['trends'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
    <div id="chartOptionsAction" *ngIf="chartOptionsAction && chartOptionsAction.series && selectedWidget && selectedWidget.key == 'Action'">
      <apx-chart
        [series]="chartOptionsAction.series"
        [chart]="chartOptionsAction.chart"
        [xaxis]="chartOptionsAction.xaxis"
        [stroke]="chartOptionsAction.stroke"
        [tooltip]="chartOptionsAction.tooltip"
        [dataLabels]="chartOptionsAction.dataLabels"
      ></apx-chart>
    </div>
    </mat-card>
    
      <div class="charts-container" *ngIf="dashboardConfigDetail && selectedWidget">

       
       
    
        <mat-card id= "5" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['ByDepartment']?.isEnabled && selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit' && (selectedWidget.key != 'Action'))">
          <commom-label labelText="{{ labels['byDepartment'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
          <div id="chartDept" *ngIf="chartOptionsDept && chartOptionsDept.series && selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit')">
            <apx-chart
              [series]="chartOptionsDept.series"
              [chart]="chartOptionsDept.chart"
              [dataLabels]="chartOptionsDept.dataLabels"
              [plotOptions]="chartOptionsDept.plotOptions"
              [xaxis]="chartOptionsDept.xaxis"
              [stroke]="chartOptionsDept.stroke"
              [fill]="chartOptionsDept.fill"
              [yaxis]="chartOptionsDept.yaxis"
              [title]="chartOptionsDept.title"
              [tooltip]="chartOptionsDept.tooltip"
              [legend]="chartOptionsDept.legend"
            ></apx-chart>
          </div>
        </mat-card>
    
        <mat-card id= "6" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['Sitewideparticipationlast7days']?.isEnabled && selectedWidget && (selectedWidget.key != 'Action')">
          <commom-label labelText="{{ labels['sitewideParticipationLast7Days'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
          <div id="chart" *ngIf="chartOptionsweek && chartOptionsweek.series ">
            <apx-chart
              [series]="chartOptionsweek.series"
              [chart]="chartOptionsweek.chart"
              [dataLabels]="chartOptionsweek.dataLabels"
              [plotOptions]="chartOptionsweek.plotOptions"
              [responsive]="chartOptionsweek.responsive"
              [xaxis]="chartOptionsweek.xaxis"
              [yaxis]="chartOptionsweek.yaxis"
              [legend]="chartOptionsweek.legend"
              [fill]="chartOptionsweek.fill"
            ></apx-chart>
          </div>
        </mat-card>
    
        <mat-card id= "7" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['RiskBehavioursatlast7days']?.isEnabled && selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit' && (selectedWidget.key != 'Action'))">
          <commom-label *ngIf="selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit')" labelText="{{ labels['riskBehavioursAtLast7Days'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
          <div id="chart" *ngIf="chartOptionsUnsafe && chartOptionsUnsafe.series && selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit')">
            <apx-chart
              [series]="chartOptionsUnsafe.series"
              [chart]="chartOptionsUnsafe.chart"
              [dataLabels]="chartOptionsUnsafe.dataLabels"
              [plotOptions]="chartOptionsUnsafe.plotOptions"
              [responsive]="chartOptionsUnsafe.responsive"
              [xaxis]="chartOptionsUnsafe.xaxis"
              [yaxis]="chartOptionsUnsafe.yaxis"
              [legend]="chartOptionsUnsafe.legend"
              [fill]="chartOptionsUnsafe.fill"
            ></apx-chart>
          </div>
        </mat-card>
    
        <mat-card id= "8" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['AtRiskbySection']?.isEnabled && selectedWidget&& (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit' && (selectedWidget.key != 'Action'))">
          <commom-label *ngIf="selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit')" labelText="{{ labels['atRiskBySection'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
          <div id="chartRistSection" *ngIf="chartOptionsRiskSection && chartOptionsRiskSection.series && selectedWidget && (selectedWidget.key != 'Field Walk' && selectedWidget.key != 'Audit')">
            <apx-chart
              [series]="chartOptionsRiskSection.series"
              [chart]="chartOptionsRiskSection.chart"
              [dataLabels]="chartOptionsRiskSection.dataLabels"
              [plotOptions]="chartOptionsRiskSection.plotOptions"
              [title]="chartOptionsRiskSection.title"
              [legend]="chartOptionsRiskSection.legend"
            ></apx-chart>
          </div>
        </mat-card>
    
        <mat-card id= "9" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['Trends']?.isEnabled && selectedWidget && (selectedWidget.key != 'Action')">
        <commom-label labelText="{{ labels['trends'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chart" class="marginTop">
          <common-area-chart [series]="capacityChartSeries" [plotOptions]="capacityChartOptions.plotOptions"
            [dataLabels]="capacityChartOptions.dataLabels" [chartOption]="capacityChartOptions.chart"
            [stroke]="capacityChartOptions.stroke" [xaxis]="capacityChartOptions.xaxis"
            [yaxis]="capacityChartOptions.yaxis" [fill]="capacityChartOptions.fill"
            [tooltip]="capacityChartOptions.tooltip" [legend]="capacityChartOptions.legend"></common-area-chart>
        </div>
      </mat-card>

      <mat-card id= "10" class="chart-Card" *ngIf="dashboardConfigDetail && dashboardConfigDetail['PercentageParticipation']?.isEnabled && selectedWidget && (selectedWidget.key != 'Action')">
        <commom-label labelText="{{ labels['percentageParticipation'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chartPercentage" *ngIf="chartOptionsPercentage && chartOptionsPercentage.series && selectedWidget ">
          <apx-chart
            [series]="chartOptionsPercentage.series"
            [chart]="chartOptionsPercentage.chart"
            [xaxis]="chartOptionsPercentage.xaxis"
            [dataLabels]="chartOptionsPercentage.dataLabels"
            [plotOptions]="chartOptionsPercentage.plotOptions"
            [stroke]="chartOptionsPercentage.stroke"
            [theme]="chartOptionsPercentage.theme"
            [colors]="chartOptionsPercentage.colors"
            [responsive]="chartOptionsPercentage.responsive"
            [fill]="chartOptionsPercentage.fill"
            [yaxis]="chartOptionsPercentage.yaxis"
            [title]="chartOptionsPercentage.title"
            [tooltip]="chartOptionsPercentage.tooltip"
            [legend]="chartOptionsPercentage.legend"></apx-chart>
        </div>
      </mat-card>
      
    
        
      </div>
      <mat-card id= "10" class="chart-Card2" *ngIf="dashboardConfigDetail && dashboardConfigDetail['MetricsByChecklist']?.isEnabled && selectedWidget && (selectedWidget.key != 'Action')">
        <commom-label labelText="{{ labels['metricsByChecklist'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div id="chartMetrics" *ngIf="chartOptionsMetrics && chartOptionsMetrics.series && selectedWidget ">
          <apx-chart
          [series]="chartOptionsMetrics.series"
          [chart]="chartOptionsMetrics.chart"
          [legend]="chartOptionsMetrics.legend"
          [dataLabels]="chartOptionsMetrics.dataLabels"
          [plotOptions]="chartOptionsMetrics.plotOptions"
          [xaxis]="chartOptionsMetrics.xaxis"
          [stroke]="chartOptionsMetrics.stroke"
        ></apx-chart>
        </div>
      </mat-card>
    </div>
  </div>
<!-- chartsParticipation -->
 <!-- chartsNonparticipation -->
  <!-- toasterNoprocess -->
  <!-- chartsCount -->
  <!-- chartsPeriod -->
  <!-- siteunitSite -->
  <!-- auditlistCoreprinciple -->
  <!-- commonfilterStartdate -->
  <!-- commonfilterEnddate -->
  <!-- toasterInfo -->
