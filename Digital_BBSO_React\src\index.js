import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import App from './App'
import reportWebVitals from './reportWebVitals'
import { I18nextProvider } from 'react-i18next'
import i18next from 'i18next'
import en from './assets/translation/en.json'
import de from './assets/translation/de.json'
import it from './assets/translation/it.json'
import es from './assets/translation/es.json'
import nl from './assets/translation/nl.json'
import fr from './assets/translation/fr.json'
import pt from './assets/translation/pt.json'
import ko from './assets/translation/ko.json'
import ja from './assets/translation/ja.json'
import zh from './assets/translation/zh.json'

i18next.init({
  interpolation: { escapeValue: false }, // React already does escaping
  lng: 'en', // language to use
  resources: {
    en: {
      global: en,
    },
    de: {
      global: de,
    },
    it: {
      global: it,
    },
    es: {
      global: es,
    },
    nl: {
      global: nl,
    },
    fr: {
      global: fr,
    },
    pt: {
      global: pt,
    },
    ko: {
      global: ko,
    },
    ja: {
      global: ja,
    },
    zh: {
      global: zh,
    },
  },
})

const root = ReactDOM.createRoot(document.getElementById('root'))
root.render(
  <React.StrictMode>
    <I18nextProvider i18n={i18next}>
      <App />
    </I18nextProvider>
  </React.StrictMode>
)

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
