import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { SchedulerRoutingModule } from "./scheduler-routing.module";
import { ScheduleListComponent } from "./schedule-list/schedule-list.component";
import { ScheduleTrackerComponent } from "./schedule-tracker/schedule-tracker.component";
import { CreateScheduleComponent } from "./create-schedule/create-schedule.component";
import { ScheduleDetailsComponent } from "./schedule-details/schedule-details.component";

@NgModule({
    declarations: [
        ScheduleListComponent,
        ScheduleTrackerComponent,
        CreateScheduleComponent,
        ScheduleDetailsComponent
    ],
    imports: [
        CommonModule,
        SchedulerRoutingModule,
        SharedModule,
    ],
    providers: [],
  })
  export class SchedulerModule { }