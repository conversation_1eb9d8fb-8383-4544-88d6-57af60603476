// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from "react";
import { Runtime, Inspector } from "@observablehq/runtime";
import notebook from "../assets/.innoart-table/my-table";
import { useSearchParams } from 'react-router-dom';
import { html } from "htl";
import Asset_JSON from '../assets/data/cognite_data.json';
import Popup from "reactjs-popup";
import format from 'date-fns/format'
import axios from "axios";
import { CogniteClient } from "@cognite/sdk";
import { PublicClientApplication } from "@azure/msal-browser";
import Pagination from "./pagination/Pagination";
import * as Constants from '../Constant'
import { translate, DynamicTranslationArea, TranslationContext, TranslationContextProvider } from '@celanese/celanese-sdk'
import { useTranslation } from 'react-i18next'

let main;
let limitSet = 10;
let firstPageIndex = 0;
let currentPageNumber = 1;
let listData = [];
let pageInfo = [];
let initFlag;
let allListData = [];
let colSummary = {


};
let displayedColumns = [
  "documentTitle",
  "region",
  "country",
  "location",
  "trainingCompany",
  "celaneseLevel",
  // "certificationRevision",
  "certificationDate",
  // "lastPerformedAuditDate",
  // "lastPerformedAuditType",
  "auditorName",
  "actions"

]
var paginationCursor = [];
let site;
let unit;
let search;
let token;
function AuditorManagementList() {
  const viewofSelectionRef = useRef();
  const [currentPage, setCurrentPage] = useState(1);
  const [dataCount, setDataCount] = useState(0);
  const [limit, setLimitCount] = useState(10);
  const [id, setId] = React.useState("5");
  const { locale, updateLocale } = useContext(TranslationContext)
  const [t, i18n] = useTranslation('global')
  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if (i18n) {
      i18n.changeLanguage(newValue)
      console.log('Selected Language: ', selectedLanguage)
      console.log('i18n.language: ', i18n)
    }
  }


  window.onmessage = function (e) {
    if (e.data.type && e.data.type == "AuthToken") {
      token = e.data.data;
    }
    if (e.data.action == 'Language') {
      console.log('Language', e.data)
      handleLanguageChange(e.data.LanguageCode)
      // remove prev language data
      const prevLang = localStorage.getItem('LocaleData')
      localStorage.removeItem('LocaleData')
      localStorage.removeItem('APP-OFWATranslationData' + prevLang)
      localStorage.setItem('LocaleData', e.data.LanguageCode)
      localStorage.setItem('APP-OFWATranslationData' + e.data.LanguageCode, JSON.stringify(e.data.labels))
      updateLocale(e.data.LanguageCode.toUpperCase())
      colFun()
      getData()
    }
    if (e.data.type && e.data.type == "FormConfig") {
      if (e.data.action == "Column") {
        displayedColumns = e.data.data;
        colFun();
      } else if (e.data.action == "Filter") {
        site = e.data.site;
        unit = e.data.unit;
        search = e.data.search;
        getData(e.data.site, e.data.unit, e.data.search);
      } else if (e.data.action == "Summary") {
        colSummary = e.data.data;
        colFun();
      } else if (e.data.action == "PageRows") {

        setCurrentPage(1);
        setLimitCount(parseInt(e.data.data))
        limitSet = parseInt(e.data.data);
        paginationCursor = [];
        getData(e.data.site, e.data.unit, e.data.search);


      }

    }

  };


  const [searchParams, setSearchParams] = useSearchParams();
  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value);
    setId(e.target.value)
    limitSet = e.target.value;

    console.log('limitSet', limitSet)
    filterData();
    // setLimit(parseInt(e.data.data))
    // limitSet = parseInt(e.data.data);
    // filterData();
  }
  function action1(x, i) {
    return html`<div style=" display: flex;
    flex-direction: row;align-item-center;">
    <div id="${i}" title="View" style="height:18px;margin-right:8px;cursor: pointer;" onClick=${() => dataView(i)} >
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>               
    </div>
    <div id="${i}" title="View" style="height:18px;margin-right:8px;cursor: pointer;" onClick=${() => dataView(i)} >
    <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
    </div>
      </div>
      `
  }

  function dataView(event) {
    window.parent.postMessage({ "type": "AuditorManagement", "action": "FormView", "data": listData[event] }, "*");
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if (element) {
      element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine("configuration", {
      columns: displayedColumns,
      header: {
        "documentTitle": "Document Title",
        "region": "Region",
        "country": "Country",
        "location": "Location",
        "trainingCompany": "Training Company",
        "celaneseLevel": "Celanese Level",
        "certificationRevision": "Certification Revision",
        "certificationDate": "Certification Date",
        "lastPerformedAuditDate": "Last Performed Audit Date",
        "lastPerformedAuditType": "Last Performed Audit Type",
        "auditorName": "Auditor Name",
        "actions": "Actions"

      },
      headerSummary: colSummary,
      format: {
        createdAt: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        lastUpdatedTime: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        id: x => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i)
      },

      align: {
        "documentTitle": "left",
        "region": "left",
        "country": "left",
        "location": "left",
        "trainingCompany": "left",
        "celaneseLevel": "left",
        "certificationRevision": "left",
        "certificationDate": "left",
        "lastPerformedAuditDate": "left",
        "lastPerformedAuditType": "left",
        "auditorName": "left",
        "actions": "center"
      },
      rows: 25,
      width: {
        "documentTitle": 200,
        "region": 200,
        "country": 200,
        "location": 200,
        "trainingCompany": 200,
        "celaneseLevel": 200,
        "certificationRevision": 200,
        "certificationDate": 200,
        "lastPerformedAuditDate": 200,
        "lastPerformedAuditType": 200,
        "auditorName": 200,
        "actions": 100

      },
      maxWidth: "100vw",
      layout: "auto",
    });
  }



  function pushObj(item) {
    if (!paginationCursor.find(({ id }) => id === item.id)) {
      paginationCursor.push(item);
    }
  }


  async function getData() {

    var dataSource = [
      {
        "documentTitle": "A, Appar",
        "region": "AS",
        "country": "India",
        "location": "Madurai",
        "trainingCompany": "TUV India",
        "celaneseLevel": "Lead Auditor",
        "certificationRevision": 2015,
        "certificationDate": "9/9/2017",
        "lastPerformedAuditDate": "",
        "lastPerformedAuditType": "",
        "auditorName": "A, Appar, Celanese",
        "actions": ""
      },
      {
        "documentTitle": "Abdul, Rahman",
        "region": "AS",
        "country": "Singapore",
        "location": "Singapore",
        "trainingCompany": "Bureau Veritas",
        "celaneseLevel": "Co-Auditor",
        "certificationRevision": 2015,
        "certificationDate": "7/2/2023",
        "lastPerformedAuditDate": "30-05-2024",
        "lastPerformedAuditType": "System",
        "auditorName": "Abdul, Rahman, Celanese",
        "actions": ""
      }
    ]
    listData = dataSource;
    setCurrentPage(1);
    initFlag = true
    filterData();

  }

  function filterData() {
    console.log('react filterData');
    console.log('listData', listData)

    var currentList = [];
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter(obj => {
          return JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !== -1;
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(firstPageIndex, (firstPageIndex + limitSet));
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, (firstPageIndex + limitSet));
      }

    }

    if (initFlag) {
      main.redefine("data", currentList);
      colFun();
    }
  }

  useEffect(() => {



    const runtime = new Runtime();
    main = runtime.module(notebook, name => {
      if (name === "viewof selection1") return new Inspector(viewofSelectionRef.current);
      if (name === "selection") {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage({ "type": "Assets", "action": "Select", "data": [], "selected": value }, "*");
          },
          // rejected(error) { console.error(error); }
        };
      }
    });
    setDataCount(1);
    colFun();
    getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose();
  }, []);

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit;
    const lastPageIndex = firstPageIndex + limit;
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage]);



  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
          //  assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>

    </>
  );
}


export default AuditorManagementList;