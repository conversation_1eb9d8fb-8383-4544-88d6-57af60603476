@use "./functions" as func;
// // X-Small devices (portrait phones, less than 576px)
// // No media query for `xs` since this is the default in Bootstrap
// @media (max-width: 576px) {
// }

// // Small devices (landscape phones, 576px and up)
// @media (min-width: 576px) {
// }

// // Medium devices (tablets, 768px and up)
// @media (min-width: 768px) {
// }

// // Large devices (desktops, 992px and up)
// @media (min-width: 992px) {
// }

// // X-Large devices (large desktops, 1200px and up)
// @media (min-width: 1200px) {
// }

// // XX-Large devices (larger desktops, 1400px and up)
// @media (min-width: 1400px) {
// }

@mixin responsive($breakpoint, $scope: "") {
  @if $breakpoint == xs {
    // X-Small devices (portrait phones, less than 576px) (xs)

    @media only screen and (max-width: 576px) {
      @content;
    }
  } @else if $breakpoint == sm {
    // Small devices (landscape phones, 576px and up)

    @if $scope == "sm-md" {
      @media only screen and (min-width: 576px) and (max-width: 768px) {
        @content;
      }
    } @else {
      @media only screen and (min-width: 576px) {
        @content;
      }
    }
  } @else if $breakpoint == md {
    // Medium devices (tablets, 768px and up)
    @media only screen and (min-width: 768px) {
      @content;
    }
  } @else if $breakpoint == lg {
    // Large devices (desktops, 992px and up)
    @media only screen and (min-width: 992px) {
      @content;
    }
  } @else if $breakpoint == xlg {
    // X-Large devices (large desktops, 1200px and up)
    @media only screen and (min-width: 1200px) {
      @content;
    }
  } @else if $breakpoint == xxlg {
    // XX-Large devices (larger desktops, 1400px and up)
    @media only screen and (min-width: 1400px) {
      @content;
    }
  }
}

@mixin mobile-menu {
  position: fixed;
  top: -100%;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: func.theme-colors("menu-bg");
  z-index: 1;
  transition: 500ms;

  &.show {
    display: block;
    text-align: center;
    top: 64px;
  }

  & li {
    padding: 2rem;

    a {
      color: #fff;

      &.active {
        color: #000;
      }
    }
  }
}
