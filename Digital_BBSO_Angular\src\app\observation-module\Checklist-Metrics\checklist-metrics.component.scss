.custom-header {
    background-color: #083D5B;
    color:white;
    font-weight: bold;
    //padding-right: 20px;
    
  }


.form-container {
  display: flex;
  flex-direction: row;
  gap: 16px; 
 // padding: 16px; 
  align-items: flex-start;
  padding-top: 10px;
  margin-bottom: -20px;
  
}


.no-results {
  text-align: center;
  font-weight: bold;
  padding: 20px;
  margin-top: 15px;
  color: #666;
}







/* Style for the table cells to align text to the right and set fixed width */
.mat-cell.align-right,
.mat-header-cell.align-right {
  text-align: right;
  width: 150px; /* Adjust width as needed */
}

/* Optional: Add some padding for better readability */
.mat-cell,
.mat-header-cell {
  padding: 8px;
}
