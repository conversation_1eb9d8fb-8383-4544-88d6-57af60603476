{"openapi": "3.0.1", "info": {"version": "1.0.0", "title": "Celanese", "description": ""}, "servers": [{"url": "https://app-dplantbbsonodeservice-d-ussc-01.azurewebsites.net/", "description": "Business Logic Server"}], "tags": [{"name": "Business"}], "paths": {"/api/service/createInstanceByProperties": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/instance"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/instance"}}}}}}, "/api/service/createEdge": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/space"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/space"}}}}}}, "/api/service/listCommonRefEnum": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/listCommonRefEnum"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/listCommonRefEnum"}}}}}}, "/api/service/listGeoRegion": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listCountry": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listReportingLocation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listBusinessLineByUnit": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listReportingSiteByCountry": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listReportingSiteFunctionalLocation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listReportingSiteCursor": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/endCursor"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/endCursor"}}}}}}, "/api/service/listReportingUnitFunctionalLocation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listReportingUnitCursor": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/endCursor"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/endCursor"}}}}}}, "/api/service/listReportingSiteById": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/byId"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/byId"}}}}}}, "/api/service/listReportingUnitById": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/byId"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/byId"}}}}}}, "/api/service/listEquipmentByFunctionalLocation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listProcess": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listProcessBySite": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listProcessConfigurationByProcess": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/byId"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/byId"}}}}}}, "/api/service/listSubCategory": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listQuestionBankByCategory": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listQuestionList": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listObservation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listObservationById": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/byId"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/byId"}}}}}}, "/api/service/listChecklistByCategory": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/byId"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/byId"}}}}}}, "/api/service/listFieldWalk": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listVendor": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listSchedule": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/scheduleSendToVedor": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listChecklist": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listAudit": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/aggregateObservation": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/aggregateAction": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/aggregateChecklist": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/createInstance": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/instance"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/instance"}}}}}}, "/api/service/listUser": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listAllUser": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listUserAzureAttribute": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listUserAzureAttributeAllData": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listUserComplement": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listScoreCard": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listApplication": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}, "/api/service/listAction": {"post": {"tags": ["Business"], "description": "", "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/body"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/body"}}}}}}}, "components": {"schemas": {"instance": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "siteCode": {"type": "string", "example": "string"}, "unitCode": {"type": "string", "example": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"property-identifier1": {"type": "string", "example": "string"}, "property-identifier2": {"type": "string", "example": "string"}}}}}, "xml": {"name": "instance"}}, "body": {"type": "object", "properties": {}, "xml": {"name": "body"}}, "byId": {"type": "object", "properties": {"externalId": {"type": "string", "example": "string"}}, "xml": {"name": "byId"}}, "listCommonRefEnum": {"type": "object", "properties": {"name": {"type": "string", "example": "string"}}, "xml": {"name": "listCommonRefEnum"}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "name": "Authorization"}}}}