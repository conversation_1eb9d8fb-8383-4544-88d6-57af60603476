import { ComponentFixture, TestBed, async, fakeAsync } from '@angular/core/testing';

import { RuleListComponent } from './rule-list.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AppModule } from 'src/app/app.module';
import { CommonService } from 'src/app/services/common.service';



fdescribe('RuleListComponent', () => {
  let component: RuleListComponent;
  let fixture: ComponentFixture<RuleListComponent>;


  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule,AppModule],
      providers:[CommonService]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RuleListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should match span text', fakeAsync (() => {
    const span = fixture.debugElement.nativeElement.querySelector('#rule_list_title');
    fixture.detectChanges();
    expect(component.ruleLisTitle).toBe(span.textContent);
  }));

  // it('should call goPage function', async(() => {
  //   spyOn(component, 'goHome');
  //   fixture.detectChanges();
  //   let button = fixture.debugElement.queryAll(By.css('button')).nativeElement; // modify here
  //   button.click();
  //   fixture.detectChanges();
  //   expect(component.goHome).toHaveBeenCalled();
  // }));
});
function spyOn(component: RuleListComponent, arg1: string) {
  throw new Error('Function not implemented.');
}

