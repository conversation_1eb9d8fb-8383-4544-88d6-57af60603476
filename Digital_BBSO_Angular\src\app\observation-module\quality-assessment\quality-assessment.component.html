<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-sectionaudit">
  <!-- <form > -->
  <div fxFlex="100" [formGroup]="observedForm">
    <div class="quality-section">
      <!-- <div class="action-section-unit-section">
        <commom-label [labelText]="'OBSERVATION.QUALITY_ASSESSMENT.TITLE'" [tagName]="'h4'"
          [cstClassName]="'heading unit-heading'"></commom-label>
      </div> -->

      <div fxLayout="row" fxLayoutAlign="start center" class="listHeadBox marginTop">
        <div fxFlex="35" class="paddingLeft-10" fxLayoutAlign="start center">
          <span class="subheading-1">
            {{ labels['formcontrolsBehaviourchecklist'] }}
          </span>
        </div>
        <div fxFlex="65" class="paddingLeft-10 leftBorder" fxLayoutAlign="start center">
          <span class="subheading-1">
            {{ labels['treeheaderSupplierselfcomments'] }}
          </span>
        </div>
      </div>

      <div class="quality-mat-tree">
        <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
          <!-- This is the tree node template for leaf nodes -->
          <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding matTreeNodePaddingIndent="0">
            <!-- use a disabled button to provide padding for tree leaf -->
            <div [formGroup]="node.formGroup" fxLayout="row" style="width: 100%;"
                fxLayoutAlign="start center" [ngClass]="{'behaviour-list-tree': node.bold == false }">

                <div fxFlex="35" style="width: 445px;" fxLayout="row" fxLayoutAlign="start center">
                    <button mat-icon-button disabled></button>

                    <span fxFlex="20" [ngClass]="node.bold? 'semi-bold' : 'caption'">
                        {{node.name}}
                    </span>
                    <span *ngIf="node.question" class="caption">
                        {{node.question}}
                    </span>
                </div>
                <div fxFlex="65" *ngIf="node.bold == false" fxLayoutAlign="center center">
                    <mat-form-field  *ngIf="node.type=='Question'" appearance="outline" style="width: 100%;" class="behav-tree-select">
                        <textarea formControlName="comment" matInput cdkTextareaAutosize
                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                            cdkAutosizeMaxRows="5"></textarea>
                    </mat-form-field>
                </div>

            </div>

        </mat-tree-node>
        <!-- This is the tree node template for expandable nodes -->
        <mat-tree-node *matTreeNodeDef="let node;when: hasChild" matTreeNodePadding
            matTreeNodePaddingIndent="0">
            <!-- [formGroup]="node.formGroup" -->
            <div  fxLayout="row" style="width: 100%;"
                fxLayoutAlign="start center" [ngClass]="{'behaviour-list-tree': node.bold == false }">

                <div fxFlex="35" style="width: 445px;" fxLayout="row" fxLayoutAlign="start center">
                    <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                      
                        <div *ngIf="treeControl.isExpanded(node)">
                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                viewBox="0 0 24 24" style="enable-background:new 0 0 512 512"
                                xml:space="preserve" class="">
                                <g>
                                    <path
                                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5.707 9.707-5 5a1 1 0 0 1-1.414 0l-5-5a1 1 0 0 1 1.414-1.414L12 13.586l4.293-4.293a1 1 0 0 1 1.414 1.414z"
                                        data-name="Layer 2" fill="#1A2254" data-original="#000000" class="">
                                    </path>
                                </g>
                            </svg>
                        </div>
                        <div *ngIf="!treeControl.isExpanded(node)">
                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                viewBox="0 0 512 512" style="enable-background:new 0 0 512 512"
                                xml:space="preserve" class="">
                                <g>
                                    <path
                                        d="M256 0C114.837 0 0 114.837 0 256s114.837 256 256 256 256-114.837 256-256S397.163 0 256 0zm79.083 271.083L228.416 377.749A21.275 21.275 0 0 1 213.333 384a21.277 21.277 0 0 1-15.083-6.251c-8.341-8.341-8.341-21.824 0-30.165L289.835 256l-91.584-91.584c-8.341-8.341-8.341-21.824 0-30.165s21.824-8.341 30.165 0l106.667 106.667c8.341 8.341 8.341 21.823 0 30.165z"
                                        fill="#1A2254" data-original="#000000" class=""></path>
                                </g>
                            </svg>
                        </div>

                    </button>

                    <span [ngClass]="node.bold == true ? 'semi-bold' : 'caption'">
                        {{node.name}}
                    </span>
                </div>
                <div fxFlex="65" *ngIf="node.bold == false" fxLayoutAlign="center center">
                  <!-- <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">
                      <textarea formControlName="comment" matInput cdkTextareaAutosize
                          #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                          cdkAutosizeMaxRows="5"></textarea>
                  </mat-form-field> -->
              </div>

            </div>


        </mat-tree-node>
      </mat-tree>
      </div>

      <div class="btn-section marginTop-20">
        <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonBack'] }} "
        (buttonAction)="onCancel()"></common-lib-button>
        <common-lib-button [className]="'cst-btn'" text="{{ labels['next'] }}"
          (click)="nextClick()"></common-lib-button>
      </div>

    </div>

  </div>
<!-- </form> -->
</div>
