.toggle-switch {
    margin-left: 16px;
}
/* General Pop<PERSON> */
.popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-box {
  background: white;
  width: 77%;
  height: 50%;
  padding: 20px;
  border-radius: 8px;
  // width: 500px;
  // max-width: 90%;
  overflow-y: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.popup-box h3 {
  margin: 0 0 20px;
  text-align: left;
  margin-bottom: 10px;
}

.popup-actions {
  display: flex;
  justify-content: end;
  margin-top: 20px;
}

.popup-actions button {
  padding: 8px 16px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.popup-actions button:hover {
  background-color: #0056b3;
}

/* Checklist Styling */
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-item {
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
}

.item-value {
  flex: 1; /* Allow the text to take up available space */
  text-align: left;
  margin-right: 10px;
  word-wrap: break-word;
}

.item-actions button {
  margin-left: 10px;
  padding: 6px 12px;
  border: none;
  background-color: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.item-actions button:hover {
  background-color: #218838;
}

/* Edit/New Item Popup */
label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
}

.inputBox[type="text"] {
  width: calc(100% - 20px);
  padding: 8px;
  margin-top: 5px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
