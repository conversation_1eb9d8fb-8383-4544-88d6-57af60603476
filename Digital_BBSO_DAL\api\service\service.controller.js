'use strict';
var https = require('https');
var _ = require('lodash');
const request = require('request');
const { json } = require("express");
var querystring = require('querystring');
var fs = require('fs');
var mime = require('mime');
const superagent = require('superagent');
var async = require('async');
var fetch = require('node-fetch');
const { v1: uuidv1 } = require('uuid');
const { CogniteClient } = require('@cognite/sdk');
const constant = require('../constant/constant');
var sequential = require("sequential-ids");

var generator = new sequential.Generator({});
exports.typeConfig = function (req, res) {
  return res.status(200).json(constant);
}
exports.createSpace = async function (req, res) {
  var postObj = req.body;
  const client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: process.env.project,
    getToken: () => req.headers.authorization.split(' ')[1]
  });
  var path = "/api/v1/projects/" + process.env.project + "/models/spaces";
  var responseData = await client.post(path, { data: postObj });
  // console.info('\x1b[36m%s\x1b[0m', "Space")
  // console.info('\x1b[36m%s\x1b[0m', "Request")
  // console.log(process.env.AzureAudience + path)
  // console.info('\x1b[36m%s\x1b[0m', "JSON")
  // console.log({ data: postObj })
  // console.info('\x1b[36m%s\x1b[0m', "Response")
  // console.log(responseData)
  return res.status(200).json(responseData.data);
}
exports.createInstanceByProperties = function (req, res) {
  // console.log("CDF start", new Date())
  // console.info('\x1b[36m%s\x1b[0m', "Instance By Prop")
  console.log(req.authInfo);
  var unique_name = req.authInfo.unique_name;
  var postObj = req.body;
  var tokenVal = req.headers.authorization.split(' ')[1];
  var allInstance = {
    "items": [],
    "autoCreateStartNodes": false,
    "autoCreateEndNodes": false,
    "skipOnVersionConflict": false,
    "replace": false
  };
  async.eachSeries(postObj.items, function (eData2, outCb2) {
    var myExternalId = "";
    console.log(eData2)
    if (eData2.externalId && eData2.externalId.length > 0) {
      myExternalId = eData2.externalId;
      eData2["modifiedBy"] = unique_name;
      delete eData2.createdBy;
      delete eData2.externalId;
    } else {
      if(postObj.type != constant.typeLabelTranslation) {
        eData2["createdBy"] = unique_name;
        eData2["modifiedBy"] = unique_name;
        myExternalId = "";
      }
    }
    if(postObj.type == constant.typeAction|| postObj.type == constant.typeActionItemEvent || postObj.type == constant.typeLabelTranslation){
      delete eData2.createdBy;
      delete eData2.modifiedBy;
    }
    var appCode = postObj.type ? constant.dataTypeList[postObj.type] : constant.AppCode;
    var dataSpace = appCode + "-" + postObj.siteCode + "-" + postObj.unitCode + "-" + constant.dataSpaceCode;
    var myInstance = {
      "instanceType": "node",
      "space": dataSpace,
      "externalId": myExternalId,
      "sources": [
        {
          "source": {
            "type": "container",
            "space":  constant.typeActionItemEvent==postObj.type ? constant.ActionSpace :constant.dataTypeList[appCode],
            "externalId": postObj.type
          },
          "properties": eData2
        }
      ]
    }
    allInstance["items"].push(myInstance);
    outCb2(null);
  }, async function (err) {
    console.log("CDF formation", new Date())
    console.log(JSON.stringify(allInstance))
    createInstance(tokenVal, allInstance, function (responseData) {
      console.log("CDF end", new Date())
      return res.status(200).json(responseData);
    });
  })
}
async function postGraphql(queryObj, callback) {
  const client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: queryObj.project,
    getToken: () => queryObj.authorization.split(' ')[1]
  });
  try {
    var graphURL = process.env.AzureAudience + "/api/v1/projects/" + queryObj.project + "/userapis/spaces/" + queryObj.space + "/datamodels/" + queryObj.datamodel + "/versions/" + queryObj.version + "/graphql"
    var responseData = {};
    await client.post("/api/v1/projects/" + queryObj.project + "/userapis/spaces/" + queryObj.space + "/datamodels/" + queryObj.datamodel + "/versions/" + queryObj.version + "/graphql",
      { data: queryObj.reqBody }).then(
        // do something with the result
        result => {
          // console.log("result",result)
          responseData = result
        }
      )
      .catch(
        // handle error
        error => {
          console.log("catch error", error);
          responseData = error;
        }
      )
      .finally(
        // do something when it has been executed successfully
        // or raised an error.
        // this work can be memory cleaning or similar task
        () => {
          callback(_.has(responseData["data"], 'error'), responseData)
        }
      );
    // console.info('\x1b[36m%s\x1b[0m', "Graph")
    // console.info('\x1b[36m%s\x1b[0m', "Request API")
    // console.log(graphURL)
    // console.info('\x1b[36m%s\x1b[0m', "JSON")
    // console.log({ data: queryObj.reqBody })
    // console.info('\x1b[36m%s\x1b[0m', "Response")
    // // console.log(responseData)
    // console.log("callback")


  } catch (err) {
    console.log(err)
    callback(true, err);
  }
  // console.log(graphURL)
  // fetch(graphURL, {
  //     method: 'POST',
  //     body: JSON.stringify(obj),
  //     headers: {
  //         'accept': 'application/json',
  //         'authorization': queryObj.authorization,
  //         'Content-Type': 'application/json'
  //     }
  // })
  //     .then(res => res.json())
  //     .then(json => {
  //         callback(null, json)
  //     })
  //     .catch(err => {
  //         callback(err, null)
  //     })
}
exports.postGraphql = postGraphql;
async function postClientClassic(queryObj, callback) {
  const client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: queryObj.project,
    getToken: () => queryObj.authorization.split(' ')[1]
  });
  try {
    console.log(`/api/v1/projects/${queryObj.project}/models/instances/aggregate`)
 var responseData = {};
    await client.post(`/api/v1/projects/${queryObj.project}/models/instances/aggregate`, { data: queryObj.reqBody} )
    .then(
        result => {
          // console.log("result")
          responseData = result
        }
      )
      .catch(
        // handle error
        error => {
          console.log("catch error", error);
          responseData = error;
        }
      )
      .finally(
        () => {
          callback(_.has(responseData["data"], 'error'), responseData)
        }
      );
  } catch (err) {
    console.log(err)
    callback(true, err);
  }

}
exports.postClientClassic = postClientClassic;

exports.graphql = function (req, res) {
  // var queryObj = req.body;
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": req.body
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.classic = async function (req, res) {
  // var queryObj = req.query;
  var postObj = req.body.obj;
  var path = req.body.path;
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion
  };

  queryObj["authorization"] = req.headers.authorization;
  const client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: process.env.project,
    getToken: () => queryObj.authorization.split(' ')[1]
  });
  var responseData = await client.post(path, { data: postObj });
  console.info('\x1b[36m%s\x1b[0m', "Classic")
  // console.info('\x1b[36m%s\x1b[0m', "Request")
  // console.log(process.env.AzureAudience + path)
  // console.info('\x1b[36m%s\x1b[0m', "JSON")
  // console.log({ data: postObj })
  // console.info('\x1b[36m%s\x1b[0m', "Response")
  // console.log(responseData)
  return res.status(200).json(responseData.data);
}
async function getLastId(tokenVal, seqKey, callback) {

  // console.log('seqKey----->',seqKey)
  console.log('call back enter', new Date());

  var client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: process.env.project,
    getToken: () => tokenVal
  });

  var space = constant.CMNDataModelSpace;
  var dataModel = constant.CMNDataModelExternalId;
  var version = constant.CMNDataModelVersion;
  var tableName = constant.typeTypeSequence;
  console.log('entitsy', seqKey)
  var mainbody = {
    "query": `{
         list${tableName}(filter:{entityType:{eq:"${seqKey}"}}){
           items {
            space
            entityType
            lastSequence
            code
            externalId
            lastUpdatedTime
           } 
         }
       }`
  }
  console.log(`{
    list${tableName}(filter:{entityType:{eq:"${seqKey}"}}){
      items {
       space
       entityType
       lastSequence
       code
       externalId
       lastUpdatedTime
      } 
    }
  }`)
  console.log("/api/v1/projects/" + process.env.project + "/userapis/spaces/" + space + "/datamodels/" + dataModel + "/versions/" + version + "/graphql")
  var responseData = await client.post("/api/v1/projects/" + process.env.project + "/userapis/spaces/" + space + "/datamodels/" + dataModel + "/versions/" + version + "/graphql",
    { data: mainbody });

  var result = responseData.data;
  console.log(result)
  if (result.data && result.data["list" + tableName] && result.data["list" + tableName].items.length > 0) {
    var tableExternalId = result.data["list" + tableName].items[0].externalId
    var lastSequenceId = result.data["list" + tableName].items[0].lastSequence;
    console.log('lastSequenceId --->', lastSequenceId)
    var sCode = result.data["list" + tableName].items[0].code
    var lastDate = new Date(result.data["list" + tableName].items[0].lastUpdatedTime);
    var currentDate = new Date();

    if (lastDate.getFullYear() == currentDate.getFullYear() && lastDate.getMonth() == currentDate.getMonth() && lastDate.getDate() == currentDate.getDate()) {
      console.log('if true')
      generator.add(seqKey, {
        digits: 4, letters: 1,
        restore: lastSequenceId
      });
      generator.start();
      callback({ sCode: sCode, tableExternalId: tableExternalId });

    } else {
      console.log('if else')
      generator.add(seqKey, {
        digits: 4, letters: 1,
        restore: "A - 0000"
      });
      generator.start();
      callback({ sCode: sCode, tableExternalId: tableExternalId });
    }
  }
}
exports.getLastId = getLastId;
exports.instance = async function (req, res) {
  var tokenVal = req.headers.authorization.split(' ')[1];
  createInstance(tokenVal, req.body, function (responseData) {
    return res.status(200).json(responseData);
  });
}
exports.createEdge = async function (req, res) {
  var postObj = {
    "items": req.body,
    "autoCreateDirectRelations": true,
    "autoCreateStartNodes": false,
    "autoCreateEndNodes": false,
    "skipOnVersionConflict": false,
    "replace": false
  }
  var responseData = [];
  try {
    var tokenVal = req.headers.authorization.split(' ')[1];
    var path = "/api/v1/projects/" + process.env.project + "/models/instances";
    const client = new CogniteClient({
      appId: process.env.AppID,
      baseUrl: process.env.AzureAudience,
      project: process.env.project,
      getToken: () => tokenVal
    });
    await client.post(path,
      { data: postObj }).then(
        result => {
          console.log("result")
          responseData = result
        }
      )
      .catch(
        error => {
          console.log("catch error", error);
          responseData = error;
        }
      )
      .finally(
        () => {
          return res.status(200).json(responseData);
        }
      );
  } catch (err) {
    console.log(err)
    return res.status(200).json(responseData);
  }
}
exports.createInstance = createInstance;
function createInstance(tokenVal, postObj, callback) {
  console.info('\x1b[36m%s\x1b[0m', "createInstance")
  var entityNameKey = postObj.items[0].sources[0].source.externalId;
  var startId;
  getLastId(tokenVal, entityNameKey, function (sCode) {
    startId = sCode.sCode
    console.log('startId', startId)
    var sid;
    async.eachSeries(postObj.items, function (eData2, outCb2) {
      var oldExId = eData2.externalId
      if (oldExId == undefined || oldExId == "") {
        sid = generator.generate(entityNameKey)
        const date = new Date();
        const currentYear = (date.getFullYear().toString()).substr(2)
        const currentMonth = ("0" + (date.getMonth() + 1)).slice(-2);
        const currentDay = ("0" + date.getDate()).slice(-2);
        var exId = startId + "-" + currentMonth + "" + currentDay + "" + currentYear + "-" + (sid.replace(/\s/g, '')).split('-').join('');
        eData2.externalId = exId
        console.log('exId', exId)
        outCb2(null);

      } else {
        outCb2(null);
      }

    }, async function (err) {
      var path = "/api/v1/projects/" + process.env.project + "/models/instances";
      console.log("CDF instance start", new Date())
      const client = new CogniteClient({
        appId: process.env.AppID,
        baseUrl: process.env.AzureAudience,
        project: process.env.project,
        getToken: () => tokenVal
      });
      console.log(JSON.stringify(postObj))
      var responseData = await client.post(path, { data: postObj });
      console.log('sid================>', sid)
      if (sid) {
        var objTypeSeq = {
          "instanceType": "node",
          "space": constant.CMNDataSpace,
          "externalId": sCode.tableExternalId,//"BA_"+(new Date()).getTime()+ind+indv2, //"WRKO-"+(new Date()).getTime()
          "sources": [
            {
              "source": {
                "type": "container",
                "space": constant.CMNDataModelSpace,
                "externalId": constant.typeTypeSequence
              },
              "properties": {
                "lastSequence": sid
              }
            }
          ]
        }
        var postObjTypeSeq = {
          "items": [objTypeSeq],
          "autoCreateStartNodes": false,
          "autoCreateEndNodes": false,
          "skipOnVersionConflict": false,
          "replace": false
        }
        var createUpData = await client.post("/api/v1/projects/" + process.env.project + "/models/instances",
          { data: postObjTypeSeq });

      }

      // console.info('\x1b[36m%s\x1b[0m', "Instance")
      // console.info('\x1b[36m%s\x1b[0m', "Request")
      // console.log(process.env.AzureAudience + path)
      // console.info('\x1b[36m%s\x1b[0m', "JSON")
      // console.log({ data: postObj })
      // console.info('\x1b[36m%s\x1b[0m', "Response")
      // console.log(responseData)
      // console.log('CDF instance API end', new Date());
      callback(responseData.data)
      // return res.status(200).json(responseData.data);

    })
  })
}
exports.fetchAndDelete = function (req, res) {
  var graphURL = process.env.AzureAudience + "/api/v1/projects/" + req.query.project + "/userapis/spaces/" + req.query.space + "/datamodels/" + req.query.datamodel + "/versions/" + req.query.version + "/graphql"
  var instanceURL = process.env.AzureAudience + "/api/v1/projects/" + req.query.project + "/models/instances/delete";
  var table = req.query.table ? req.query.table : "";
  var myFetchQry = {
    "query": `{
            list${table}(first:1000){
            items{
              externalId
            }
          }
        }`
  }
  fetch(graphURL, {
    method: 'POST',
    body: JSON.stringify(myFetchQry),
    headers: {
      'accept': 'application/json',
      'authorization': req.headers.authorization,
      'Content-Type': 'application/json'
    }
  })
    .then(res2 => res2.json())
    .then(json => {
      console.log("json")
      console.log(json)
      var myData = json.data["list" + table].items;
      console.log("graphURL : OK");
      var deleteItems = {
        "items": []
      }
      async.eachSeries(myData, function (eData2, outCb2) {
        deleteItems["items"].push({
          "instanceType": "node",
          "externalId": eData2.externalId,
          "space": "SDM_AppData_2"
        })
        outCb2(null)
      }, function (err) {
        console.log("instanceURL : Start", deleteItems["items"].length);
        fetch(instanceURL, {
          method: 'POST',
          body: JSON.stringify(deleteItems),
          headers: {
            'accept': 'application/json',
            'authorization': req.headers.authorization,
            'Content-Type': 'application/json'
          }
        })
          .then(res2 => res2.json())
          .then(json => {
            res.status(200).send({
              code: 200,
              status: "success",
              data: [],
              message: "Successfully"
            })
          })
      })
    })
}
exports.listCommonRefEnum = function (req, res) {

  var nameQ = "";
  if (req.body.name) {
    nameQ = `{name: {eq:"${req.body.name}"}}`;
  }
  var dataSetTypeQ = "";
  if (req.body.dataSetType) {
    nameQ = `{dataSetType: {eq: "${req.body.dataSetType}"}}`;
  }
  var listCommonRefEnum = {
    "query": `{
            list${constant.typeCommonRefEnum}(filter: {and:[
        ${dataSetTypeQ} ${nameQ}
      ]}) {
              items {
              externalId
                space
                value
                name
                description
              }
            }
          }`
  }
  console.log("listCommonRefEnum", listCommonRefEnum );
  var queryObj = {
    "project": process.env.project,
    "space": constant.CMNDataModelSpace,
    "datamodel": constant.CMNDataModelExternalId,
    "version": constant.CMNDataModelVersion,
    "reqBody": listCommonRefEnum
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });


}
exports.listReportingSite = function (req, res) {
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": constant.listReportingSite
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingSiteFunctionalLocation = function (req, res) {
  var reqObj = {
    "query": `
          {
            list${constant.typeReportingSite}(first:1000, filter:{and:[ {space:{eq:"${constant.CoreDataSpace}"}} ]}) {
              items {
                space
                externalId
                name
                description
                siteCode
                functionalLocations {
                  items {
                    externalId
                    name
                    space
                  }
                }
                country{
                  externalId
                  space
                  parent{
                    externalId
                    name
                    space
                    description
                  }
                }
                reportingLocations(first:1000){
                  items{
                    space
                    description
                    externalId
                    name
                    reportingUnit{
                      externalId
                      space
                      name
                      description
                      
                    }
                  }
              }
                reportingUnits(first:1000){
                  items{
                    externalId
                    name
                    description
                    space
                    isActive
                  }
                }
              }
              pageInfo {
                hasNextPage
                endCursor
              }
            }
          }`,
    "variables": {}
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingSiteCursor = function (req, res) {
  var pageInfo = req.body;
  var reqQuery = {
    "query": `{
                list${constant.typeReportingSite}(first:1000, filter:{and:[{space:{eq:"${constant.CoreDataSpace}"}} ]},after:"${pageInfo.endCursor}") {
                  items {
                    externalId
                    name
                    space
                    siteCode
                    functionalLocations {
                      items {
                        externalId
                        name
                        space
                      }
                    }
                    country{
                        externalId
                        name
                        space
                        parent{
                          externalId
                          name
                          space
                          description
                        }
                    }
                  }
                  pageInfo {
                    hasNextPage
                    endCursor
                  }
                }
              }`
  };
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingUnitFunctionalLocation = function (req, res) {
  // var siteId = req.body.externalId;
  var reqBody = req.body;
  var siteE = "";
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    _.each(reqBody.sites, function (eData) {
      siteE = siteE + `{externalId: {eq: "${eData}"}},`
    })
    // {externalId:{eq:"${externalId}"}}
    siteQ = `or: [${siteE}]`;
  }


  var reqQuery = {
    "query": `{
                list${constant.typeReportingSite}(filter: {${siteQ}}) {
                  items {
                    space
                    externalId
                    description
                    reportingLocations(first:1000){
                      items{
                        space
                        description
                        externalId
                        name
                      }
                  }
                    reportingUnits(first:1000) {
                      items {
                        externalId
                        name
                        space
                        description
                        refersTo {
                          items {
                            space
                            externalId
                            name
                          }
                        }
                        businessSegment{
                            externalId
                            space
                            name
                            description
                        }
                       # reportingLocations{
                        #    items{
                          #     space
                          #     externalId
                          #     description
                          #    }
                          #  }
                      }
                      pageInfo {
                        hasNextPage
                        endCursor
                      }
                    }
                  }
                }
              }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingUnitCursor = function (req, res) {
  var siteId = req.body.externalId;
  var reqQuery = {
    "query": `{
                list${constant.typeReportingSite}(filter: {externalId: {eq: "${siteId}"}}) {
                  items {
                    reportingLocations(first:1000){
                      items{
                        space
                        description
                        externalId
                        name
                      }
                  }
                    reportingUnits(first:1000,after: ${pageInfo.endCursor}) {
                      items {
                        externalId
                        name
                        description
                        space
                        refersTo {
                          items {
                            externalId
                            name
                            space
                          }
                        }
                        businessSegment{
                            externalId
                            space
                            name
                            description
                        }
                        # reportingLocations{
                          #    items{
                            #     space
                            #     externalId
                            #    description
                              #  }
                              # }
                      }
                      pageInfo {
                        hasNextPage
                        endCursor
                      }
                    }
                  }
                }
              }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingSiteById = function (req, res) {
  var siteId = req.body.externalId;
  var reqQuery = {
    "query": `
            {
              list${constant.typeReportingSite}(filter: {externalId: {eq: "${siteId}"}}) {
                items {
                  name
                  externalId
                  siteCode
                  space
                  description
                  functionalLocations {
                    items {
                      externalId
                      name
                      space
                    }
                  }
                }
              }
            }`,
    "variables": {}
  };
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingUnitById = function (req, res) {
  var unitId = req.body.externalId;
  var reqQuery = {
    "query": `
            {
              list${constant.typeReportingUnit}(filter: {externalId: {eq: "${unitId}"}}) {
                items {
                  name
                  space
                  externalId
                  description
                  refersTo {
                    items {
                      externalId
                      name
                      space
                    }
                  }
                }
              }
            }`,
    "variables": {}
  };
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listGeoRegion = function (req, res) {
  var reqBody = req.body;
  var reqQuery = {
    "query": `{
        list${constant.typeGeoRegion}(first:1000) {
          items{
            name
            space
            description
            externalId
          }
        }
      }`
  };
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  console.log(reqQuery)
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listCountry = function (req, res) {
  var reqBody = req.body;
  var regionE = "";
  var regionQ = "";
  if (reqBody.regions && reqBody.regions.length > 0) {
    _.each(reqBody.regions, function (eData) {
      regionE = regionE + `{externalId: {prefix: "${eData}"}},`
    })
    // {externalId:{eq:"${externalId}"}}
    regionQ = `parent:{or: [${regionE}]}`;
  }
  var reqQuery = {
    "query": `{
            list${constant.typeCountry}(first:1000,filter:{and:[{createdTime:{isNull:false}}  ${regionQ}]}){
                items{
                  name
                  space
                  externalId
                  parent {
                    externalId
                    description
                    space
                  }
                }
              }
      }
  `
  }

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listBusinessLineByUnit = function (req, res) {
  var reqBody = req.body;
  var unitE = "";
  var unitQ = "";
  if (reqBody.units && reqBody.units.length > 0) {
    _.each(reqBody.units, function (eData) {
      unitE = unitE + `{externalId: {eq: "${eData}"}},`
    })
    // {externalId:{eq:"${externalId}"}}
    unitQ = `or: [${unitE}]`;
  }

  var reqQuery = {
    "query": `{
                list${constant.typeReportingUnit}(first:1000,filter: {and:[{createdTime:{isNull:false}} ${unitQ}]}) {
                  items {
                    name
                    space
                    externalId
                    businessSegment{
                      name
                      externalId
                      space
                      description
                    }
                  }
                }
              }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingSiteByCountry = function (req, res) {
  var reqBody = req.body;
  var countriesE = "";
  var countryEQ = "";
  if (reqBody.countries && reqBody.countries.length > 0) {
    _.each(reqBody.countries, function (eData) {
      countriesE = countriesE + ` `
    })
    countryEQ = `,{country:{or: [${countriesE}]}}`;
  }


  console.log("reqQuery")
  var reqQuery = {
    "query": `
              {
                list${constant.typeReportingSite}(first:1000, filter:{and:[ {space:{eq:"${constant.CoreDataSpace}"}}${countryEQ}]} ) {
                  items {
                    externalId
                    name
                    description
                    siteCode
                    space
                    functionalLocations {
                      items {
                        externalId
                        name
                        space
                      }
                    }
                    country{
                      externalId
                      name
                      space
                      parent{
                        externalId
                        name
                        space
                        description
                      }
                    }
                  }
                  pageInfo {
                    hasNextPage
                    endCursor
                  }
                }
              }`,
    "variables": {}
  }
  console.log(reqQuery)
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listReportingLocation = function (req, res) {
  
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: "${reqBody.cursor}"`
  }
  var reqQuery = {
    "query": `{
        list${constant.typeReportingLocation}(${limitQ} ${cursorQ}){
          items{
            name
            externalId
            description
            space
            # refersTo(first:1000){
              # items{
                #    externalId
                #  description
                #  space
                #  }
                #  }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.getReportingSiteList = async function (req, res) {

  var sdmApp2Space = constant.DataModelSpace;
  var sdmApp2Model = constant.DataModelExternalId;
  var sdmApp2version = constant.DataModelVersion;
  var limit = req.body.limit
  var sdmQuery = {
    "query": `
          list${constant.typeReportingSite}(first:${limit}, filter:{and:[ {space:{eq:"${constant.CoreDataSpace}"}}]}) {
            items {
              externalId
              name
              space
              siteCode
              country {
                externalId
                space
                name
                parent {
                  name
                  space
                  externalId
                }
              }
              functionalLocations (first:1) {
                items {
                  externalId
                }
              }
              reportingLocations(first:1000){
                items{
                  space
                  description
                  externalId
                  name
                }
            }
              reportingUnits(first:${limit}) {
                items {
                  externalId
                  space
                  name
                  description
                  refersTo(first:1) {
                    items {
                      externalId
                      name
                      space
                    }
                  }
                  businessSegment {
                    externalId
                    name
                    space
                    description
                  }
                  #  reportingLocations {
                    #   items {
                      #     externalId
                      #     space
                      #    description
                      #   }
                    #}
                }
              }
            },
            pageInfo{
              hasNextPage
              endCursor
            }
          }
        }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": sdmApp2Space,
    "datamodel": sdmApp2Model,
    "version": sdmApp2version,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listEquipmentByFunctionalLocation = function (req, res) {
  var functionalLocationName = req.body.functionalLocationName;
  var equipmentName = req.body.equipmentName;
  var siteSpace = ``;
  var reqBody = req.body;
  if(req.body.siteCode){
    siteSpace = ` {space:{eq:"SAP-${req.body.siteCode}-ALL-DAT"}}`
  }
  var externalIdQ = "";
  if (reqBody.externalId && reqBody.externalId.length > 0) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }
  var functionalLocationNameQ = "";
  if (functionalLocationName) {
    functionalLocationNameQ = `{functionalLocationParent: {name: {prefix: "${functionalLocationName}"}}},`;
  }
  var equipmentNameQ = "";
  if (equipmentName) {
    equipmentNameQ = `{name:{prefix:"${equipmentName}"}},`;
  }
  var reqQuery = {
    "query": `{
          list${constant.typeEquipment}(
            first: 1000
            filter: {and:[ ${siteSpace} ${externalIdQ} ${functionalLocationNameQ} ${equipmentNameQ}]}
          ) {
            items {
                externalId
                name
                space
                description
                actualInstallationDate
                actualPurchaseDate
                actualStartupDate
                catalogProfile
                changedBy
                changedDate
                companyCode
                costCenter
                createdBy
                createdDate
                equipmentSystemStatus
                functionalLocationDescription
                location
                maintenancePlanningPlant
                maintenancePlant
                manufacturerCompany
                manufacturerSerialNumber
                modelNumber
                plannerGroup
                plantSection
                price
                priceCurrencyKey
                room
                sortField
                techIdNumber
                techObjectAbcIndicator
                techObjectType
                warrantyEndDate
                workCenter
                createdTime
                functionalLocationParent {
                  externalId
                  space
                }
                
            }
          }
        }`
  };

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listProcess = async function (req, res) {
  var reqBody = req.body;
  var siteQ = ``;
  if(reqBody.sites){
    siteQ = `,{refSite:{externalId:{eq:"${reqBody.sites}"}}}`
  }
  
  var sdmQuery = {
    "query": `{
      list${constant.typeProcess}(first:1000 filter:{and:[{createdTime:{isNull:false}}
      ${siteQ} ]}){
        items {
          space
          externalId
          name
          description
          sequence
          processType
          sequence
          isActive
          refSite{
            space
            externalId
            description
          }
          refOFWAProcess{
            space
            externalId
            name
            processType
            sequence
            iconImage
            isActive
            guidelineDocument {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink{
                downloadUrl
              }
            }
          }
          iconImage
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listProcessBySite = async function (req, res) {
  var externalId = req.body.externalId;
  var sdmQuery = {
    "query": `{
      list${constant.typeProcess}(first:1000, filter:{refSite:{externalId:{eq:"${externalId}"}}}){
        items {
          space
          externalId
          name
          description
          sequence
          processType
          isActive
          guidelineDocument {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink{
                downloadUrl
              }
          }
          refSite{
            space
            externalId
            description
          }
          refOFWAProcess{
            space
            externalId
            name
            processType
            iconImage
            sequence
            isActive
            guidelineDocument {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink{
                downloadUrl
              }
            }
          }
          iconImage
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listProcessConfigurationByProcess = async function (req, res) {
  var reqBody = req.body;
  var externalId = ``;
  if(reqBody.externalId){
    externalId = `,{refOFWAProcess:{externalId:{eq:"${reqBody.externalId}"}}}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  // {or:[
  //   {refOFWAProcess:{name:{eq:"Observation"}}}
  // {refOFWAProcess:{name:{eq:"Hazards"}}}
  // ]} 
  var sdmQuery = {
    "query": `{
      list${constant.typeProcessConfiguration}(first:1000 ${cursorQ} filter:{and:[{createdTime:{isNull:false}}
      ${externalId}]}){
        items {
          space
          externalId
          configDetail
          dashboardConfig
          columnConfig
          refSite {
            space
            externalId
            name
            description
          }
          refOFWAProcess {
            space
            externalId
            name
            guidelineDocument {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink{
                downloadUrl
              }
            }
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
        pageInfo{
          hasNextPage
          endCursor
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listSubCategory = async function (req, res) {
  var reqBody = req.body;
  var siteQ = "";
  if (reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }
  var processQ = "";
  if (reqBody.process.length > 0) {
    processQ = `{refOFWAProcess:{refOFWAProcess:{refOFWAProcess:{externalId:{in:${JSON.stringify(reqBody.process)}}}}}}`;
  }
  var sdmQuery = {
    "query": `{
      list${constant.typeProcess}(sort:{externalId:DESC} first: 1000, filter: {and:[
        ${siteQ} ${processQ}
      ]}) {
        items {
          space
          externalId
          name
          description
          sequence
          processType
          createdTime
          lastUpdatedTime
          createdBy
          modifiedBy
          isActive
          refOFWAProcess {
            externalId
            space
            name
            isActive
            sequence
            guidelineDocument {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink{
                downloadUrl
              }
            }
            refOFWAProcess {
              externalId
              space
              name
              isActive
              sequence
              guidelineDocument {
                id
                externalId
                name
                directory
                source
                mimeType
                metadata
                dataSetId
                downloadLink{
                  downloadUrl
                }
              }
              refOFWAProcess {
                externalId
                space
                name
                isActive
                sequence
                guidelineDocument {
                  id
                  externalId
                  name
                  directory
                  source
                  mimeType
                  metadata
                  dataSetId
                  downloadLink{
                    downloadUrl
                  }
                }
                refOFWAProcess {
                  externalId
                  space
                  name
                  isActive
                  sequence
                  guidelineDocument {
                    id
                    externalId
                    name
                    directory
                    source
                    mimeType
                    metadata
                    dataSetId
                    downloadLink{
                      downloadUrl
                    }
                  }
                }
              }
            }
          }
          refSite {
            externalId
            space
            name
            description
          }
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listQuestionList = async function (req, res) {
  var reqBody = req.body;
  var subCategory = "";
  var category = "";
  var processQ = "";
  var nameQ = "";
  var descriptionQ = "";
  //subCategory
  if (reqBody.subCategory && reqBody.subCategory.length > 0) {
    subCategory = `{refOFWASubCategory:{externalId:{eq:"${reqBody.subCategory}"}}}`;
  }
  if (reqBody.category && reqBody.category.length > 0) {
    category = `{refOFWACategory:{externalId:{eq:"${reqBody.category}"}}}`;
  }
  if (reqBody.process && reqBody.process.length > 0) {
    processQ = `{refOFWASubProcess:{externalId:{eq:"${reqBody.process}"}}}`;
  }
  if (reqBody.name && reqBody.name.length > 0) {
    nameQ = `{name:{eq:"${reqBody.name}"}}`;
  }
  if (reqBody.description && reqBody.description.length > 0) {
    descriptionQ = `{name:{eq:"${reqBody.description}"}}`;
  }
  var sdmQuery = {
    "query": `{
      list${constant.typeOFWAQuestion}(sort:{externalId:ASC} first:1000 filter:{and:[{createdTime:{isNull:false}} ${subCategory} ${processQ} ${category} ${nameQ} ${descriptionQ}]}){
        items{
          space
          externalId
          name
          description
          sequence
          refOFWASubCategory {
            space
            externalId
            name
           # isConfigurable
            processType
            description
            iconImage
        
          }
            refOFWASubProcess {
            space
            externalId
            name
          #  isConfigurable
            processType
            description
            iconImage
          }
          refOFWACategory {
            space
            externalId
            name
          #  isConfigurable
            processType
            description
            iconImage
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
        pageInfo{
          hasNextPage
          endCursor
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listQuestionBankByCategory = async function (req, res) {
  var reqBody = req.body;
  console.log(`list${constant.typeOFWAQuestion}(sort:{name:ASC} filter:{or:[{refOFWASubProcess:{externalId:{eq:"${reqBody.externalId}"}}}{refOFWACategory:{externalId:{eq:"${reqBody.externalId}"}}} {refOFWASubCategory:{externalId:{eq:"${reqBody.externalId}"}}}]}){`)
  var sdmQuery = {
    "query": `{
      list${constant.typeOFWAQuestion}(sort:{name:ASC} first:1000 filter: {or:[{refOFWASubProcess:{externalId:{eq:"${reqBody.externalId}"}}}{refOFWACategory:{externalId:{eq:"${reqBody.externalId}"}}} {refOFWASubCategory:{externalId:{eq:"${reqBody.externalId}"}}}]}){
        items {
          space
          externalId
          name
          description
          sequence
          refOFWASubProcess{
            space
            externalId
            name
            isActive
          }
          refOFWACategory{
            space
            externalId
            name
            isActive
          }
          refOFWASubCategory{
            space
            externalId
            name
            isActive
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listObservation = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  console.log(reqBody)
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {date:{gte:"${reqBody.startDate}"}},
      {date:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }


  var searchQ = "";
  if (reqBody.search && reqBody.search.length > 0) {
    searchQ = `{observedOnBehalfOf:{displayName:{prefix:${JSON.stringify(reqBody.search)}}}}`;
  }


  var deptQ = "";
  if (reqBody.department && reqBody.department.length > 0) {
    var mapDept = reqBody.department.map(item => item.externalId)
    deptQ = `{refDeparment:{externalId:{in:${JSON.stringify(mapDept)}}}}`;
  }
  var unitQ = "";
  if (reqBody.units && reqBody.units.length > 0) {
    unitQ = `{refUnit:{externalId:{in:${JSON.stringify(reqBody.units)}}}}`;
  }
  var activeQ = "{isActive:{eq:true}}";
  if (!reqBody.isActive && reqBody.isActive == false) {
    activeQ = `{isActive:{eq:false}}`;
  }
  
  var processQ = "";
  if (reqBody.process && reqBody.process.length > 0) {
    processQ = `{refProcess:{externalId:{in:${JSON.stringify(reqBody.process)}}}}`;
  }
  var subProcessQ = "";
  if (reqBody.subProcess && reqBody.subProcess.length > 0) {
    subProcessQ = `{refSubProcess:{externalId:{in:${JSON.stringify(reqBody.subProcess)}}}}`;
  }  

  var behalfQ = "";
  if (reqBody.behalf && reqBody.behalf.length > 0) {
    behalfQ = `{observedOnBehalfOf:{externalId:{in:${JSON.stringify(reqBody.behalf)}}}}`;
  }
  var locationQ = "";
  if (reqBody.location && reqBody.location.length > 0) {
    locationQ = `{refReportingLocation:{externalId:{in:${JSON.stringify(reqBody.location)}}}}`;
  }

  var fieldWalkQ = `{refProcess:{not:{name:{eq:"Hazards"}}}}`;
  if(reqBody.listType && reqBody.listType == "Hazards" ){
    fieldWalkQ = `{refProcess:{name:{eq:"Hazards"}}}`
  }
  
  var operLearningQ = "";
  if (!reqBody.operLearning && reqBody.operLearning == false) {
    operLearningQ = `{isOperationalLearning:{eq:false}}`;
  }
  else if (reqBody.operLearning && reqBody.operLearning == true){
    operLearningQ = `{isOperationalLearning:{eq:true}}`;
  }

  var checklist = ``;
  if(reqBody.subProcessId){
    checklist = `refOFWACategory:{refOFWAProcess:{externalId:{eq:"${reqBody.subProcessId}"}}}`
  }
  
var projectNameQ=""
if(reqBody.projectName&& reqBody.projectName.length > 0){
  projectNameQ = `{projectName:{eq:"${reqBody.projectName}"}}`
}
var workOrderNumberQ=""
if(reqBody.workOrderNumber&& reqBody.workOrderNumber.length > 0){
  workOrderNumberQ = `{workOrderNumber:{eq:"${reqBody.workOrderNumber}"}}`
}

var createdByfilterQ=""
if(reqBody.createdByfilter&& reqBody.createdByfilter.length > 0){
  createdByfilterQ = `{createdBy:{eq:"${reqBody.createdByfilter}"}}`
}

var statusfilterQ=""
if(reqBody.statusfilter&& reqBody.statusfilter.length > 0){
  statusfilterQ = `{observationStatus:{eq:"${reqBody.statusfilter}"}}`
}

var categoryQ=""
if(reqBody.category&& reqBody.category.length > 0){
  categoryQ = `externalId:{eq:"${reqBody.category}"}`
}
var subCategoryQ=""
if(reqBody.subCategory&& reqBody.subCategory.length > 0){
  subCategoryQ = `{refOFWAProcess:{externalId:{eq:"${reqBody.subCategory}"}}}`
}

  var sdmQuery = {
    "query": `{
      list${constant.typeObservation}(sort:[{date:DESC_NULLS_LAST},{externalId:DESC}] ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${deptQ} ${createdByfilterQ} ${statusfilterQ} ${projectNameQ} ${dateFilter} ${searchQ} ${operLearningQ} ${behalfQ} ${locationQ} ${siteQ} ${unitQ} ${activeQ} ${fieldWalkQ}
      ${processQ} ${subProcessQ} ${workOrderNumberQ}  ]}){
        pageInfo{
          hasNextPage
          endCursor
        }
        items {
          space
          externalId
          isActive
          crafts
          observedCrafts
          observerCrafts
          shiftIdentifier
          date
          startTime
          endTime
          isContractor
          contractorDetails
          contractorsNames
          isOperationalLearning
          operationalLearningDescription
          description
          observationStatus
          projectName
          comments
          signature
          score
          shift
          shortDescription
          art
          problem
          cause
          solution
          measure
          floor
          urgency
          week
          month
          eventType
          eventDesccription
          correctiveActionFlag
          descripeCorrectiveActionTaken
        refDeparment{
          externalId
          space
          name
          description
         }
          refWorkOrderHeader{
          externalId
          space
          name
          number
          description
         }

          observedOnBehalfOf {
            space
            externalId
            lastName
            firstName
            displayName
          }
            secondaryObserver{
            space
            externalId
            lastName
            firstName
            displayName
            }
          performerAzureDirectoryUserID {
            space
            externalId
            lastName
            firstName
          }
          refSite{
            externalId
            space
            description
            siteCode
          }
          refUnit{
            externalId
            space
            name
            description
          }
          inspectionNo
          inspectionGrade
          inspectionOutcome
          approvalStatus
          classification
          refCompExAsset{
            externalId
            space
            itemNo
            subTagNumber
            description
            itemType
            certificateNo
            areaClassification
            atexCategoryArea
            equipmentGroupArea
            manufacturer
            manufacturerPartNo
            serialNo
            rfID
            drawingNumber
            barcode
            isActive
            associateDevice1
            associateDevice2
            deviceType
            zone
            exEex
            exClass
            gasGroup
            temperatureClass
            equipmentGroupDevice
            eplDevice
            atexCategoryDevice
            createdBy
            modifiedBy
         }
         refOFWAChecklist(
        sort: { externalId: DESC }
        filter: {${checklist}}
      ){
            items {
              space
              externalId
              activity
              riskyAction
              risk
              riskAgreement
              reasonForAction
              safeBehaviour
              suggestedSolution
              isSafe
              isUnSafe
              isNotObserved
              isInjuryPotential
              isSatisfactory
              isOfi
              isUnsatisfactory
              comment
              note
              refOFWACategory{
                space
                externalId
                name
                refOFWAProcess{
        name
        externalId
        space
      }
              }
              refOFWASubCategory{
                space
                externalId
                name
              }
              refOFWAQuestion{
                space
                externalId
                name
                description
              }
              createdBy
              modifiedBy
              createdTime
              lastUpdatedTime
            }
          }
          refOFWAProcess{
            items{
              externalId
              space
              name
              description
              processType
              guidelineDocument {
                id
                externalId
                name
                directory
                source
                mimeType
                metadata
                dataSetId
                downloadLink{
                  downloadUrl
                }
              }
           # isConfigurable
              refOFWAProcess{
                externalId
                name
                description
                processType
                guidelineDocument {
                  id
                  externalId
                  name
                  directory
                  source
                  mimeType
                  metadata
                  dataSetId
                  downloadLink{
                    downloadUrl
                  }
                }
                  refOFWAProcess{
                    externalId
                    space
                    name
                    processType
                    guidelineDocument {
                      id
                      externalId
                      name
                      directory
                      source
                      mimeType
                      metadata
                      dataSetId
                      downloadLink{
                        downloadUrl
                      }
                    }
                    refOFWAProcess {
                      externalId
                      space
                      processType
                      guidelineDocument {
                        id
                        externalId
                        name
                        directory
                        source
                        mimeType
                        metadata
                        dataSetId
                        downloadLink{
                          downloadUrl
                        }
                      }
                      refOFWAProcess {
                        externalId
                        space
                        processType
                        guidelineDocument {
                          id
                          externalId
                          name
                          directory
                          source
                          mimeType
                          metadata
                          dataSetId
                          downloadLink{
                            downloadUrl
                          }
                        }
                      }
                    }
                  }
                
              }
              
            }
          }
          refReportingLocation{
            externalId
            space
            description
            name
          }
          refProcess{
            externalId
            space
            name
          }
          refSubProcess{
            externalId
            space
            name
          }
          refCategory(
            filter:{${categoryQ} }){
            items{
              externalId
              space
              name
            }
          }
          refSubCategory{
            items{
              externalId
              space
              name
            }
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }

console.log("reqQuery ----->",sdmQuery)
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      if(categoryQ?.length > 0 || subCategoryQ?.length > 0){
        console.log("rData ----->",rData["data"])
        const filteredData = rData["data"].data.listObservation.items.filter(item => 
          item.refCategory && item.refCategory.items && item.refCategory.items.length > 0
        );
        rData["data"].data.listObservation.items = filteredData;
      }
      

      return res.status(200).json(rData["data"]);
      // return res.status(200).json(rData["data"]);
    }
  });
}



exports.listDepartment = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }

  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{reportingSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }



  var sdmQuery = {
    "query": `{
      list${constant.typeDepartment}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}}  ${siteQ} ]}){
          items{
      externalId
      space
      name
      description
      reportingSite{
        externalId
        space
        name
        description
      }
      managedBy{
        items{
          externalId
          space
          firstName
          lastName
        }
      }
      
    }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listObservationById = async function (req, res) {
  var reqBody = req.body;
  var sdmQuery = {
    "query": `{
      list${constant.typeObservation}(filter:
        {externalId:{eq:"${reqBody.externalId}"}}
      ){
        items {
          space
          externalId
          crafts
          observedCrafts
          observerCrafts
          shiftIdentifier
          date
          startTime
          endTime
          isContractor
          contractorDetails
          contractorsNames
          isOperationalLearning
          operationalLearningDescription
          description
          projectName
          comments
          signature
          score
          shift
          shortDescription
          art
          problem
          cause
          solution
          measure
          floor
          urgency
          week
          month
          eventType
          eventDesccription
          correctiveActionFlag
          descripeCorrectiveActionTaken
          refDeparment{
           externalId
           space
           name
           description
          }
          refWorkOrderHeader{
            externalId
            space
            name
            number
            description
          }
          observedOnBehalfOf {
            space
            externalId
            lastName
            firstName
          }
            secondaryObserver{
            space
            externalId
            lastName
            firstName
            displayName
            }
          performerAzureDirectoryUserID {
            space
            externalId
            lastName
            firstName
          }
          refEquipment{
            items{
              space
              externalId
              description
              name
            }
          }
          evidenceDocument(sort:{externalId:DESC}){
            items{
              space
              externalId
              name
              evidenceDocumentId
              mimetype
            }
          }

          inspectionNo
          inspectionGrade
          inspectionOutcome
          approvalStatus
          classification
          refCompExAsset{
            externalId
            space
            itemNo
            subTagNumber
            description
            itemType
            certificateNo
            areaClassification
            atexCategoryArea
            equipmentGroupArea
            manufacturer
            manufacturerPartNo
            serialNo
            rfID
            drawingNumber
            barcode
            isActive
            associateDevice1
            associateDevice2
            deviceType
            zone
            exEex
            exClass
            gasGroup
            temperatureClass
            equipmentGroupDevice
            eplDevice
            atexCategoryDevice
            createdBy
            modifiedBy
         }
          refOFWAChecklist(sort:{externalId:DESC}){
            items {
              space
              externalId
              activity
              riskyAction
              risk
              riskAgreement
              reasonForAction
              safeBehaviour
              suggestedSolution
              isSafe
              isUnSafe
              isNotObserved
              isInjuryPotential
              isSatisfactory
              isOfi
              isUnsatisfactory
              comment
              note
              refOFWACategory{
                space
                externalId
                name
              }
              refOFWASubCategory{
                space
                externalId
                name
              }
              refOFWAQuestion{
                space
                externalId
                name
              }
              createdBy
              modifiedBy
              createdTime
              lastUpdatedTime
            }
          }
          refProcess {
            externalId
            space
            name
            refSite{
              space
              externalId
              description
            }
          } 
          refCorePrinciple {
            externalId
            space
            name
            refSite{
              space
              externalId
              description
            }
          }  
          refSubProcess {
            externalId
            space
            name
            refSite{
              space
              externalId
              description
            }
          }
          refCategory (sort:{externalId:DESC}) {
            items {
              space
              externalId
              name
              refSite{
                space
                externalId
                description
              }
              refOFWAProcess {
                externalId
                space
                name
                guidelineDocument {
                  id
                  externalId
                  name
                  directory
                  source
                  mimeType
                  metadata
                  dataSetId
                  downloadLink{
                    downloadUrl
                  }
                }
                refSite{
                  space
                  externalId
                  description
                }
              }
            }
          }
          refSubCategory (sort:{externalId:DESC}) {
            items {
              space
              externalId
              name
              refSite{
                space
                externalId
                description
              }
              refOFWAProcess {
                externalId
                space
                name
                guidelineDocument {
                  id
                  externalId
                  name
                  directory
                  source
                  mimeType
                  metadata
                  dataSetId
                  downloadLink{
                    downloadUrl
                  }
                }
                refSite{
                  space
                  externalId
                  description
                }
              }
            }
          }   
          refOFWAProcess(sort:{externalId:DESC}) {
            items {
              space
              externalId
              name
              guidelineDocument {
                id
                externalId
                name
                directory
                source
                mimeType
                metadata
                dataSetId
                downloadLink{
                  downloadUrl
                }
              }
              refOFWAProcess {
                externalId
                space
                name
                guidelineDocument {
                  id
                  externalId
                  name
                  directory
                  source
                  mimeType
                  metadata
                  dataSetId
                  downloadLink{
                    downloadUrl
                  }
                }
                refOFWAProcess {
                  externalId
                  space
                  name
                  guidelineDocument {
                    id
                    externalId
                    name
                    directory
                    source
                    mimeType
                    metadata
                    dataSetId
                    downloadLink{
                      downloadUrl
                    }
                  }
                  refSite{
                    space
                    externalId
                    description
                  }
                  refOFWAProcess {
                    externalId
                    space
                    name
                    guidelineDocument {
                      id
                      externalId
                      name
                      directory
                      source
                      mimeType
                      metadata
                      dataSetId
                      downloadLink{
                        downloadUrl
                      }
                    }
                    refOFWAProcess {
                      externalId
                      space
                      name
                      guidelineDocument {
                        id
                        externalId
                        name
                        directory
                        source
                        mimeType
                        metadata
                        dataSetId
                        downloadLink{
                          downloadUrl
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          refSite{
            externalId
            space
            description
          }
          refUnit{
            externalId
            space
            description
          }
          refReportingLocation{
            externalId
            space
            description
            name
          }
          
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listChecklistByCategory = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var externalId ='';
  if(reqBody.externalId){
    externalId =`{refOFWACategory:{externalId:{eq:"${reqBody.externalId}"}}}`
  }
  var lStartDate = ``
  if(reqBody.lStartDate){
     var startDateVal = reqBody.lStartDate+'T00:00:00.000Z'
   
    // {lastUpdatedTime:{gte:"2024-02-29T03:35:29.262Z"}}
    // {lastUpdatedTime:{lte:"2024-04-29T03:35:29.262Z"}}
    lStartDate = `{lastUpdatedTime:{gte:"${startDateVal}"}}`
  }
  var lEndDate =``
  if(reqBody.lEndDate){
     var endDateVal =reqBody.lEndDate+'T23:59:59.999Z'
    lEndDate = `{lastUpdatedTime:{lte:"${endDateVal}"}}`
  }

  var checklist = ``;
  if(reqBody.checklistId){
    checklist = `,{refOFWACategory:{refOFWAProcess:{externalId:{eq:"${reqBody.checklistId}"}}}}`
  }


  var sdmQuery = {
    "query": `{
      list${constant.typeChecklist}(${limitQ} ${cursorQ} sort:{externalId:DESC}, filter:{and:[ {createdTime:{isNull:false}} ${externalId}
      ${lStartDate}  ${lEndDate} ${checklist}
      ]}){
        items {
          space
          externalId
          activity
          riskyAction
          risk
          riskAgreement
          reasonForAction
          safeBehaviour
          suggestedSolution
          isSafe
          isUnSafe
          isNotObserved
          isInjuryPotential
          isSatisfactory
          isOfi
          isUnsatisfactory
          comment
          note
          refOFWACategory{
            space
            externalId
            name
          }
          refOFWASubCategory{
            space
            externalId
            name
          }
          refOFWAQuestion {
            name
            description
            space
            externalId
          }
          refOFWASchedule {
            space
            externalId
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listFieldWalk = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  console.log(reqBody)
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {date:{gte:"${reqBody.startDate}"}},
      {date:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }
  
  var behalfQ = "";
  if (reqBody.behalf && reqBody.behalf.length > 0) {
    behalfQ = `{observedOnBehalfOf:{externalId:{in:${JSON.stringify(reqBody.behalf)}}}}`;
  }
  var locationQ = "";
  if (reqBody.location && reqBody.location.length > 0) {
    locationQ = `{refReportingLocation:{externalId:{in:${JSON.stringify(reqBody.location)}}}}`;
  }
  var unitQ = "";
  if (reqBody.units && reqBody.units.length > 0) {
    unitQ = `{refUnit:{externalId:{in:${JSON.stringify(reqBody.units)}}}}`;
  }
  var createdByfilterQ=""
if(reqBody.createdByfilter&& reqBody.createdByfilter.length > 0){
  createdByfilterQ = `{createdBy:{eq:"${reqBody.createdByfilter}"}}`
}

  var processQ = "";
  if (reqBody.process && reqBody.process.length > 0) {
    processQ = `{refProcess:{externalId:{in:${JSON.stringify(reqBody.process)}}}}`;
  }

  var subProcessQ = "";
  if (reqBody.subProcess && reqBody.subProcess.length > 0) {
    subProcessQ = `{refSubProcess:{externalId:{in:${JSON.stringify(reqBody.subProcess)}}}}`;
  }

  var fieldWalkQ = `{refProcess:{not:{name:{eq:"Hazards"}}}}`;
  if(reqBody.listType && reqBody.listType == "Hazards" ){
    fieldWalkQ = `{refProcess:{name:{eq:"Hazards"}}}`
  }

  var OperLearningQ = "";
  if (!reqBody.OperLearning && reqBody.OperLearning == false) {
    OperLearningQ = `{isOperationalLearning:{eq:false}}`;
  }
  else if (reqBody.OperLearning && reqBody.OperLearning == true){
    OperLearningQ = `{isOperationalLearning:{eq:true}}`;
  }

  var projectNameQ=""
if(reqBody.projectName&& reqBody.projectName.length > 0){
  projectNameQ = `{projectName:{eq:"${reqBody.projectName}"}}`
}
var workOrderNumberQ=""
if(reqBody.workOrderNumber&& reqBody.workOrderNumber.length > 0){
  workOrderNumberQ = `{workOrderNumber:{eq:"${reqBody.workOrderNumber}"}}`
}

  var activeQ = "{isActive:{eq:true}}";
  if (!reqBody.isActive && reqBody.isActive == false) {
    activeQ = `{isActive:{eq:false}}`;
  }

  var sdmQuery = {
    "query": `{
      list${constant.typeFieldWalk}(sort:[{date:DESC_NULLS_LAST},{externalId:DESC}] ${limitQ} ${cursorQ}  filter:{and:[ ${fieldWalkQ} ${createdByfilterQ} ${projectNameQ} ${OperLearningQ} ${dateFilter} ${siteQ} ${behalfQ} ${locationQ} ${unitQ}
      ${processQ} ${subProcessQ} ${activeQ} ${workOrderNumberQ}]}){
        pageInfo{
          hasNextPage
          endCursor
        }
        items {
          space
          externalId
          date
          refUnit{
            externalId
            space
          }
          startTime
          title
          endTime
          signature
          isActive
          whatWentWell
          isOperationalLearning
          operationalLearningDescription
          projectName
          whatNeedsAttention
          whatDidYouDoAboutIt
          overallSummary
          score
          refWorkOrderHeader{
            externalId
            space
            name
            number
            description
          }
          attendee{
            space
            externalId
            firstName
            lastName
          }
          refAttendee{
            items{
              externalId
              space
              firstName
              lastName
              email
              #azureUserId
              active
              #lanId
            }
          }
          performerAzureDirectoryUserID{
            space
            externalId
            firstName
            lastName
          }
          refSite {
            externalId
            space
            description
          }
          refCorePrinciple {
            space
            externalId
            name
          }
          refSubProcess {
            space
            externalId
            name
           }
          refProcess {
            space
            externalId
            name
          }
          refUnit{
            externalId
            space
            description
          }
          refReportingLocation {
            externalId
            space
            description
          }
          refOFWAProcess {
            items {
              space
              externalId
              name
              refSite{
                space
                externalId
                description
              }
              refOFWAProcess{
                externalId
                space
              }
            }
          }
          refEquipment(sort:{externalId:DESC}){
            items{
              space
              externalId
              description
              name
            }
          }
          evidenceDocument(sort:{externalId:DESC}){
            items {
              modifiedBy
              name
              externalId
              space
              pathURL
              createdBy
              pathStore
              evidenceDocumentId
              mimetype
              description
            }
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
        pageInfo{
          hasNextPage
          hasPreviousPage
          endCursor
          startCursor
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.priviewImage = async function (req, res) {
  var reqBody = req.body;
  var graphURL = process.env.AzureAudience + "/api/v1/projects/" + process.env.project + "/documents/2871501430532443/preview/image/pages/1"

  fetch(graphURL, {
    method: 'GET',
    headers: {
      'accept': 'image/png',
      'authorization': req.headers.authorization,
      'Content-Type': 'image/png'
    }
  })
    .then(res => res)
    .then(response => {
      console.log("res", response)
      return res.status(200).sendFile(response);
    })
    .catch(err => {
      console.log("err", err)
      return res.status(200).json(err);
    })
}

exports.listVendor = async function (req, res) {
  console.log("dkkkkkkk")
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {date:{gte:"${reqBody.startDate}"}},
      {date:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var vendorNumber = ``;
  if(reqBody.vendorNumber){
    vendorNumber = `{vendorNumber:{prefix:"${reqBody.vendorNumber}"}}`

  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }
  console.log(`list${constant.typeVendor}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${dateFilter} ${siteQ}]})`)
  var sdmQuery = {
    "query": `{
      list${constant.typeVendor}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${dateFilter} ${vendorNumber} ${siteQ}]}){
        items {
          space
          externalId
          vendorNumber
          vendorName
          companyName
          contactName
          mobile
          jobTitle
          email
          corporateWebSite
          address {
            space
            externalId
            name
            state
            city
            postalCode
            streetAddress1
            streetAddress2
            postOfficeBoxNumber
            phoneNumber
            createdBy
            modifiedBy
            country{
              name
              parent{
                description
              }
            }
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}



exports.listSupplier = async function (req, res) {
  console.log("dkkkkkkk")
  console.log("listSupplier")
  console.log(req)
  console.log(res)
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }

  var SupplierNumber = ``;
  if(reqBody.SupplierNumber){
    SupplierNumber = `{number:{prefix:"${reqBody.SupplierNumber}"}}`

  }

  
  var sdmQuery = {
    "query": `{
       list${constant.typeSupplier}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}}  ${SupplierNumber} ]})
     {
    items{
      
      name
      number
      address{
        city
        postalCode
        streetAddress1
        streetAddress2
        postOfficeBoxNumber
        phoneNumber
        externalId
        space
        name
        country{
          name
          externalId
          space
          parent {
            externalId
            description
          }
        }   
}
      space
externalId
createdTime

    }
  }

    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listOFWALog = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {observationStartDate:{gte:"${reqBody.startDate}"}},
      {observationEndDate:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }

  var objectQ = "";
  if (reqBody.objectId && reqBody.objectId.length > 0) {
    objectQ = `{objectExternalId:{in:["${reqBody.objectId}"]}}`;
  }

  var externalIdQ = "";
  if (reqBody.externalId) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }
  var yearQ = "";
  if (reqBody.years) {
    yearQ = `{year:{in:[${reqBody.years}]}}`;
  }
  console.log("yearQ",yearQ)
  var sdmQuery = {
    "query": `{
      list${constant.typeOFWALog}(sort:{dateTime:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${objectQ} ${externalIdQ} ]}){
         items{
      externalId
      space
      objectExternalId
      objectType
      logType
      refUser{
        externalId
        space
        firstName
        lastName
        email
        displayName
      }
      dateTime
      beforeJSON
      afterJSON
      createdBy
      modifiedBy
      
      
    }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listSchedule = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {observationStartDate:{gte:"${reqBody.startDate}"}},
      {observationEndDate:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }

  var externalIdQ = "";
  if (reqBody.externalId) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }
  var corePrincipleId = "";
  if (reqBody.corePrincipleId) {
    corePrincipleId = `{ refOFWACorePrinciple:{ externalId:{eq:"${reqBody.corePrincipleId}"}}}`;
  }
  var processId = "";
  if (reqBody.processId) {
    processId = `{ refOFWAProcess:{ externalId:{eq:"${reqBody.processId}"}}}`;
  }
  var subProcessId = "";
  if (reqBody.subProcessId) {
    subProcessId = `{ refOFWAObservationType:{ externalId:{eq:"${reqBody.subProcessId}"}}}`;
  }
  
  var yearQ = "";
  if (reqBody.years) {
    yearQ = `{year:{in:[${reqBody.years}]}}`;
  }
  console.log("yearQ",yearQ)
  var sdmQuery = {
    "query": `{
      list${constant.typeSchedule}(sort:[{observationStartDate:DESC_NULLS_LAST},{externalId:DESC}] ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${dateFilter} ${siteQ} ${externalIdQ} ${yearQ} ${corePrincipleId} ${processId} ${subProcessId}]})
{
       items {
          externalId
          space
          title
          auditNumber
          email
          observationStartDate
          observationEndDate
          observationPoints
          year
          quarter
          priority
          status
          isFirstTimeAudit
          legalEntity
          siteCertificateNumber
          evidenceChecklist
          coAuditor{
            space
            externalId
            firstName
            lastName
          }
          performerAzureDirectoryUserID{
            space
            externalId
            firstName
            lastName
          }
          procurementPPRAzureDirectoryUserID{
            space
            externalId
            firstName
            lastName
          }
          refOFWAVendor{
            externalId
            space
            vendorNumber
            vendorName
            email
            address {
              name
              state
              city
              postalCode
              streetAddress1
              streetAddress2
              postOfficeBoxNumber
              phoneNumber
              createdBy
              modifiedBy
              country{
                name
                parent{
                  description
                }
              }
            }
          }
          refOFWAObservationType{
            space
            externalId
            name
          }
          refSupplier{
            name
            number
            address{
              name
              country{
                name
                externalId
                space
              }
              state
              city
              space
              externalId
              
            }
            space
            externalId
          }
          occurrence
          isEnable
          refOFWAProcess{
            externalId
            space
            name
            refSite{
              externalId
              space
              name
            }
          }
          refOFWACategory{
            externalId
            space
            name
          }
          refOFWASubCategory{
            externalId
            space
            name
          }
          refOFWACorePrinciple{
            externalId
            space
            name
          }
          refGeoRegion {
            externalId
            space
          }
          refCountry {
            externalId
            space
          }
          refSite {
            externalId
            space
          }
          refUnit {
            externalId
            space
          }
          refReportingLocation{
            externalId
            space
            name
            description
            reportingUnit{
              externalId
              space
              name
              description
          }
      }
          refBusinessSegment {
            externalId
            space
          }
         
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listScheduleDetail = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {observationStartDate:{gte:"${reqBody.startDate}"}},
      {observationEndDate:{lte:"${reqBody.endDate}"}}
    ]}`
  }
  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }

  var scheduleQ = "";
  if (reqBody.scheduleId && reqBody.scheduleId.length > 0) {
    scheduleQ = `{refOFWASchedule:{externalId:{in:${JSON.stringify(reqBody.scheduleId)}}}}`;
  }

  var externalIdQ = "";
  if (reqBody.externalId) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }
  var yearQ = "";
  if (reqBody.years) {
    yearQ = `{year:{in:[${reqBody.years}]}}`;
  }
  console.log("yearQ",yearQ)
  var sdmQuery = {
    "query": `{
      list${constant.typeOFWAScheduleDetail}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${scheduleQ} ${externalIdQ} ]}){
          items{
      externalId
      space
      refOFWASchedule{
          externalId
          space
          title
          observationStartDate
          observationEndDate
          observationPoints
          year
          quarter
          priority
          status
          isFirstTimeAudit
          legalEntity
          siteCertificateNumber
          evidenceChecklist
          coAuditor{
            space
            externalId
            firstName
            lastName
          }
          performerAzureDirectoryUserID{
            space
            externalId
            firstName
            lastName
          }
          procurementPPRAzureDirectoryUserID{
            space
            externalId
            firstName
            lastName
          }
          refOFWAVendor{
            externalId
            space
            vendorNumber
            vendorName
            email
            address {
              name
              state
              city
              postalCode
              streetAddress1
              streetAddress2
              postOfficeBoxNumber
              phoneNumber
              createdBy
              modifiedBy
              country{
                name
                parent{
                  description
                }
              }
            }
          }
          refOFWAObservationType{
            space
            externalId
            name
             description
          sequence
          processType
          refSite{
            space
            externalId
            description
          }
          }
          occurrence
          isEnable
          refOFWAProcess{
            externalId
            space
            name
            description
            sequence
            processType
          refSite{
            space
            externalId
            description
          }
          }
          refOFWACategory{
            externalId
            space
            name
          }
          refOFWASubCategory{
            externalId
            space
            name
          }
          refOFWACorePrinciple{
            externalId
            space
            name
             description
          sequence
          processType
          refSite{
            space
            externalId
            description
          }
          }
          refGeoRegion {
            externalId
            space
          }
          refCountry {
            externalId
            space
          }
          refSite {
            externalId
            space
          }
          refUnit {
            externalId
            space
          }
          refReportingLocation {
            externalId
            space
          }
          refBusinessSegment {
            externalId
            space
          }
         
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      startDate
      endDate
      status
      isEnable
      createdBy
      modifiedBy
      createdTime
    }
      }
    }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listChecklist = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }

  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }

  var externalIdQ = "";
  if (reqBody.externalId) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }

  var categoryQ = "";
  if (reqBody.categories) {
    categoryQ = `{refOFWACategory:{externalId:{in:${JSON.stringify(reqBody.categories)}}}}`;
  }

  var scheduleQ = "";
  if (reqBody.schedules) {
    scheduleQ = `{refOFWASchedule:{externalId:{in:${JSON.stringify(reqBody.schedules)}}}}`;
  }

  console.log(`list${constant.typeChecklist}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${siteQ} ${externalIdQ} ${categoryQ}${scheduleQ}]})`)
  var sdmQuery = {
    "query": `{
      list${constant.typeChecklist}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${siteQ} ${externalIdQ} ${categoryQ}${scheduleQ}]}){
        items {
          space
          externalId
          activity
          riskyAction
          risk
          riskAgreement
          reasonForAction
          safeBehaviour
          suggestedSolution
          isSafe
          isUnSafe
          isNotObserved
          isInjuryPotential
          actionLevel
          isSatisfactory
          isOfi
          isUnsatisfactory
          comment
          note
          refOFWAQuestion {
            space
            externalId
          }
          refOFWACategory {
            space
            externalId
            name
          }
          refOFWASubCategory {
            space
            externalId
          }
          refOFWASchedule {
            space
            externalId
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  console.log(sdmQuery)
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listAudit = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }

  var siteQ = "";
  if (reqBody.sites && reqBody.sites.length > 0) {
    siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
  }

  var externalIdQ = "";
  if (reqBody.externalId) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }


  var createdByfilterQ=""
if(reqBody.createdByfilter&& reqBody.createdByfilter.length > 0){
  createdByfilterQ = `{createdBy:{eq:"${reqBody.createdByfilter}"}}`
}
  var scheduleQ = "";
  if (reqBody.schedules) {
    scheduleQ = `{refOFWASchedule:{externalId:{in:${JSON.stringify(reqBody.schedules)}}}}`;
  }

  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {createdTime:{gte:"${reqBody.startDate}T00:00:00.000Z"}},
      {createdTime:{lte:"${reqBody.endDate}T23:59:59.999Z"}}
    ]}`
  }

  var processQ = "";
  if (reqBody.process && reqBody.process.length > 0) {
    processQ = `{refProcess:{externalId:{in:${JSON.stringify(reqBody.process)}}}}`;
  }

  var subProcessQ = "";
  if (reqBody.subProcess && reqBody.subProcess.length > 0) {
    subProcessQ = `{refSubProcess:{externalId:{in:${JSON.stringify(reqBody.subProcess)}}}}`;
  }  
  var behalfQ = "";
  if (reqBody.behalf && reqBody.behalf.length > 0) {
    behalfQ = `{observedOnBehalfOf:{externalId:{in:${JSON.stringify(reqBody.behalf)}}}}`;
  }
  var locationQ = "";
  if (reqBody.location && reqBody.location.length > 0) {
    locationQ = `{refReportingLocation:{externalId:{in:${JSON.stringify(reqBody.location)}}}}`;
  }
  var unitQ = "";
  if (reqBody.units && reqBody.units.length > 0) {
    unitQ = `{refUnit:{externalId:{in:${JSON.stringify(reqBody.units)}}}}`;
  }

  var OperLearningQ = "";
  if (!reqBody.OperLearning && reqBody.OperLearning == false) {
    OperLearningQ = `{isOperationalLearning:{eq:false}}`;
  }
  else if (reqBody.OperLearning && reqBody.OperLearning == true){
    OperLearningQ = `{isOperationalLearning:{eq:true}}`;
  }

  var auditTypeQ = "";
  if(reqBody.auditType && reqBody.auditType.length > 0){

    auditTypeQ = `{refProcess:{externalId:{in:${JSON.stringify(reqBody.auditType)}}}}`;
    // auditTypeQ = `{refOFWASchedule:{refOFWACategory:{refOFWAProcess:{externalId:{eq:"${reqBody.auditType}"}}}}}`
  }


  var SubProcessTypeQ = "";
  if(reqBody.SubProcessType && reqBody.SubProcessType.length > 0){
    SubProcessTypeQ = `{refSubProcess:{externalId:{in:${JSON.stringify(reqBody.SubProcessType)}}}}`;
    // SubProcessTypeQ = `{refOFWASchedule:{refOFWACategory:{refOFWAProcess:{externalId:{eq:"${reqBody.SubProcessType}"}}}}}`
  }



  var sdmQuery = {
    "query": `{

      list${constant.typeAudit}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${siteQ} ${dateFilter} ${behalfQ} ${createdByfilterQ} ${OperLearningQ} ${locationQ} ${unitQ} ${externalIdQ}${scheduleQ} ${auditTypeQ} ${SubProcessTypeQ}
      
      ${processQ} ${subProcessQ}]}){
        pageInfo{
          hasNextPage
          endCursor
        }
        items {
          space
          externalId
          status
          refCategory{
            items{
              externalId
              space
              name
            }
          }
          refWorkOrderHeader{
            externalId
            space
            name
            description
          }
          notes
          signature
          startTime
          endTime
          date
          score
          generalNote
          refDeparment{
           externalId
           space
           name
           description
          }
          workPermitNumber
          isOperationalLearning
          operationalLearningDescription
          workPermitType
          companyType
          activityAudited
          companyName
          mobileNumber 
         emailAddress 
         product          
         corporateWebSite 
          jobTitle 
          discussionPoint
          result
          measure
          totalQuestions
          totalQuestionsAudited
          satisfactoryWeightage
          opportunityForImprovementWeightage
          unsatisfactoryWeightage
          refOFWAChecklist{
            items{
            isInjuryPotential
            actionLevel
            note
              refOFWAQuestion{
                name
                description
                refOFWACategory{
                  name
                  description
                  space
                  externalId
                }
                refOFWASubCategory{
                  name
                  description
                  space
                  externalId
                }
                space
                externalId
              }
                refOFWASubProcess{
                name
                description
                space
                externalId
              }
              refOFWACategory{
                name
                description
                space
                externalId
              }
              refOFWASubCategory{
                name
                description
                space
                externalId
              }
              activity
              riskyAction
              risk
              riskAgreement
              reasonForAction
              safeBehaviour
              safeBehaviour
              suggestedSolution
              isSafe
              isUnSafe
              isNotObserved
              isInjuryPotential
              isSatisfactory
              isOfi
              isUnsatisfactory
              externalId
              space
            }
            
          }
          observedOnBehalfOf {
            displayName
            firstName
            lastName
            email
            active
            deleted
          }
          shift
          refOFWAScorecard {
            space
            externalId
            isSummaryRecord
            satisfactory
            opportunityForImprovement
            unsatisfactory
            createdBy
            modifiedBy
          }
          refOFWAAuditSummary {
            space
            externalId
            productFamily
            locationManufacturingSite
            conclusion
            pathForward
            auditorSignature
            managerSignature
            createdBy
            modifiedBy
          }
          evidenceDocument{
            items{
              space
              externalId
              name
              mimetype
              evidenceDocumentId
              description
            }
          }
          refGeoRegion {
            externalId
            space
          }
          refCountry {
            externalId
            space
          }
          refSite {
            externalId
            space
          }
          refUnit {
            externalId
            space
            description
          }
          refReportingLocation {
            externalId
            space
            description
          }
          refBusinessSegment {
            externalId
            space
          }
          refReportingLine {
            externalId
            space
          }
          refOFWASchedule {
            externalId
            space
            title
            email
            auditNumber
            observationStartDate
            observationEndDate
            observationPoints
            year
            quarter
            priority
            status
            evidenceChecklist
            performerAzureDirectoryUserID{
              space
              externalId
              firstName
              lastName
            }
            procurementPPRAzureDirectoryUserID{
              space
              externalId
              firstName
              lastName
            }
            refSupplier{
              externalId
              space
              name
              space
              address{
                   name
                    city
                    postalCode
                    streetAddress1
                    streetAddress2
                    phoneNumber
                    country{
                     name
              space
              externalId
              parent{
                description
                name
                externalId
                space
              }
                    }
              }
            }
            refOFWAVendor {
              externalId
              space
              vendorName
              email
              address {
                name
                state
                city
                postalCode
                streetAddress1
                streetAddress2
                postOfficeBoxNumber
                phoneNumber
                createdBy
                modifiedBy
              }
            }
            refOFWAProcess {
              externalId
              space
              refSite{
                externalId
                space
              }
              processType
              refOFWAProcess{
              externalId
              space
              }
            }
            refOFWACategory {
              externalId
              space
              name
              refOFWAProcess{
                space
                externalId
                name
              }
            }
          }
          refOFWAProcess{
          items{
            name
            externalId
            space
          }
        }
      refSubProcess{
       externalId
        space
        name
        refSite{
          name
          externalId
          space
        }
      }
        refCorePrinciple{
        name
        externalId
        space
      }
        refProcess{
        externalId
        space
        name
      }
          refOFWAAuditSummary{
            space
            externalId
            productFamily
            locationManufacturingSite
            conclusion
            pathForward
            auditorSignature
            managerSignature
            refOFWAVendor {
              space
              externalId
              vendorNumber
              vendorName
              companyName
              contactName
              mobile
              jobTitle
              email
              corporateWebSite
              createdBy
              modifiedBy
              address {
                name
                state
                city
                postalCode
                streetAddress1
                streetAddress2
                postOfficeBoxNumber
                phoneNumber
                createdBy
                modifiedBy
                country{
                  name
                  parent{
                    description
                  }
                }
              }
            }
            createdBy
            modifiedBy
            createdTime
            lastUpdatedTime
          }
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  console.log(sdmQuery)
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    console.log(rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.listAuditSummary = async function (req, res) {
  var reqBody = req.body;

  var sdmQuery = {
    "query": `{
      list${constant.typeAuditSummary}{
       items { 
              space 
              externalId 
              productFamily
              locationManufacturingSite
               refSupplier{
               externalId
          space
        name
        number
        address{
          name
          externalId
          space
          country{
            name
            externalId
            space
          }
          state
          city
          postalCode
          streetAddress1
          streetAddress2
          postOfficeBoxNumber
          phoneNumber
          
        }
      }
              refOFWAVendor{
                vendorNumber
                vendorName
                companyName
                contactName
                mobile
                jobTitle
                email
                corporateWebSite
                externalId
                space
                address{
                  country
                  {
                    name
                    streetAddress1
                    externalId
                    space
                  }
                  state
                  name
                  city
                  externalId
                  space
                  postalCode
                }
              }
              conclusion
              pathForward
              managerSignature
              createdBy
              modifiedBy
              
          } 
          }

    }`
  }
  console.log(sdmQuery)
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    console.log(rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listExternalIdsByTableName = function (req, res) {
  var reqBody = req.body;
  var reqQuery = {
    "query": `{
            list${reqBody.table}(first:1000){
            items{
              externalId
              space
            }
          }
        }`
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.deleteInstance = async function (req, res) {

  var reqBody = req.body;
  const client = new CogniteClient({
    appId: process.env.AppID,
    baseUrl: process.env.AzureAudience,
    project: process.env.project,
    getToken: () => `${req.headers.authorization}`.split(' ')[1]
  });
  try {
    var graphURL = process.env.AzureAudience + "/api/v1/projects/" + process.env.project + "/models/instances/delete";
    var responseData = {};
    await client.post("/api/v1/projects/" + process.env.project + "/models/instances/delete",
      { data: reqBody }).then(
        result => {
          console.log("result")
          responseData = result
        }
      )
      .catch(
        error => {
          console.log("catch error", error);
          responseData = error;
        }
      )
      .finally(
        () => {
          return res.status(200).json(responseData);
        }
      );


  } catch (err) {
    console.log(err)
    return res.status(200).json(err);
  }
}


exports.listUser = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: "${reqBody.cursor}"`
  }

  var emailFilter = ``;
  if(reqBody.email){
    emailFilter = `{externalId:{eq:"${reqBody.email}"}},`
  }
  var searchUser = `query:"",`;
  if(reqBody.searchUser){
    searchUser = `query: "${reqBody.searchUser}",`
  }
   
  var sdmQuery = {
    "query": `
    {
      searchUser(${limitQ} ${searchUser} filter:{and:[
       { space:{isNull:false}},
       { active:{eq:true}},
        ${emailFilter}
       
      ]}){
        items{
          externalId
          space
          firstName
          lastName
          email
          #azureUserId
          active
         # lanId
          
        }
        pageInfo{
          hasNextPage
          hasPreviousPage
          endCursor
          startCursor
        }
      }
    }`
  }
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.USERDataModelSpace,
    "datamodel": constant.USERDataModelExternalId,
    "version": constant.USERDataModelVersion,
    "reqBody": sdmQuery
  };
  console.log(sdmQuery)
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.searchUser = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: "${reqBody.cursor}"`
  }

  var siteFilter = ``;
  if(reqBody.site){
    siteFilter = `{searchTags: {containsAny: "${reqBody.site}"}},`
  }
  var searchUser = `query:"",`;
  if(reqBody.searchUser){
    searchUser = `query: "${reqBody.searchUser}",`
  }
   
  var sdmQuery = {
    "query": `
    {
      searchUserComplement(${limitQ} ${searchUser} filter:{and:[
       { space:{isNull:false}},
      ${siteFilter}
       
      ]}){
          items {
            externalId
            userAzureAttribute {
              user {
                externalId
                space
                firstName
                lastName
                email
                active
              }
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
            endCursor
            startCursor
          }
        }
    }`
  }
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.USERDataModelSpace,
    "datamodel": constant.USERDataModelExternalId,
    "version": constant.USERDataModelVersion,
    "reqBody": sdmQuery
  };
  console.log(sdmQuery)
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listUserAzureAttribute = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: "${reqBody.cursor}"`
  }

  var emailFilter = ``;
  if(reqBody.email){
    emailFilter = `{user:{externalId:{eq:"${reqBody.email}"}}}`
  }
  var searchUser = ``;
  if(reqBody.searchUser){
    searchUser = `{ or:[{ user:{displayName:{prefix:"${reqBody.searchUser}"}} },{ user:{firstName:{prefix:"${reqBody.searchUser}"}}},{ user:{lastName:{prefix:"${reqBody.searchUser}"}}}] }`
  } 
  var sdmQuery = {
    "query": `
    {
      list${constant.typeUserAzureAttribute}(${limitQ} ${cursorQ} filter:{and:[
        {createdTime:{isNull:false}} 
        ${emailFilter}
        ${searchUser}
      ]}
      ){
        items{
          externalId
          space
          user{
            externalId
            space
            firstName
            lastName
            email
          }
          azureUserId
          lanId
        companyName
          jobTitle
          
          
        }
        pageInfo{
          hasNextPage
          hasPreviousPage
          endCursor
          startCursor
        }
      }
    }`
  }
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.USERDataModelSpace,
    "datamodel": constant.USERDataModelExternalId,
    "version": constant.USERDataModelVersion,
    "reqBody": sdmQuery
  };
  console.log(sdmQuery)
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.searchUserAzureAttribute = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var searchUser = `query:"",`;
  if(reqBody.searchUser){
    searchUser = `query: "${reqBody.searchUser}",`
  }
  var sdmQuery = {
    "query": `
    {
       search${constant.typeUserAzureAttribute}(${limitQ} ${searchUser}){
        items{
          externalId
          space
          user{
            externalId
            space
            firstName
            lastName
            email
          }
          azureUserId
          lanId
        companyName
          jobTitle
          
          
        }
        pageInfo{
          hasNextPage
          hasPreviousPage
          endCursor
          startCursor
        }
      }
    }`
  }
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.USERDataModelSpace,
    "datamodel": constant.USERDataModelExternalId,
    "version": constant.USERDataModelVersion,
    "reqBody": sdmQuery
  };
  console.log(sdmQuery)
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listScoreCard = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var auditFilter = "";
  if (reqBody.auditId) {
    auditFilter= `{refOFWAAudit:{externalId:{eq:"${reqBody.auditId}"}}} `
  }
  var summaryFilter = "";
  if (reqBody.isSummaryRecord+"".toString().length>0) {
    summaryFilter = `{isSummaryRecord:{eq:${reqBody.isSummaryRecord}}}`
  }
  var sdmQuery = {
    "query": `
    {
      list${constant.typeScoreCard}( ${cursorQ} ${limitQ} filter:{and:[{createdTime:{isNull:false}}  ${auditFilter} ${summaryFilter}]}){
        items {
          space
          externalId
          isSummaryRecord
          satisfactory
          opportunityForImprovement
          unsatisfactory
          createdBy
          modifiedBy
          createdTime
          lastUpdatedTime
        }
      }
    }`
  }
  console.log("sdmQuery === ",sdmQuery)
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
 
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    console.log(rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listApplication = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var externalId = ``;
  if(reqBody.externalId){
    externalId = `,{externalId:{eq:"${reqBody.externalId}"}}`
  }

 
  var sdmQuery = {
    "query": `
    {
      list${constant.typeApplication}( ${cursorQ} ${limitQ} filter:{and:[{createdTime:{isNull:false}} ${externalId}  ]}){
        items{
          externalId
          space
          name
          alias
          description
          url
          azureAppId
          iconUrl
          
          
        }
      }
    }`
  }
  console.log("sdmQuery === ",sdmQuery)
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
 
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    console.log(rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listAction = async function (req, res) {
  var reqBody = req.body;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var externalId = ``;
  if(reqBody.externalId){
    externalId = `,{externalId:{eq:"${reqBody.externalId}"}}`
  }
  var applicationId = ``;
  if(reqBody.applicationId){
    applicationId = `,{application:{externalId:{eq:"${reqBody.applicationId}"}}}`
  }

  var objectType = [];
  var objectTypeQ = ``;
  if(reqBody.objectType){
    
    reqBody.objectType.forEach(element => {
        objectType.push(`{objectType:{eq:"${element}"}}`)
    })
    if (objectType.length > 0) {
      objectTypeQ = `,{or:[${objectType}]}`;
    }
  }

  
  var objectType = ``;
  if(reqBody.objectType){
    objectType = `,{objectType:{eq:"${reqBody.objectType}"}}`
  }
  
  var objectExternalId = ``;
  if(reqBody.objectExternalId){
    objectExternalId = `,{objectExternalId:{eq:"${reqBody.objectExternalId}"}}`
  }
  var siteE = "";
  _.each(reqBody.reportingSite, function (eData) {
    siteE = siteE + `{externalId: {eq: "${eData}"}},`
  })
  var dateFilter = "";
  if (reqBody.startDate && reqBody.endDate) {
    dateFilter = `{and:[
      {assignmentDate:{gte:"${reqBody.startDate}"}},
      {assignmentDate:{lte:"${reqBody.endDate}"}}
    ]}`
  }

  var siteQ = ``;

  if (reqBody.reportingSite) {

    if(reqBody.reportingSite.length > 0){
      siteQ = `,{reportingSite: {or: [${siteE}]}}`;
    }
   
  }
 
  var sdmQuery = {
    "query": `
    {
      list${constant.typeAction}( ${cursorQ} ${limitQ} sort:{externalId:DESC} filter:{and:[{createdTime:{isNull:false}} ${externalId} ${applicationId} ${dateFilter} ${siteQ} ${objectExternalId} ${objectTypeQ} ]}){
        items{
          externalId
          space
          application{
            externalId
            space
            name
            description
          }
          assignedTo{
            externalId
            space
            user{
              externalId
              space
              email
              firstName
              lastName
            }
          },
          title
          description
          assignmentDate
          dueDate
          reportingSite{
            externalId
            space
            name
            description
          }
          objectType
          objectExternalId
          priority
           reportingUnit{
           externalId
           space
           name
           description
          }
         reportingLocation{
          externalId
          space
          name
          description
          }

        }
        pageInfo{
          hasNextPage
          endCursor
        }
      }
    }`
  }
  console.log("sdmQuery === ",sdmQuery)
 
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": sdmQuery
  };
 
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    console.log(rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}


exports.aggregateObservation = async function (req, res) {
  var reqBody = req.body;
  var tableName = constant.typeObservation;
  var tableVersion = constant.codeObservation;
  var mainTableSpace =   constant.DataModelSpace;
  var coreDataSpace = constant.CoreDataSpace;
 
  var unitquery;


  var siteE = [];
  _.each(reqBody.reportingSite, function (eData) {

   siteE.push({ 
    "space": coreDataSpace, 
    "externalId":eData
 })

  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })
  var locationE = [];
  _.each(reqBody.reportingLocation, function (eData) {
    locationE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })

  var repLineE = [];
  _.each(reqBody.reportingLine, function (eData) {
    repLineE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })
 
  })
  var deptE = [];
  _.each(reqBody.department, function (eData) {
    deptE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })

  var processE = [];
  _.each(reqBody.process, function (eData) {
    processE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })

  var corePrincipleE = [];
  _.each(reqBody.corePrinciple, function (eData) {
    corePrincipleE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })
  

  var siteQ = {};
  var unitQ = {};
  var repLineQ = {};
  var deparmentQ ={};
  var locationQ ={};
  var processQ ={};
  var corePrincipleQ ={};

  console.log("reqBody.process")
  console.log(reqBody.process)
  if (reqBody.process && reqBody.process.length > 0) {
    processQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refProcess" 
        ], 
        values: processE
      } 
    };
  }

  
  if (reqBody.corePrinciple && reqBody.corePrinciple.length > 0) {
    corePrincipleQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refCorePrinciple" 
        ], 
        values: corePrincipleE
      } 
    };
  }

  if (reqBody.department && reqBody.department.length > 0) {
    deparmentQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refDeparment" 
        ], 

        values: deptE
      } 
    };

  }


  if (reqBody.reportingSite && reqBody.reportingSite.length > 0) {
    siteQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refSite" 
        ], 

        values: siteE
      } 
    };

  }
  if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refUnit" 
        ], 
        
        values: unitE
      } 
    };

  }
  if (reqBody.reportingLocation && reqBody.reportingLocation.length > 0) {
    locationQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLocation" 
        ], 
        
        values: locationE
      } 
    };

  }

  if (reqBody.reportingLine && reqBody.reportingLine.length > 0) {
    repLineQ ={
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLine" 
        ], 
        
        values: repLineE
      } 
    };

  }
  var startQ = {}
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    startQ ={
      range: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "date" 
        ], 
        gte: reqBody.startDate,
    
      } 
    }
  }
  var endQ = {}
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    endQ =  {
        range: { 
          property: [ 
          mainTableSpace, 
          tableName+"/"+tableVersion, 
          "date" 
          ], 
          lte: reqBody.endDate,
       
        } 
      }
  }
    
    var isActiveQ = {};
   if (typeof reqBody.isActive === 'boolean') {
     isActiveQ = {
       equals: {
         property: [
           mainTableSpace,
           tableName + "/" + tableVersion,
           "isActive"
         ],
         value: reqBody.isActive
       }
     };
   }

 
  unitquery = { 
    and: [
    
    
    ],

  }
 

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
  if(Object.keys(locationQ).length >0){
    unitquery.and.push(locationQ);
  }
  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(repLineQ).length >0){
    unitquery.and.push(repLineQ);
  }
  
  if(Object.keys(deparmentQ).length >0){
    unitquery.and.push(deparmentQ);
  }

  if(Object.keys(processQ).length >0){
    unitquery.and.push(processQ);
  }
  if(Object.keys(corePrincipleQ).length >0){
    unitquery.and.push(corePrincipleQ);
  }
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    unitquery.and.push(startQ);
  }
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    unitquery.and.push(endQ);
  }
  if (Object.keys(isActiveQ).length > 0) {
    unitquery.and.push(isActiveQ);
  }
  
  var InstanceJsonObj = { 
    aggregates: [
      {
        count: {
               property: "externalId"
            }
      }
  ],
    view: { 
        type: "view", 
        space: mainTableSpace,
        externalId: tableName, 
        version: tableVersion,//"13327d2435b4fc" 
    }, 
    limit: 1000, 
   
    instanceType: "node" 
  }
  if(unitquery.and.length >0 ){
    InstanceJsonObj.filter = unitquery 
  }
  // if(groupByArray.length > 0){
  //   InstanceJsonObj.groupBy = groupByArray
  // }
  console.log('InstanceJsonObj',JSON.stringify(InstanceJsonObj))

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postClientClassic(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
     // return res.status(200).json(rData["data"]);
      var queryArray = [];
      var monthData = rData["data"];
      console.log('monthData',monthData)
      if(rData["data"].items.length > 0){
        async.eachSeries(rData["data"].items, function (eData2, outCb2) {
        
     
          var localObj =  {
            "count": {
              "externalId": eData2.aggregates[0].value
            },
           // "group":eData2.group?eData2.group:null
          }
          queryArray.push(localObj)
         
          outCb2(null);
        }, async function (err) {
          return res.status(200).json(queryArray);
        })
      }else{
        return res.status(200).json([]);
      }
  


    }
  });
}

exports.aggregateFieldWalk = async function (req, res) {
  var reqBody = req.body;
  var tableName = constant.typeFieldWalk;
  var tableVersion = constant.codeFieldWalk;
  var mainTableSpace =   constant.DataModelSpace;
  var coreDataSpace = constant.CoreDataSpace;
  var unitquery;

  var siteE = [];
  _.each(reqBody.reportingSite, function (eData) {

   siteE.push({ 
    "space": coreDataSpace, 
    "externalId":eData
 })

  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })
  var locationE = [];
  _.each(reqBody.reportingLocation, function (eData) {
    locationE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })

  var repLineE = [];
  _.each(reqBody.reportingLine, function (eData) {
    repLineE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })
 
  })

  var processE = [];
  _.each(reqBody.process, function (eData) {
    processE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })

  var corePrincipleE = [];
  _.each(reqBody.corePrinciple, function (eData) {
    corePrincipleE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })
  

  var siteQ = {};
  var unitQ = {};
  var repLineQ = {};
  
  var locationQ ={};
  var processQ ={};
  var corePrincipleQ ={};

  if (reqBody.process && reqBody.process.length > 0) {
    processQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refProcess" 
        ], 
        values: processE
      } 
    };
  }

  
  if (reqBody.corePrinciple && reqBody.corePrinciple.length > 0) {
    corePrincipleQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refCorePrinciple" 
        ], 
        values: corePrincipleE
      } 
    };
  }
  if (reqBody.reportingLocation && reqBody.reportingLocation.length > 0) {
    locationQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLocation" 
        ], 
        
        values: locationE
      } 
    };

  }

  if (reqBody.reportingSite && reqBody.reportingSite.length > 0) {
    siteQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refSite" 
        ], 

        values: siteE
      } 
    };

  }
  if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refUnit" 
        ], 
        
        values: unitE
      } 
    };

  }

  if (reqBody.reportingLine && reqBody.reportingLine.length > 0) {
    repLineQ ={
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLine" 
        ], 
        
        values: repLineE
      } 
    };

  }
  var startQ = {}
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    startQ ={
      range: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "date" 
        ], 
        gte: reqBody.startDate,
    
      } 
    }
  }
  var endQ = {}
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    endQ =  {
        range: { 
          property: [ 
          mainTableSpace, 
          tableName+"/"+tableVersion, 
          "date" 
          ], 
          lte: reqBody.endDate,
       
        } 
      }
  }

    var isActiveQ = {};
  if (typeof reqBody.isActive === 'boolean') {
    isActiveQ = {
      equals: {
        property: [
          mainTableSpace,
          tableName + "/" + tableVersion,
          "isActive"
        ],
        value: reqBody.isActive
      }
    };
  }
 
  unitquery = { and: [ ]}

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(repLineQ).length >0){
    unitquery.and.push(repLineQ);
  }
  if(Object.keys(locationQ).length >0){
    unitquery.and.push(locationQ);
  }
  if(Object.keys(processQ).length >0){
    unitquery.and.push(processQ);
  }
  if(Object.keys(corePrincipleQ).length >0){
    unitquery.and.push(corePrincipleQ);
  }
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    unitquery.and.push(startQ);
  }
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    unitquery.and.push(endQ);
  }
  if (Object.keys(isActiveQ).length > 0) {
    unitquery.and.push(isActiveQ);
  }
  var InstanceJsonObj = { 
    aggregates: [ { count: { property: "externalId" } } ],
    view: { 
        type: "view", 
        space: mainTableSpace,
        externalId: tableName, 
        version: tableVersion,//"13327d2435b4fc" 
    }, 
    limit: 1000, 
    instanceType: "node" 
  }
  if(unitquery.and.length >0 ){
    InstanceJsonObj.filter = unitquery 
  }
  console.log('InstanceJsonObj',JSON.stringify(InstanceJsonObj))

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postClientClassic(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      var queryArray = [];
      var monthData = rData["data"];
      console.log('monthData',monthData)
      if(rData["data"].items.length > 0){
        async.eachSeries(rData["data"].items, function (eData2, outCb2) {
          var localObj =  {
            "count": {
              "externalId": eData2.aggregates[0].value
            },
          }
          queryArray.push(localObj)
          outCb2(null);
        }, async function (err) {
          return res.status(200).json(queryArray);
        })
      }else{
        return res.status(200).json([]);
      }
    }
  });
}
exports.aggregateAudit = async function (req, res) {
  var reqBody = req.body;
  var tableName = constant.typeAudit;
  var tableVersion = constant.codeAudit;
  var mainTableSpace =   constant.DataModelSpace;
  var coreDataSpace = constant.CoreDataSpace;
  var unitquery;

  var siteE = [];
  _.each(reqBody.reportingSite, function (eData) {

   siteE.push({ 
    "space": coreDataSpace, 
    "externalId":eData
 })

  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })
  var locationE = [];
  _.each(reqBody.reportingLocation, function (eData) {
    locationE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })

  var repLineE = [];
  _.each(reqBody.reportingLine, function (eData) {
    repLineE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })
 
  })

  var processE = [];
  _.each(reqBody.process, function (eData) {
    processE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })

  var corePrincipleE = [];
  _.each(reqBody.corePrinciple, function (eData) {
    corePrincipleE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
  })
  

  var siteQ = {};
  var unitQ = {};
  var repLineQ = {};
  var locationQ ={};
  var processQ ={};
  var corePrincipleQ ={};

  if (reqBody.process && reqBody.process.length > 0) {
    processQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refProcess" 
        ], 
        values: processE
      } 
    };
  }

  
  if (reqBody.corePrinciple && reqBody.corePrinciple.length > 0) {
    corePrincipleQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refCorePrinciple" 
        ], 
        values: corePrincipleE
      } 
    };
  }

  if (reqBody.reportingSite && reqBody.reportingSite.length > 0) {
    siteQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refSite" 
        ], 

        values: siteE
      } 
    };

  }
  if (reqBody.reportingLocation && reqBody.reportingLocation.length > 0) {
    locationQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLocation" 
        ], 
        
        values: locationE
      } 
    };

  }
  if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refUnit" 
        ], 
        
        values: unitE
      } 
    };

  }

  if (reqBody.reportingLine && reqBody.reportingLine.length > 0) {
    repLineQ ={
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLine" 
        ], 
        
        values: repLineE
      } 
    };

  }
  var startQ = {}
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    startQ ={
      range: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "date" 
        ], 
        gte: reqBody.startDate,
    
      } 
    }
  }
  var endQ = {}
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    endQ =  {
        range: { 
          property: [ 
          mainTableSpace, 
          tableName+"/"+tableVersion, 
          "date" 
          ], 
          lte: reqBody.endDate,
       
        } 
      }
  }
 
  unitquery = { and: [ ]}

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(repLineQ).length >0){
    unitquery.and.push(repLineQ);
  }
  if(Object.keys(locationQ).length >0){
    unitquery.and.push(locationQ);
  }
  if(Object.keys(processQ).length >0){
    unitquery.and.push(processQ);
  }
  if(Object.keys(corePrincipleQ).length >0){
    unitquery.and.push(corePrincipleQ);
  }
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    unitquery.and.push(startQ);
  }
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    unitquery.and.push(endQ);
  }
  var InstanceJsonObj = { 
    aggregates: [ { count: { property: "externalId" } } ],
    view: { 
        type: "view", 
        space: mainTableSpace,
        externalId: tableName, 
        version: tableVersion,//"13327d2435b4fc" 
    }, 
    limit: 1000, 
    instanceType: "node" 
  }
  if(unitquery.and.length >0 ){
    InstanceJsonObj.filter = unitquery 
  }
  console.log('InstanceJsonObj',JSON.stringify(InstanceJsonObj))

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postClientClassic(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      var queryArray = [];
      var monthData = rData["data"];
      console.log('monthData',monthData)
      if(rData["data"].items.length > 0){
        async.eachSeries(rData["data"].items, function (eData2, outCb2) {
          var localObj =  {
            "count": {
              "externalId": eData2.aggregates[0].value
            },
          }
          queryArray.push(localObj)
          outCb2(null);
        }, async function (err) {
          return res.status(200).json(queryArray);
        })
      }else{
        return res.status(200).json([]);
      }
    }
  });
}
exports.aggregateAction = async function (req, res) {
  var reqBody = req.body;
  var tableName = constant.typeAction;
  var tableVersion = constant.codeAction;
  var mainTableSpace =   constant.ActionSpace;
  var coreDataSpace = constant.CoreDataSpace;
  var userDataSpace =constant.userDataSpace;
 
  var unitquery;


  var siteE = [];
  _.each(reqBody.reportingSite, function (eData) {

   siteE.push({ 
    "space": coreDataSpace, 
    "externalId":eData
 })

  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })
  var locationE = [];
  _.each(reqBody.reportingLocation, function (eData) {
    locationE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })

  var repLineE = [];
  _.each(reqBody.reportingLine, function (eData) {
    repLineE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })
 
  })
  var appE= [];
  if (reqBody.applicationId && reqBody.applicationId.length > 0) {
    appE.push({ 
      "space": userDataSpace, 
      "externalId":reqBody.applicationId
   })
  }


  var siteQ = {};
  var unitQ = {};
  var repLineQ = {};
  var locationQ ={};
//  applicationId
  var applicationIdQ = {};
  // reqBody.status application
  if (reqBody.applicationId && reqBody.applicationId.length > 0) {
   
    applicationIdQ =   {
    in: { 
      property: [ 
      mainTableSpace, 
      tableName+"/"+tableVersion, 
      "application" 
      ], 

      values: appE
    } 
  };
 
 }
  if (reqBody.reportingSite && reqBody.reportingSite.length > 0) {
    siteQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "reportingSite" 
        ], 

        values: siteE
      } 
    };

  }
  if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "reportingUnit" 
        ], 
        
        values: unitE
      } 
    };

  }
  if (reqBody.reportingLocation && reqBody.reportingLocation.length > 0) {
    locationQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "reportingLocation" 
        ], 
        
        values: locationE
      } 
    };

  }

  if (reqBody.reportingLine && reqBody.reportingLine.length > 0) {
    repLineQ ={
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLine" 
        ], 
        
        values: repLineE
      } 
    };

  }
  var startQ = {}
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    startQ ={
      range: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "assignmentDate" 
        ], 
        gte: reqBody.startDate,
    
      } 
    }
  }
  var endQ = {}
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    endQ =  {
        range: { 
          property: [ 
          mainTableSpace, 
          tableName+"/"+tableVersion, 
          "assignmentDate" 
          ], 
          lte: reqBody.endDate,
       
        } 
      }
  }

  // ObjectType filter
  var objectTypeQ = {};
  if (reqBody.objectType && reqBody.objectType.length > 0) {
    objectTypeQ = {
      in: {
        property: [
          mainTableSpace,
          tableName + "/" + tableVersion,
          "objectType"
        ],
        values: reqBody.objectType
      }
    };
  }    
 
 
  unitquery = { 
    and: [
    
    
    ],

  }
 

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(repLineQ).length >0){
    unitquery.and.push(repLineQ);
  }
  if (reqBody.applicationId && reqBody.applicationId.length > 0) {
    unitquery.and.push(applicationIdQ);
  }
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    unitquery.and.push(startQ);
  }
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    unitquery.and.push(endQ);
  }
  if(Object.keys(locationQ).length >0){
    unitquery.and.push(locationQ);
  }
  
  if (reqBody.objectType && reqBody.objectType.length > 0) {
    unitquery.and.push(objectTypeQ);
  }

  
  var InstanceJsonObj = { 
    aggregates: [
      {
        count: {
               property: "externalId"
            }
      }
  ],
    view: { 
        type: "view", 
        space: mainTableSpace,
        externalId: tableName, 
        version: tableVersion,//"13327d2435b4fc" 
    }, 
    limit: 1000, 
   
    instanceType: "node" 
  }
  if(unitquery.and.length >0 ){
    InstanceJsonObj.filter = unitquery 
  }
  // if(groupByArray.length > 0){
  //   InstanceJsonObj.groupBy = groupByArray
  // }
  console.log('InstanceJsonObj',JSON.stringify(InstanceJsonObj))

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postClientClassic(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
     // return res.status(200).json(rData["data"]);
      var queryArray = [];
      var monthData = rData["data"];
      console.log('monthData',monthData)
      if(rData["data"].items.length > 0){
        async.eachSeries(rData["data"].items, function (eData2, outCb2) {
        
     
          var localObj =  {
            "count": {
              "externalId": eData2.aggregates[0].value
            },
           // "group":eData2.group?eData2.group:null
          }
          queryArray.push(localObj)
         
          outCb2(null);
        }, async function (err) {
          return res.status(200).json(queryArray);
        })
      }else{
        return res.status(200).json([]);
      }
  


    }
  });
}
exports.aggregateChecklist = async function (req, res) {
  var reqBody = req.body;
  var tableName = constant.typeChecklist;
  var tableVersion = constant.codeChecklist;
  var userDataSpace =constant.userDataSpace;
  var mainTableSpace =   constant.DataModelSpace;
  var coreDataSpace = constant.CoreDataSpace;
 
  var unitquery;


  var siteE = [];
  _.each(reqBody.reportingSite, function (eData) {

   siteE.push({ 
    "space": coreDataSpace, 
    "externalId":eData
 })

  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })

  var repLineE = [];
  _.each(reqBody.reportingLine, function (eData) {
    repLineE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })
 
  })
 

  var siteQ = {};
  var unitQ = {};
  var repLineQ = {};

  if (reqBody.reportingSite && reqBody.reportingSite.length > 0) {
    siteQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "reportingSite" 
        ], 

        values: siteE
      } 
    };

  }
  if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refUnit" 
        ], 
        
        values: unitE
      } 
    };

  }

  if (reqBody.reportingLine && reqBody.reportingLine.length > 0) {
    repLineQ ={
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refReportingLine" 
        ], 
        
        values: repLineE
      } 
    };

  }
  var startQ = {}
  if (reqBody.startDate && reqBody.startDate.length > 0) {
    startQ ={
      range: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "assignmentDate" 
        ], 
        gte: reqBody.startDate,
    
      } 
    }
  }
  var endQ = {}
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    endQ =  {
        range: { 
          property: [ 
          mainTableSpace, 
          tableName+"/"+tableVersion, 
          "assignmentDate" 
          ], 
          lte: reqBody.endDate,
       
        } 
      }
  }
    

  var groupByArray =reqBody.groupBy
 
 
  unitquery = { 
    and: [
    
    {
      equals: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "isUnSafe" 
        ], 
        
        value: true
      } 
    }
    
    ],

  }
 

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(repLineQ).length >0){
    unitquery.and.push(repLineQ);
  }

  if (reqBody.startDate && reqBody.startDate.length > 0) {
    unitquery.and.push(startQ);
  }
  if (reqBody.endDate && reqBody.endDate.length > 0) {
    unitquery.and.push(endQ);
  }

  // externalId
  // isSafe
  var InstanceJsonObj = { 
    aggregates: [
      {
        count: {
               property: "externalId"
            }
      },
      // {
      //   count: {
      //          property: "isSafe"
      //       }
      // },
      {
        count: {
               property: "isUnSafe"
            }
      }
  ],
    view: { 
        type: "view", 
        space: mainTableSpace,
        externalId: tableName, 
        version: tableVersion,//"13327d2435b4fc" 
    }, 
    limit: 1000, 
    groupBy: [
     // "isSafe",
      "isUnSafe",
      "refOFWACategory"
      ],
   
    instanceType: "node" 
  }
  if(unitquery.and.length >0 ){
    InstanceJsonObj.filter = unitquery 
  }
  // if(groupByArray.length > 0){
  //   InstanceJsonObj.groupBy = groupByArray
  // }
  console.log('InstanceJsonObj',JSON.stringify(InstanceJsonObj))

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
  queryObj["authorization"] = req.headers.authorization;
  postClientClassic(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
     return res.status(200).json(rData["data"]);
  


    }
  });
}
exports.aggregateFunctionalLocation = function (req, res) {
  var reqBody = req.body;
  var searchFilter = `{name: {prefix: "${reqBody.search ? reqBody.search : ""}"}}`;

  var unitFilter = "";
  if (reqBody.unit && reqBody.unit.length > 0 && reqBody.unit != "All") {
    unitFilter = `,{functionalLocation: {prefix: "${reqBody.unit}"}}`;
  }
  var siteFilter = "";
  if (reqBody.site && reqBody.site.length > 0 && reqBody.site != "All") {
    siteFilter = `,{functionalLocation: {prefix: "${reqBody.site}"}}`;
  }

  var reqQuery =
  {
    "query": `
        {
          aggregate${constant.typeFunctionalLocation} (filter: {and: [${searchFilter}${unitFilter}${siteFilter}]}) {
            items {
              count {
                externalId
              }
            }
          }
        }`,
    "variables": {}
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listFunctionalLocation = function (req, res) {

  var reqBody = req.body;
  var searchFilter = `{name: {prefix: "${reqBody.search ? reqBody.search : ""}"}}`;
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var limitQ = `first: 1000`;
  if (reqBody.limit) {
    limitQ = `first: ${reqBody.limit}`
  }
  var cursorQ = "";
  if (reqBody.cursor) {
    cursorQ = `after: ${reqBody.cursor}`
  }
  var unitFilter = "";
  if (reqBody.unit && reqBody.unit.length > 0 && reqBody.unit != "All") {
    unitFilter = `,{functionalLocation: {prefix: "${reqBody.unit}"}}`;
  }
  var siteFilter = "";
  if (reqBody.site && reqBody.site.length > 0 && reqBody.site != "All") {
    siteFilter = `,{functionalLocation: {prefix: "${reqBody.site}"}}`;
  }

  var siteSpace = "";
  if(req.body.siteCode){
    siteSpace = ` {space:{eq:"SAP-${req.body.siteCode}-ALL-DAT"}}`
  }
  var externalIdQ = "";
  if (reqBody.externalId && reqBody.externalId.length > 0) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }

  var functionalLocationName = req.body.functionalLocationName;
  var functionalLocationNameQ = "";
  if (functionalLocationName) {
    functionalLocationNameQ = `{description:{prefix:"${functionalLocationName}"}},`;
  }

  var siteSpace = "";
  if(req.body.siteCode){
    siteSpace = ` {space:{eq:"SAP-${req.body.siteCode}-ALL-DAT"}}`
  }
  var externalIdQ = "";
  if (reqBody.externalId && reqBody.externalId.length > 0) {
    externalIdQ = `{externalId:{in:["${reqBody.externalId}"]}}`;
  }

  var functionalLocationName = req.body.functionalLocationName;
  var functionalLocationNameQ = "";
  if (functionalLocationName) {
    functionalLocationNameQ = `{description:{prefix:"${functionalLocationName}"}},`;
  }
  var reqQuery = {
    "query": `{
          list${constant.typeFunctionalLocation}(
            ${limitQ}, ${cursorQ},
            filter: {and: [${searchFilter}${siteFilter}${unitFilter}
            ${siteSpace} ${externalIdQ} ${functionalLocationNameQ} ]}
            ${limitQ}, ${cursorQ},
            filter: {and: [${searchFilter}${siteFilter}${unitFilter}
            ${siteSpace} ${externalIdQ} ${functionalLocationNameQ} ]}
          ) {
            pageInfo {
              endCursor
              hasNextPage
              hasPreviousPage
              startCursor
            }
            items {
              space
              externalId
              externalId
              name
              description
              description
              changedBy
              changedDate
              companyCode
              companyCodeDescription
              companyCodeDescription
              costCenter
              costCenterDescription
              costCenterDescription
              createdBy
              createdDate
              fdmLastTimeUpdated
              fdmLastTimeUpdated
              functionalLocation
              functionalLocationStatus
              statusCode
              functionalLocationStatus
              statusCode
              maintenancePlant
              maintenancePlantDescription
              maintenancePlantDescription
              plannerGroup
              plannerGroupDescription
              planningPlant
              planningPlantDescription
              plannerGroupDescription
              planningPlant
              planningPlantDescription
              plantSection
              plantSectionDescription
              plantSectionDescription
            }
          }
        }`,
    "variables": {}
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listFunctionalLocationCursor = function (req, res) {

  var reqBody = req.body;
  var searchFilter = `{name: {prefix: "${reqBody.search ? reqBody.search : ""}"}}`;

  var unitFilter = "";
  if (reqBody.unit && reqBody.unit.length > 0 && reqBody.unit != "All") {
    unitFilter = `,{functionalLocation: {prefix: "${reqBody.unit}"}}`;
  }
  var siteFilter = "";
  if (reqBody.site && reqBody.site.length > 0 && reqBody.site != "All") {
    siteFilter = `,{functionalLocation: {prefix: "${reqBody.site}"}}`;
  }
  var reqQuery = {
    "query": `
  {
    list${constant.typeFunctionalLocation}(first: ${reqBody.limit},after:"${reqBody.cursor}",
    filter:{and: [${searchFilter}${siteFilter}${unitFilter}]}) {
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      items {
        externalId
        space
      name
      createdTime
      changedBy
      changedDate
      companyCode
      costCenter
      createdBy
      createdDate
      description
      functionalLocation
      maintenancePlanningPlant
      maintenancePlant
      plannerGroup
      plantSection
      functionalLocationStatus
      parent{
        externalId
        space
      }
      }
    }
  }
  `,
    "variables": {}
  }

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.aggregateEquipment = function (req, res) {

  var reqBody = req.body;
  var search = "";
  if (reqBody.search && reqBody.search.length > 0) {
    search = reqBody.search;
  }
  var reqQuery = {
    "query": `
        {
          aggregate${constant.typeEquipment}(filter: {name: {prefix: "${search}"}}) {
            items {
              count {
                externalId
              }
            }
          }
        }`,
    "variables": {}
  }

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}

exports.listEquipment = function (req, res) {

  var reqBody = req.body;
  var search = "";
  if (reqBody.search && reqBody.search.length > 0) {
    search = reqBody.search;
  }
  var unit = "";
  if (reqBody.unit && reqBody.unit.length > 0 && reqBody.unit != "All") {
    unit = reqBody.unit;
  }
  var reqQuery = {
    "query": `{
          list${constant.typeEquipment}(
            first: ${reqBody.limit},
            filter: {and : [{functionalLocationParent: {name: {prefix: "${unit}"}  }},{name: {prefix: "${search}"}}]}
          ) {
            pageInfo {
              endCursor
              hasNextPage
              hasPreviousPage
              startCursor
            }
            items {
              externalId
              space
              name
              description
              actualInstallationDate
              actualPurchaseDate
              actualStartupDate
              catalogProfile
              changedBy
              changedDate
              companyCode
              costCenter
              createdBy
              createdDate
              equipmentSystemStatus
              functionalLocationDescription
              location
              maintenancePlanningPlant
              maintenancePlant
              manufacturerCompany
              manufacturerSerialNumber
              modelNumber
              plannerGroup
              plantSection
              price
              priceCurrencyKey
              room
              sortField
              techIdNumber
              techObjectAbcIndicator
              techObjectType
              warrantyEndDate
              workCenter
              createdTime
              functionalLocationParent {
                externalId
                space
              }
            }
          }
        }`,
    "variables": {}
  }

  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.listEquipmentCursor = function (req, res) {

  var reqBody = req.body;
  var search = "";
  if (reqBody.search && reqBody.search.length > 0) {
    search = reqBody.search;
  }
  var unit = "";
  if (reqBody.unit && reqBody.unit.length > 0 && reqBody.unit != "All") {
    unit = reqBody.unit;
  }
  var reqQuery = {
    "query": `
      {
        list${constant.typeEquipment}(first: ${reqBody.limit},after:"${reqBody.cursor}",
        filter:{and : [{functionalLocationParent: {name: {prefix: "${unit}"}  }},{name: {prefix: "${search}"}}]}) {
          pageInfo {
            endCursor
            hasNextPage
            hasPreviousPage
            startCursor
          }
          items {
                externalId
                space
                name
                description
                actualInstallationDate
                actualPurchaseDate
                actualStartupDate
                catalogProfile
                changedBy
                changedDate
                companyCode
                costCenter
                createdBy
                createdDate
                equipmentSystemStatus
                functionalLocationDescription
                location
                maintenancePlanningPlant
                maintenancePlant
                manufacturerCompany
                manufacturerSerialNumber
                modelNumber
                plannerGroup
                plantSection
                price
                priceCurrencyKey
                room
                sortField
                techIdNumber
                techObjectAbcIndicator
                techObjectType
                warrantyEndDate
                workCenter
                createdTime
                functionalLocationParent {
                    externalId
                    space
                }
          }
        }
      }
      `,
    "variables": {}
  }
  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": reqQuery
  };
  queryObj["authorization"] = req.headers.authorization;
  postGraphql(queryObj, function (error, rData) {
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      return res.status(200).json(rData["data"]);
    }
  });
}
exports.aggregateObservationOFWA = function (req, res) {
  var reqBody = req.body;
  console.log(req.body)
  var siteQ = {};
  var tableName = constant.typeObservation;
  var tableVersion = constant.codeObservation;
  var mainTableSpace = constant.DataModelSpace;
  var coreDataSpace = constant.CoreDataSpace;
  var unitquery = { and:[ ] } 
  if(reqBody.startDate){
    var startDate = new Date(reqBody.startDate);
    // const startMonth = ("0" + (startDate.getMonth() + 1)).slice(-2);
    // const startDay = ("0" + startDate.getDate()).slice(-2);
    // var mainStartDate = startDate.getFullYear() + "-" + startMonth + "-" + startDay;
    unitquery.and.push({
      range: {
        property: [
        mainTableSpace,
        tableName+"/"+tableVersion,
        // "startTime"
        "date"
        ],
        // gte: mainStartDate,
        gte: startDate,
      }
    })
  }
  if(reqBody.endDate){
    var endDate = new Date(reqBody.endDate);
    // const endMonth = ("0" + (endDate.getMonth() + 1)).slice(-2);
    // const endDay = ("0" + endDate.getDate()).slice(-2);
    // var mainEndDate = endDate.getFullYear() + "-" + endMonth + "-" + endDay;
    unitquery.and.push( {
       range: {
        property: [
        mainTableSpace,
        tableName+"/"+tableVersion,
        // "endTime"
        "date"
        ],
        // lte: mainEndDate,
        lte: endDate,
      }
    },)
   // qEndDate = `{activitydate:{lte:"${mainEndDate}"}}`;
  }
  var siteE = [];
  _.each(reqBody.site, function (eData) {
 
   siteE.push({
    "space": coreDataSpace,
    "externalId":eData
 })
 
  })
  var unitE = [];
  _.each(reqBody.reportingUnit, function (eData) {
    unitE.push({ 
      "space": coreDataSpace, 
      "externalId":eData
   })

  })
  var deptE = [];
  _.each(reqBody.department, function (eData) {
    deptE.push({ 
      "space": eData.space, 
      "externalId":eData.externalId
   })
 
  })
  var deparmentQ ={};

  if (reqBody.department && reqBody.department.length > 0) {
    deparmentQ =   {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refDeparment" 
        ], 

        values: deptE
      } 
    };

  }
  
  if (reqBody.site.length > 0) {
    siteQ =   {
      in: {
        property: [
        mainTableSpace,
        tableName+"/"+tableVersion,
        "refSite"
        ],
 
        values: siteE
      }
    };
 
  }
  
  var unitQ = {}; 	
   if (reqBody.reportingUnit && reqBody.reportingUnit.length > 0) {
    unitQ = {
      in: { 
        property: [ 
        mainTableSpace, 
        tableName+"/"+tableVersion, 
        "refUnit" 
        ], 
        
        values: unitE
      } 
    };

  }

  if(Object.keys(siteQ).length >0){
    unitquery.and.push(siteQ);
  }
 

  if(Object.keys(unitQ).length >0){
    unitquery.and.push(unitQ);
  }
  if(Object.keys(deparmentQ).length >0){
    unitquery.and.push(deparmentQ);
  }
  /*
  var currentDate = new Date();
  const startMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
  const startDay = ("0" + currentDate.getDate()).slice(-2);
  var currentStartDate = currentDate.getFullYear() + "-" + startMonth + "-" + startDay+"T00:00:00.000Z";
  var unitquery = {

  }
  if (reqBody.startDate) {
    var startDate = new Date(reqBody.startDate);
    const startMonth = ("0" + (startDate.getMonth() + 1)).slice(-2);
    const startDay = ("0" + startDate.getDate()).slice(-2);
    var mainStartDate = startDate.getFullYear() + "-" + startMonth + "-" + startDay;
    unitquery.and.push({
      equals: {
        property: [
          mainTableSpace,
          tableName + "/" + tableVersion,
          "year"
        ],
        value: Year,
      }
    })
  }
  */
  /* if (reqBody.endDate) {
    var endDate = new Date(reqBody.endDate);
    const endMonth = ("0" + (endDate.getMonth() + 1)).slice(-2);
    const endDay = ("0" + endDate.getDate()).slice(-2);
    var mainEndDate = endDate.getFullYear() + "-" + endMonth + "-" + endDay;
   
    unitquery.and.push({
      range: {
        property: [
          mainTableSpace,
          tableName + "/" + tableVersion,
          "createdOn"
        ],
        lte: mainEndDate,
      }
    },)
   
  } */
 
 
  //if (Object.keys(siteQ).length > 0) {
  //  unitquery.and.push(siteQ);
  //}
  // if (Object.keys(unitQ).length > 0) {
  //   unitquery.and.push(unitQ);
  // }
  // if(Object.keys(regionQ).length >0){
  //   unitquery.and.push(regionQ);
  // }
  // if(Object.keys(countryQ).length >0){
  //   unitquery.and.push(countryQ);
  // }

  //if(Object.keys(businessLineQ).length >0){
  //  unitquery.and.push(businessLineQ);
  //}
 
  var InstanceJsonObj = {
    aggregates: [
      {
        count: {
          property: "description"
        }
      }
    ],
 
    view: {
      type: "view",
      space: mainTableSpace,
      externalId: tableName,
      version: tableVersion,//"13327d2435b4fc"
    },
    limit: 1000,
    filter: unitquery,
    instanceType: "node"
  }
 
  if (reqBody.groupBy && reqBody.groupBy.length > 0) {
    InstanceJsonObj["groupBy"] = [reqBody.groupBy];
  }
 
  console.log('InstanceJsonObj', JSON.stringify(InstanceJsonObj))


  var queryObj = {
    "project": process.env.project,
    "space": constant.DataModelSpace,
    "datamodel": constant.DataModelExternalId,
    "version": constant.DataModelVersion,
    "reqBody": InstanceJsonObj
  };
 
  queryObj["authorization"] = req.headers.authorization;
  console.log(InstanceJsonObj,queryObj )
  postClientClassic(queryObj, function (error, rData) {
    console.log(error,rData["data"])
    if (error) {
      return res.status(200).json(rData["data"]);
    } else {
      //return res.status(200).json(rData["data"]);
     
      if (req.body.apiType && req.body.apiType == "Graph") {
        getChartData(req,rData["data"].items,function (chartData) {
          return res.status(200).json({ x: chartData.x, y: chartData.y,"chartData":rData["data"].items });
        });
        } else {
          var total = 0;
          var ans={}
          var data= rData["data"].items
          /* for( data in rData["data"].items){
              ans[data.aggregates[0].value] = data.aggregates[0].value
          } */
          data.forEach(element => {
              ans[element.group.refReportingLocation.externalId]=element.aggregates[0].value
          });
         

          return res.status(200).json({ ans });
        }
         
    }
  });
  }

 
  exports.listSetting = async function (req, res) {
    var reqBody = req.body;
    var limitQ = `first: 1000`;
    if (reqBody.limit) {
      limitQ = `first: ${reqBody.limit}`
    }
    var cursorQ = "";
    if (reqBody.cursor) {
      cursorQ = `after: ${reqBody.cursor}`
    }
 
    var siteQ = "";
    if (reqBody.sites && reqBody.sites.length > 0) {
      siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
    }
  
    var sdmQuery = {
      "query": `{
        list${constant.typeSetting}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} ${siteQ}]}){
          items {
            externalId
            space
            dateFormat
            timeFormat
            applicationName
            contractors
            hourFormat
            craft
            refSite{
              externalId
              space
              description
              siteCode
            }
            createdBy
            modifiedBy
          }
        }
      }`
    }
    var queryObj = {
      "project": process.env.project,
      "space": constant.DataModelSpace,
      "datamodel": constant.DataModelExternalId,
      "version": constant.DataModelVersion,
      "reqBody": sdmQuery
    };
    queryObj["authorization"] = req.headers.authorization;
    postGraphql(queryObj, function (error, rData) {
      if (error) {
        return res.status(200).json(rData["data"]);
      } else {
        return res.status(200).json(rData["data"]);
      }
    });
  }

  exports.listWorkOrderHeader = async function (req, res) {
    var reqBody = req.body;
    var limitQ = `first: 1000`;
    if (reqBody.limit) {
      limitQ = `first: ${reqBody.limit}`
    }
    var cursorQ = "";
    if (reqBody.cursor) {
      cursorQ = `after: ${reqBody.cursor}`
    }
 
    var siteQ = "";
    if (reqBody.siteCode && reqBody.siteCode.length > 0) {
      siteQ = `{space:{eq:"SAP-${reqBody.siteCode}-ALL-DAT"}}`;
    }
  
    var workOrderNumberQ = "";
    if (reqBody.workOrderNumber) {
      workOrderNumberQ = `{number:{prefix:"${reqBody.workOrderNumber}"}},`;
    }

    var sdmQuery = {
      "query": `{
        list${constant.typeWorkOrderHeader}(${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} 
        ${siteQ} ${workOrderNumberQ}]}){
          items {
            space
            externalId
            name
            description
            notificationNumber
            number
          }
        }
      }`
    }
    console.log(sdmQuery)
    var queryObj = {
      "project": process.env.project,
      "space": constant.MaintenanceDataModelSpace,
      "datamodel": constant.MaintenanceDataModelExternalId,
      "version": constant.MaintenanceDataModelVersion,
      "reqBody": sdmQuery
    };
    queryObj["authorization"] = req.headers.authorization;
    postGraphql(queryObj, function (error, rData) {
      if (error) {
        return res.status(200).json(rData["data"]);
      } else {
        return res.status(200).json(rData["data"]);
      }
    });
  }

  exports.getProcessData = async function (req, res) {
    var reqBody = req.body;
    var limitQ = `first: 1000`;
    if (reqBody.limit) {
      limitQ = `first: ${reqBody.limit}`
    }
    var cursorQ = "";
    if (reqBody.cursor) {
      cursorQ = `after: ${reqBody.cursor}`
    }
 
    var siteQ = "";
    if (reqBody.sites && reqBody.sites.length > 0) {
      siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
    }


    var processTypeQ = "";
    if (reqBody.processType && reqBody.processType.length > 0) {
      processTypeQ = `{processType:{in:${JSON.stringify(reqBody.processType)}}}`;
    }
  
    var sdmQuery = {
      "query": `{
        list${constant.typeProcess}(sort:{externalId:DESC} ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} 
        ${processTypeQ} ${siteQ}]}){
          items {
            space
            externalId
            name
            description
            sequence
            processType
            iconImage
            createdBy
            modifiedBy
            isActive
            refSite{
              externalId
              space
            }
            refOFWAProcess{
              space
              externalId
            }
          }
          pageInfo{
            hasNextPage
            endCursor
          }
        }
      }`
    }
    var queryObj = {
      "project": process.env.project,
      "space": constant.DataModelSpace,
      "datamodel": constant.DataModelExternalId,
      "version": constant.DataModelVersion,
      "reqBody": sdmQuery
    };
    queryObj["authorization"] = req.headers.authorization;
    postGraphql(queryObj, function (error, rData) {
      if (error) {
        return res.status(200).json(rData["data"]);
      } else {
        return res.status(200).json(rData["data"]);
      }
    });
  }

  exports.listUserRoleSite = async function (req, res) {
    var reqBody = req.body;
    var limitQ = `first: 1000`;
    if (reqBody.limit) {
      limitQ = `first: ${reqBody.limit}`
    }
    var cursorQ = "";
    if (reqBody.cursor) {
      cursorQ = `after: "${reqBody.cursor}"`
    }
  

    var siteQ = "";
    if (reqBody.sites && reqBody.sites.length > 0) {
      siteQ = `{role: {site: {externalId: {in:${JSON.stringify(reqBody.sites)}}}}}`;
    }

    var sdmQuery = {
      "query": `
      {
        list${constant.typeUserRoleSite}(${limitQ} ${cursorQ} filter:{and:[
          {role: {roleCategory: {externalId: {eq: "RoleSite"}}}}
          ${siteQ}
        ]}
        ){
          items{
            externalId
            space
            usersComplements(
              first: 1000
              filter: {userAzureAttribute: {user: {active: {eq: true}}}}
            ) {
              items {
                externalId
                space
                userAzureAttribute {
                  externalId
                  space
                  azureUserId
                  user {
                    externalId
                    space
                    email
                    active
                    externalId
                    displayName
                    firstName
                    lastName
                  }
                }
              }
            }
            role {
              externalId
              name
            }
            reportingSite {
              externalId
              name
            }
          }
          pageInfo{
            hasNextPage
            hasPreviousPage
            endCursor
            startCursor
          }
        }
      }`
    }
   
    var queryObj = {
      "project": process.env.project,
      "space": constant.USERDataModelSpace,
      "datamodel": constant.USERDataModelExternalId,
      "version": constant.USERDataModelVersion,
      "reqBody": sdmQuery
    };
    console.log(sdmQuery)
    queryObj["authorization"] = req.headers.authorization;
    postGraphql(queryObj, function (error, rData) {
      if (error) {
        return res.status(200).json(rData["data"]);
      } else {
        return res.status(200).json(rData["data"]);
      }
    });
  }
  exports.getDataSetId = async function (req, res) {
    superagent
    .post(process.env.AzureAudience+"/api/v1/projects/"+process.env.project+"/datasets/byids")
    .send(req.body)
    .set("Content-Type", "application/json")
    .set("Authorization", `${req.headers.authorization}`)
    .end((error, resp) => {
        console.log(resp.body)
        return res.status(200).json(resp.body);
    });
  }

  exports.listCompExAsset = async function (req, res) {
    var reqBody = req.body;
    var limitQ = `first: 1000`;
    if (reqBody.limit) {
      limitQ = `first: ${reqBody.limit}`
    }
    var cursorQ = "";
    if (reqBody.cursor) {
      cursorQ = `after: ${reqBody.cursor}`
    }
    var dateFilter = "";
    
    var siteQ = "";
    if (reqBody.sites && reqBody.sites.length > 0) {
      siteQ = `{refSite:{externalId:{in:${JSON.stringify(reqBody.sites)}}}}`;
    }
    
    var searchQ = "";
    if (reqBody.search && reqBody.search.length > 0) {
      searchQ = `{or:[ {itemNo:{prefix:${JSON.stringify(reqBody.search)}}}
          {subTagNumber:{prefix:${JSON.stringify(reqBody.search)}}}]}`;
    }
  
    var sdmQuery = {
      "query": `{
        list${constant.typeCompExAsset}(sort:[{externalId:DESC}]  ${limitQ} ${cursorQ}  filter:{and:[{createdTime:{isNull:false}} 
        ${searchQ} ${siteQ}]}){
          pageInfo{
            hasNextPage
            endCursor
          }
          items {
            space
            externalId
            itemNo
            subTagNumber
            description
            itemType
            certificateNo
            areaClassification
            atexCategoryArea
            equipmentGroupArea
            manufacturer
            manufacturerPartNo
            serialNo
            rfID
            drawingNumber
            barcode
            isActive
            associateDevice1
            associateDevice2
            deviceImage {
              id
              externalId
              name
              directory
              source
              mimeType
              metadata
              dataSetId
              downloadLink {
                downloadUrl
              }
            }
            deviceType
            zone
            exEex
            exClass
            gasGroup
            temperatureClass
            equipmentGroupDevice
            eplDevice
            atexCategoryDevice
            refFunctionalLocation{
              externalId
              space
              description
              name
            }
            refSite {
              externalId
              space
              description
            }
            
            createdBy
            modifiedBy
            createdTime
            lastUpdatedTime
          }
          pageInfo{
            hasNextPage
            hasPreviousPage
            endCursor
            startCursor
          }
        }
      }`
    }
    var queryObj = {
      "project": process.env.project,
      "space": constant.DataModelSpace,
      "datamodel": constant.DataModelExternalId,
      "version": constant.DataModelVersion,
      "reqBody": sdmQuery
    };
    queryObj["authorization"] = req.headers.authorization;
    postGraphql(queryObj, function (error, rData) {
      if (error) {
        return res.status(200).json(rData["data"]);
      } else {
        return res.status(200).json(rData["data"]);
      }
    });
  }