{"name": "bbso-bl", "version": "1.1.0", "description": "websocket", "main": "server.js", "scripts": {"start": "nodemon --max-old-space-size=8192 server.js"}, "author": "", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.2.0", "@cognite/sdk": "^8.2.0", "@swimlane/ngx-datatable": "^18.0.0", "async": "^3.2.0", "aws-sdk": "^2.761.0", "axios": "^0.20.0", "body-parser": "^1.19.0", "composable-middleware": "^0.3.0", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dayjs": "^1.11.3", "express": "^4.18.1", "express-joi-validation": "^5.0.0", "express-rate-limit": "^6.6.0", "file-extension": "^4.0.5", "fs": "^0.0.1-security", "googleapis": "^65.0.0", "joi": "^17.3.0", "json-to-graphql-query": "^2.1.0", "lodash": "^4.17.20", "moment": "^2.29.4", "mongoose": "^5.10.15", "monkeys-referrer": "^1.0.1", "morgan": "^1.10.0", "multer": "^1.4.4", "node-fetch": "^2.6.11", "node-rtsp-stream-jsmpeg": "0.0.2", "node-schedule": "^1.3.2", "node-vault": "^0.9.22", "nodejs-unique-numeric-id-generator": "^1.0.1", "nodemailer": "^4.7.0", "nodemon": "^2.0.19", "open": "^9.1.0", "passport": "^0.6.0", "passport-azure-ad": "^4.3.4", "pg": "^8.6.0", "qrcode": "^1.5.0", "random-location": "^1.1.3", "redis": "^4.3.1", "request": "^2.88.2", "rtsp-ffmpeg": "0.0.15", "socket.io": "^4.4.1", "superagent": "^8.0.9", "swagger-autogen": "^2.23.5", "swagger-ui-express": "^4.5.0", "uuid": "^8.3.2", "xml2js": "^0.4.23"}}