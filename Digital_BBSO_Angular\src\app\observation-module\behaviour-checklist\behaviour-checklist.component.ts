import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Inject, Input, NgZone, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {  trigger, state, style, transition, animate, sequence } from '@angular/animations'; 
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { MsalService } from '@azure/msal-angular';
import { CogniteAuthentication, CogniteClient } from '@cognite/sdk';
import { TokenService } from 'src/app/services/token.service';
import { DomSanitizer, SafeHtml, SafeUrl } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ActionPopupComponent } from '../action-popup/action-popup.component';
import { MatStepper } from '@angular/material/stepper';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

/**
 * Food data with nested structure.
 * Each node has a name and an optional list of children.
 */


/** Flat node with expandable and level information */
interface ExampleFlatNode {
  expandable: boolean;
  bold: boolean;
  name: string;
  level: number;
  guidelineDocument:object;
}

export interface ImageItem {
  image: SafeUrl; 
}


import { MatDateFormats } from '@angular/material/core';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-behaviour-checklist',
  templateUrl: './behaviour-checklist.component.html',
  styleUrls: ['./behaviour-checklist.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},]
  
})
export class BehaviourChecklistComponent implements OnInit {

  userbehalf:any;
  userbehalf2:any;
  observedForm: FormGroup;
  observedForm2: FormGroup;
  imageSrcs: string[] = [];
  showSuccessPopup = false;
  isSuccess: boolean = true;
  showFailurePopup = false;
  selectedCategory: any = 1;
  subTaskSelected = []
  selectedCategories: any[] = [];
  lastSelectedCategory: any; 
  isEditMode: boolean = true;
  @Input() process: any;
  @Input() corePrinciple: any;
  @Input() subProcess: any;
  @Input() observation: any;
  @Input() scheduleDetails:any;
  fileUP: any;
  client: CogniteClient;
  timeFormat = "";
  hourFormat = "";
  dateFromat = "";
  maxImages: number = 50;
  maxMedia = 50;
  showOverlayIndex: number | null = null;
  viewedImageIndex: number | null = null;
  showModal = false;
  modalImage = '';
  evidenceObj: any[] = [];
  images: ImageItem[] = [];
  videos: any[] = [];
  viewedVideoIndex: number | null = null;

  categorycount: number=0;
  labels = {}
 


  private _transformer = (node: any, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      bold: node.bold,
      isCategory:node.isCategory,
      id: node.id,
      question: node.question,
      sequence: node.sequence,
      level: level,
      formGroup: node.formGroup,
      checkListId: node.externalId,
      checkListSpace: node.space,
      guidelineDocument:node.guidelineDocument,
      isDuplicatCategoryName:node.isDuplicatCategoryName
    };
  };

  treeControl = new FlatTreeControl<any>(
    node => node.level,

    node => node.expandable,
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children,
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();
  feedBackEnable: boolean = false;


  checkListArray = [];

  incNum = 1

  urgencyList: any[] = [];
  selectedUrgency:any = "Medium";
  tabsListArr = []
  categoryList: any[];
  selectedSubCategory: any;
  subCategoryList: any[];
  @ViewChild('treeExpand') treeExpand;


  reportingLocationControl: FormControl = new FormControl("");
  reportingLocationList = [];
  filteredReportingLocationList = this.reportingLocationList.slice();

  craftList = [];
  filteredCraftList = this.craftList.slice();
  contractorList2 = [];
  filteredContractorList2 = this.contractorList2.slice();

  craftList2 = [];
  filteredCraftList2 = this.craftList2.slice();
  departmentList = [];
  filteredDepartmentList = this.departmentList.slice();

  behalfList = [];
  behalfList2 = [];
  filteredBehalfList = this.behalfList.slice();
  filteredBehalfList2 = this.behalfList2.slice();
  filteredUnitList1: any;
  matchingUnitList: any;
  contractorList = [];
  filteredContractorList = this.contractorList.slice();
  selectedRegion: any;
  selectedCountry: any;
  selectedSite: any;
  selectedUnit: any;
  selectedReportingLine: any;
  selectedBusinessLine: any;
  selectedReportingLocation:any;
  questionList: any = [];
  // evidenceObj: any = [];
  checklistSubCategoryArray: any = [];
  checklistCategoryArray: any = [];
  imageSrc: SafeUrl;
  checklistAnswer: any = [];

  loaderFlag: Boolean;
  isView: boolean;
  processList:any=[];

  shiftList = [];
  filteredshiftList = this.shiftList.slice();


  unitControl: FormControl = new FormControl("");
  unitList = [];
  filteredUnitList = [];
  filterFlag: string;
  tempLocation = [];
  fl_searchVal: any;
  langChangeSubscription: Subscription;
  configDetail: any;
  obRes: any;
  userEmail: any;
  defaultItem: any;
  monthControl: FormControl = new FormControl("");
  monthList = [
    { name: 'Jan', value: 'january' },
    { name: 'Feb', value: 'february' },
    { name: 'Mar', value: 'march' },
    { name: 'Apr', value: 'april' },
    { name: 'may', value: 'may'},
    { name: 'Jun', value: 'june' },
    { name: 'Jul', value: 'july' },
    { name: 'Aug', value: 'august'},
    { name: 'Sep', value: 'september' },
    { name: 'october', value: 'october' },
    { name: 'Nov', value: 'november' },
    { name: 'Dec', value: 'december' }
  ];
  filteredMonthList = this.monthList.slice();

  weekControl: FormControl = new FormControl("");
  weekList = [
    { key: 1, name: 'week1', value: "Week 1" },
    { key: 2,  name: 'week2', value: 'Week 2' },
    { key: 3,  name: 'week3', value: 'Week 3' },
    { key: 4,  name: 'week4', value: 'Week 4' },
    { key: 5,  name: 'week5', value: 'Week 5'},
  
  ];
  filteredWeekList = this.weekList.slice();
  monthWeekBool: boolean = true;
  newConfigDetail: { safe: any; notsafe: any; notobserved: any; };
  loaderCount = 0;
  matchingUnits: any = [];
  editEquipment: any[] =[];
  dataSetImageId: any;
  actionPopupResponse: any;
  showSafe: any=true;
  showNotSafe: any=true;
  showNotObserved: any=true;
  showInjurypotential: any=true;
  isSafeNotesMandatory: any;
  isNotSafeNotesMandatory: any;
  isNotObservedNotesMandatory: any;
  checkboxcss: any= '100';
  notescss: any= '24';
  notescsschild: any= '25';
  nonotobserveredcss: string='48px';
  outercheckboxcss: string='25';
  outercheckboxcsschild: string='30';
  zeroflex: string;
  safeFxFlex: string='33';
  notSafeFxFlex: string='33';
  notObservedFxFlex: string='33';
  marginLeft: string='28px';
  z: number;
  workOrderNumberList: any;
  filteredWorkOrderNumberList: any;
  processConfig: any;
  colorList = [];
  selectedColor: string = ''; // Holds the selected color value
  selectedColorLabel: string = ''; // Holds the label of the selected color
  notificationGroupEnable: any;
  notificationGroup: any;

  updateBackgroundColor(): void {
    const selected = this.colorList.find((color) => color.value === this.selectedColor);
    this.selectedColorLabel = selected ? selected.name : '';
  }
  getWeekOfMonth(date) {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const dayOfMonth = date.getDate();
    const dayOfWeek = startOfMonth.getDay();
    const adjustedDate = dayOfMonth + dayOfWeek - 1;
    
    return Math.ceil(adjustedDate / 7);
  }

  isEditValueSet:boolean = true;
  constructor(private sanitizer: DomSanitizer, private router: Router, private fb: FormBuilder, public commonService: CommonService,
    private dataService: DataService, private tokenService: TokenService,
    private cdRef: ChangeDetectorRef,
    private translate: TranslateService,private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string,
    private cd: ChangeDetectorRef,public dialog: MatDialog, public translationService: TranslationService,
    public languageService: LanguageService, public ngZone: NgZone
    ) {
    var _this = this;
    _this.setDateFormat();

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
        'formcontrolsBasicdetails': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBasicdetails'] || 'formcontrolsBasicdetails',
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
        'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
        'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'floor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'floor'] || 'floor',
        'commonfilterChoosefloor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosefloor'] || 'commonfilterChoosefloor',
        'observedOnBehalfOf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'] || 'observedOnBehalfOf',
        'observedOnBehalfOfSec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOfSec'] || 'observedOnBehalfOfSec',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'formcontrolsChoosedepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosedepartment'] || 'formcontrolsChoosedepartment',
        'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
        'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
        'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
        'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
        'formcontrolsChoosemonth': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosemonth'] || 'formcontrolsChoosemonth',
        'january': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'january'] || 'january',
        'february': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'february'] || 'february',
        'march': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'march'] || 'march',
        'april': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'april'] || 'april',
        'may': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'may'] || 'may',
        'june': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'june'] || 'june',
        'july': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'july'] || 'july',
        'august': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'august'] || 'august',
        'september': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'september'] || 'september',
        'october': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'october'] || 'october',
        'november': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'november'] || 'november',
        'december': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'december'] || 'december',
        'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
        'week1': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week1'] || 'week1',
        'week2': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week2'] || 'week2',
        'week3': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week3'] || 'week3',
        'week4': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week4'] || 'week4',
        'week5': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week5'] || 'week5',
        'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
        'commonfilterChooseassets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassets'] || 'commonfilterChooseassets',
        'commonfilterChooseworkorder': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseworkorder'] || 'Choose Work Order',
        'formcontrolsContractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsContractor'] || 'formcontrolsContractor',
        'contractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractorDetail'] || 'contractorDetail',
        'formcontrolsContractorcraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsContractorcraft'] || 'formcontrolsContractorcraft',
        'commonfilterChoosecraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecraft'] || 'commonfilterChoosecraft',
        'formcontrolsChooseshift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChooseshift'] || 'formcontrolsChooseshift',
        'commonfilterNodata': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'] || 'commonfilterNodata',
        'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
        'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
        'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
        'chipsStopwork': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsStopwork'] || 'chipsStopwork',
        'chipsUsecaution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsUsecaution'] || 'chipsUsecaution',
        'chipsContinuereport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsContinuereport'] || 'chipsContinuereport',
        'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
        'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
        'commonfilterChoosecategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecategories'] || 'commonfilterChoosecategories',
        'formcontrolsCompchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCompchecklist'] || 'formcontrolsCompchecklist',
        'formcontrolsUploadphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsUploadphotos'] || 'formcontrolsUploadphotos',
        'formcontrolsMaxphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMaxphotos'] || 'formcontrolsMaxphotos',
        'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
        'department': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'department'] || 'department',
        'yourDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yourDepartment'] || 'yourDepartment',
        'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
        'buttonClose': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClose'] || 'buttonClose',
        'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
        'formcontrolsPleassechoosesubcat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleassechoosesubcat'] || 'formcontrolsPleassechoosesubcat',
        'formcontrolsSelectall': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSelectall'] || 'formcontrolsSelectall',
        'toasterSuccess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSuccess'] || 'toasterSuccess',
        'toasterRespsuccess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespsuccess'] || 'toasterRespsuccess',
        'toasterFailure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailure'] || 'toasterFailure',
        'toasterRespfailed': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespfailed'] || 'toasterRespfailed',
        'injuryPotential': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'injuryPotential'] || 'injuryPotential',
        'notes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'] || 'notes',
        'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
        'safe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'] || 'safe',
        'notSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'] || 'notSafe',
        'notObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'] || 'notObserved',
        'urgency': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'urgency'] || 'urgency',
        'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
        'formcontrolsPleasechoosecat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleasechoosecat'] || 'formcontrolsPleasechoosecat',
        'buttonExportPDF': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonExportPDF'] || 'buttonExportPDF',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'unsafeActBehavior': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unsafeActBehavior'] || 'unsafeActBehavior',
        'unsafecondition': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unsafecondition'] || 'unsafecondition',
        'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
        'shiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftIdentifier'] || 'shiftIdentifier',
        'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
        'art': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'art'] || 'art',
        'problem': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'problem'] || 'problem',
        'cause': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cause'] || 'cause',
        'solution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'solution'] || 'solution',
        'measureActivity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measureActivity'] || 'measureActivity',
        'describeTheObservations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheObservations'] || 'describeTheObservations',
        'additionalCommentsOnObservation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'additionalCommentsOnObservation'] || 'additionalCommentsOnObservation',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'eventType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventType'] || 'eventType',
        'eventDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventDescription'] || 'eventDescription',
        'didCorrectiveActionsOccurSpecificToTheStopWorkEvent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'didCorrectiveActionsOccurSpecificToTheStopWorkEvent'] || 'didCorrectiveActionsOccurSpecificToTheStopWorkEvent',
        'describeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeCorrectiveActionTaken'] || 'describeCorrectiveActionTaken',
        'feedback': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'] || 'feedback',
        'activity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'] || 'activity',
        'riskyAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'] || 'riskyAction',
        'risk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'] || 'risk',
        'riskAgreement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'] || 'riskAgreement',
        'reasonForAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'] || 'reasonForAction',
        'safeBehaviour': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'] || 'safeBehaviour',
        'suggestedSolution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'] || 'suggestedSolution',
        'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
        'shiftA': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftA'] || 'shiftA',
        'shiftB': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftB'] || 'shiftB',
        'shiftC': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftC'] || 'shiftC',
        'shiftD': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftD'] || 'shiftD',
        'earlyShift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'earlyShift'] || 'earlyShift',
        'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
        'night': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'night'] || 'night',
        'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
        'operationalLearningTooltip': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'] || 'operationalLearningTooltip',
        'chooseWorkOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
          'chooseContractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseContractorDetail'] || 'chooseContractorDetail',
          'formcontrolsImpossible': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsImpossible'] || 'formcontrolsImpossible',
          'red': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'red'] || 'red',
          'blue': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'blue'] || 'blue',
          'green': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'green'] || 'green',
          'yellow': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yellow'] || 'yellow',
          'grey': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'grey'] || 'grey',
          'chooseShiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseShiftIdentifier'] || 'chooseShiftIdentifier',
          'commonfilterChooseSecbehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseSecbehalf'] || 'commonfilterChooseSecbehalf',
      }
      _this.dropdownSettings.searchPlaceholderText = _this.labels['commonfilterSearch']
      _this.dropdownSettings.noDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
      _this.dropdownSettings.noFilteredDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
      _this.dropdownSettingsContractor.searchPlaceholderText = _this.labels['commonfilterSearch']
      _this.dropdownSettingsContractor.noDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
      _this.dropdownSettingsContractor.noFilteredDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
    
      _this.dropdownSettingsWorkOrderNumber.searchPlaceholderText = _this.labels['commonfilterSearch']
      _this.dropdownSettingsWorkOrderNumber.noDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
      _this.dropdownSettingsWorkOrderNumber.noFilteredDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
    
    }
console.log(_this.commonService.configuration["dateFormat"])
    _this.timeFormat = _this.commonService.configuration["dateFormat"];
    _this.hourFormat = _this.commonService.configuration["hourFormat"];
    _this.dateFromat = _this.commonService.configuration["dateFormat"];;
    _this.processList = _.clone(_this.commonService.processList);
    _this.processList.forEach(element => {
      element.selected = false
      if(element.refOFWAProcess){
        element.refOFWAProcess.selected = false
      }
    });
    this.dataSource.data = [];
    _this.getInialTokel(tokenService.getToken());
    if (history.state.action == "View") {
      _this.isView = true;
    }
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();

      
      _this.behalfList2 =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList2.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList2 = _this.behalfList2.slice();

      _this.cdRef.detectChanges();
     
     
     
      // if(_this.observation && _this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }

    })

    
    _this.dataService.postData({ "name": "Priority" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      var priorityList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _.each(priorityList,function(eData){
        _this.urgencyList.push(eData.value);
      })
    })
_this.commonService.notificationGroup();
 
  }
  getDataSetId(){
    var _this =this
    var objPost = {
      "items": [
        {
          "externalId": `${_this.commonService.configuration["AppCode"]}-${_this["selectedSite"].siteCode}-${_this.commonService.configuration["allUnitCode"]}-${_this.commonService.configuration["dataSpaceCode"]}`
        }
      ],
      "ignoreUnknownIds": false
    }
  _this.dataService.postData(objPost, _this.dataService.NODE_API + "/api/service/getDataSetId").subscribe((resData: any) => {
    
    if(resData && resData.items && resData.items.length > 0){
      _this.dataSetImageId = resData.items[0].id

    }

    })
  }
  hasChild = (_: number, node: ExampleFlatNode) => node.expandable;

  dropdownSettings: IDropdownSettings = {
    "singleSelection": false,
    "defaultOpen": false,
    "idField": "externalId",
    "textField": "nameDesc",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };

  dropdownSettingsContractor: IDropdownSettings = {
    "singleSelection": false,
    "defaultOpen": false,
    "idField": "value",
    "textField": "value",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };
  dropdownSettingsWorkOrderNumber: IDropdownSettings = {
    "singleSelection": true,
    "defaultOpen": false,
    "idField": "externalId",
    "textField": "number",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };
  assetsList: any;
  assetsListDeSelect: any
  isFeedback = false;
  isSignature = false;
  isTimeCapture = false;

  floorOption = [
    1,2,3,4,5,6,7,8,9,10
  ]
  ngOnInit(): void {

    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
          'formcontrolsBasicdetails': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBasicdetails'] || 'formcontrolsBasicdetails',
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
          'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
          'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
          'floor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'floor'] || 'floor',
          'commonfilterChoosefloor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosefloor'] || 'commonfilterChoosefloor',
          'observedOnBehalfOf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'] || 'observedOnBehalfOf',
          'observedOnBehalfOfSec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOfSec'] || 'observedOnBehalfOfSec',
          'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
          'formcontrolsChoosedepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosedepartment'] || 'formcontrolsChoosedepartment',
          'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
          'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
          'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
          'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
          'formcontrolsChoosemonth': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosemonth'] || 'formcontrolsChoosemonth',
          'january': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'january'] || 'january',
          'february': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'february'] || 'february',
          'march': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'march'] || 'march',
          'april': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'april'] || 'april',
          'may': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'may'] || 'may',
          'june': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'june'] || 'june',
          'july': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'july'] || 'july',
          'august': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'august'] || 'august',
          'september': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'september'] || 'september',
          'october': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'october'] || 'october',
          'november': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'november'] || 'november',
          'december': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'december'] || 'december',
          'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
          'week1': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week1'] || 'week1',
          'week2': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week2'] || 'week2',
          'week3': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week3'] || 'week3',
          'week4': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week4'] || 'week4',
          'week5': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week5'] || 'week5',
          'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
          'chooseWorkOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
          'commonfilterChooseassets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassets'] || 'commonfilterChooseassets',
          'commonfilterChooseworkorder': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseworkorder'] || 'Choose Work Order',
          'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
          'contractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractorDetail'] || 'contractorDetail',
          'formcontrolsContractorcraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsContractorcraft'] || 'formcontrolsContractorcraft',
          'commonfilterChoosecraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecraft'] || 'commonfilterChoosecraft',
          'formcontrolsChooseshift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChooseshift'] || 'formcontrolsChooseshift',
          'commonfilterNodata': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'] || 'commonfilterNodata',
          'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
          'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
          'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
          'chipsStopwork': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsStopwork'] || 'chipsStopwork',
          'chipsUsecaution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsUsecaution'] || 'chipsUsecaution',
          'chipsContinuereport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chipsContinuereport'] || 'chipsContinuereport',
          'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
          'commonfilterChoosecategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecategories'] || 'commonfilterChoosecategories',
          'formcontrolsCompchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCompchecklist'] || 'formcontrolsCompchecklist',
          'formcontrolsUploadphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsUploadphotos'] || 'formcontrolsUploadphotos',
          'formcontrolsMaxphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMaxphotos'] || 'formcontrolsMaxphotos',
          'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
          'department': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'department'] || 'department',
          'yourDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yourDepartment'] || 'yourDepartment',
          'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
          'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
          'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
          'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
          'buttonClose': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClose'] || 'buttonClose',
          'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
          'formcontrolsPleassechoosesubcat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleassechoosesubcat'] || 'formcontrolsPleassechoosesubcat',
          'formcontrolsSelectall': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSelectall'] || 'formcontrolsSelectall',
          'toasterSuccess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSuccess'] || 'toasterSuccess',
          'toasterRespsuccess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespsuccess'] || 'toasterRespsuccess',
          'toasterFailure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailure'] || 'toasterFailure',
          'toasterRespfailed': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespfailed'] || 'toasterRespfailed',
          'injuryPotential': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'injuryPotential'] || 'injuryPotential',
          'notes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'] || 'notes',
          'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
          'safe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'] || 'safe',
          'notSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'] || 'notSafe',
          'notObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'] || 'notObserved',
          'urgency': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'urgency'] || 'urgency',
          'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
          'formcontrolsPleasechoosecat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleasechoosecat'] || 'formcontrolsPleasechoosecat',
          'buttonExportPDF': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonExportPDF'] || 'buttonExportPDF',
          'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
          'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
          'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
          'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
          'unsafeActBehavior': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unsafeActBehavior'] || 'unsafeActBehavior',
          'unsafecondition': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unsafecondition'] || 'unsafecondition',
          'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
          'shiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftIdentifier'] || 'shiftIdentifier',
          'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
          'art': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'art'] || 'art',
          'problem': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'problem'] || 'problem',
          'cause': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cause'] || 'cause',
          'solution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'solution'] || 'solution',
          'measureActivity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measureActivity'] || 'measureActivity',
          'describeTheObservations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheObservations'] || 'describeTheObservations',
          'additionalCommentsOnObservation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'additionalCommentsOnObservation'] || 'additionalCommentsOnObservation',
          'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
          'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
          'eventType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventType'] || 'eventType',
          'eventDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventDescription'] || 'eventDescription',
          'didCorrectiveActionsOccurSpecificToTheStopWorkEvent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'didCorrectiveActionsOccurSpecificToTheStopWorkEvent'] || 'didCorrectiveActionsOccurSpecificToTheStopWorkEvent',
          'describeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeCorrectiveActionTaken'] || 'describeCorrectiveActionTaken',
          'feedback': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'] || 'feedback',
          'activity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'] || 'activity',
          'riskyAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'] || 'riskyAction',
          'risk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'] || 'risk',
          'riskAgreement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'] || 'riskAgreement',
          'reasonForAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'] || 'reasonForAction',
          'safeBehaviour': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'] || 'safeBehaviour',
          'suggestedSolution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'] || 'suggestedSolution',
          'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
          'shiftA': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftA'] || 'shiftA',
        'shiftB': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftB'] || 'shiftB',
        'shiftC': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftC'] || 'shiftC',
        'shiftD': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftD'] || 'shiftD',
        'earlyShift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'earlyShift'] || 'earlyShift',
        'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
        'night': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'night'] || 'night',
        'operationalLearningTooltip': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'] || 'operationalLearningTooltip',
        'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
          'chooseContractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseContractorDetail'] || 'chooseContractorDetail',
          'formcontrolsImpossible': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsImpossible'] || 'formcontrolsImpossible',
          'red': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'red'] || 'red',
          'blue': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'blue'] || 'blue',
          'green': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'green'] || 'green',
          'yellow': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yellow'] || 'yellow',
          'grey': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'grey'] || 'grey',
          'chooseShiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseShiftIdentifier'] || 'chooseShiftIdentifier',
          'commonfilterChooseSecbehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseSecbehalf'] || 'commonfilterChooseSecbehalf',
        }
        _this.dropdownSettings= {
          "singleSelection": false,
          "defaultOpen": false,
          "idField": "externalId",
          "textField": "nameDesc",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
        };
        _this.dropdownSettingsContractor= {
          "singleSelection": false,
          "defaultOpen": false,
          "idField": "value",
          "textField": "value",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
        };
        _this.dropdownSettingsWorkOrderNumber= {
          "singleSelection": true,
          "defaultOpen": false,
          "idField": "externalId",
          "textField": "number",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
        };
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })
    // _this.dropdownSettings.searchPlaceholderText = _this.translate.instant('COMMONFILTER.SEARCH')
    // _this.translate.use(_this.commonService.selectedLanguage)
    // _this.langChangeSubscription = _this.translate.onLangChange.subscribe((lan) => {
    //   console.log('lan',lan)
    // });
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      _this.filterInit(fiterType);
    })

    var currDate = new Date();
    const monthName = currDate.toLocaleString('default', { month: 'long' });
    console.log('Month: ', monthName)
    _this.monthControl.setValue(monthName.toLowerCase())

    const currentWeek = this.getWeekOfMonth(currDate);
    console.log(currentWeek);  // Output: The current week number (1-5)
    var weekFind = _this.weekList.find(e => e.key == currentWeek);
    this.weekControl.setValue(weekFind?.value)

    
  

    // })
    _this.dataService.postData({ "name": "Contractor" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      _this.contractorList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _this.filteredContractorList = _this.contractorList.slice();
    })
_this.dataService.postData({ "name": "shift" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      _this.shiftList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _this.filteredshiftList = _this.shiftList.slice();
    })
    
    _this.dataService.postData({ "dataSetType": "shiftIdentifier" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      _this.colorList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      console.log(_this.colorList)
    })

    _this.loaderFlag = true;
    _this.dataService.postData({ "sites": _this.subProcess.refSite.externalId }, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
      var listCraft = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
      _this.craftList = [];
      _this.contractorList2 = [];
      if (listCraft.length > 0) {
        var settingData = listCraft[0];
          _this.dateFromat = settingData["dateFormat"];
          _this.timeFormat = settingData["timeFormat"];
          _this.hourFormat = settingData["hourFormat"];
          var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
          _this._locale = dateFormat.local;
          _this.commonService.dateFormat = dateFormat;
          _this.setDateFormat();
          _this._adapter.setLocale(_this._locale);
          _this.cd.detectChanges();

        if(listCraft[0]["craft"]){
          _.each(listCraft[0]["craft"],function(eData){
            _this.craftList.push({"value":eData})
          })
        }

        if(listCraft[0]["contractors"]){
          _.each(listCraft[0]["contractors"],function(eData){
            _this.contractorList2.push({"value":eData})
          })
        }
        
        _this.craftList = _this.craftList.sort(function (a, b) {
          if (a.value < b.value) { return -1; }
          if (a.value > b.value) { return 1; }
          return 0;
        })
        _this.contractorList2 = _this.contractorList2.sort(function (a, b) {
          if (a.value < b.value) { return -1; }
          if (a.value > b.value) { return 1; }
          return 0;
        })
        _this.craftList2 = _this.craftList.slice();
        _this.filteredCraftList2 = _this.craftList2.slice();
        _this.filteredCraftList = _this.craftList.slice();
        _this.filteredContractorList2 = _this.contractorList2.slice();
        _this.cd.detectChanges();

      }else{
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
        _this._locale = dateFormat.local;
        _this.commonService.dateFormat = dateFormat;
        _this._adapter.setLocale(_this._locale);
        _this.setDateFormat();
        _this.cd.detectChanges();
      }
      
    })
    _this.dataService.postData({ "externalId": _this.subProcess.externalId }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
      var processConfig;
      if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
        processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
        const hardCodedConfig = _this.commonService.defaultConfigList.find(e => e.refProcess === _this.process.name);
          if (hardCodedConfig) {
            const mergedConfig = _this.mergeConfigs(processConfig, hardCodedConfig);
            console.log(mergedConfig);
            _this.processConfig = mergedConfig;
          }
          _this.processConfig = processConfig;
          console.log(_this.processConfig);
          console.log(_this.commonService.defaultConfigList.find(e => e.refProcess == _this.process.name))
      }else{
        processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.process.name)
      }
        var configDetail = processConfig["configDetail"];
        const { high, medium, low } = configDetail;
        var myUrgencyList = [];
        console.log(">>>>>>>>>>>>>>>>>>>")
        console.log(_this.urgencyList)
        console.log(high)
        console.log(medium)
        console.log(low)
        _this.urgencyList.filter(e=>{
          if((e == "High" && (high && high.isEnabled)) || (e == "Medium" && (medium && medium.isEnabled)) || (e == "Low" && (low && low.isEnabled)) ){
            myUrgencyList.push(e)
          }
        })
        _this.notificationGroupEnable = configDetail?.notificationEnableValue
        _this.notificationGroup = configDetail?.notificationGroup
        console.log(myUrgencyList)
        console.log(configDetail)
        _this.urgencyList = myUrgencyList;
        _this.showSafe= configDetail?.safe?.isEnabled
        _this.showNotSafe= configDetail?.notsafe?.isEnabled
        _this.showNotObserved= configDetail?.notobserved?.isEnabled
        _this.showInjurypotential= configDetail?.injuryPotential?.isEnabled
        if(configDetail?.safe?.isNotesMandatory){
          _this.isSafeNotesMandatory= true;
        }
        if(configDetail?.notsafe?.isNotesMandatory ){
          _this.isNotSafeNotesMandatory= true;
        }
        if(configDetail?.notobserved?.isNotesMandatory){
          _this.isNotObservedNotesMandatory= true;
        }
       
        const { safe, notsafe, notobserved } = configDetail;
        _this.newConfigDetail = { safe, notsafe, notobserved };
        _this.defaultItem = Object.values(_this.newConfigDetail).find(item => item.isDefault);
        if (_this.defaultItem) {
          if (_this.defaultItem.field === "Safe") {
            _this.defaultItem.value = "safe";
          } else if (_this.defaultItem.field === "Not Safe") {
            _this.defaultItem.value = "unsafe";
          } else if (_this.defaultItem.field === "Not Observed") {
            _this.defaultItem.value = "notObserved";
          }
      }
      
      // Alter true/false to "Yes"/"No"
      for (const key in _this.defaultItem) {
        if(_this.defaultItem[key] =="null"){
          console.log("hii")
        }
          else if (_this.defaultItem[key] === true) {
            _this.defaultItem[key] = "Yes";
          } else if (_this.defaultItem[key] === false) {
            _this.defaultItem[key] = "No";
          }
          // else if(_this.defaultItem[key] == "null"){
          //   _this.defaultItem[key] = "null";
          // }
      }

        const currentTime = new Date();
        const formattedTime = this.formatTime(currentTime);

        _this.observedForm = _this.fb.group({
          locationObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.locationObserved.isMandatory ? Validators.required : undefined],
          behalf: [{ value: '', disabled: _this.isView },configDetail && configDetail.observedOnBehalfOf.isMandatory ? Validators.required : undefined],
          behalf2: [{ value: '', disabled: _this.isView },configDetail && configDetail.observedOnBehalfOfSec.isMandatory ? Validators.required : undefined],
          crafts:  [{ value: '', disabled: _this.isView },configDetail && configDetail.crafts.isMandatory ? Validators.required : undefined],
          department:  [{ value: '', disabled: _this.isView },configDetail && configDetail.department.isMandatory ? Validators.required : undefined],
          datetime: [{ value: new Date(), disabled: _this.isView },configDetail && configDetail.date && configDetail.date.isMandatory ? Validators.required : undefined],
          startTime:  [{ value: formattedTime, disabled: _this.isView },configDetail && configDetail.startTime && configDetail.startTime.isMandatory ? Validators.required : undefined],
          endTime:  [{ value: '', disabled: _this.isView },configDetail && configDetail.endTime && configDetail.endTime.isMandatory ? Validators.required : undefined],
          assets:  [{ value: '', disabled: _this.isView },configDetail && configDetail.assets.isMandatory ? Validators.required : undefined],
          contractor:  [{ value: 'No', disabled: _this.isView },configDetail && configDetail.contractor.isMandatory ? Validators.required : undefined],
          contractorDetails:  [{ value: '', disabled: _this.isView }],
          observedCraft:  [{ value: '', disabled: _this.isView },configDetail && configDetail.observedCraft.isMandatory ? Validators.required : undefined],
          describeObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.describeTheObservations.isMandatory ? Validators.required : undefined],
          operationalLearning:  [{ value: 'No', disabled: _this.isView },configDetail && configDetail?.operationalLearning?.isMandatory ? Validators.required : undefined],
          operationalLearningDescription :  [{ value: '', disabled: _this.isView }],
          commentsObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.additionalCommentsOnObservation.isMandatory ? Validators.required : undefined],
          shift:  [{ value: '', disabled: _this.isView },configDetail && configDetail.shift.isMandatory ? Validators.required : undefined],
          shiftIdentifier:  [{ value: '', disabled: _this.isView },configDetail && configDetail.shiftIdentifier.isMandatory ? Validators.required : undefined],
          shortDescription:  [{ value: '', disabled: _this.isView },configDetail && configDetail.shortDescription.isMandatory ? Validators.required : undefined],
          floor:  [{ value: '', disabled: _this.isView },configDetail && configDetail.floor.isMandatory ? Validators.required : undefined],
          eventType:  [{ value: 'Unsafe Act/Behavior', disabled: _this.isView },configDetail && configDetail.eventType && configDetail.eventType.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          eventDesccription:  [{ value: '', disabled: _this.isView }],
          correctiveActionFlag:  [{ value: 'Yes', disabled: _this.isView },configDetail && configDetail.correctiveActionFlag && configDetail.correctiveActionFlag.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          descripeCorrectiveActionTaken:  [{ value: '', disabled: _this.isView }],
          art:  [{ value: '', disabled: _this.isView },configDetail && configDetail.art?.isMandatory ? Validators.required : undefined],
          problem:  [{ value: '', disabled: _this.isView },configDetail && configDetail.problem?.isMandatory ? Validators.required : undefined],
          cause:  [{ value: '', disabled: _this.isView },configDetail && configDetail.cause?.isMandatory ? Validators.required : undefined],
          solution:  [{ value: '', disabled: _this.isView },configDetail && configDetail.solution?.isMandatory ? Validators.required : undefined],
          measure:  [{ value: '', disabled: _this.isView },configDetail && configDetail.measure?.isMandatory ? Validators.required : undefined],
          workOrderNumber: [{ value: '', disabled: _this.isView },configDetail && configDetail.workOrderNumber?.isMandatory ? Validators.required : undefined],
          projectName: [{ value: '', disabled: _this.isView },configDetail && configDetail.projectName?.isMandatory ? Validators.required : undefined],
        });

        _this.observedForm2 = _this.fb.group({
          
          signature:  [{ value: '', disabled: _this.isView },configDetail && configDetail.signature.isMandatory ? Validators.required : undefined],
        })

        _this.dynamicValueChanges();

        if(_this.observation){
          _this.isEditValueSet = false;
        }

        
  //       _this.showSafe= configDetail.safe.isEnabled
  //       _this.showNotSafe= configDetail.notsafe.isEnabled
  //       _this.showNotObserved= configDetail.notobserved.isEnabled
  //       // _this.checkboxcss='66.3'
  //       // _this.notescss='30'
  //       // _this.nonotobserveredcss='4px';
  //       // _this.outercheckboxcss='16.5';


  //       if (!_this.showSafe && !_this.showNotSafe) {
  //         _this.checkboxcss = (parseFloat(_this.checkboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.notescss = (parseFloat(_this.notescss) * 1.33).toString(); // Increase by 33%
  //         if (!_this.showNotObserved) {
  //           _this.nonotobserveredcss = '4px';
  //         }
  //       } else if (!_this.showSafe || !_this.showNotSafe || !_this.showNotObserved) {
  //         _this.checkboxcss = (parseFloat(_this.checkboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.notescss = (parseFloat(_this.notescss) * 1.33).toString(); // Increase by 33%
  //         if (!_this.showNotObserved) {
  //           _this.nonotobserveredcss = '4px';
  //         }
  //       }else if (!_this.showSafe && !_this.showNotSafe && !_this.showNotObserved) {
  //          // Decrease values by 33% for each disabled option
  // const factor = Math.pow(0.67, 3); // 0.67 * 0.67 * 0.67

  // _this.checkboxcss = (parseFloat(_this.checkboxcss) * factor).toString(); // Adjust checkboxcss
  // _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * factor).toString(); // Adjust outercheckboxcss
  // _this.notescss = (parseFloat(_this.notescss) * Math.pow(1.33, 3)).toString(); // Increase notescss by 33% for each disabled option
  // _this.nonotobserveredcss = '4px'; // Set nonotobserveredcss to 4px
  //       }
        // Modify fxFlex for disabled radio buttons
if (!_this.showSafe) {
  _this.safeFxFlex = '0';
}

if (!_this.showNotSafe) {
  _this.notSafeFxFlex = '0';
}

if (!_this.showNotObserved) {
  _this.notObservedFxFlex = '0';
}
if (!_this.showInjurypotential){
  _this.marginLeft='80px';
  _this.notescss = '30'
}
        // Further adjustments if multiple boxes are disabled
        let disabledCount = 0;
        if (!_this.showSafe) disabledCount++;
        if (!_this.showNotSafe) disabledCount++;
        if (!_this.showNotObserved) disabledCount++;
        
        if (disabledCount >= 1) {
          const factor = Math.pow(0.67, disabledCount); // Adjust by 33% for each disabled box
          _this.checkboxcss = (parseFloat(_this.checkboxcss) * factor).toString();
          _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * factor).toString();
          _this.outercheckboxcsschild = (parseFloat(_this.outercheckboxcsschild) * factor).toString();
          _this.notescss = (parseFloat(_this.notescss) * Math.pow(1.31, disabledCount)).toString();
          _this.notescsschild = (parseFloat(_this.notescsschild) * Math.pow(1.31, disabledCount)).toString();
          if (!_this.showNotObserved) {
            _this.nonotobserveredcss = '-44px';
          }
          if(!_this.showNotObserved && disabledCount<3){
            _this.nonotobserveredcss = '1px';
            _this.notescss = '30';
          }
          else if(!_this.showSafe && !_this.showNotSafe)
          {
            _this.nonotobserveredcss = '10px';
          }
        }
        _this.observedForm.get("correctiveActionFlag").valueChanges.subscribe(value => {
          if (value == "No" && history.state.action != "View" && _this.isEditValueSet) {
            const dialogConfig = new MatDialogConfig();
            dialogConfig.data = {
              process:_this.process.name,
              site: _this.subProcess.refSite.externalId,
              unit: _this.unitControl.value,
              location: _this.observedForm.get("locationObserve").value
            };
            dialogConfig.disableClose = true; // Prevents closing on outside click
            dialogConfig.position = { top: "50px", }
            dialogConfig.panelClass = "react-table-modalbox"
            const dialogRef = this.dialog.open(ActionPopupComponent, dialogConfig);
            dialogRef.afterClosed().subscribe(result => {
              console.log("result")
              console.log(result)
              if(result && history.state.action != "Edit"){
                _this.actionPopupResponse = result;
              } else if(result && _this.observation && _this.observation.correctiveActionFlag && _this.observation.correctiveActionFlag == "Yes"){
                _this.actionPopupResponse = result;
              } else if(result && _this.observation && !_this.observation.correctiveActionFlag){
                _this.actionPopupResponse = result;
              } else{
                _this.observedForm.get("correctiveActionFlag").setValue("Yes");
              }
              
            });
          }
        })
        
        _this.configDetail = processConfig["configDetail"];
        _this.observedForm.get("datetime").valueChanges.subscribe(x => {
          console.log(x)
          if(history.state.action != "Edit" || history.state.action != "View"){
            var currDate = new Date(x);
            const monthName = currDate.toLocaleString('default', { month: 'long' });
            _this.monthControl.setValue(monthName)
            const currentWeek = _this.getWeekOfMonth(currDate);
            var weekFind = _this.weekList.find(e => e.key == currentWeek);
            _this.weekControl.setValue(weekFind.value)
          }
         
        });
        // _this.loaderFlag = false;
        _this.cd.detectChanges();
        _this.afterSettingInit();
       
        // _this.isFeedback = processConfig["isFeedback"] == true ? true : false;
        // _this.isSignature = processConfig["isSignature"] == true ? true : false;
        // _this.isTimeCapture = processConfig["isTimeCapture"] == true ? true : false;
      // } else {
      //   _this.configDetail = undefined;
      //   _this.loaderFlag = false;
      //   _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterNoconfig'] });
      // }
      
  console.log("id____________________.",_this.subProcess)
  this.questionFun("SubProcess", _this.subProcess.externalId, _this.subProcess);

  })
    var site = _this.commonService.siteList.find(e => e.externalId == _this.subProcess.refSite.externalId)
    _this.selectedSite = site;
    console.log('_this.selectedSite ',_this.selectedSite)
    _this.getDataSetId();
    // _this.siteChanged()
    _this.selectedCountry = site["country"];
    if (site["country"] && site["country"]["parent"]) {
      _this.selectedRegion = site["country"]["parent"];
    }
    if (site && site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      if(_this.filteredReportingLocationList.length == 1){
        _this.observedForm.get("locationObserve").setValue(_this.filteredReportingLocationList[0]["externalId"]);
       }
    }
    if(_this.selectedSite){
      _this.commonService.siteList.filter(function (e) {
        if (_this.selectedSite.externalId.indexOf(e.externalId) > -1) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.unitList = _this.unitList.filter(item => item.isActive === true);
            _this.filteredUnitList = _this.unitList.slice();
          }
  
       
        }
        //return selectedSite.indexOf(e.externalId) > -1;
      });
    }
    

    // _this.commonService.siteChanges$.subscribe((sit: any) => {
    // console.log('sit',sit)
    // })
 
    _this.unitControl.valueChanges.subscribe(xArray => {
      var tempArray =_this.tempLocation.filter(item => item.reportingUnit.externalId ==  xArray);
     
      _this.reportingLocationList =tempArray
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
     // _this.cd.dete
     if(_this.reportingLocationList.length == 1){
      _this.observedForm.get("locationObserve").setValue(_this.reportingLocationList[0]["externalId"]);
     }

     });

    
    var childrenCategory = _this.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.subProcess.externalId);
    })
    _this.categoryList = childrenCategory.filter(item => item?.isActive !== false);;
    _this.categoryList  = _this.categoryList .sort(function(a, b){
      if((a.sequence ? a.sequence : 0) < (b.sequence ? b.sequence : 0)) { return -1; }
      if((a.sequence ? a.sequence : 0) > (b.sequence ? b.sequence : 0)) { return 1; }
      return 0;
  })
  
  


  
  }

  mergeConfigs(apiConfig: any, defaultConfig: any): any {
    // Recursively merge objects
    const mergeObjects = (target: any, source: any) => {
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null) {
            if (!target[key] || typeof target[key] !== 'object') {
              target[key] = Array.isArray(source[key]) ? [] : {};
            }
            mergeObjects(target[key], source[key]);
          } else if (target[key] === undefined) {
            target[key] = source[key];
          }
        }
      }
    };
  
    const result = { ...apiConfig };
    mergeObjects(result, defaultConfig);
    if (!apiConfig.dashboardConfig || Object.keys(apiConfig.dashboardConfig).length === 0) {
      result.dashboardConfig = { ...defaultConfig.dashboardConfig };
    }
  
    if (!apiConfig.columnConfig || Object.keys(apiConfig.columnConfig).length === 0) {
      result.columnConfig = { ...defaultConfig.columnConfig };
    }
  
    return result;
  }

  dynamicValueChanges(): void {
    if(this.observedForm.get('contractor').value==''){
      
      this.clearFieldValidators('observedCraft');
    }
    this.observedForm.get('contractor')?.valueChanges.subscribe((value) => {
      if (value === 'Yes') {
        // Enable and set mandatory fields
        this.setFieldValidators('contractorDetails', this.configDetail.contractorDetail.isMandatory);
      } else if (value === 'No') {
        // Disable and clear validators
        this.clearFieldValidators('contractorDetails');
      } else {
        // Clear all validators
        this.clearFieldValidators('contractorDetails');
      }
    });
    this.observedForm.get('operationalLearning')?.valueChanges.subscribe((value) => {
      if (value === 'Yes') {
        // Enable and set mandatory fields
        this.setFieldValidators('operationalLearningDescription', this.configDetail.operationalLearningDescription.isMandatory);
      } else if (value === 'No') {
        // Disable and clear validators
        this.clearFieldValidators('operationalLearningDescription');
      } else {
        // Clear all validators
        this.clearFieldValidators('operationalLearningDescription');
      }
    })


    // this.selectedUrgency.get.valueChanges.subscribe((value) => {
    //   console.log("HIIIIIIIIIIIIIII",value)
    //     if (value === 'High') {
    //       // Enable and set mandatory fields
    //       this.setFieldValidators('eventDesccription', this.configDetail.eventDesccription.isMandatory);
    //       this.setFieldValidators('descripeCorrectiveActionTaken', this.configDetail.descripeCorrectiveActionTaken.isMandatory);
    //     } else if (value != 'High') {
    //       // Disable and clear validators
    //       this.observedForm.get('eventDesccription').clearValidators();
    //       this.observedForm.get('descripeCorrectiveActionTaken').clearValidators();
    //     } else {
    //       // Clear all validators
    //       this.clearFieldValidators('eventDesccription');
    //       this.clearFieldValidators('descripeCorrectiveActionTaken');
    //     }
    // })
  }

  setFieldValidators(controlName: string, isMandatory: boolean): void {
    const control = this.observedForm.get(controlName);
    if (control) {
      control.setValidators(isMandatory ? Validators.required : null);
      control.updateValueAndValidity();
    }
  }

  clearFieldValidators(controlName: string): void {
    const control = this.observedForm.get(controlName);
    if (control) {
      control.clearValidators();
      control.updateValueAndValidity();
    }
  }

  expandAll(){
    var _this =this;
    _this.cd.detectChanges();
    if(_this.loaderCount == 0){
      setTimeout(function(){
      // _this.updateBoldProperties();
    const tempArr = [];
      _this.checkListArray.forEach(ele => {
        if (ele.children.length > 0) {
          ele.bold = true;
          ele.children.forEach(ele2 => {
            if (ele.children.length == 1 && ele2.name.toLowerCase() == ele.name.toLowerCase()) {
              ele2.isDuplicatCategoryName = true;
            }
            if (ele2.children && ele2.children.length > 0 ) {
              ele2.bold = true;
            }
          });
        } else {
          ele.bold = false;
        }
        tempArr.push(ele);
      });
      _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
      setTimeout(function(){
        _this.treeExpand.treeControl.expandAll();
      _this.cd.detectChanges();
      }, 500);
    }, 1500);
    }
  }

  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  afterSettingInit(){
    var _this = this;
    
    var locationObserve = "";
    var behalf = "";
    var datetime = new Date();
    // var startTime = new Date();
    // var endTime = new Date();
    var assets = [];
    var contractor = "";
    var contractorDetails = "";
    var operationalLearning = "";
    var operationalLearningDescription = "";
    var workOrderNumber:any;
    var projectName = "";
    var describeObserve = "";
    var shift = "";
    var shiftIdentifier = "";
    var shortDescription = "";
    var art = "";
    var problem = "";
    var cause = "";
    var solution = "";
    var measure = "";
    var floor = "";
    
    var commentsObserve = "";
    var signature = "";
   
    _this.observedForm.get("locationObserve").valueChanges.subscribe(value => {
      var location = _this.reportingLocationList.find(e => e.externalId == value);
      _this.selectedReportingLocation = location;
    });
  
    _this.observedForm.get("behalf").valueChanges.subscribe(x => {
 
    //   _this.dataService.postData({user: x,site_id: _this.selectedSite.externalId},_this.dataService.NODE_API + '/api/service/getUnitInfo')
    // .subscribe((details:any) => 
    let details=_this.commonService.userUnitList
       _this.matchingUnits = this.unitList.filter(unit =>
    details.some(detailsUnit => detailsUnit.unitCode === unit.externalId)
);
if(_this.matchingUnits.length==0){
  _this.matchingUnits = this.unitList;
}
if (_this.matchingUnits.length > 0) {
  _this.loaderFlag = false;
  if(this.isView){
    _this.unitControl.disable();  
    _this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
  }
  else if(_this.observation && _this.observation.refUnit && _this.observation.refUnit.externalId){
    _this.unitControl.setValue(_this.observation.refUnit.externalId)
    _this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
  _this.z=0;
  }
  else {
    _this.unitControl.setValue(_this.matchingUnits[0].externalId);
  _this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
  _this.z=1; 
}
} 
else {
//   _this.unitControl.setValue(_this.unitList[0].externalId);
_this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
}
    
  // )      
    });

    _this.observedForm.get("assets").valueChanges.subscribe(x => {
      // console.log("ASSET ", x)
    });

    if (_this.observation) {
      console.log('_this.observation',_this.observation)
      _this.loaderFlag = true;
      locationObserve = "";
      behalf = "";
      // var myDate = new Date(_this.observation.date);
      // datetime = new Date(myDate.setDate(new Date().getDate()));
      datetime = _this.observation.date
      // startTime = new Date(_this.observation.startTime);
      // endTime = new Date(_this.observation.endTime);
      assets = [];
      contractor = _this.observation.isContractor ? "Yes" : (_this.observation.isContractor==null)?"":"No";
      contractorDetails = _this.observation.contractorsNames;
      workOrderNumber = _this.observation.refWorkOrderHeader;
      projectName = _this.observation.projectName;
      operationalLearning = _this.observation.isOperationalLearning?"Yes":"No";
      operationalLearningDescription = _this.observation.operationalLearningDescription;
      describeObserve = _this.observation.description;
      shift = _this.observation.shift;
      shiftIdentifier = _this.observation.shiftIdentifier;
      shortDescription = _this.observation.shortDescription;
      art = _this.observation.art;
      problem = _this.observation.problem;
      cause = _this.observation.cause;
      solution = _this.observation.solution;
      measure = _this.observation.measure;
      floor = _this.observation.floor;
      _this.selectedUrgency = _this.observation.urgency ? _this.observation.urgency:"";
      commentsObserve = _this.observation.comments;
      signature = _this.observation.signature;
      locationObserve = _this.observation.refReportingLocation ? _this.observation.refReportingLocation.externalId : "";
      // "11:15 pm"
      if(_this.observation.startTime || _this.observation.endTime){
            _this.observedForm.get("startTime").setValue(_this.observation.startTime?.split("T")[1].split(":")[0]+":"+_this.observation.startTime?.split("T")[1].split(":")[1]);
            _this.observedForm.get("endTime").setValue(_this.observation.endTime?.split("T")[1].split(":")[0]+":"+_this.observation.endTime?.split("T")[1].split(":")[1]);
        }
          _this.observedForm.get("datetime").setValue(datetime);
       if(_this.observation.week){
              _this.weekControl.setValue(_this.observation.week)  
        }
       if(_this.isView){
            _this.weekControl.disable();
            _this.unitControl.disable();
        }
      _this.observedForm.get("contractor").setValue(contractor);
      var contractorsNames = [];
      _.each(contractorDetails,function(eData){
        contractorsNames.push({"value": eData});
      })

      if(workOrderNumber){
        _this.observedForm.get("workOrderNumber").setValue([
          {name:workOrderNumber.name,value:workOrderNumber.externalId,number:workOrderNumber.number}
        ]);
      }
      _this.observedForm.get("contractorDetails").setValue(contractorsNames);
      
      _this.observedForm.get("projectName").setValue(projectName);
      _this.observedForm.get("operationalLearning").setValue(operationalLearning);
      _this.observedForm.get("operationalLearningDescription").setValue(operationalLearningDescription);
      _this.observedForm.get("describeObserve").setValue(describeObserve);
      _this.observedForm.get("shift").setValue(shift);
      _this.observedForm.get("shiftIdentifier").setValue(shiftIdentifier);
      _this.selectedColor=shiftIdentifier
      _this.updateBackgroundColor();
      _this.observedForm.get("crafts").setValue(_this.observation.observerCrafts);
      _this.observedForm.get("shortDescription").setValue(shortDescription);
      _this.observedForm.get("art").setValue(art);
      _this.observedForm.get("problem").setValue(problem);
      _this.observedForm.get("cause").setValue(cause);
      _this.observedForm.get("solution").setValue(solution);
      _this.observedForm.get("measure").setValue(measure);
      _this.observedForm.get("floor").setValue(parseInt(floor));
      _this.observedForm.get("commentsObserve").setValue(commentsObserve);
      _this.observedForm2.get("signature").setValue(signature);
      _this.observedForm.get("observedCraft").setValue(_this.observation.observedCrafts);
      _this.observedForm.get("eventType").setValue(_this.observation.eventType ? _this.observation.eventType :"Unsafe Act/Behavior");
      _this.observedForm.get("eventDesccription").setValue(_this.observation.eventDesccription ? _this.observation.eventDesccription :"");
      _this.observedForm.get("correctiveActionFlag").setValue(_this.observation.correctiveActionFlag ? _this.observation.correctiveActionFlag :"Yes");
      _this.observedForm.get("descripeCorrectiveActionTaken").setValue(_this.observation.descripeCorrectiveActionTaken ? _this.observation.descripeCorrectiveActionTaken :"");
      if(_this.observation.refDeparment){
        _this.observedForm.get("department").setValue(_this.observation.refDeparment.externalId);
      }
     
      if(_this.observation.refUnit){
        _this.unitControl.setValue(_this.observation.refUnit.externalId);
      }
      _this.observedForm.get("locationObserve").setValue(locationObserve);
      _this.isEditValueSet = false;
      // if(_this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }
   
      async function processMediaList(mediaList) {
        let delayTime = 500; // Initial delay time in milliseconds
        const maxDelayTime = 16000; // Maximum delay time in milliseconds
        const maxRetries = 5; // Maximum number of retries for each request
    
        for (let i = 0; i < mediaList.length; i++) {
            const item = mediaList[i];
            const mediaType = item.mimetype.split('/')[0]; // Get the type (image or video)
            let mediaUrl;
            let retries = 0; // Retry counter
    
    
            if (mediaType === 'image') {
                mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
    
                while (retries < maxRetries) {
                    try {
                        const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                        const objectURL = URL.createObjectURL(resData);
                        item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                        this.images.push(item.image);
                        this.cd.detectChanges(); // Trigger change detection after image is loaded
    
                        // Reset delay time after a successful request
                        delayTime = 500;
                        break; // Exit retry loop on success
                    } catch (error) {
                        if (error.status === 429) {
                            console.error('Rate limit exceeded. Retrying after delay...');
                            await delay(delayTime);
                            delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                            retries++; // Increment retry counter
                        } else {
                            // Handle other errors
                            break; // Exit retry loop on other errors
                        }
                    }
                }
            } else if (mediaType === 'video') {
                mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
    
                while (retries < maxRetries) {
                    try {
                        const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                        const objectURL = URL.createObjectURL(resData);
                        item.video = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                        this.videos.push(item.video);
                        this.cd.detectChanges(); // Trigger change detection after video is loaded
    
                        // Reset delay time after a successful request
                        delayTime = 500;
                        break; // Exit retry loop on success
                    } catch (error) {
                        console.error('Error loading video:', error);
                        if (error.status === 429) {
                            console.error('Rate limit exceeded. Retrying after delay...');
                            await delay(delayTime);
                            delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                            retries++; // Increment retry counter
                        } else {
                            // Handle other errors
                            break; // Exit retry loop on other errors
                        }
                    }
                }
            }
    
            // Fixed delay between requests
            await delay(delayTime);
        }
    
        console.log('All items processed');
        console.log('Total images:', this.images.length);
        console.log('Total videos:', this.videos.length);
    }
    
    function delay(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    if (this.observation.evidenceDocument && this.observation.evidenceDocument.items.length > 0) {
        const mediaList = this.observation.evidenceDocument.items;
        processMediaList.call(this, mediaList);
    }
    
    
    
    
    
    
      _this.editView();
    }

    if(_this.dataService.userInfo){
    //   _this.observedForm.get("behalf").setValue(_this.dataService.userInfo.user.externalId);
    // }
    if(_this.observation?.observedOnBehalfOf?.externalId){
      var user = _this.behalfList.find(u => u.externalId === _this.observation.observedOnBehalfOf.externalId);
      var userId= _this.observation.observedOnBehalfOf.externalId
    }
    else{
      var user = _this.behalfList.find(u => u.externalId === _this.dataService.userInfo.user.externalId);
      var userId= _this.dataService.userInfo.user.externalId
    }
        if (!user) {
          // If user not found in the first 1000 records, perform search with externalId
          _this.dataService.postData({ searchUser: userId }, _this.dataService.NODE_API + "/api/service/listAllUser")
            .subscribe((userData: any) => {
              const searchResult = userData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
              if (searchResult.length > 0 && userId) {
                const selectedUser = searchResult[0];
                selectedUser.name = selectedUser.firstName + ' ' + selectedUser.lastName;
                _this.behalfList.push(selectedUser);
                _this.filteredBehalfList = _this.behalfList.slice();
                try {
                  _this.userbehalf=selectedUser.externalId
                  _this.observedForm.get("behalf").setValue(selectedUser.externalId);
                  _this.z=1
                } catch (error) {
                  console.error('Error setting value in behalf form control:', error);
                }
              }
            });
            _this.cdRef.detectChanges();
        }
        else{
          try {
            _this.observedForm.get("behalf").setValue(user.externalId);
          } catch (error) {
            console.error('Error setting value in behalf form control:', error);
          }
          _this.cdRef.detectChanges();

        }
        if(_this.observation?.secondaryObserver?.externalId){
          var user2 = _this.behalfList2.find(u => u.externalId === _this.observation.secondaryObserver.externalId);
          var userId2= _this.observation.secondaryObserver.externalId
        }
        // else{
        //   var user2 = _this.behalfList2.find(u => u.externalId === "_this.dataService.userInfo.user.externalId");
        //   var userId2= _this.dataService.userInfo.user.externalId
        // }
          console.log(user2, userId2)
            if (!user2) {
                  // If user not found in the first 1000 records, perform search with externalId
                  _this.dataService.postData({ searchUser: userId2 }, _this.dataService.NODE_API + "/api/service/listAllUser")
                    .subscribe((userData: any) => {
                      const searchResult = userData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
                      if (searchResult.length > 0 && userId2) {
                        const selectedUser = searchResult[0];
                        selectedUser.name = selectedUser.firstName + ' ' + selectedUser.lastName;
                        _this.behalfList2.push(selectedUser);
                        _this.filteredBehalfList2 = _this.behalfList2.slice();
                        try {
                          _this.userbehalf2=selectedUser.externalId
                          _this.observedForm.get("behalf2").setValue(selectedUser.externalId);
                          _this.z=1
                        } catch (error) {
                          console.error('Error setting value in behalf form control:', error);
                        }
                      }
                    });
                    _this.cdRef.detectChanges();
                }
                else{
                  try {
                    _this.observedForm.get("behalf2").setValue(user.externalId);
                  } catch (error) {
                    console.error('Error setting value in behalf form control:', error);
                  }
                  _this.cdRef.detectChanges();
        
                }
              }
    
        _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "" ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
        subscribe((resData: any) => {

          var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
          asset.forEach(element => {
            element.nameDesc = element.name+' / '+element.description
            
          });
          _this.assetsList = asset
          var mapAss = undefined;
         if(_this.observation){
          mapAss = _this.observation.refEquipment && _this.observation.refEquipment["items"].map(item => item.externalId)
         }
         _this.resetSelectionWorkOrderNumber();
          _this.dataService.postData({ externalId:mapAss?mapAss:[] ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
          subscribe((resData: any) => {
            var assetMap = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
            assetMap.forEach(element => {
              element.nameDesc = element.name+' / '+element.description
            });
            _this.assetsList = _.unionBy(assetMap, _this.assetsList, 'externalId');
            if(_this.observation) {

              var asL = [];
              _.each(_this.observation.refEquipment["items"], function (eAsset) {

                eAsset.nameDesc = eAsset.name+' / '+eAsset.description
                asL.push(eAsset)

              })
              _this.editEquipment =  _.cloneDeep(asL);  
          
              _this.observedForm.get("assets").setValue(asL);
            }
          })
        
        })
        if(_this.userbehalf && _this.userbehalf.length>0){
          _this.observedForm.get("behalf").setValue(_this.userbehalf);
        }
        if(_this.userbehalf2 && _this.userbehalf2.length>0){
          _this.observedForm.get("behalf2").setValue(_this.userbehalf2);
        }
    // _this.treeExpand.treeControl.expandAll();
  }
  getDepartments(){
    var _this =this;
    _this.dataService.postData({ "sites":_this["selectedSite"]?[_this["selectedSite"].externalId]:[] }, _this.dataService.NODE_API + "/api/service/listDepartment").subscribe((resData: any) => {
      _this.departmentList = resData["data"]["list" + _this.commonService.configuration["typeDepartment"]]["items"]
      _this.departmentList =  _this.departmentList.sort(function(a, b){
        if(a.description < b.description) { return -1; }
        if(a.description > b.description) { return 1; }
        return 0;
      })
      _this.filteredDepartmentList = _this.departmentList.slice();
   
    })
  }

  
  getTooltipTextobservedCraft(): string {
    const selectedValues = this.observedForm.get('observedCraft')?.value || [];
    return selectedValues.length ? selectedValues.join(', ') : 'No items selected';
  }
  getTooltipTextcrafts(): string {
    const selectedValues = this.observedForm.get('crafts')?.value || [];
    return selectedValues.length ? selectedValues.join(', ') : 'No items selected';
  }


  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredUnitList1 = _this.matchingUnitList.slice();
    }
    if(_this.observation){
      if(_this.matchingUnits.length==0){
        _this.matchingUnits = this.unitList;
      }
      _this.filteredUnitList1=this.unitList
      _this.matchingUnitList=this.unitList
    }
  }
  ngAfterViewInit() {

   
    var _this = this;
    
    if (_this.commonService.filterListFetched) {
   
      _this.filterInit("Unit");

    }

    _this.getDepartments()
  }
  siteChanged() {
    var _this = this;
    console.log('    _this.selectedSite ')
    var selectedSite = _this.commonService.getSelectedValue(_this.selectedSite.externalId);
    _this.unitList = [];
    _this.filteredUnitList = _this.unitList.slice();

  
    if (selectedSite.length > 0) {

      var myCountries = [];
      var myRegions = [];
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e["country"]) {

            myCountries.push(e["country"]["externalId"])
            if (e["country"]["parent"])
              myRegions.push(e["country"]["parent"]["externalId"])

          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
      _this.filterFlag = "Site";
 
    } else {
      selectedSite = _this.commonService.siteList.map(eItem => (eItem.externalId))
    }

    if (selectedSite.length > 0) {
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }

       
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
    } else {

      if (_this.commonService.siteList.length > 0) {
        _this.unitList = _this.commonService.unitList;
        _this.filteredUnitList = _this.unitList.slice();
      }

  
    }
  }

filterInit(fiterType) {
    var _this = this;
 
    // if (fiterType == "Unit") {
    //   _this.unitList = _this.commonService.unitList;
    //   _this.filteredUnitList = _this.commonService.unitList.slice();

    // }
    // console.log('   _this.unitList',   _this.unitList)
   

  }

  editView() {
    console.log("Edit");

    const handleItems = (items) => {
        items.forEach(item => {
            const selCategory = this.processList.find(e => e.externalId === item.refOFWAProcess.externalId);
            if (selCategory) {
                this.selectCategory(selCategory);
            }

            this.loaderFlag = false;
            const selSubCategory = this.processList.find(e => e.externalId === item.externalId);
            if(selSubCategory) {
              setTimeout(() => {
                this.selectSubCategory(selSubCategory);
            }, 500);
            }
            
        });
    };

    const handleItems2 = (items) => {
      items.forEach(item => {
          this.loaderFlag = false;
          const selCategory = this.processList.find(e => e.externalId === item.externalId);
          setTimeout(() => {
              this.selectCategory(selCategory);
          }, 500);
      });
  };

    if (this.observation["refOFWAProcess"] && this.observation["refOFWAProcess"]["items"].length > 0) {
        handleItems(this.observation["refOFWAProcess"]["items"]);
    }

    if (this.observation["refCategory"] && this.observation["refCategory"]["items"].length > 0) {
        handleItems2(this.observation["refCategory"]["items"]);
    } else {
        this.loaderFlag = false;
    }
}

  infieldClick() {
    window.open('https://cognite-infield.cogniteapp.com/observation', '_blank', 'noopener, noreferrer')
  }
  async onFilterChange(item: any) {
    var _this = this;
    _this.dataService.postData({ "functionalLocationName": "", "equipmentName": item,"siteCode":_this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {
        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
      })

  }
  async onContractorFilterChange(search: any) {
    var _this = this;
    const filteredData = _.filter(_this.contractorList2, (item) => { return item.value.toLowerCase().indexOf(search.toLowerCase()) !== -1; });
    _this.filteredContractorList2 = filteredData;
  }
  async onFilterChangeWorkOrderNumber(item: any) {
    var _this = this;
    _this.dataService.postData({ "workOrderNumber": item, "siteCode": _this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listWorkOrderHeader").
      subscribe((resData: any) => {
        var orderList = resData["data"]["list" + _this.commonService.configuration["typeWorkOrderHeader"]]["items"]
        _this.filteredWorkOrderNumberList = orderList;
      })
  }
  resetSelection() {
  //  this.selectedItemsRoot = []; 
  var _this =this;
  _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "" ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {
        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
      })       
    }
  resetSelectionContractor() {
    var _this = this;
    _this.filteredContractorList2 = _this.contractorList2.slice();
  }
  resetSelectionWorkOrderNumber() {
    var _this =this;
    _this.dataService.postData({ "workOrderNumber": "", "siteCode": _this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listWorkOrderHeader").
      subscribe((resData: any) => {
        var orderList = resData["data"]["list" + _this.commonService.configuration["typeWorkOrderHeader"]]["items"]
        console.log(orderList)
        _this.filteredWorkOrderNumberList = orderList;
        _this.loaderFlag = false;
        _this.cd.detectChanges();
      })
  }
  async onBehalfChange(item: any) {
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.behalfList = [];
    _this.filteredBehalfList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
        _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.behalfList.forEach((element)=>{
          element.name =  element.firstName+' '+element.lastName
        })
        _this.filteredBehalfList = _this.behalfList.slice();
      
    _this.cdRef.detectChanges()
  
      })
      _this.cdRef.detectChanges()
       
  }
}

async onBehalfChange2(item: any) {
  var _this = this;
  
 var filter:any = document.getElementsByClassName('mat-filter-input');
 
 if(filter.length > 0){
  _this.fl_searchVal = filter[0]["value"]
  _this.behalfList2 = [];
  _this.filteredBehalfList2 = [];
    _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList2 =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList2.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList2 = _this.behalfList2.slice();
    
      _this.cdRef.detectChanges()

    })
    _this.cdRef.detectChanges()
    
  }
}
  onItemSelect(item: any) {
  }
  onSelectAll(items: any) {
  }
  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }

  async uploadFile($event: any) {
    this.loaderFlag = true;
    this.cd.detectChanges();  // Trigger change detection
    
    const fileInput = $event.target;
    const files = fileInput.files;
  
    // Check if files are selected
    if (!files || files.length === 0) {
        this.loaderFlag = false;
        this.cd.detectChanges();  // Trigger change detection
        console.log('No files selected.');
        return;
    }
  
    const totalFiles = files.length + this.images.length + this.videos.length;
    if (totalFiles > this.maxMedia) {
        this.loaderFlag = false;
        this.cd.detectChanges();  // Trigger change detection
        this.commonService.triggerToast({ type: 'error', title: '', msg: this.commonService.toasterLabelObject['toasterUploadreached'] });
        return;
    }
  
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        const isImage = file.type.startsWith('image/');
        const isVideo = file.type.startsWith('video/');
  
        // Using a promise to handle file reading
        await new Promise<void>((resolve, reject) => {
            reader.onload = (e: any) => {
                if (isImage) {
                    this.images.push(e.target.result);
                    console.log(`Image ${i + 1}/${files.length} loaded:`, file.name);
                } else if (isVideo) {
                    this.videos.push(e.target.result);
                    console.log(`Video ${i + 1}/${files.length} loaded:`, file.name);
                }
                this.cd.detectChanges();  // Trigger change detection after each file is loaded
                resolve();
            };
            reader.onerror = (e: any) => {
                console.error(`Error loading file ${file.name}:`, e);
                reject(e);
            };
  
            reader.readAsDataURL(file);
        });
  
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
  
        const myObj = {
            name: fileupload.name,
            mimetype: fileupload.mimeType,
            pathURL: fileupload.uploadUrl,
            pathStore: "CDF",
            evidenceDocumentId: fileupload.id + "",
            description: ""
        };
  
        console.log('External ID of newly uploaded file:', myObj["externalId"]);
        this.evidenceObj.push(myObj);
    }
  
    this.loaderFlag = false;
    this.cd.detectChanges();  // Trigger change detection after all files are processed
    
    // Reset file input value to allow re-selection of the same file
    fileInput.value = '';
}


  
  
  
  async saveImages() {
    console.log("saveImages called");
    if (this.evidenceObj.length === 0) {
      return; 
    }
  
    this.loaderFlag = true;
  
    const postObj = {
      type: this.commonService.configuration["typeEvidenceDocument"],
      siteCode: this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      unitCode: this.commonService.configuration["allUnitCode"],
      items: this.evidenceObj
    };

  
    try {
      const response = await this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").toPromise();
      console.log('All files data posted:', response);
      this.evidenceObj = response["items"];
      this.evidenceEdgeCreation(this.obRes);
  
      this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterImguploadsuccess'] });
    } catch (error) {
      console.error('Error saving images:', error);
      this.commonService.triggerToast({ type: 'error', title: 'Error', msg: this.commonService.toasterLabelObject['toasterImguploadfailed'] });
    } finally {
      this.loaderFlag = false; 
    }
   
  }

  removeImage(index: number) {
    if (index >= 0 && index < this.images.length) {
      // Remove the image from the UI
      this.images.splice(index, 1);
      console.log('Image removed at index', index);
      this.evidenceObj.splice(index, 1);
      this.removeObservationImage(index);
    }
  }


  removeVideo(index: number) {
    this.videos.splice(index, 1);
  }


  viewVideo(index: number) {
    this.viewedVideoIndex = index;
  }

  closeViewedVideo() {
    this.viewedVideoIndex = null;
  }

  viewImage(index: number): void {
    this.viewedImageIndex = index;
  }

  updateServerData() {
    var _this = this;
    const postObj = {
      "type": this.commonService.configuration["typeEvidenceDocument"],
      "siteCode": this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      "unitCode": this.commonService.configuration["allUnitCode"],
      "items": this.evidenceObj
    };
  
    this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        this.evidenceObj = data["items"];
      }
        _this.loaderFlag = false;
        this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterUploadsuccess'] });
      })
  }

  closeViewedImage(): void {
    this.viewedImageIndex = null;
  }


  removeObservationImage(index: number) {
    var _this = this;
    if (index >= 0 && index < this.observation.evidenceDocument.items.length) {
      const removedImage = this.observation.evidenceDocument.items[index];
      this.observation.evidenceDocument.items.splice(index, 1);


      const evidenceIndex = this.evidenceObj.findIndex(item => item.externalId === removedImage.externalId);
    if (evidenceIndex !== -1) {
      this.evidenceObj.splice(evidenceIndex, 1);
    }

    
      const disEdge = {
        items: [{
          instanceType: "edge",
          externalId: `${this.observation.externalId}-${removedImage.externalId}`,
          space: this.observation.space
        }]
      };
  
      if (this.dataService.postData) {
        console.log('Calling deleteInstance API...');
        _this.dataService.postData(disEdge, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(
          (response) => {
            console.log('Image deleted successfully from the table', response);
          },
          (error) => {
            console.error('Error deleting image from the table', error);
          }
        );
      } else {
        console.error('postData method not found in dataService');
      }
    } else {
      console.error('Invalid index for image removal:', index);
    }
  }

  closeModal() {
    this.showModal = false;
    this.modalImage = '';
  }

  

showOverlay(index: number): void {
  this.showOverlayIndex = index;
}

closeOverlay(): void {
  this.showOverlayIndex = null;
}
  selectUrgency(item) {
    var _this = this;
    _this.selectedUrgency = item;
    {
        if (item === 'High') {
          // Enable and set mandatory fields
          this.setFieldValidators('eventDesccription', this.configDetail.eventDesccription.isMandatory);
          this.setFieldValidators('descripeCorrectiveActionTaken', this.configDetail.descripeCorrectiveActionTaken.isMandatory);
        } else if (item != 'High') {
          // Disable and clear validators
          this.observedForm.get('eventDesccription').clearValidators();
          this.observedForm.get('descripeCorrectiveActionTaken').clearValidators();this.clearFieldValidators('eventDesccription');
          this.clearFieldValidators('descripeCorrectiveActionTaken');
        } else {
          // Clear all validators
          this.clearFieldValidators('eventDesccription');
          this.clearFieldValidators('descripeCorrectiveActionTaken');
        }
    }
  }

  previousClickedCategory: any = null;
  newClickedCategory: any = null;
  categorySubcategoryState = new Map();
  

  isSelected(item: any): boolean {
    return this.selectedCategories.some(selected => selected.externalId === item.externalId);
  }
  
  
  handleCategoryCheckboxChange(checked: boolean, item: any) {
    if (checked) {
      this.selectCategory(item);
    } else {
      this.deselectCategory(item);
    }
  }
  
  switchCategory(category: any) {
    if (category !== this.lastSelectedCategory) {
      // Store current subcategory state before switching
      if (this.lastSelectedCategory) {
        this.categorySubcategoryState.set(this.lastSelectedCategory.externalId, this.subCategoryList);
      }
  
      this.lastSelectedCategory = category;
  
      // Retrieve or initialize subcategory list based on the selected category
      if (this.categorySubcategoryState.has(category.externalId)) {
        // Restore subcategory state if it exists
        this.subCategoryList = this.categorySubcategoryState.get(category.externalId);
      } else {
        // Fetch subcategories for the selected category if state doesn't exist
        this.subCategoryList = this.getSubcategoriesForCategory(category);
      }
  
    }
  }
  

  
  isStep2Completed(): boolean {
    return this.categorycount> 0; // or any other condition for completing step 2
  }
  selectCategory(item) {
    var seletedCategoryIndex = this.categoryList.findIndex(e=> e.externalId == item.externalId);
    this.categorycount++
    const isSelected = this.isSelected(item);
    const actionMode = history.state.action;
    if (!isSelected || (actionMode === "Edit" || actionMode === "View")) {
      // Store current subcategory state before switching
      if (this.lastSelectedCategory && actionMode !== "Edit" && actionMode !== "View") {
        this.categorySubcategoryState.set(this.lastSelectedCategory.externalId, this.subCategoryList);
      }
  
      if (!isSelected) {
        this.selectedCategories.push(item);
      }
      this.lastSelectedCategory = item;
  
      // Restore subcategory state if it exists
      if (item && this.categorySubcategoryState && this.categorySubcategoryState.has(item.externalId)) {
        this.subCategoryList = this.categorySubcategoryState.get(item.externalId);
      } else {
        const childrenSubCategory = _.clone(this.processList).filter(e => {
          return (e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId;
        });
  
        this.subCategoryList = JSON.parse(JSON.stringify(childrenSubCategory));
        this.subCategoryList = this.subCategoryList.sort((a, b) => {
          if (a.sequence < b.sequence) { return -1; }
          if (a.sequence > b.name) { return 1; }
          return 0;
        });
        // this.categoryList[seletedCategoryIndex]["subCategoryList"] = this.subCategoryList;
      }
      
      // If no sub-categories, directly call questionFun and set isChecked
      if (this.subCategoryList.length == 0) {
        this.questionFun("Category", item["externalId"], item);
        item.isChecked = true; // Set isChecked if no sub-categories
        this.cd.detectChanges();
      }
      this.subCategoryList=this.subCategoryList.filter(e=> e?.isActive !== false);
      if(seletedCategoryIndex!=-1){
        this.categoryList[seletedCategoryIndex]["subCategoryList"] = this.subCategoryList;
      }
      if(this.isView && this.subCategoryList.length==1 && seletedCategoryIndex!=-1){
        this.toggleSelectAllSubCategories(this.categoryList[seletedCategoryIndex]["subCategoryList"], true)
      }
  
      if(this.subCategoryList.length==1 && !this.isView && this.z==1 && this.subCategoryList[0]["isActive"] != false && seletedCategoryIndex!=-1){
        this.toggleSelectAllSubCategories(this.categoryList[seletedCategoryIndex]["subCategoryList"],true)
      }
    }
  }
  
  
  deselectCategory(item) {
    
    this.categorycount--
    const isSelected = this.isSelected(item);
    const actionMode = history.state.action;
    var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
    if(myIndex!=-1){
      this.tabsListArr.splice(myIndex, 1);
    }
    if (isSelected) {
  
      // Remove the category from selectedCategories
      this.selectedCategories = this.selectedCategories.filter(selected => selected.externalId !== item.externalId);
      this.lastSelectedCategory = null;
      this.removeCategoryAndRelatedItems(item);
  
      // Clear subcategory list
      this.subCategoryList = [];
  
      // Clear subcategory state from memory
      this.categorySubcategoryState.delete(item.externalId);
  
      // If in edit mode, also clear subcategory selections in processList
      if (actionMode === "Edit" || actionMode === "View") {
        const childrenSubCategory = this.processList.filter(e => {
          return (e.refOFWAProcess && e.refOFWAProcess.externalId) === item.externalId;
        });
  
        childrenSubCategory.forEach(element => {
          element.selected = false;
          var myIndex = this.tabsListArr.findIndex(e=> e.id == element.id);
          if(myIndex!=-1){
            this.tabsListArr.splice(myIndex, 1);
          }
        });
      }
  
  
      
    }
  }
  
  
  toggleSelectAllSubCategories(subCategoryList,isSelected: boolean): void {
    if (isSelected) {
      var _this = this;
      // Select all subcategories if not already selected
      subCategoryList.forEach(item => {
        if (!item.selected && item.isActive !=false) {
          item.selected = true;
          // Check if subcategory is already in checklist array
          if (!this.checkListArray.some(checkListItem => checkListItem.id === item.refOFWAProcess.externalId)) {
            // Add subcategory to checklist array
            const categoryFormGroup = new FormGroup({
              isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
        isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
              activity: new FormControl(''),
              riskyAction: new FormControl(''),
              risk: new FormControl(''),
              riskAgreement: new FormControl('No'),
              reasonForAction: new FormControl(''),
              safeBehaviour: new FormControl('Possible'),
              suggestedSolution: new FormControl('')
            });
            
            var myCategory  = _this.commonService.processList.find(e => e.externalId == item.refOFWAProcess.externalId);
  
            const parentObj = {
              name: item.refOFWAProcess.name,
              bold: true,
              isCategory: true,
              id: item.refOFWAProcess.externalId,
              children: [],
              formGroup: categoryFormGroup,
              sequence:item.refOFWAProcess.sequence,
              guidelineDocument:myCategory.guidelineDocument ? myCategory.guidelineDocument: undefined
            };
  
            if (!_this.checkListArray.find(e => e.id == parentObj.id)) {
              _this.checkListArray.push(parentObj)
            }
            setTimeout(function(){
              _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
              _this.cd.detectChanges();
            }, 2000);
          } else {
            setTimeout(function(){
              _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
              _this.cd.detectChanges();
            }, 2000);
          }
        }
      });
    } else {
      // Deselect all subcategories
      const selectedSubcategoriesBefore = subCategoryList.filter(sub => sub.selected);
      subCategoryList.forEach(item => {
        item.selected = false;
        var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
        if(myIndex!=-1){
          this.tabsListArr.splice(myIndex, 1);
        }
      });
  
      // Deselect all selected subcategories individually
      // selectedSubcategoriesBefore.forEach(subcategory => {
      //   this.deselectSubcategory(subcategory);
        Promise.all(selectedSubcategoriesBefore.map(subcategory => this.deselectSubcategory(subcategory))).then(() => {
        this.deselectCategory(selectedSubcategoriesBefore[0].refOFWAProcess);
      });
    }
  
    // Log total number of subcategories selected
    const selectedSubcategoriesAfter = this.subCategoryList.filter(sub => sub.selected);
    console.log(`Total selected items: ${selectedSubcategoriesAfter.length}`);
  
    // this.treeExpand.treeControl.expandAll();
  }
  
  deselectSubcategory(subCategory: any): void {
    // Find the index of the parent item in checkListArray
    const parentIndex = this.checkListArray.findIndex(e => e.id === subCategory.refOFWAProcess.externalId);
  
    if (parentIndex !== -1) {
      // Find the index of the subCategory item in children array
      const myIndex = this.checkListArray[parentIndex].children.findIndex(e => e.id === subCategory.externalId);
  
      if (myIndex !== -1) {
        // Remove the subCategory item from children array
        this.checkListArray[parentIndex].children.splice(myIndex, 1);
        console.log('Removed item externalId:', subCategory.externalId);
  
        // Update the checklist array if parent has no more children
        if (this.checkListArray[parentIndex].children.length === 0) {
          this.checkListArray.splice(parentIndex, 1);
        }
  
        let tempArr = [];
        this.checkListArray.forEach(ele => {
          if (ele.children.length > 0) {
            ele.children.forEach(ele2 => {
              if (ele2.children && ele2.children.length > 0) {
                ele2.bold = true;
              }
            });
          }else{
            ele.bold = true;
          }
          tempArr.push(ele);
        });
        this.dataSource.data = tempArr;
  
        //this.treeExpand.treeControl.expandAll();
        console.log('Updated checkListArray:', this.checkListArray);
  
        this.removeCategoryAndRelatedItems(myIndex);
      } else {
        console.log('Subcategory with externalId', subCategory.externalId, 'not found in children array.');
      }
    } else {
      console.log('Parent item with externalId', subCategory.refOFWAProcess.externalId, 'not found in checkListArray.');
    }
  }
  
  isAllSubCategoriesSelected(subCategoryList): boolean {
    return subCategoryList.every(item => item.selected);
  }
  
  
  
  
  
  
  
  selectSubCategory(item) {
    const _this = this;
 
    // Check if item is already selected and not in edit or view mode
    if (item.selected && (!history.state.action || (history.state.action !== "Edit" && history.state.action !== "View"))) {
        item.selected = false;
        var myIndex = _this.tabsListArr.findIndex(e=> e.id == item.externalId);
        if(myIndex!=-1){
          _this.tabsListArr.splice(myIndex, 1);
        }
        _this.selectedSubCategory = null;
        _this.removeSubcategory(item);
 
        // Reset selected status for relevant categories
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = false;
            }
        });
    } else if (item.selected && history.state.action && (history.state.action === "Edit" || history.state.action === "View")) {
        // Ensure deselection in edit mode
        item.selected = false;
        _this.selectedSubCategory = null;
        _this.removeSubcategory(item);
 
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = false;
            }
        });
    } else {
        item.selected = true;
 
        // Update selected status for relevant categories
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = true;
            }
        });
 
        if (_this.subCategoryList.findIndex(e => e.externalId === item.externalId) !== -1) {
            _this.subCategoryList[_this.subCategoryList.findIndex(e => e.externalId === item.externalId)].selected = true;
        }
    }
 
    this.selectedSubCategory = item;
 
    // Ensure it only affects relevant data structures, not refOFWAChecklist
    if (!_this.checkListArray.find(e => e.id === item.refOFWAProcess.externalId) && item.selected) {
        const categoryFormGroup = new FormGroup({
            isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
            isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
            note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
            activity: new FormControl(''),
            riskyAction: new FormControl(''),
            risk: new FormControl(''),
            riskAgreement: new FormControl('No'),
            reasonForAction: new FormControl(''),
            safeBehaviour: new FormControl('Possible'),
            suggestedSolution: new FormControl('')
        });
 
        var myCategory  = _this.commonService.processList.find(e => e.externalId == item.refOFWAProcess.externalId);
        const parentObj = {
            name: item.refOFWAProcess.name,
            bold: true,
            isCategory: true,
            id: item.refOFWAProcess.externalId,
            children: [],
            formGroup: categoryFormGroup,
            sequence:item.refOFWAProcess.sequence,
            guidelineDocument:myCategory.guidelineDocument ? myCategory.guidelineDocument: undefined
        };
 
        if (!_this.checkListArray.find(e => e.id == parentObj.id)) {
          _this.checkListArray.push(parentObj)
        }
        _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
    } else {
        _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
    }
 
    _this.treeExpand.treeControl.expandAll();
}
  
  
  
  
  
  
  // Add this method to fetch subcategories for a given category
  getSubcategoriesForCategory(item) {
    const childrenSubCategory = _.clone(this.processList).filter(e => {
      return (e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId;
    });
  
    let subCategories = JSON.parse(JSON.stringify(childrenSubCategory));
    return subCategories.sort((a, b) => {
      if (a.sequence < b.sequence) { return -1; }
      if (a.sequence > b.sequence) { return 1; }
      return 0;
    });
  }
  
  
  
  removeCategoryAndRelatedItems(category) {
    const _this = this;
    const actionMode = history.state.action;
    // Array to store indices of items to be removed
    const itemsToRemoveIndices = [];
    // Array to store external IDs of all related subcategories
    const relatedSubCategoryExternalIds = [];
  
    // Remove the category itself from checkListArray
    const parentIndex = _this.checkListArray.findIndex(e => e.id === category.externalId);
    if (parentIndex !== -1) {
      console.log('Removing category:', category);
      _this.checkListArray.splice(parentIndex, 1);
      itemsToRemoveIndices.push(parentIndex); // Add parentIndex to the removal list
    }
  
    // Find related items in processList and remove them from checkListArray
    const relatedItems = _this.processList.filter(item => {
      return item.refOFWAProcess && item.refOFWAProcess.externalId === category.externalId;
    });
  
    relatedItems.forEach(item => {
      var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
      if(myIndex!=-1){
        this.tabsListArr.splice(myIndex, 1);
      }
      const itemIndex = _this.checkListArray.findIndex(e => e.id === item.externalId);
      if (itemIndex !== -1) {
        console.log('Removing related item:', item);
        _this.checkListArray.splice(itemIndex, 1);
        itemsToRemoveIndices.push(itemIndex); // Add itemIndex to the removal list
      }
      // Add all related subcategory external IDs to the array
      relatedSubCategoryExternalIds.push(item.externalId);
    });
  
    // Log all related subcategory external IDs
    console.log('Related subcategory external IDs:', relatedSubCategoryExternalIds);
  
    // Update the UI or data structure as needed
    var tempArr = [];
    _this.checkListArray.forEach(ele => {
        if (ele.children && ele.children.length > 0) {
            ele.children.forEach(ele2 => {
                if (ele2.children && ele2.children.length > 0) {
                    ele2.bold = true;
                }
            }); 
            tempArr.push(ele);
        } else {
            // Ensure categories without subcategories are not removed
            tempArr.push(ele);
        }
    });
  
    _this.dataSource.data = tempArr;
    _this.treeExpand.treeControl.expandAll();
    console.log('Updated checkListArray:', _this.checkListArray);
  
    // Call removeChecklistItem for each related item
    relatedItems.forEach(item => {
      const itemIndex = _this.checkListArray.findIndex(e => e.id === item.externalId);
      if (itemIndex !== -1) {
        _this.removeChecklistItem(itemIndex);
      }
    });
  
    
  //  _this.removeMainChecklistItem(parentIndex);
  
  if (actionMode === "Edit" ) {
    _this.deleteAllRelatedSubCategoryExternalIds(relatedSubCategoryExternalIds);
}
   
  }
  
  deleteAllRelatedSubCategoryExternalIds(externalIds: string[]) {
    externalIds.forEach(externalId => {
      // Perform deletion logic here for each external ID
      const index = this.observation.refOFWAProcess.items.findIndex(item => item.externalId === externalId);
      if (index !== -1) {
        this.removeChecklistItem(index);
      } else {
        console.warn(`External ID ${externalId} not found in observation items.`);
      }
    });
  }
  
  
  
  
  
  
  removeChecklistItem(index: number) {
    if (index >= 0 && index < this.observation && this.observation.refOFWAProcess.items.length) {
      // Log the external ID of the item before deletion
      const removedItem = this.observation.refOFWAProcess.items[index];
      console.log('Removing item with externalId:', removedItem.externalId);
  
      // Remove the item from the array
      this.observation.refOFWAProcess.items.splice(index, 1);
      console.log('Updated observation:', this.observation);
  
      // Construct the disEdge object with the externalId of the removed item
      const disEdge = {
        items: [{
          instanceType: "edge",
          externalId: `${this.observation.externalId}-${removedItem.externalId}`,
          space: this.observation.space
        }]
      };
  
      console.log('disEdge object:', disEdge);
      
      // Check if dataService and postData are available
      if (this.dataService && this.dataService.postData) {
        console.log('Calling deleteInstance API...');
        // Call postData to delete the instance
        this.dataService.postData(disEdge, `${this.dataService.NODE_API}/api/service/deleteInstance`).subscribe(
          (response) => {
            console.log('Item deleted successfully from the table', response);
          },
          (error) => {
            console.error('Error deleting item from the table', error);
          }
        );
      } else {
        console.error('DataService or postData method not found.');
      }
    } else {
      console.error('Invalid index for item removal:', index);
    }
  }
  // questionFun(type,categoryId,item:any) {
  //   console.log("categoryId ----->",categoryId)
    
  //   console.log("item ----->",item)
  //   var _this = this;
  //   var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == categoryId);
    
  //   console.log(myQuestions)
  //   if (myQuestions.length == 0) {
  //     _this.loaderCount ++;
  //     _this.dataService.postData({ "externalId": categoryId }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
  //       console.log(resData)
  //       _this.loaderCount --;
  //       var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
  //       _this.questionList = _.concat(_this.questionList, myQuestionList)
  //       if(type == "Category"){
  //         _this.addCategory(item);
  //         _this.checklistCategoryQuestion(item);
  //       }else{
  //         _this.checkListUpdate(item);
  //         _this.checklistQuestion(item);
  //       }
        
  //     })
  //   } else {
  //     if(type == "Category"){
  //       _this.addCategory(item);
  //       _this.checklistCategoryQuestion(item);
  //     }else{
  //       _this.checkListUpdate(item);
  //       _this.checklistQuestion(item);
  //     }
  //   }
  //   //_this.treeExpand.treeControl.expandAll();
  // }
  
  checklistPrimaryQuestionArray: any = [];
  checklistSubQuestionArray: any = [];
  
 questionFun(type,categoryId,item:any) {
    var _this = this;
    if (type === "SubProcess") {
        var primaryQuestion = _this.questionList.filter(e => e["refOFWASubProcess"]["externalId"] == categoryId);
        if (primaryQuestion.length == 0) {
          _this.loaderCount++;
          _this.dataService.postData({ "externalId": categoryId }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
            _this.loaderCount--;
            var primaryQuestionList = (resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"]).filter(item => item.refOFWACategory === null && item.refOFWACategory?.isActive !== false);
            if(primaryQuestionList.length > 0){
              
            _this.questionList = _.concat(_this.questionList, primaryQuestionList);
            
            // Add primary question to checklist
            _this.addPrimaryQuestion(item);
            _this.checklistPrimaryQuestion(item);
            _this.cd.detectChanges();
            }
          });
        } else {
          // Add primary question if already fetched
          _this.addPrimaryQuestion(item);
          _this.checklistPrimaryQuestion(item);
          _this.cd.detectChanges();
        }
      } else {
        var myQuestions = _this.questionList.filter(e => e["refOFWACategory"] && e["refOFWACategory"]["externalId"] == categoryId);

    if (myQuestions.length == 0) {
      _this.loaderCount ++;
      _this.dataService.postData({ "externalId": categoryId }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
        _this.loaderCount --;
        var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"].filter(item =>item?.isActive !== false);
        _this.questionList = _.concat(_this.questionList, myQuestionList)
        if(type == "Category"){
          _this.addCategory(item);
          _this.checklistCategoryQuestion(item);
        }else{
          _this.checkListUpdate(item);
          _this.checklistQuestion(item);
        }
        
      })
    } else {
      if(type == "Category"){
        _this.addCategory(item);
        _this.checklistCategoryQuestion(item);
      }else{
        _this.checkListUpdate(item);
        _this.checklistQuestion(item);
      }
    }
    //_this.treeExpand.treeControl.expandAll();
  }}

  addPrimaryQuestion(item) {
    const _this = this;
    let primaryQuestionFormGroup;
    let primaryQuestionAns = null;
    // Fetch answers for the primary question (if applicable)
    if (this.observation) {
      primaryQuestionAns = this.observation.refOFWAChecklist.items.find(e => e.refOFWASubProcess && e.refOFWASubProcess.externalId === item.externalId);
    }
  
    if (primaryQuestionAns) {
      let ansVal;
      let isSafe = "";
  
      if (primaryQuestionAns.isSafe) isSafe = "safe";
      if (primaryQuestionAns.isUnSafe) isSafe = "unsafe";
      if (primaryQuestionAns.isNotObserved) isSafe = "notObserved";
  
      primaryQuestionFormGroup = new FormGroup({
        isSafe: new FormControl(isSafe),
        note: new FormControl({
          value: primaryQuestionAns.note || '',
          disabled: _this.isView 
            ? true 
            : primaryQuestionAns.note 
              ? false 
              : ansVal 
                ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") 
                : _this.defaultItem?.isNotes === "No"
        }),
        activity: new FormControl(primaryQuestionAns.activity),
        riskyAction: new FormControl(primaryQuestionAns.riskyAction),
        risk: new FormControl(primaryQuestionAns.risk),
        riskAgreement: new FormControl(primaryQuestionAns.riskAgreement),
        reasonForAction: new FormControl(primaryQuestionAns.reasonForAction),
        safeBehaviour: new FormControl(primaryQuestionAns.safeBehaviour),
        suggestedSolution: new FormControl(primaryQuestionAns.suggestedSolution)
      });
      
      if (_this.isView) primaryQuestionFormGroup.disable();
  
    } else {
      primaryQuestionFormGroup = new FormGroup({
        isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
        note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
        activity: new FormControl(''),
        riskyAction: new FormControl(''),
        risk: new FormControl(''),
        riskAgreement: new FormControl('No'),
        reasonForAction: new FormControl(''),
        safeBehaviour: new FormControl('Possible'),
        suggestedSolution: new FormControl('')
      });
    }
  
    const primaryObj = {
      name: item.name,
      bold: true,
      isPrimaryQuestion: true,
      id: item.externalId,
      children: [], // Initialize children as an empty array
      formGroup: primaryQuestionFormGroup,
      sequence:item.sequence,
      guidelineDocument: item.guidelineDocument ? item.guidelineDocument : undefined
    };
    if (!_this.checkListArray.find(e => e.id == primaryObj.id)) {
      _this.checkListArray.push(primaryObj)
    }
    _this.updateBoldProperties();
    _this.expandAll();
    
    _this.cd.detectChanges();
  }
  checklistPrimaryQuestion(primaryQuestion: any) {
    const _this = this;
  
    // Find the index of the primary question in the checklist array
    const primaryQuestionIndex = _this.checkListArray.findIndex(e => e && e.id == primaryQuestion["externalId"]);
  
    if (primaryQuestionIndex != -1) {
      // Filter the questions related to the primary question (previously subProcess)
      const myQuestion = _this.questionList.filter(e => e["refOFWASubProcess"]["externalId"] === primaryQuestion["externalId"]);
    
      if (myQuestion.length > 0) {
        if (_this.checkListArray[primaryQuestionIndex]["children"].length === 0) {
          myQuestion.forEach((question) => {
            let primaryQuestionFormGroup;
            const questionExist = _this.checkListArray[primaryQuestionIndex]["children"].find(e => e.id === question["externalId"]);
    
            if (!questionExist) {
              // Handle existing answer logic if `observation` exists
              if (_this.observation) {
                var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => 
                  (e.refOFWAQuestion && e.refOFWAQuestion.externalId === question["externalId"])
                );
    
                if (questionAns) {
                  let ansVal = '';
                  let isSafe = '';
    
                  // Set the safety status based on the observation answer
                  if (questionAns.isSafe) {
                    isSafe = "safe";
                    ansVal = "safe";
                  }
                  if (questionAns.isUnSafe) {
                    isSafe = "unsafe";
                    ansVal = "notsafe";
                  }
                  if (questionAns.isNotObserved) {
                    isSafe = "notObserved";
                    ansVal = "notobserved";
                  }
    
                  // Create a form group with the observation answer data
                  primaryQuestionFormGroup = new FormGroup({
                    isSafe: new FormControl(isSafe),
                    isInjuryPotential: new FormControl({ value: questionAns.isInjuryPotential ? "Yes" : "No", disabled: true }), 
                    note: new FormControl({
                      value: questionAns.note || '',
                      disabled: _this.isView 
                        ? true 
                        : questionAns.note 
                          ? false 
                          : ansVal 
                            ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") 
                            : _this.defaultItem?.isNotes === "No"
                    }),                  
                    activity: new FormControl(questionAns.activity),
                    riskyAction: new FormControl(questionAns.riskyAction),
                    risk: new FormControl(questionAns.risk),
                    riskAgreement: new FormControl(questionAns.riskAgreement),
                    reasonForAction: new FormControl(questionAns.reasonForAction),
                    safeBehaviour: new FormControl(questionAns.safeBehaviour),
                    suggestedSolution: new FormControl(questionAns.suggestedSolution)
                  });
                  _this.cd.detectChanges();
                } else {
                  // Create a new form group with default values if no observation answer is found
                  primaryQuestionFormGroup = new FormGroup({
                    isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
                    isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
                    note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                    activity: new FormControl(''),
                    riskyAction: new FormControl(''),
                    risk: new FormControl(''),
                    riskAgreement: new FormControl('No'),
                    reasonForAction: new FormControl(''),
                    safeBehaviour: new FormControl('Possible'),
                    suggestedSolution: new FormControl('')
                  });
                }
              } else {
                // Create a default form group when there's no observation data
                primaryQuestionFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
                  isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
                  note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }
    
              // Add the primary question to the checklist array
              _this.checkListArray[primaryQuestionIndex]["children"].push({
                name: question["name"],
                question: question["description"],
                sequence: question["sequence"] ? question["sequence"] : question["name"],
                bold: false,
                id: question["externalId"],
                formGroup: primaryQuestionFormGroup,
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
              });
    
              // Adjust bold property and sorting logic
              const tempArr = [];
              _this.checkListArray.forEach(ele => {
                if (ele.children.length > 0) {
                  ele.children.forEach(ele2 => {
                    if (ele2.question) {
                      ele2.bold = false;
                      ele.bold = true;
                    }
                  });
                } else {
                  ele.bold = false;
                }
                tempArr.push(ele);
              });
              _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
              _this.expandAll();
            }
          });
        }
      }
    }
    
    _this.cd.detectChanges();
    
  }

  configSetupValidation() {
    var _this = this;
    const tempArr = [];
    _this.checkListArray.forEach(ele => {
      if (ele.children.length > 0) {
        ele.children.forEach(ele2 => {
          if (ele2.question) {
            ele2.bold = false;
            ele.bold = true;
          }
        });
      } else {
        ele.bold = false;
      }
      tempArr.push(ele);
    });
    _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
    _this.expandAll();
  }

  addCategory(item) {
    const _this = this;
    let subCategoryFormGroup;
    let subCategoryAns = null;
  
    if (this.observation) {
      subCategoryAns = this.observation.refOFWAChecklist.items.find(e => {
        const subCategoryMatch = !e.refOFWACategory || e.refOFWACategory.externalId === item.externalId;
        return subCategoryMatch && !e.refOFWAQuestion;
      });
    }
    if (subCategoryAns) {
      var ansVal;
      // Populate form group with existing data
      let isSafe = "";
      if (subCategoryAns.isSafe) {
        isSafe = "safe";
        ansVal = "safe";
      }
      if (subCategoryAns.isUnSafe) {
        isSafe = "unsafe";
        ansVal = "notsafe";
      } else if (subCategoryAns.isNotObserved) {
        isSafe = "notObserved";
        ansVal = "notobserved";
      }
      if(ansVal){
        _this.newConfigDetail[ansVal]["isNotes"]
      }else{
        _this.defaultItem?.isNotes === "No"
      }
      
      subCategoryFormGroup = new FormGroup({
        isSafe: new FormControl(isSafe),
        isInjuryPotential: new FormControl({ value: subCategoryAns.isInjuryPotential ? "Yes" : "No", disabled: true}),
        note: new FormControl({ value: subCategoryAns.note || '', disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
        activity: new FormControl(subCategoryAns.activity),
        riskyAction: new FormControl(subCategoryAns.riskyAction),
        risk: new FormControl(subCategoryAns.risk),
        riskAgreement: new FormControl(subCategoryAns.riskAgreement),
        reasonForAction: new FormControl(subCategoryAns.reasonForAction),
        safeBehaviour: new FormControl(subCategoryAns.safeBehaviour),
        suggestedSolution: new FormControl(subCategoryAns.suggestedSolution)
      });
  
      if (_this.isView) {
        subCategoryFormGroup.disable();
      }
    } else {
      // Create new form group with default values
      subCategoryFormGroup = new FormGroup({
        isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
        isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
        note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
        activity: new FormControl(''),
        riskyAction: new FormControl(''),
        risk: new FormControl(''),
        riskAgreement: new FormControl('No'),
        reasonForAction: new FormControl(''),
        safeBehaviour: new FormControl('Possible'),
        suggestedSolution: new FormControl('')
      });
    }
  
    const parentObj = {
      name: item.name,
      bold: true,
      isCategory: true,
      id: item.externalId,
      children: [], // Initialize children as an empty array
      formGroup: subCategoryFormGroup,
      sequence:item.sequence,
      guidelineDocument:item.guidelineDocument ? item.guidelineDocument: undefined
    };
  
    if(subCategoryAns && subCategoryAns.isUnSafe){
      _this.radioButton(parentObj)
    }
    if (!_this.checkListArray.find(e => e.id == parentObj.id)) {
      _this.checkListArray.push(parentObj)
    }
  
    // Update bold property based on children presence
    _this.updateBoldProperties();
  
    _this.expandAll();
  }
  
  updateBoldProperties() {
    var _this = this;
    // Iterate through checkListArray to update bold property based on children presence
    _this.checkListArray.forEach(ele => {
      ele.bold = ele.children && ele.children.length > 0;
    });
  
    // Sort checkListArray by 'sequence' if applicable
    _this.dataSource.data = _.sortBy(_this.checkListArray, ['sequence']);
  }
  

  removeSubcategory(subCategory) {
    const _this = this;
    
    // Find the index of the parent item in checkListArray
    const parentIndex = _this.checkListArray.findIndex(e => e.id === subCategory.refOFWAProcess.externalId);
    
    if (parentIndex !== -1) {
      // Find the index of the subCategory item in children array
      const myIndex = _this.checkListArray[parentIndex].children.findIndex(e => e.id === subCategory.externalId);
      
      if (myIndex !== -1) {
        // Remove the subCategory item from children array
        _this.checkListArray[parentIndex].children.splice(myIndex, 1);
        
        // Log the removed item's externalId
        
        // Update dataSource.data if needed
        let tempArr = [];
        _this.checkListArray.forEach(ele => {
          if (ele.children.length > 0) {
            ele.children.forEach(ele2 => {
              if (ele2.children && ele2.children.length > 0) {
                ele2.bold = true;
              }
            });
          }else{
            ele.bold = false;
          }
          tempArr.push(ele);
        });
        _this.dataSource.data = tempArr;
        
        // Expand all items in tree control
        _this.expandAll();
        
        // Optionally log the updated checkListArray for verification
  
        // Call removeChecklistItem for the subCategory item
        _this.removeChecklistItem(myIndex);
        _this.checkListArray.forEach((question) => {
          if(question.children.length ==0){
            var cat = { externalId: question.id };
            _this.deselectCategory(cat);
          }
          })
      } else {
        console.log('Subcategory with externalId', subCategory.externalId, 'not found in children array.');
      }
    } else {
      console.log('Parent item with externalId', subCategory.refOFWAProcess.externalId, 'not found in checkListArray.');
    }
  }

  checkListUpdate(subCategory: any) {
    const _this = this;
    _this.checkListArray.forEach(ele => {
      console.log(ele.children && ele.children.length > 0)
    });
    // _this.checkListArray.forEach(item=> {
    //   var itemnew=item
    //   console.log("I AM IN",itemnew)
    //   console.log("I AM IN",itemnew.bold)
    //   delete itemnew.formGroup;
    //   console.log("I AM IN",itemnew)
    //   // delete itemnew.children.formGroup;
    //   console.log("I AM IN",itemnew.children)
    //   if(item?.children.length==1){
    //     console.log("I AM IN")
    //   if (item?.name == item?.children[0].name)
    //     console.log("RETUNNINGGG")
    //     return;
    // }
    // })
    
    const parentIndex = _this.checkListArray.findIndex(e => e && e.id === subCategory.refOFWAProcess.externalId);
    const myIndex = _this.checkListArray.findIndex(e => e && e.id === subCategory.externalId);
  
    if (subCategory.selected === true) {
      let subCategoryFormGroup;
      let subCateggoryAns = null;
  
      if (this.observation) {
    subCateggoryAns = this.observation.refOFWAChecklist.items.find(e => {
        const subCategoryMatch = e.refOFWASubCategory && e.refOFWASubCategory.externalId === subCategory.externalId;
        return subCategoryMatch && (!e.refOFWAQuestion || !e.refOFWAQuestion.externalId );
    });
}

  
      if (subCateggoryAns) {
        // Populate form group with existing data
        var ansVal;
        let isSafe = "";
        if (subCateggoryAns.isSafe) {
          isSafe = "safe";
          ansVal = "safe";
        }
        if (subCateggoryAns.isUnSafe) {
          isSafe = "unsafe";
          ansVal = "notsafe";
        } else if (subCateggoryAns.isNotObserved) {
          isSafe = "notObserved";
          ansVal = "notobserved";
        }
        if (ansVal) {
          _this.newConfigDetail[ansVal]["isNotes"]
        } else {
          _this.defaultItem?.isNotes === "No"
        }
        subCategoryFormGroup = new FormGroup({
          isSafe: new FormControl(isSafe),
          isInjuryPotential: new FormControl({ value: subCateggoryAns.isInjuryPotential ? "Yes" : "No", disabled: true }),
          note: new FormControl({ value: subCateggoryAns.note || '', disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
          activity: new FormControl(subCateggoryAns.activity),
          riskyAction: new FormControl(subCateggoryAns.riskyAction),
          risk: new FormControl(subCateggoryAns.risk),
          riskAgreement: new FormControl(subCateggoryAns.riskAgreement),
          reasonForAction: new FormControl(subCateggoryAns.reasonForAction),
          safeBehaviour: new FormControl(subCateggoryAns.safeBehaviour),
          suggestedSolution: new FormControl(subCateggoryAns.suggestedSolution)
        });
  
        if (_this.isView) {
          subCategoryFormGroup.disable();
        }
      } else {
        // Create a new form group with default values
        subCategoryFormGroup = new FormGroup({
          isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
          isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
          note: new FormControl({ value: '', disabled: _this.defaultItem && _this.defaultItem.isNotes === "No" }),
          activity: new FormControl(''),
          riskyAction: new FormControl(''),
          risk: new FormControl(''),
          riskAgreement: new FormControl('No'),
          reasonForAction: new FormControl(''),
          safeBehaviour: new FormControl('Possible'),
          suggestedSolution: new FormControl('')
        });
      }
  
      const nodeObj = {
        name: subCategory.name,
        bold: false, // Ensure subcategories are not bolded
        id: subCategory.externalId,
        sequence:subCategory.sequence,
        children: [],
        checkListId: subCateggoryAns ? subCateggoryAns.externalId : "",
        checkListSpace: subCateggoryAns ? subCateggoryAns.space : "",
        formGroup: subCategoryFormGroup,
        guidelineDocument:subCategory.guidelineDocument ? subCategory.guidelineDocument: undefined
      };

      // if(subCateggoryAns && subCateggoryAns.isUnSafe){
      //   _this.radioButton(nodeObj)
      // }
  
      // Check if myIndex is -1 and add the node to checkListArray
      if (myIndex === -1) {
        // If parentIndex is found, add to children of that category
        if (parentIndex !== -1) {
          if(!_this.checkListArray[parentIndex].children.find(e=> e.id == nodeObj.id)){
            _this.checkListArray[parentIndex].children.push(nodeObj);
          }
          
          _this.checkListArray[parentIndex].children = _.sortBy(_this.checkListArray[parentIndex].children, ['sequence']);
         if(_this.observation && _this.observation.refOFWAChecklist){
            var subCateggoryAnsN = _this.observation.refOFWAChecklist.items.find(e => {
              const subCategoryMatch = e.refOFWASubCategory && e.refOFWAQuestion  && e.refOFWASubCategory.externalId === subCategory.externalId;
              return subCategoryMatch 
          });
            if(subCateggoryAns && subCateggoryAns.isUnSafe && !subCateggoryAnsN){
              _this.radioButton(nodeObj)
            }
          }
        } else {
          // Add at the end of the array
          var myCategory = _this.commonService.processList.find(e => e.externalId == subCategory.refOFWAProcess.externalId);
          var myObj = {
            name: subCategory.refOFWAProcess.name,
            bold: true, // Ensure categories with children are bolded
            id: subCategory.refOFWAProcess.externalId,
            children: [nodeObj],
            sequence:subCategory.refOFWAProcess.sequence,
            guidelineDocument: myCategory.guidelineDocument ? myCategory.guidelineDocument : undefined
          }
          if (!_this.checkListArray.find(e => e.id == myObj.id)) {
            _this.checkListArray.push(myObj);
          }

          
        }
  
        // Sort checkListArray by id after adding new node
        _this.checkListArray.sort((a, b) => a.id.localeCompare(b.id));
  
        // Update dataSource.data after sorting
        _this.dataSource.data = _.sortBy(_this.checkListArray, ['id']);
      }
    }
  
    // Ensure UI updates
    _this.expandAll();
    _this.checklistQuestion(subCategory);
  }
  
  
  
  
  

//   onSelectChange(a:any){
//     var _this = this;
//     if (a.controls.isInjuryPotential.value === 'Yes') {
//       a.controls.note.enable()
//   } else if (a.controls.isInjuryPotential.value === 'No') {
//     a.controls.note.disable() 
//   }
//   if (_this.newConfigDetail.notobserved.isNotes===false  
//     || _this.newConfigDetail.notobserved.isNotes=="No" || _this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No" ||
//     _this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No")
//     a.controls.note.disable() 

// }


  onCheckboxChange(a:any) {
    var _this = this;
    // SAFE
    if (a.value.isSafe === 'safe') {
      if(_this.newConfigDetail.safe.isInjuryPotential!="null"){
        a.controls.isInjuryPotential.disable()
        if(_this.newConfigDetail.safe.isInjuryPotential===false || _this.newConfigDetail.safe.isInjuryPotential=="No")
          {
            a.controls.isInjuryPotential.setValue('No')
            if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
              a.controls.note.disable()
            }
            else a.controls.note.enable()
        }
        else
        {
          a.controls.isInjuryPotential.setValue('Yes')
          if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
            a.controls.note.disable()
          }
          else a.controls.note.enable()
        }
    }
    else
    {
      a.controls.isInjuryPotential.enable()
      a.controls.isInjuryPotential.setValue('')
      if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
  }
  // UNSAFE
  if (a.value.isSafe === 'unsafe') {
    if(_this.newConfigDetail.notsafe.isInjuryPotential!="null"){
      a.controls.isInjuryPotential.disable()
      if(_this.newConfigDetail.notsafe.isInjuryPotential===false || _this.newConfigDetail.notsafe.isInjuryPotential=="No")
        {
          a.controls.isInjuryPotential.setValue('No')
          if(_this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No"){
            a.controls.note.disable()
          }
          else a.controls.note.enable()
      }
      else
      {
        a.controls.isInjuryPotential.setValue('Yes')
        if(_this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No"){
          a.controls.note.disable()
        }
        else a.controls.note.enable()
      }
  }
  else
  {
    a.controls.isInjuryPotential.enable()
    a.controls.isInjuryPotential.setValue('')
    if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
      a.controls.note.disable()
    }
    else a.controls.note.enable()
  }
}
// NOT OBSERVED
if (a.value.isSafe === 'notObserved') {
  if(_this.newConfigDetail.notobserved.isInjuryPotential!="null"){
    a.controls.isInjuryPotential.disable()
    if(_this.newConfigDetail.notobserved.isInjuryPotential===false || _this.newConfigDetail.notobserved.isInjuryPotential=="No")
      {
        a.controls.isInjuryPotential.setValue('No')
        if(_this.newConfigDetail.notobserved.isNotes===false || _this.newConfigDetail.notobserved.isNotes=="No"){
          a.controls.note.disable()
        }
        else a.controls.note.enable()
    }
    else
    {
      a.controls.isInjuryPotential.setValue('Yes')
      if(_this.newConfigDetail.notobserved.isNotes===false || _this.newConfigDetail.notobserved.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
}
else
{
  a.controls.isInjuryPotential.enable()
  a.controls.isInjuryPotential.setValue('')
  if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
    a.controls.note.disable()
  }
  else a.controls.note.enable()
}
}
  }

  checklistCategoryQuestion(category: any) {
    var _this = this;
    
    var categoryIndex = _this.checkListArray.findIndex(e => e && e.id == category["externalId"]);
   
    if (categoryIndex != -1) {
      var myQuestion = _this.questionList.filter(e => e["refOFWACategory"] && e["refOFWACategory"]["externalId"] == category["externalId"]);
      if (myQuestion.length > 0 ) {
        if (_this.checkListArray[categoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var categoryFormGroup;
            var questionExist = _this.checkListArray[categoryIndex]["children"].find(e=>e.id ==question["externalId"])
            if(!questionExist){
            if (_this.observation) {
              var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => (e.refOFWACategory.externalId == category.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              if (questionAns) {
                var ansVal;
                var isSafe = "";
                if (questionAns.isSafe) {
                  isSafe = "safe";
                  ansVal = "safe";
                }
                if (questionAns.isUnSafe) {
                  isSafe = "unsafe";
                  ansVal = "notsafe";
                }
                if (questionAns.isNotObserved) {
                  isSafe = "notObserved";
                  ansVal = "notobserved";
                }
                var isInjuryPotential = "No";
                if (questionAns.isInjuryPotential) {
                  isInjuryPotential = "Yes"
                }
                
                if (ansVal.length > 0) {
                  _this.newConfigDetail[ansVal]["isNotes"]
                } else {
                  _this.defaultItem?.isNotes === "No"
                }
                categoryFormGroup = new FormGroup({
                  isSafe: new FormControl(isSafe),
                  // isInjuryPotential: new FormControl(isInjuryPotential),
                  isInjuryPotential:new FormControl({value:isInjuryPotential ? isInjuryPotential: _this.defaultItem.isInjuryPotential ,disabled: true }),
                  // note: new FormControl(questionAns.note),
                  note: new FormControl({ value: questionAns? questionAns.note  : "", disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
                  activity: new FormControl(questionAns.activity),
                  riskyAction: new FormControl(questionAns.riskyAction),
                  risk: new FormControl(questionAns.risk),
                  riskAgreement: new FormControl(questionAns.riskAgreement),
                  reasonForAction: new FormControl(questionAns.reasonForAction),
                  safeBehaviour: new FormControl(questionAns.safeBehaviour),
                  suggestedSolution: new FormControl(questionAns.suggestedSolution)
                });
                if (_this.isView) {
                  categoryFormGroup.disable();
                }
              } else {
                categoryFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                  isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }

            } else {
              categoryFormGroup = new FormGroup({
                isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                activity: new FormControl(''),
                riskyAction: new FormControl(''),
                risk: new FormControl(''),
                riskAgreement: new FormControl('No'),
                reasonForAction: new FormControl(''),
                safeBehaviour: new FormControl('Possible'),
                suggestedSolution: new FormControl('')
              });
            }


          var myObj = {
            name: question["name"],
            question: question["description"],
            sequence:question["sequence"] ? question["sequence"] : question["name"],
            bold: false,
            id: question["externalId"],
            checkListId: questionAns ? questionAns.externalId :"",
            checkListSpace: questionAns ? questionAns.space : "",
            formGroup: categoryFormGroup
          }
          if (!_this.checkListArray[categoryIndex]["children"].find(e => e.id == myObj.id)) {
            _this.checkListArray[categoryIndex]["children"].push(myObj)
          }
           
            

          }
          var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                if(ele2.question){
                  ele2.bold = false;
                  ele.bold = true;
                }
                // if(ele2.children.length > 0){
                //   ele2.bold = true
                // }
              });
            }else{
              ele.bold = false;
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
          })
          
          
        }

      }else{
        var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                if(ele2.children && ele2.children.length > 0){
                  ele2.bold = true
                }else{
                  ele2.bold = false;
                }
              });
            }else{
              ele.bold = false
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
      }
    }

  }

  checklistQuestion(subCategory: any) {
    var _this = this;
    var categoryIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    if (categoryIndex != -1) {
      var subCategoryIndex = _this.checkListArray[categoryIndex].children.findIndex(e => e["id"] == subCategory["externalId"]);
      var myQuestion = _this.questionList.filter(e => e["refOFWASubCategory"] && e["refOFWASubCategory"]["externalId"] == subCategory["externalId"]);
      if (myQuestion.length > 0 && subCategoryIndex != -1) {
        if (_this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var subCategoryFormGroup;
            var questionExist = _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].find(e=>e.id ==question["externalId"])
            if(!questionExist){
            if (_this.observation) {
              var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => (e.refOFWASubCategory.externalId == subCategory.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              if (questionAns) {
                var ansVal;
                var isSafe = "";
                if (questionAns.isSafe) {
                  isSafe = "safe";
                  ansVal = "safe";
                }
                if (questionAns.isUnSafe) {
                  isSafe = "unsafe";
                  ansVal = "notsafe";
                }
                if (questionAns.isNotObserved) {
                  isSafe = "notObserved";
                  ansVal = "notobserved";
                }
                var isInjuryPotential = "No";
                if (questionAns.isInjuryPotential) {
                  isInjuryPotential = "Yes"
                }
                if(ansVal.length>0){
                  _this.newConfigDetail[ansVal]["isNotes"]
                }else{
                  _this.defaultItem?.isNotes === "No"
                }
                subCategoryFormGroup = new FormGroup({
                  isSafe: new FormControl(isSafe),
                  // isInjuryPotential: new FormControl(isInjuryPotential),
                  isInjuryPotential:new FormControl({value:isInjuryPotential ? isInjuryPotential: _this.defaultItem.isInjuryPotential ,disabled: true }),
                  // note: new FormControl(questionAns.note),
                  note: new FormControl({ value: questionAns? questionAns.note  : "", disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No"  }),
                  activity: new FormControl(questionAns.activity),
                  riskyAction: new FormControl(questionAns.riskyAction),
                  risk: new FormControl(questionAns.risk),
                  riskAgreement: new FormControl(questionAns.riskAgreement),
                  reasonForAction: new FormControl(questionAns.reasonForAction),
                  safeBehaviour: new FormControl(questionAns.safeBehaviour),
                  suggestedSolution: new FormControl(questionAns.suggestedSolution)
                });
                if (_this.isView) {
                  subCategoryFormGroup.disable();
                }
              } else {
                subCategoryFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                  isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }

            } else {
              subCategoryFormGroup = new FormGroup({
                isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                activity: new FormControl(''),
                riskyAction: new FormControl(''),
                risk: new FormControl(''),
                riskAgreement: new FormControl('No'),
                reasonForAction: new FormControl(''),
                safeBehaviour: new FormControl('Possible'),
                suggestedSolution: new FormControl('')
              });
            }


           
            _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].push({
              name: question["name"],
              question: question["description"],
              bold: false,
              sequence:question["sequence"] ? question["sequence"] : question["name"],
              id: question["externalId"],
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
              formGroup: subCategoryFormGroup
            })

          }
          var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                if(ele2.children && ele2.children.length > 0){
                  ele2.bold = true
                }else{
                  ele2.bold = false
                }
              });
              // tempArr.push(ele)
            }else{
              ele.bold = false
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
          })
          
          
        }

      }else{
      }
    }

  }

  radioButton(node) {
    
    if (!node.question && !this.tabsListArr.find(e => e.id == node.id)) {
      this.tabsListArr.push(node)
    }
  }
  spliceFeedback(node){
    var myIndex = this.tabsListArr.findIndex(e=> e.id == node.id);
    if(myIndex!=-1){
      this.tabsListArr.splice(myIndex, 1);
    }
    // const removed = this.tabsListArr.splice(2, 2, "guava");
    // this.tabsListArr.push(node)
  }


  popupAnimation = trigger('popupAnimation', [
    state('hide', style({
      opacity: 0,
      transform: 'translateY(-50px)'
    })),
    state('show', style({
      opacity: 1,
      transform: 'translateY(0)'
    })),
    transition('hide <=> show', animate('300ms ease-in-out'))
  ]);

toString(json){
  return "JSON.stringify(json)"
}
  notesValidation() {
    var _this = this;
    var notesInvalid = false;
    _.each(_this.dataSource.data, function (eCatergory) {
      var notesInvalid0 = false;
      const notesControlCat = eCatergory?.formGroup?.controls.note;
      notesControlCat.clearValidators();
      notesControlCat.updateValueAndValidity();
      notesControlCat.markAllAsTouched();
      _this.cd.detectChanges();

      if ((!eCatergory.formGroup.value.note || eCatergory.formGroup.value.note.length == 0) && (!eCatergory.children || (eCatergory.children && eCatergory.children.length <= 0))) {
        if (_this.isSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "safe") {
          notesInvalid0 = true;
        }
        if (_this.isNotSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "unsafe") {
          notesInvalid0 = true;
        }
        if (_this.isNotObservedNotesMandatory && eCatergory.formGroup.value.isSafe == "notObserved") {
          notesInvalid0 = true;
        }
        if (notesInvalid0) {
          notesInvalid = true
          notesControlCat.setValidators(Validators.required);
          notesControlCat.updateValueAndValidity();
          notesControlCat.markAllAsTouched();
          _this.cd.detectChanges();
        }
      }

      _.each(eCatergory.children, function (eSubCatergory) {
        var notesInvalid1 = false;
        const notesControl = eSubCatergory?.formGroup?.controls.note;
        notesControl.clearValidators();
        notesControl.updateValueAndValidity();
        _this.cd.detectChanges();
        if ((!eSubCatergory.formGroup.value.note || eSubCatergory.formGroup.value.note.length == 0) && (!eSubCatergory.children || (eSubCatergory.children && eSubCatergory.children.length <= 0))) {
          if (_this.isSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "safe") {
            notesInvalid1 = true;
          }
          if (_this.isNotSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "unsafe") {
            notesInvalid1 = true;
          }
          if (_this.isNotObservedNotesMandatory && eSubCatergory.formGroup.value.isSafe == "notObserved") {
            notesInvalid1 = true;
          }
          if (notesInvalid1) {
            notesInvalid = true;
            notesControl.setValidators(Validators.required);
            notesControl.updateValueAndValidity();
            notesControl.markAllAsTouched();
            _this.cd.detectChanges();
          }
        }
        _.each(eSubCatergory.children, function (eQuestion) {
          var notesInvalid2 = false;
          const notesControlQ = eQuestion?.formGroup?.controls.note;
          notesControlQ.clearValidators();
          notesControlQ.updateValueAndValidity();
          _this.cd.detectChanges();
          if (!eQuestion.formGroup.value.note || eQuestion.formGroup.value.note.length == 0) {
            if (_this.isSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "safe") {
              notesInvalid2 = true;
            }
            if (_this.isNotSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "unsafe") {
              notesInvalid2 = true;
            }
            if (_this.isNotObservedNotesMandatory && eQuestion.formGroup.value.isSafe == "notObserved") {
              notesInvalid2 = true;
            }
            if (notesInvalid2) {
              notesInvalid = true;
              notesControlQ.setValidators(Validators.required);
              notesControlQ.updateValueAndValidity();
              notesControlQ.markAllAsTouched();
              _this.cd.detectChanges();
            }
          }
        });
      });
    });
  }

  getColor(node: any): string {
    if (node.bold && node.level == 0) {
      return; // Example: Red for bold and level 0
    }
    return '#017BA4'; // Default color
  }
  submitClick() {   
    var _this = this;
   
    let safecheck: string;
    let foundUnsafe = false;
    let foundNotobserved = false;
    safecheck='';
     var notesInvalid = false;
     _.each(_this.dataSource.data, function (eCatergory) {
      var notesInvalid0 = false;
      const notesControlCat = eCatergory?.formGroup?.controls.note;
      notesControlCat?.clearValidators();
      notesControlCat?.updateValueAndValidity();
      notesControlCat?.markAllAsTouched();
      // Check if eCatergory has no children
      if (!eCatergory.children || (eCatergory.children && eCatergory.children.length <= 0)) {
    
      if (foundUnsafe) return; 
      if (eCatergory.formGroup.value.isSafe === "safe" && !foundNotobserved) {
        safecheck = "Safe";
      } else if (eCatergory.formGroup.value.isSafe === "unsafe") {
        safecheck = "Unsafe";
        foundUnsafe = true;
        return;
      } else if (eCatergory.formGroup.value.isSafe === "notObserved" || eCatergory.formGroup.value.isSafe === "") {
        safecheck = "";
        foundNotobserved = true;
      }}

      if ((!eCatergory.formGroup.value.note || eCatergory.formGroup.value.note.length == 0) && (!eCatergory.children || (eCatergory.children && eCatergory.children.length <= 0))) {
        if (_this.isSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "safe") {
          notesInvalid0 = true;
        }
        if (_this.isNotSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "unsafe") {
          notesInvalid0 = true;
        }
        if (_this.isNotObservedNotesMandatory && eCatergory.formGroup.value.isSafe == "notObserved") {
          notesInvalid0 = true;
        }
        if(notesInvalid0){
          notesInvalid = true
          notesControlCat.setValidators(Validators.required);
          notesControlCat.updateValueAndValidity();
          notesControlCat.markAllAsTouched();
        }
      }
    
      _.each(eCatergory.children, function (eSubCatergory) {
        var notesInvalid1 = false;
        const notesControl = eSubCatergory?.formGroup?.controls.note;
        notesControl.clearValidators();
        notesControl.updateValueAndValidity();
        if ((!eSubCatergory.formGroup.value.note || eSubCatergory.formGroup.value.note.length == 0) && (!eSubCatergory.children || (eSubCatergory.children && eSubCatergory.children.length <= 0))) {
          if (_this.isSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "safe") {
            notesInvalid1 = true;
          }
          if (_this.isNotSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "unsafe") {
            notesInvalid1 = true;
          }
          if (_this.isNotObservedNotesMandatory && eSubCatergory.formGroup.value.isSafe == "notObserved") {
            notesInvalid1 = true;
          }
          if(notesInvalid1){
            notesInvalid = true;
            notesControl.setValidators(Validators.required);
            notesControl.updateValueAndValidity();
            notesControl.markAllAsTouched();
          }
        }
       
        // Check if eSubCatergory has no children
        if (!eSubCatergory.children || (eSubCatergory.children && eSubCatergory.children.length <= 0)) {
    
        if (foundUnsafe) return; 
        if (eSubCatergory.formGroup.value.isSafe === "safe" && !foundNotobserved) {
          safecheck = "Safe";
        } else if (eSubCatergory.formGroup.value.isSafe === "unsafe") {
          safecheck = "Unsafe";
          foundUnsafe = true;
          return;
        } else if (eSubCatergory.formGroup.value.isSafe === "notObserved" || eSubCatergory.formGroup.value.isSafe === "") {
          safecheck = "";
          foundNotobserved = true;
        }}
    
        _.each(eSubCatergory.children, function (eQuestion) {
          var notesInvalid2 = false;
          const notesControlQ = eQuestion?.formGroup?.controls.note;
          notesControlQ.clearValidators();
          notesControlQ.updateValueAndValidity();
          // Assuming eQuestion is the final level and doesn't have children
          if (!eQuestion.formGroup.value.note || eQuestion.formGroup.value.note.length == 0) {
            if (_this.isSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "safe") {
              notesInvalid2 = true;
            }
            if (_this.isNotSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "unsafe") {
              notesInvalid2 = true;
            }
            if (_this.isNotObservedNotesMandatory && eQuestion.formGroup.value.isSafe == "notObserved") {
              notesInvalid2 = true;
            }
            if(notesInvalid2){
              notesInvalid = true;
              notesControlQ.setValidators(Validators.required);
              notesControlQ.updateValueAndValidity();
              notesControlQ.markAllAsTouched();
            }
          }
          if (foundUnsafe) return; 
          if (eQuestion.formGroup.value.isSafe === "safe" && !foundNotobserved) {
            safecheck = "Safe";
          } else if (eQuestion.formGroup.value.isSafe === "unsafe") {
            safecheck = "Unsafe";
            foundUnsafe = true;
            return;
          } else if (eQuestion.formGroup.value.isSafe === "notObserved" || eQuestion.formGroup.value.isSafe === "") {
            safecheck = "";
            foundNotobserved = true;
          }
        });
      });
    });
    
    if(this.observedForm.get('contractor').value == 'No'){
      this.observedForm.get('contractorDetails').clearValidators();
    }
    this.observedForm.get('contractorDetails').updateValueAndValidity();
    if(this.observedForm.get('operationalLearning').value == 'No'){
      this.observedForm.get('operationalLearningDescription').clearValidators();
    }
    this.observedForm.get('operationalLearningDescription').updateValueAndValidity();
    if(this.selectedUrgency != 'High'){
    this.observedForm.get('eventDesccription').clearValidators();
    this.observedForm.get('descripeCorrectiveActionTaken').clearValidators();
    }
    this.observedForm.get('eventDesccription').updateValueAndValidity();
    this.observedForm.get('descripeCorrectiveActionTaken').updateValueAndValidity();
    if(this.observedForm.valid && !notesInvalid && this.observedForm2.valid && (this.categorycount> 0)){
      var unitFind = _this.unitList.find(item => item.externalId == _this.unitControl.value)
    
      _this.loaderFlag = true;
      var observedObj = this.observedForm.value;
      var observedObj2 = this.observedForm2.value;

      var deptFind = _this.departmentList.find(item => item.externalId == observedObj["department"])
      var obDate = new Date(observedObj["datetime"]);
      const currentYear = obDate.getFullYear().toString()
      const currentMonth = ("0" + (obDate.getMonth() + 1)).slice(-2);
      const currentDay = ("0" + obDate.getDate()).slice(-2);
      var strDate = currentYear + "-" + currentMonth + "-" + currentDay;
      //06:15 AM
      const convertTime12to24 = (time12h) => {
        var tempDate = new Date(observedObj["datetime"]);
        if(time12h){
          var time12hArray =  time12h.split(':');
          var hours = time12hArray[0];
        // let [hours, minutes] = time12h.split(':');
        var timeMinute = time12hArray[1].split(" ");
        var minutes = timeMinute[0];
        if(timeMinute && timeMinute.length>1 && timeMinute[1] == "PM"){
          hours = parseInt(time12hArray[0]) + 12;
        }
        tempDate.setHours(hours);
        tempDate.setMinutes(minutes);
      }
        return tempDate;
      }
      var contractorsNames = [];
      if(observedObj["contractorDetails"] && observedObj["contractorDetails"].length>0) {
        _.each(observedObj["contractorDetails"],function(eData){
          contractorsNames.push(eData.value);
        })
      }
      var postObj = {
        
        "date": strDate.toString(),
        
        "contractorsNames": contractorsNames,
        // "workOrderNumber": observedObj["workOrderNumber"],
        "projectName":observedObj["projectName"],
        "isOperationalLearning": observedObj["operationalLearning"] == 'Yes' ? true : false,
        "operationalLearningDescription": observedObj["operationalLearningDescription"],
        "description": observedObj["describeObserve"],
        "comments": observedObj["commentsObserve"],
        "signature": observedObj2["signature"],
        "shift": observedObj["shift"],
        "shiftIdentifier": observedObj["shiftIdentifier"],
        "shortDescription": observedObj["shortDescription"],
        "art": observedObj["art"],
        "problem": observedObj["problem"],
        "cause": observedObj["cause"],
        "solution": observedObj["solution"],
        "measure": observedObj["measure"],
        "floor": observedObj["floor"]+"",
        "urgency": _this.selectedUrgency,
        "isActive" :true,
        "observationStatus": safecheck,
        "eventType": observedObj["eventType"],
        "eventDesccription": observedObj["eventDesccription"],
        "correctiveActionFlag": observedObj["correctiveActionFlag"],
        "descripeCorrectiveActionTaken": observedObj["descripeCorrectiveActionTaken"],
      }
      if ( observedObj["contractor"]!=""){
        postObj["isContractor"]= observedObj["contractor"] == 'Yes' ? true : false
        }
      if(_this.observation){
        postObj["externalId"] = _this.observation["externalId"];
      }
      if(deptFind){
        postObj["refDeparment"] = {
          "space":deptFind["space"],
          "externalId":deptFind["externalId"]
        }
      }
  
      if(observedObj["startTime"] || observedObj["endTime"]){
      // 2023-09-11T21:45:00.000Z
      var startTimeGet = convertTime12to24(observedObj["startTime"]);
      var endTimeGet = convertTime12to24(observedObj["endTime"]);
      const currentstartMonth = ("0" + (startTimeGet?.getMonth() + 1)).slice(-2);
      const currentstartDay = ("0" + startTimeGet?.getDate()).slice(-2);
      const currentstartHour = ("0" + (startTimeGet?.getHours())).slice(-2);
      const currentstartMinute = ("0" + startTimeGet?.getMinutes()).slice(-2);
  
      const currentendMonth = ("0" + (endTimeGet?.getMonth() + 1)).slice(-2);
      const currentendDay = ("0" + endTimeGet?.getDate()).slice(-2);
      const currentendHour = ("0" + (endTimeGet?.getHours())).slice(-2);
      const currentendMinute = ("0" + endTimeGet?.getMinutes()).slice(-2);
      var startTime = startTimeGet?.getFullYear() + "-" + currentstartMonth + "-" + currentstartDay + "T" + currentstartHour + ":" + currentstartMinute + ":00.000Z";
      var endTime = endTimeGet?.getFullYear() + "-" + currentendMonth + "-" + currentendDay + "T" + currentendHour + ":" + currentendMinute + ":00.000Z";
  
        postObj["startTime"] = startTime;
        if(endTime!="NaN-aN-aNTaN:aN:00.000Z")
          postObj["endTime"] = endTime;
      }
      // "mediaFilePath": "",
      postObj["observerCrafts"] = observedObj["crafts"]? observedObj["crafts"] : [];
      
      postObj["observedCrafts"] = observedObj["observedCraft"]? observedObj["observedCraft"] : [];
      // postObj["crafts"] = observedObj["crafts"]
      
      // postObj["observedCraft"] = observedObj["observedCraft"]
      
      var behalf = _this.behalfList.find(e=> e.externalId == observedObj["behalf"])
     if(behalf){
      postObj["observedOnBehalfOf"] = {
        "space":behalf["space"],
        "externalId":behalf["externalId"]
      }
     }
     var behalf2 = _this.behalfList2.find(e=> e.externalId == observedObj["behalf2"])
     if(behalf2){
      postObj["secondaryObserver"] = {
        "space":behalf2["space"],
        "externalId":behalf2["externalId"]
      }}
     if(observedObj["workOrderNumber"] && observedObj["workOrderNumber"].length>0){
      var workOrderNumber = _this.filteredWorkOrderNumberList.find(e=> e.externalId == observedObj["workOrderNumber"][0]["externalId"])
      if(workOrderNumber){
        postObj["refWorkOrderHeader"] = {
          "space":workOrderNumber["space"],
          "externalId":workOrderNumber["externalId"]
        }
       }
     }
     
     if(_this.commonService["userInfo"] && _this.commonService["userInfo"]["externalId"]){
      postObj["performerAzureDirectoryUserID"] = {
        "space":_this.commonService["userInfo"]["space"],
        "externalId":_this.commonService["userInfo"]["externalId"]
      }
     }
     
    //   observedOnBehalfOf: User
    // performerAzureDirectoryUserID: User
  
      if (_this["selectedRegion"]) {
        postObj["refGeoRegion"] = {
          "space": _this["selectedRegion"].space,
          "externalId": _this["selectedRegion"].externalId
        }
      }
      if (_this["selectedCountry"]) {
        postObj["refCountry"] = {
          "space": _this["selectedCountry"].space,
          "externalId": _this["selectedCountry"].externalId
        }
      }
  
      if (_this["selectedSite"]) {
        postObj["refSite"] = {
          "space": _this["selectedSite"].space,
          "externalId": _this["selectedSite"].externalId
        }
      }
      if (_this["scheduleDetails"] && _this["scheduleDetails"]["refOFWASchedule"]) {
        postObj["refOFWASchedule"] = {
          "space": _this["scheduleDetails"]["refOFWASchedule"].space,
          "externalId": _this["scheduleDetails"]["refOFWASchedule"].externalId
        }
      }
      if (_this["scheduleDetails"]) {
        postObj["refOFWAScheduleDetail"] = {
          "space": _this["scheduleDetails"].space,
          "externalId": _this["scheduleDetails"].externalId
        }
      }
      // refOFWASchedule: OFWASchedule
      // refOFWAScheduleDetail: OFWAScheduleDetail
      if (unitFind) {
        postObj["refUnit"] = {
          "space": unitFind.space,
          "externalId": unitFind.externalId
        }
      }
      if (_this["selectedReportingLocation"]) {
        postObj["refReportingLocation"] = {
          "space": _this["selectedReportingLocation"].space,
          "externalId": _this["selectedReportingLocation"].externalId
        }
      }
      if (_this["selectedReportingLine"]) {
        postObj["refReportingLine"] = {
          "space": _this["selectedReportingLine"].space,
          "externalId": _this["selectedReportingLine"].externalId
        }
      }
  
      // if (_this["selectedFunctionalLocation"]) {
      //   postObj["refFunctionalLocation"] = {
      //     "space": _this["selectedFunctionalLocation"].space,
      //     "externalId": _this["selectedFunctionalLocation"].externalId
      //   }
      // }
      // if (_this["selectedEquipment"]) {
      //   postObj["refEquipment"] = {
      //     "space": _this["selectedEquipment"].space,
      //     "externalId": _this["selectedEquipment"].externalId
      //   }
      // }
      if (_this["selectedBusinessLine"]) {
        postObj["refBusinessSegment"] = {
          "space": _this["selectedBusinessLine"].space,
          "externalId": _this["selectedBusinessLine"].externalId
        }
      }

      if (_this.process) {
        postObj["refProcess"] = {
          "space": _this.process.space,
          "externalId": _this.process.externalId
        }
      }
      if (_this.corePrinciple) {
        postObj["refCorePrinciple"] = {
          "space": _this.corePrinciple.space,
          "externalId": _this.corePrinciple.externalId
        }
      } if (_this.subProcess) {
        postObj["refSubProcess"] = {
          "space": _this.subProcess.space,
          "externalId": _this.subProcess.externalId
        }
      }

      if (_this.monthControl.value) {
        postObj["month"] = _this.monthControl.value;
      }
      if (_this.weekControl.value) {
        postObj["week"] = _this.weekControl.value+"";
      }
  
      var observeObj = {
        "type": _this.commonService.configuration["typeObservation"],
        "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          postObj
        ]
      }
      console.log('observeObj',observeObj)
      _this.dataService.postData(observeObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
        if (data["items"].length > 0) {

          var logObj = {
            objectExternalId:data["items"][0].externalId,
            objectType:_this.commonService.configuration["typeObservation"],
            refUser:{
              "space": _this.dataService.userInfo.user.space,
              "externalId": _this.dataService.userInfo.user.externalId
            },
            logType:"Created",
            dateTime:new Date(),
            beforeJSON:postObj,
            afterJSON:postObj
          }
          if(_this.observation){
            logObj["beforeJSON"] = _this.observation;
            logObj["logType"] = "Edited";
          }
          var mainLog = {
            "type": _this.commonService.configuration["typeOFWALog"],
            "siteCode": _this.commonService.configuration["allSiteCode"],
            "unitCode": _this.commonService.configuration["allUnitCode"],
            "items": [
              logObj
            ]
          }
          _this.dataService.postData(mainLog, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(logdata => {
     
          })
           
          if (_this["scheduleDetails"]) {
            var postObjDetail = {
              externalId:_this["scheduleDetails"].externalId,
              status: "Completed",
              isEnable:true
            }
            var instanceAuditObj = {
              "type": _this.commonService.configuration["typeOFWAScheduleDetail"],
              "siteCode": _this["scheduleDetails"].space?_this["scheduleDetails"].space.split('-')[1]:"COR",
              "unitCode": _this.commonService.configuration["allUnitCode"],
              "items": [
                postObjDetail
              ]
              }
              console.log('instanceAuditObj',instanceAuditObj);
              _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
              
                var postObjDetail2 = {
                  externalId:_this["scheduleDetails"]["refOFWASchedule"].externalId,
                  status: "In progress",
                  isEnable:true
                }
                var instanceAuditObj2 = {
                  "type":  _this.commonService.configuration["typeSchedule"],
                  "siteCode": _this["scheduleDetails"]["refOFWASchedule"].space?_this["scheduleDetails"]["refOFWASchedule"].space.split('-')[1]:"COR",
                  "unitCode": _this.commonService.configuration["allUnitCode"],
                  "items": [
                    postObjDetail2
                  ]
                  }
                  console.log('instanceAuditObj2',instanceAuditObj2);
                  _this.dataService.postData(instanceAuditObj2, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
                 
                    _this.dataService.postData({scheduleId:_this["scheduleDetails"]["refOFWASchedule"].externalId}, _this.dataService.NODE_API + "/api/service/listScheduleDetail").subscribe(data3 => {

                      var listProcess = data3['data']['list' + _this.commonService.configuration["typeOFWAScheduleDetail"]]['items'];
                      var count = 0;
                      listProcess.forEach(element => {
                        if(element.status == "Completed"){
                          count++;
                        }
                      });
                      if(count == listProcess.length){
                        var postObjDetail4 = {
                          externalId:_this["scheduleDetails"]["refOFWASchedule"].externalId,
                          status: "Completed",
                          isEnable:true
                        }
                        var instanceAuditObj4 = {
                          "type":  _this.commonService.configuration["typeSchedule"],
                          "siteCode": _this["scheduleDetails"]["refOFWASchedule"].space?_this["scheduleDetails"]["refOFWASchedule"].space.split('-')[1]:"COR",
                          "unitCode": _this.commonService.configuration["allUnitCode"],
                          "items": [
                            postObjDetail4
                          ]
                          }
                          console.log('instanceAuditObj4',instanceAuditObj4);
                          _this.dataService.postData(instanceAuditObj4, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data4 => {
                          })        

                      }
                    })
                 
                  })
              })

          }
          _this.postChecklistArray(data["items"][0]);
          if(_this.actionPopupResponse && observedObj["correctiveActionFlag"] && observedObj["correctiveActionFlag"] == "No"){
            _this.createAction(data["items"][0].externalId)
          }
          _this.assetEdgeCreation(observedObj, data["items"][0]);
          _this.obRes = data["items"][0]
          _this.saveImages(); 
          //_this.evidenceEdgeCreation(data["items"][0]);
         
         // this.commonService.triggerToast({ type: 'success', title: '', msg: 'Saved successfully' }); 
          _this.showSuccessPopup = true;
          console.log("pop--->",_this.showSuccessPopup)
          var notificationGroupUser = [];
          if(_this.notificationGroupEnable?.isEnabled && _this.notificationGroup?.groupName){
            if(_this.commonService.notificationGroupData){

              // Extract users' emails where externalId matches
              notificationGroupUser = _this.commonService.notificationGroupData.message
                .filter(item => item.externalId === _this.notificationGroup.groupName)
                .flatMap(item => item.users.map(user => user.email));

              console.log(notificationGroupUser); // Output: Array of email addresses

            }
            var instanceNotification = [
              {
                application: _this.commonService.applicationInfo.name,
                description: 'New/Updated Observation',
                users: notificationGroupUser,
                severity: _this.selectedUrgency,
                properties: [
                  {
                    name: 'Description',
                    value: observedObj["describeObserve"],
                    type: 'text',
                  },
                  {
                    name: 'Start',
                    value: new Date(
                      postObj["startTime"]
                    ).toDateString(),
                    type: 'text',
                  },
                  {
                    name: 'End',
                    value: new Date(
                      postObj["endTime"]
                    ).toDateString(),
                    type: 'text',
                  },
                ],
              },
            ];

            let notificationType = 'New Observation Created';
            _this.commonService.triggerToast({
              type: 'success',
              title: '',
              msg: _this.commonService.toasterLabelObject['toasterSavesuccess'],
            });
            _this.commonService.notification(
              instanceNotification,
              notificationType
            );
          }
          _this.loaderFlag = false; 
          _this.cd.detectChanges();
          // Navigate to observation list
        //  this.router.navigate(['observations/observation-list']);
        } else {
          _this.commonService.triggerToast({ type: 'error', title: '', msg: _this.commonService.toasterLabelObject['toasterFailed'] });
          //_this.showFailurePopup = true;
        }
        _this.loaderFlag = false; 
      }, error => {
        console.error('Error saving observation:', error);
        _this.commonService.triggerToast({ type: 'error', title: '', msg: _this.commonService.toasterLabelObject['toasterFailedtosaveobs'] });
        //_this.showFailurePopup = true;
        console.log("pop--->",this.showFailurePopup)
        _this.loaderFlag = true; 
      });
  
    } else {
      this.commonService.triggerToast({ type: 'error', title: '', msg: _this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
      //_this.showFailurePopup = true;
    }
  }


  

  postChecklistArray(fdmObj) {
    var _this = this;

    var questionArray = []
    console.log("checklist array")
    console.log(_this.dataSource.data)
    _.each(_this.dataSource.data, function (eCatergory) {
      var catergoryId = eCatergory.id;
      _this.checklistCategoryArray.push(eCatergory);
      var category = _this.processList.find(e => e.externalId == catergoryId);
      if(!eCatergory.children || eCatergory.children.length==0){
        var questionAnswer = {};
        var questionId = eCatergory.id;
        questionAnswer["refOFWACategory"] = {
          space: category["space"],
          externalId: category["externalId"]
        }
        var questionValue = eCatergory.formGroup.value;
        if (eCatergory.checkListId && eCatergory.checkListId.length > 0) {
          questionAnswer["externalId"] = eCatergory.checkListId
        }
        var isInjuryPotential = false;
        if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
          isInjuryPotential = true;
        }

        questionAnswer["isSafe"] = false;
        questionAnswer["isUnSafe"] = false;
        questionAnswer["isNotObserved"] = false;
        if (questionValue.isSafe == "safe") {
          questionAnswer["isSafe"] = true;
        }
        if (questionValue.isSafe == "unsafe") {
          questionAnswer["isUnSafe"] = true;
        }
        if (questionValue.isSafe == "notObserved") {
          questionAnswer["isNotObserved"] = true;
        }
        questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eCatergory);
        questionAnswer["note"] = questionValue.note;
        questionAnswer["activity"] = questionValue.activity;
        questionAnswer["riskyAction"] = questionValue.riskyAction;
        questionAnswer["risk"] = questionValue.risk;
        questionAnswer["riskAgreement"] = questionValue.riskAgreement;
        questionAnswer["reasonForAction"] = questionValue.reasonForAction;
        questionAnswer["safeBehaviour"] = questionValue.safeBehaviour;
        questionAnswer["suggestedSolution"] = questionValue.suggestedSolution;

        questionArray.push(questionAnswer);
      }
      _.each(eCatergory.children, function (eSubCatergory) {


        var subCategoryId = eSubCatergory.id;
        var subCategory = _this.processList.find(e => e.externalId == subCategoryId);
        if(subCategory){
          var subCategoryValue = eSubCatergory.formGroup.value;
          _this.checklistSubCategoryArray.push(subCategory);
          var subCategoryAnswer = {};
          if (eSubCatergory.checkListId && eSubCatergory.checkListId.length > 0) {
            subCategoryAnswer["externalId"] = eSubCatergory.checkListId;
          }
          subCategoryAnswer["refOFWACategory"] = {
            space: category["space"],
            externalId: category["externalId"]
          }
          subCategoryAnswer["refOFWASubCategory"] = {
            space: subCategory["space"],
            externalId: subCategory["externalId"]
          }
  
          var isInjuryPotential = false;
          if (subCategoryValue.isInjuryPotential && subCategoryValue.isInjuryPotential == "Yes") {
            isInjuryPotential = true;
          }
  
          subCategoryAnswer["isSafe"] = false;
          subCategoryAnswer["isUnSafe"] = false
          subCategoryAnswer["isNotObserved"] = false
          if (subCategoryValue.isSafe == "safe") {
            subCategoryAnswer["isSafe"] = true;
          }
          if (subCategoryValue.isSafe == "unsafe") {
            subCategoryAnswer["isUnSafe"] = true;
          }
          if (subCategoryValue.isSafe == "notObserved") {
            subCategoryAnswer["isNotObserved"] = true;
          }

        subCategoryAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eSubCatergory);

          subCategoryAnswer["note"] = subCategoryValue.note;
          subCategoryAnswer["activity"] = subCategoryValue.activity;
          subCategoryAnswer["riskyAction"] = subCategoryValue.riskyAction;
          
          subCategoryAnswer["risk"] = subCategoryValue.risk;
          subCategoryAnswer["riskAgreement"] = subCategoryValue.riskAgreement;
          subCategoryAnswer["reasonForAction"] = subCategoryValue.reasonForAction;
          subCategoryAnswer["safeBehaviour"] = subCategoryValue.safeBehaviour;
          subCategoryAnswer["suggestedSolution"] = subCategoryValue.suggestedSolution;
  
          questionArray.push(subCategoryAnswer);
  
          _.each(eSubCatergory.children, function (eQuestion) {
            var questionAnswer = {};
            var questionId = eQuestion.id;
            var question = _this.questionList.find(e => e.externalId == questionId);
            questionAnswer["refOFWACategory"] = {
              space: category["space"],
              externalId: category["externalId"]
            }
            questionAnswer["refOFWASubCategory"] = {
              space: subCategory["space"],
              externalId: subCategory["externalId"]
            }
            questionAnswer["refOFWAQuestion"] = {
              space: question["space"],
              externalId: question["externalId"]
            }
            var questionValue = eQuestion.formGroup.value;
            if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
              questionAnswer["externalId"] = eQuestion.checkListId
            }
            var isInjuryPotential = false;
            if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
              isInjuryPotential = true;
            }
  
            questionAnswer["isSafe"] = false;
            questionAnswer["isUnSafe"] = false;
            questionAnswer["isNotObserved"] = false;
            if (questionValue.isSafe == "safe") {
              questionAnswer["isSafe"] = true;
            }
            if (questionValue.isSafe == "unsafe") {
              questionAnswer["isUnSafe"] = true;
            }
            if (questionValue.isSafe == "notObserved") {
              questionAnswer["isNotObserved"] = true;
            }

          questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eQuestion);

            questionAnswer["note"] = questionValue.note;
            questionArray.push(questionAnswer);
          })
        }else{
         // _this.checklistCategoryArray.push(eCatergory);
         var eQuestion = eSubCatergory;
          // _.each(eSubCatergory.children, function (eQuestion) {
            var questionAnswer = {};
            var questionId = eQuestion.id;
            var question = _this.questionList.find(e => e.externalId == questionId);
            if(category.processType=="Sub Process"){
              questionAnswer["refOFWASubProcess"] = {
                space: category["space"],
                externalId: category["externalId"]
              };
              questionAnswer["refOFWAQuestion"] = {
                space: question["space"],
                externalId: question["externalId"]
              };
            }else{
            questionAnswer["refOFWACategory"] = {
              space: category["space"],
              externalId: category["externalId"]
            }
            questionAnswer["refOFWAQuestion"] = {
              space: question["space"],
              externalId: question["externalId"]
            }}
            var questionValue = eQuestion.formGroup.value;
            if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
              questionAnswer["externalId"] = eQuestion.checkListId
            }
            var isInjuryPotential = false;
            if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
              isInjuryPotential = true;
            }
  
            questionAnswer["isSafe"] = false;
            questionAnswer["isUnSafe"] = false;
            questionAnswer["isNotObserved"] = false;
            if (questionValue.isSafe == "safe") {
              questionAnswer["isSafe"] = true;
            }
            if (questionValue.isSafe == "unsafe") {
              questionAnswer["isUnSafe"] = true;
            }
            if (questionValue.isSafe == "notObserved") {
              questionAnswer["isNotObserved"] = true;
            }
            questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eQuestion);;
            questionAnswer["note"] = questionValue.note;
            questionArray.push(questionAnswer);
          // })
        }
        
      })
    })

    console.log("questionArray>>>>>>>>>>>")
    console.log(questionArray)
    _this.processEdgeCreation(fdmObj);
    var postObjChecklist = {
      "type": _this.commonService.configuration["typeChecklist"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": questionArray
    }
    _this.dataService.postData(postObjChecklist, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        _this.checklistEdgeCreation(data["items"], fdmObj);
      }

      // this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    })

  }

    getTrueInjuryPotential(eSubCategory) {
    console.log(eSubCategory.formGroup.controls.isInjuryPotential.value)
    console.log(eSubCategory.formGroup.getRawValue().isInjuryPotential)
    return eSubCategory.formGroup.getRawValue().isInjuryPotential == "Yes";
  }

  checklistEdgeCreation(checkListObj, fdmObj) {
    var _this = this;
    var myCheckListArray = [];
    _.each(checkListObj, function (eCheckList) {
      var edgeObj = {
        "instanceType": "edge",
        "space": fdmObj["space"],
        "externalId": fdmObj["externalId"] + "-" + eCheckList["externalId"],
        "type": {
          "space": _this.commonService.configuration["DataModelSpace"],
          "externalId": _this.commonService.configuration["typeObservation"] + ".refOFWAChecklist"
        },
        "startNode": {
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"]
        },
        "endNode": {
          "space": eCheckList["space"],
          "externalId": eCheckList["externalId"]
        }
      }
      myCheckListArray.push(edgeObj);
    });

    _this.dataService.postData(myCheckListArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

    });
  }
  assetEdgeCreation(observedObj, fdmObj) {

    var _this = this;
    if(_this.editEquipment.length > 0){

       
      var disconnectNode=   {
        "items": [
        
         ]
      }
  
      _this.editEquipment.forEach(element => {
        var ob =   {
          "instanceType":"edge",
          "externalId": fdmObj["externalId"]+"-"+element.externalId, 
          "space": fdmObj["space"]
        }
        disconnectNode.items.push(ob)
      })
      console.log('disconnectNode',disconnectNode)
      _this.dataService.postData(disconnectNode, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(disNode => {
        if (observedObj.assets && observedObj.assets.length > 0) {
          var myAssetArray = [];
          _.each(observedObj.assets, function (eAsset) {
            var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
            var edgeObj = {
              "instanceType": "edge",
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
              "type": {
                "space": _this.commonService.configuration["DataModelSpace"],
                "externalId": _this.commonService.configuration["typeObservation"] + ".refEquipment"
              },
              "startNode": {
                "space": fdmObj["space"],
                "externalId": fdmObj["externalId"]
              },
              "endNode": {
                "space": myAsset.space,
                "externalId": myAsset.externalId
              }
            }
            myAssetArray.push(edgeObj);
          })
        }
        _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
    
        });
      })
    }else{
      if (observedObj.assets && observedObj.assets.length > 0) {
        var myAssetArray = [];
        _.each(observedObj.assets, function (eAsset) {
          var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
          var edgeObj = {
            "instanceType": "edge",
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
            "type": {
              "space": _this.commonService.configuration["DataModelSpace"],
              "externalId": _this.commonService.configuration["typeObservation"] + ".refEquipment"
            },
            "startNode": {
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"]
            },
            "endNode": {
              "space": myAsset.space,
              "externalId": myAsset.externalId
            }
          }
          myAssetArray.push(edgeObj);
        })
      }
      _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
  
      });
    }
 
  }

  evidenceEdgeCreation(fdmObj) {
    var _this = this;
    var myEvidenceArray = [];
    if (_this.evidenceObj) {
      _.each(_this.evidenceObj, function (eEvidence) {
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eEvidence["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeObservation"] + ".evidenceDocument"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eEvidence["space"],
            "externalId": eEvidence["externalId"]
          }
        }
        myEvidenceArray.push(edgeObj);
      });

      _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
  }

  loadExistingImages() {
    if (this.observation.evidenceDocument && this.observation.evidenceDocument.items.length > 0) {
      this.images = []; // Clear existing images array
      this.observation.evidenceDocument.items.forEach(item => {
        const image = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
        this.dataService.getImage(image).subscribe(
          (resData: any) => {
            const objectURL = URL.createObjectURL(resData);
            item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
            console.log('Existing Image loaded successfully:', item.image);
            this.images.push(item.image); // Add the image URL to images array
          },
          (error) => {
            console.error('Error loading existing image:', error);
          }
        );
      });
    }
    console.log('Final images array after loading existing images:', this.images);
  }
  
  // Function to upload new images and store them
  async uploadAndStoreNewImages($event: any) {
    this.loaderFlag = true;
  
    if ($event.target.files && $event.target.files.length) {
      const files = $event.target.files;
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        reader.onload = (e: any) => {
          const imageObjectURL = e.target.result;
          this.images.push(imageObjectURL); 
          console.log(`File ${i + 1}/${files.length} loaded:`, file.name);
        };
        reader.readAsDataURL(file);
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type, }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
        const myObj = {
          name: fileupload.name,
          mimetype: fileupload.mimeType,
          pathURL: fileupload.uploadUrl,
          pathStore: "CDF",
          evidenceDocumentId: fileupload.id + "",
          description: ""
        };
        if (this.observation && this.observation["evidenceDocument"] && this.observation["evidenceDocument"]["items"].length > 0) {
          myObj["externalId"] = this.observation["evidenceDocument"]["items"][0]["externalId"];
        }
        this.evidenceObj.push(myObj);
      }
    }
  
    this.loaderFlag = false;
    console.log('Final images array after uploading new images:', this.images);
  }

  processEdgeCreation(fdmObj) {
    var _this = this;
    var mySubCategoryArray = [];
    if (_this.checklistSubCategoryArray) {
      _.each(_this.checklistSubCategoryArray, function (eSubCatergory) {
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eSubCatergory["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeObservation"] + ".refOFWAProcess"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eSubCatergory["space"],
            "externalId": eSubCatergory["externalId"]
          }
        }
        
        mySubCategoryArray.push(edgeObj);
      });

      _this.dataService.postData(mySubCategoryArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
    if (_this.checklistCategoryArray) {
      var myCategoryArray=[]
      _.each(_this.checklistCategoryArray, function (eCatergory) {

        var edgeObjNewSubCat = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eCatergory["id"]?eCatergory["id"] : eCatergory["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeObservation"] + ".refCategory"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": _this.process["space"],
            "externalId": eCatergory["id"]?eCatergory["id"] : eCatergory["externalId"]
          }
        }
        myCategoryArray.push(edgeObjNewSubCat);
      });

      _this.dataService.postData(myCategoryArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
  }
  cancelClick() {
    if(this.process.name == "Observation"){
      this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }else{
      this.router.navigate(['observations/list'], { state: { listType: "Hazards" } });
    }
  }


  closeSuccessPopup() {
    this.showSuccessPopup = false;
    console.log(this.process)
    if(this.process.name == "Observation"){
      this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }else{
      this.router.navigate(['observations/list'], { state: { listType: "Hazards" } });
    }
  }

  closeSuccessPopup2() {
    this.showFailurePopup= false;
    //this.router.navigate(['observations/observation-list']);
  }

  backClick() {
    if (this.observation && this.process.name == 'Observation') {
      this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }
    if (this.observation && this.process.name == 'Hazards') {
      this.router.navigate(['observations/list'], { state: { listType: "Hazards" } });
    }
    this.observation = null;
    this.observedForm.reset();
    this.selectedCategory = null;
    this.selectedSubCategory = null;
    // this.dataSource.data = [];
    this.subCategoryList = null;
    this.categoryList = null;
    this.dataSource.disconnect();
   
    this.newItemEvent.emit({ "type": "Cancel","processType":"Observation" });
  }
  goPage(page) {
    this.router.navigate([page]);
  }
  goPrevious(stepper: MatStepper) {
    stepper.previous();
  }

  goNext(stepper: MatStepper) {
    stepper.next();
  }
  feedBackClick() {
    if (this.feedBackEnable == true) {
      this.feedBackEnable = false
    } else {
      this.feedBackEnable = true
    }
  }

  logSelectedCraft(selectedCraft: string) {
    console.log('Selected Craft:', selectedCraft);
    // You can perform any additional actions with the selected value here
  }

  createAction(observationId) {
    var _this = this;
    _this.actionPopupResponse.postObj["items"][0]["eventMetadata"]["objectId"] = observationId;
    _this.actionPopupResponse.postObj["items"][0]["eventMetadata"]["sourceId"] = observationId;
    _this.dataService.postData(_this.actionPopupResponse.postObj, _this.dataService.NODE_API + '/api/service/createInstanceByProperties'
    ).subscribe((data) => {
      _this.commonService.notification(
        _this.actionPopupResponse.instanceNotification,
        _this.actionPopupResponse.notificationType
      );
    });
  }
  imageView(item) {
    console.log(item)
    var _this = this;
    if (item.guidelineDocument && item.guidelineDocument.length > 0) {
      var fileId = item.guidelineDocument[0]["id"];
      _this.loaderFlag = true;
      _this.dataService.getImage(_this.commonService.configuration["AzureAudience"] + "/api/v1/projects/" + _this.commonService.configuration["Project"] + "/documents/" + fileId + "/preview/image/pages/1").
        subscribe((resData: any) => {
          let objectURL = URL.createObjectURL(resData);
          _this.loaderFlag = false;
          _this.cd.detectChanges();
          window.open(objectURL, '_blank');
        });
    }
  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }

  exportToPDF() {
    this.loaderFlag = true;
    this.cd.detectChanges();
    const heading = document.getElementById('exportable_heading');
    const content = document.getElementById('exportable_content');
  
    if (heading && content) {
      html2canvas(heading).then(canvasHeading => {
        const imgWidth = 208; // PDF page width
        const pageHeight = 290; // PDF page height
        const headingImgHeight = (canvasHeading.height * imgWidth) / canvasHeading.width;
  
        const headingDataURL = canvasHeading.toDataURL('image/png');
  
        const pdf = new jsPDF('p', 'mm', 'a4'); // Initialize jsPDF
  
        // Add the heading to the first page
        pdf.addImage(headingDataURL, 'PNG', 0, 15, imgWidth, headingImgHeight);
  
        // Render the content and handle overflow
        html2canvas(content).then(canvasContent => {
          const contentHeight = canvasContent.height;
          const contentImgHeight = (contentHeight * imgWidth) / canvasContent.width;
          let contentPosition = 0;
          let availableHeight = pageHeight - headingImgHeight - 20; // Adjust for heading and padding
          let contentHeightLeft = contentHeight;
  
          while (contentHeightLeft > 0) {
            const pageCanvas = document.createElement('canvas');
            pageCanvas.width = canvasContent.width;
            pageCanvas.height = Math.min(contentHeightLeft, (availableHeight * canvasContent.width) / imgWidth);
  
            const pageContext = pageCanvas.getContext('2d');
            if (pageContext) {
              pageContext.drawImage(
                canvasContent,
                0,
                contentPosition,
                canvasContent.width,
                pageCanvas.height,
                0,
                0,
                pageCanvas.width,
                pageCanvas.height
              );
  
              const contentDataURL = pageCanvas.toDataURL('image/png');
              pdf.setFontSize(10);

// Text values
const corePrincipleText = `${this.labels['corePrinciple']}: ${this.corePrinciple.name}`;
const processText = `${this.labels['process']}: ${this.process.name}`;
const observationTypeText = `${this.labels['observationType']}: ${this.subProcess.name}`;

// Calculate total page width and margins
const pageWidth = imgWidth; // Page width in mm
const leftMargin = 5; // Left margin
const rightMargin = 5; // Right margin
const totalAvailableWidth = pageWidth - leftMargin - rightMargin;

// Measure text widths
const corePrincipleWidth = pdf.getTextWidth(corePrincipleText);
const processWidth = pdf.getTextWidth(processText);
const observationTypeWidth = pdf.getTextWidth(observationTypeText);

// Calculate gaps between items
const totalTextWidth = corePrincipleWidth + processWidth + observationTypeWidth;
const remainingWidth = totalAvailableWidth - totalTextWidth;
const gapBetweenItems = remainingWidth / 2; // Equal gap between the three items

// Set positions
let currentX = leftMargin;


  
              if (contentPosition === 0) {

                // pdf.setFontSize(10);
                pdf.text(corePrincipleText, currentX, 10);
                currentX += corePrincipleWidth + gapBetweenItems;

                pdf.text(processText, currentX, 10);
                currentX += processWidth + gapBetweenItems;

                pdf.text(observationTypeText, currentX, 10);

                
                // First page already has the heading, so add content below it
                pdf.addImage(contentDataURL, 'PNG', 0, headingImgHeight + 16, imgWidth, (pageCanvas.height * imgWidth) / pageCanvas.width);
              } else {
                // Add a new page and the heading for each subsequent page
                pdf.addPage();
                // pdf.setFontSize(10);
                pdf.text(corePrincipleText, currentX, 10);
                currentX += corePrincipleWidth + gapBetweenItems;

                pdf.text(processText, currentX, 10);
                currentX += processWidth + gapBetweenItems;

                pdf.text(observationTypeText, currentX, 10);
                
                
                pdf.addImage(headingDataURL, 'PNG', 0, 15, imgWidth, headingImgHeight); // Adjust the Y position
                pdf.addImage(contentDataURL, 'PNG', 0, headingImgHeight + 16, imgWidth, (pageCanvas.height * imgWidth) / pageCanvas.width);
              }
  
              contentPosition += pageCanvas.height; // Move down in the content canvas
              contentHeightLeft -= pageCanvas.height; // Decrease the remaining height
            }
          }
  
          // Save the final PDF
          pdf.save('exported-file-with-heading-and-content.pdf');
          this.loaderFlag = false;
          this.cd.detectChanges();
        });
      });
    }
  }
  
  
}
