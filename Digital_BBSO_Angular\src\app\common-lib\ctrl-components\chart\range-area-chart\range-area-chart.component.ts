
import { ViewportRuler } from "@angular/cdk/overlay";
import { ChangeDetectionStrategy, Component, Input, OnInit, ViewChild } from "@angular/core";
import { ApexAxisChartSeries, ApexChart, ApexDataLabels, ApexFill, ApexForecastDataPoints, ApexLegend, 
    ApexMarkers, ApexPlotOptions, ApexStroke, ApexTitleSubtitle, ApexTooltip, ApexXAxis, ApexYAxis } from "ng-apexcharts";
import { ChartOptions } from "src/app/modals/chart.modal";




@Component({
  selector: 'range-area-chart',
  templateUrl: './range-area-chart.component.html',
  changeDetection:ChangeDetectionStrategy.OnPush,
})

export class RangeAreaChartComponent implements OnInit {
  @Input() series: ApexAxisChartSeries = [];
  @Input() chartOptions: Partial<ChartOptions>;
  docReact: any;
   constructor(private viewportRuler: ViewportRuler){
    this.docReact = this.viewportRuler.getViewportRect();
   }
   ngOnInit(): void {
    this.chartOptions.chart.height = this.chartOptions.chart.height && this.docReact.height >= 900 ? this.chartOptions.chart.height : this.docReact.height <= 750 ? 300 : 300;
   }
}