<div class="c-dialog palette4" style="max-height: 40em !important;">
  <form [formGroup]="form">
    <h3 mat-dialog-title class="my-title">{{ labels['reacttableListcols'] }}</h3>
    <div class="c-dialog-content">
      <div formArrayName="columns" *ngFor="let column of columnFormArray.controls; let i = index">
        <mat-slide-toggle [formControlName]="i" (ngModelChange)="updateValue($event, i)">
          {{ labels[checkColumns[i].key] }}
        </mat-slide-toggle>
      </div>
    </div>
    <mat-dialog-actions align="end">
      <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
      (buttonAction)="cancel()"></common-lib-button>
    </mat-dialog-actions>
  </form>
</div>