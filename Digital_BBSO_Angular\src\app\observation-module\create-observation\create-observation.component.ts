import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { Observable } from 'rxjs';
import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { NgZone } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-create-observation',
  templateUrl: './create-observation.component.html',
  styleUrls: ['./create-observation.component.scss']
})
export class CreateObservationComponent implements OnInit,OnChanges  {
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  @Input() corePrinciple: any;
  @Input() process: any;
  @Input() subProcess: any;
  @Input() search: any;
  
  processList = []
  labels = {}

  selectedProcess: any;
  selectedSubProcess: any;
  defaultIcon: string;
  constructor(
    private sanitizer: DomSanitizer,
    private dataService: DataService,
    private commonService: CommonService,
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
    public translationService: TranslationService, private languageService: LanguageService, public ngZone: NgZone, private changeDetector: ChangeDetectorRef

  ) {
    this.defaultIcon = dataService.defaultIcon;
    // this.corePrincipleList=  this.dataService.homeCreateObservationList
    console.log('commonService label', this.commonService.labelObject)
    console.log('commonService label', this.commonService.selectedLanguage)

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
      }
    }

  }
  ngOnInit(): void {
    console.log(this.corePrinciple)
    // var _this = this;
    this.ngZone.run(() => {
      console.log('Out subscribe', this.labels)
      this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', this.commonService.selectedLanguage)
        console.log('obs labels', this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
      }
        console.log('commonService label', this.labels)
        this.changeDetector.detectChanges();
      })
      this.changeDetector.detectChanges();
    })
  
    this.processSearch();
  }

  processSearch(){
    var _this = this;
    var processList = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == this.corePrinciple.externalId);
    })

    var myList = [];
    _.each(processList,function(eData){
      var subProcessList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == eData.externalId) && (e.isActive !== false);
      })
      
      if (subProcessList.length === 1 && subProcessList[0].isActive === false) {
        return; 
      }
      if(_this.search && _this.search.length>0){
        const result = subProcessList.filter(item => item.name.toLowerCase().includes(_this.search.toLowerCase()));
        eData["subProcessList"] = result;
      }else{
        eData["subProcessList"] = subProcessList;
      }
      
      if(eData.name == "Observation"){
        eData["sequence"] = eData.sequence ? eData.sequence : 1;
        eData["code"] = _this.dataService.appMenuCode.homeObservation;
        eData["visible"] = true;
      }else
      if(eData.name == "Audit"){
        eData["sequence"] = eData.sequence ? eData.sequence : 3;
        eData["code"] = _this.dataService.appMenuCode.homeAudit;
        eData["visible"] = true;
      }else
      if(eData.name == "Field Walk"){
        eData["sequence"] = eData.sequence ? eData.sequence : 2;
        eData["code"] = _this.dataService.appMenuCode.homeFieldWalk;
        eData["visible"] = true;
      }else{
        eData["sNo"] = 4;
      }
      myList.push(eData)
    });
    processList = _.sortBy(myList, ['sequence']);
    _this.processList = processList;
    // _this.selectedProcess = _this.process;
    // _this.selectedSubProcess = _this.subProcess;
    _this.selectedProcess = undefined;
    _this.selectedSubProcess = undefined;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.selectedProcess = this.process;
    this.selectedSubProcess = this.subProcess;
    this.search = this.search;
    if (changes['search']) {
      changes['search']['currentValue'];
      this.processSearch();
    }
  }

  siteSelected(region) {
  }
  unitSelected(region) {
  }

  selectProcess(item) {
    this.selectedProcess = item;
    this.goSubPage();
  }

  selectSubProcess(process,item) {
    if(item.isActive == false){
      return true;
    }
    this.selectedProcess = process;
    this.selectedSubProcess = item;
    this.goCreatePage();
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  goBack() {
    this.newItemEvent.emit({ "type": "Cancel","processType":"Process" });
  }
  goSubPage() {

    if (this.selectedProcess) {
      this.newItemEvent.emit({ "type": "Next","processType":"Process","selectedOFWAProcess":this.selectedProcess });
    } else {
      this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterPleasechoosecoreprinciple'] });

    }
  }


  goCreatePage() {
    var _this = this;
    _this.newItemEvent.emit({
      "type":"Next",
      "processType":"Process",
      "selectedProcess":this.selectedProcess,
      "selectedSubProcess":this.selectedSubProcess});
  }
}
