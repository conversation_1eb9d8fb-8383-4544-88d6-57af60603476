import { After<PERSON>iewInit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import _ from "lodash";
import { TokenService } from 'src/app/services/token.service';
@Component({
  selector: 'app-schedule-tracker',
  templateUrl: './schedule-tracker.component.html',
  styleUrls: ['./schedule-tracker.component.scss']
})
export class ScheduleTrackerComponent implements OnInit, AfterViewInit, OnDestroy {

  siteControl: FormControl = new FormControl("");
  yearControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");

  displayedColumns: any = [];

  allColumns = [

    { key: 'name', displayName: "Name", name: "name", activeFlag: true, summary: false },
    { key: 'jan', displayName: "Jan", name: "jan", activeFlag: true, summary: false },
    { key: 'feb', displayName: "Feb", name: "feb", activeFlag: true, summary: false },
    { key: 'mar', displayName: "Mar", name: "mar", activeFlag: true, summary: false },
    { key: 'apr', displayName: "Apr", name: "apr", activeFlag: true, summary: false },
    { key: 'may', displayName: "May", name: "may", activeFlag: true, summary: false },
    { key: 'jun', displayName: "Jun", name: "jun", activeFlag: true, summary: false },
    { key: 'jul', displayName: "Jul", name: "jul", activeFlag: true, summary: false },
    { key: 'aug', displayName: "Aug", name: "aug", activeFlag: true, summary: false },
    { key: 'sep', displayName: "Sep", name: "sep", activeFlag: true, summary: false },
    { key: 'oct', displayName: "Oct", name: "oct", activeFlag: true, summary: false },
    { key: 'nov', displayName: "Nov", name: "nov", activeFlag: true, summary: false },
    { key: 'dec', displayName: "Dec", name: "dec", activeFlag: true, summary: false },
    { key: 'total', displayName: "Total", name: "total", activeFlag: true, summary: false }
  ];

  url: any = "";
  filteredYearList: any;
  yearList: any;
  constructor(private tokenService: TokenService, private router: Router, private dataService: DataService, private dialog: MatDialog, private commonService: CommonService,) {
    this.url = this.dataService.React_API + "/scheduleTracker";
    var _this = this;
    _this.dataService.postData({ "name": "Year" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      _this.yearList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _this.filteredYearList = _this.yearList.slice();
    })
    _this.yearControl.setValue(new Date().getFullYear()+"")

  }

  ngOnInit(): void {
    var _this = this;
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "FormConfig") {
        console.log(e.data.action)
        if (e.data.action == "FormView") {
          console.log('FormView-->')
        } else if (e.data.action == "FormEdit") {
          console.log('FormEdit-->')
        }
      };
    }
    _this.siteControl.valueChanges.subscribe(value => {
      console.log(value)
      _this.dataService.siteId = value;
      _this.applyFilter();
    });
    _this.yearControl.valueChanges.subscribe(value => {
      _this.applyFilter();
    });

  }
  ngAfterViewInit(): void {

    var _this = this;
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listSchedule").subscribe(data => {
      var resData = data["data"]["list" + _this.commonService.configuration["typeSchedule"]]["items"];
      console.log(resData);
      var monthData = [];
      _.each(resData, function (eData) {
        eData["month"] = new Date(eData.createdTime).getMonth();
        monthData.push(eData);
      })
      var myData = [];
      var userGroup = _.groupBy(monthData, function (b) { return b.performerAzureDirectoryUserId });
      for (const key in userGroup) {
        var myObj = {
          name: key
        }
        if (userGroup.hasOwnProperty(key)) {
          var monthGroup = _.groupBy(userGroup[key], function (b) { return b.month });
          myObj["bymonth"] = monthGroup;
          myData.push(myObj)
        }
      }

      console.log(myData)
    })
    var _this = this;
    var iframe = document.getElementById('iFrameScheduleTracker');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "ScheduleTracker", "action": "Column", "data": this.displayedColumns }, '*');
    // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');

  }

  
  applyFilter() {
    var _this = this;
    var iframe = document.getElementById('iFrameScheduleTracker');
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var sites = []
    if(this.siteControl.value){
      sites = [this.siteControl.value]
    }
    var years = []
    if(this.yearControl.value){
      years = [parseInt(this.yearControl.value)]
    }
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "ScheduleTracker", "action": "Filter",sites:sites,years:years }, '*');
  }

  
  ngOnDestroy(): void {

  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    console.log('_this.displayedColumns,', _this.displayedColumns,)
    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol', summaryCol)
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameScheduleTracker');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "ScheduleTracker", "action": "Summary", "data": summaryCol }, '*');
  }


  goPage(page) {
    this.router.navigate([page]);
  }

}
