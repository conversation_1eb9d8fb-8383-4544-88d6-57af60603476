// https://observablehq.com/@observablehq/input-table@886

import define1 from "https://api.observablehq.com/@mkfreeman/plot-tooltip.js?v=3&resolutions=253d3c1b5eb8410c@1948";
import define2 from "https://api.observablehq.com/@john-guerra/file-input-with-default-value.js?v=3&resolutions=253d3c1b5eb8410c@1948";


function _1(md) {
  return (
    md`<div style="color: grey; font: 13px/25.5px var(--sans-serif); text-transform: uppercase;"><h1 style="display: none">Table Input / Observable Inputs</h1><a href="/@observablehq/inputs">Observable Inputs</a> › Table · <a href="https://github.com/observablehq/inputs/blob/main/README.md#table">API</a></div>

# Input: Table

A Table displays tabular data. It’s fast: rows are rendered lazily on scroll. It sorts: click a header to sort, and click again to reverse. And it selects: click a checkbox on any row, and the selected rows are exported as a view value. (And for searching, see the [Search input](/@observablehq/input-search).) For more applied examples, see [Hello, Inputs!](/@observablehq/hello-inputs)`
  )
}

function _2(md) {
  return (
    md`By default, all columns are visible. Only the first dozen rows are initially visible, but you can scroll to see more. Column headers are fixed for readability.`
  )
}


function myData(FileAttachment, d3) {
  // var d  = d3.json("http://localhost:8001/api/ngsi/entities?type=Alert&options=keyValues", function(error, data) {
  //   console.log(data)
  // })
  // return d;
  return [];
}

function _4(Inputs, data) {
  return (
    Inputs.table(data)
  )
}

function _5(md) {
  return (
    md`To show a subset of columns, or to reorder them, pass an array of property names
as the _columns_ option. By default, columns are inferred from _data_.columns if
present, and otherwise by iterating over the data to union the property names.

The _header_ option lets you redefine the column title; this doesn’t change the
name used to reference the data.
`
  )
}

function _6(data) {
  return (
    data.columns
  )
}

function _7(Inputs, data) {
  return (
    Inputs.table(data, {
      columns: [
        "people_id",
        "createdAt",
        "name",
        "gender"
      ],
      header: {
        people_id: "ID",
        createdAt: "createdAt",
        name: "name",
        gender: "gender"
      }
    })
  )
}

function _8(md) {
  return (
    md`By default, rows are displayed in input order. You can change the order by
specifying the name of a column to _sort_ by, and optionally the _reverse_
option for descending order. The male Gentoo penguins are the largest in this
dataset, for example. Undefined values go to the end.
`
  )
}

function _9(Inputs, data) {
  return (
    Inputs.table(data, { sort: "body_mass_g", reverse: true })
  )
}

function _10(md) {
  return (
    md`Tables are [view-compatible](/@observablehq/introduction-to-views): using the
viewof operator, you can use a table to select rows and refer to them from other
cells, say to chart a subset of the data. Click the checkbox on the left edge of
a row to select it, and click the checkbox in the header row to clear the
selection. You can shift-click to select a range of rows.
`
  )
}

function _selection(Inputs, data) {
  return (
    Inputs.table(data, { required: false })
  )
}

function _selection1(Inputs, data, configuration) {
  return (

    Inputs.table(data, configuration)
  )
}

function configFun() {
  return { required: false }
}

function search() {
  Inputs.search(data)
}


function _12(selection) {
  return (
    selection
  )
}

function _13(md) {
  return (
    md`The _required_ option determines the selection when no items are selected from
the table. If it is true (default), the selection contains the full dataset.
Otherwise, the selection is empty.
`
  )
}

function _14(md) {
  return (
    md`The table component assumes that all values in a given column are the same type,
and chooses a suitable formatter based on the first non-null value in each
column.

- Numbers are formatted using
  [_number_.toLocaleString](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/toLocaleString);
- Dates are formatted in [ISO8601](https://en.wikipedia.org/wiki/ISO_8601);
- undefined and NaN values are empty;
- everything else is displayed as-is.

To override the default formatting, pass _format_ options for the desired
columns.
`
  )
}

function _15(Inputs, data) {
  return (
    Inputs.table(data, {
      format: {
        culmen_length_mm: x => x.toFixed(1),
        culmen_depth_mm: x => x.toFixed(1),
        sex: x => x === "MALE" ? "M" : x === "FEMALE" ? "F" : ""
      }
    })
  )
}

function _16(md) {
  return (
    md`The _format_ function can return a text string or an HTML element.  
For example, this can be used to render inline visualizations such as bars or [sparklines](/@mbostock/covid-cases-by-state).`
  )
}

function _17(Inputs, data, sparkbar, d3) {
  return (
    Inputs.table(data, {
      format: {
        culmen_length_mm: sparkbar(d3.max(data, d => d.culmen_length_mm)),
        culmen_depth_mm: sparkbar(d3.max(data, d => d.culmen_depth_mm)),
        flipper_length_mm: sparkbar(d3.max(data, d => d.flipper_length_mm)),
        body_mass_g: sparkbar(d3.max(data, d => d.body_mass_g)),
        sex: x => x.toLowerCase()
      }
    })
  )
}

function _sparkbar(htl) {
  return (
    function sparkbar(max) {
      return x => htl.html`<div style="
    background: lightblue;
    width: ${100 * x / max}%;
    float: right;
    padding-right: 3px;
    box-sizing: border-box;
    overflow: visible;
    display: flex;
    justify-content: end;">${x.toLocaleString("en")}`
    }
  )
}

function _19(md) {
  return (
    md`There’s a similar _width_ option if you want to give certain columns specific
widths. The table component defaults to a fixed _layout_ if there are twelve or
fewer columns; this improves performance and avoids reflow when scrolling.

You can switch _layout_ to auto if you prefer sizing columns based on content.
This makes the columns widths resize with the data, which can cause the columns
to jump around as the user scrolls. A horizontal scroll bar is added if the
total width exceeds the table’s width.

Set _layout_ to fixed to pack all the columns into the width of the table.
`
  )
}

function _20(md) {
  return (
    md`The table’s width can be controlled by the _width_ option, in pixels. Individual
column widths can alternatively be defined by specifying an object with column
names as keys, and widths as values. Use the _maxWidth_ option if the sum of
column widths exceeds the desired table’s width.

The _align_ option allows to change the text-alignment of cells, which can be
right, left, or center; it defaults to right for numeric columns, and left for
everything else.

The _rows_ option indicates the number of rows to display; it defaults to 11.5.
The _height_ and _maxHeight_ options respectively set the height and maximum
height of the table, in pixels. The height defaults to (rows + 1) \\* 22 - 1.
`
  )
}

function _21(Inputs, data) {
  return (
    Inputs.table(data, {
      width: {
        culmen_length_mm: 140,
        culmen_length_mm: 140,
        flipper_length_mm: 140
      },
      align: {
        culmen_length_mm: "right",
        culmen_depth_mm: "center",
        flipper_length_mm: "left"
      },
      rows: 18,
      maxWidth: 840,
      multiple: false,
      layout: "fixed"
    })
  )
}

function _22(md) {
  return (
    md`You can preselect some rows in the table by setting the _value_ option to an
array of references to the actual objects in your data.

For example, to preselect the first two items, you could write:

> \`\`\`js
>  {value: data.slice(0, 2)})
> \`\`\`

or, if you just want to preselect the rows 1, 3, 7 and 9:

> \`\`\`js
> { value: data.filter((_,i)=>  [1, 3, 7, 9].includes(i))}
> \`\`\`

The _multiple_ option allows multiple rows to be selected (defaults to true).
When false, only one row can be selected. To set the initial value in that case,
just reference the preselected object:

> \`\`\`js
> { multiple: false, value: data[4] }
> \`\`\`
`
  )
}

function _23(Inputs, data) {
  return (
    Inputs.table(data, {
      value: data.filter((_, i) => [1, 3, 7, 9].includes(i)),
      multiple: true
    })
  )
}

function _24(md) {
  return (
    md`---

## Appendix`
  )
}

function _25(md) {
  return (
    md`Thanks to [Ilyá Belsky](https://observablehq.com/@oluckyman) and [Brett Cooper](https://observablehq.com/@hellonearthis) for suggestions.`
  )
}

function _Table(Inputs) {
  return (
    Inputs.table
  )
}

function _col_summary(SummarizeColumn, data) {
  return (
    SummarizeColumn(data, "category")
  )
}


function _SummarizeColumn(d3, getType, htl, icon_fns, SmallStack, pct_format, Histogram, html) {
  return (
    (data, col) => {

      let content, value, format, finiteFormat, el, chart, missing_label, pct_missing, min, max, median, mean, sd;
      const notFiniteFormat = d3.format(",.0f");

      // Construct content based on type  
      const type = getType(data, col)

      const col1 = htl.html`<td style="white-space: nowrap;vertical-align:middle;padding-right:5px;padding-left:3px;">${icon_fns[type]()}<strong style="vertical-align:middle;">${col === "" ? "unlabeled" : col}</strong></td>`

      switch (type) {
        // Categorical columns
        case 'ordinal':
          format = d3.format(",.0f")

          // Calculate category percent and count
          const categories = d3.rollups(
            data,
            v => ({ count: v.length, pct: v.length / data.length || 1 }),
            d => d[col]
          ).sort((a, b) => b[1].count - a[1].count)
            .map(d => {
              let obj = {}
              obj[col] = (d[0] === null || d[0] === "") ? "(missing)" : d[0]
              obj.count = d[1].count
              obj.pct = d[1].pct
              return obj
            })

          // Calculate pct. missing        
          pct_missing = data.filter(d => (d[col] === null || d[col] === "")).length / data.length

          // Create the chart
          const stack_chart = SmallStack(categories, col)

          // element to return
          el = htl.html`<tr style="font-family:sans-serif;font-size:13px;">
          ${col1}          
          <td><div style="position:relative;">${stack_chart}</div></td>
          <td>${pct_format(pct_missing)}</td>
          <td>-</td>
          <td>-</td>
          <td>-</td>
        </tr>`;

          value = {
            column: col, type, min: null, max: null, mean: null, median: null,
            sd: null, missing: pct_missing, n_categories: categories.length
          }
          break;

        // Date columns
        case "date":
          // Calculate and format start / end      
          const start = d3.min(data, d => +d[col])
          const end = d3.max(data, d => +d[col])
          mean = d3.mean(data, d => +d[col]);
          median = d3.median(data, d => +d[col]);
          sd = d3.deviation(data, d => +d[col]);

          // Calculate pct. missing         
          pct_missing = data.filter(d => d[col] === null || d[col] === "").length / data.length
          chart = Histogram(data, col, type)

          // Element to return
          el = htl.html`<tr style="font-family:sans-serif;font-size:13px;">
            ${col1}
            <td><div style="position:relative;">${chart}</div></td>
            <td>${pct_format(pct_missing)}</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
          </tr>`
          value = {
            column: col, type, min: start, max: end, mean: null, median: null,
            sd: null, missing: pct_missing, n_categories: null
          }
          break;

        // Continuous columns
        default:
          // Compute values 
          min = d3.min(data, d => +d[col])
          max = d3.max(data, d => +d[col])
          mean = d3.mean(data, d => +d[col])
          median = d3.median(data, d => +d[col])
          sd = d3.deviation(data, d => +d[col])
          if (Number.isFinite(sd)) {
            finiteFormat = d3.format(",." + d3.precisionFixed(sd / 10) + "f");
            format = x => Number.isFinite(x) ? finiteFormat(x) : notFiniteFormat(x);
          }
          else {
            format = notFiniteFormat;
          }
          pct_missing = data.filter(d => d[col] === null || isNaN(d[col])).length / data.length

          chart = Histogram(data, col, type)
          // Element to return
          el = htl.html`<tr style="font-family:sans-serif;font-size:13px;">
            ${col1}
            <td><div style="position:relative;top:3px;">${chart}</div></td>
            <td>${pct_format(pct_missing)}</td>
            <td>${format(mean)}</td>
            <td>${format(median)}</td>
            <td>${format(sd)}</td>
          </tr>`

          value = { column: col, type, min, max, mean, median, sd, missing: pct_missing, n_categories: null }
          break;
      }
      el.value = value;
      el.appendChild(html`<style>td {vertical-align:middle;} </style>`)
      return el
    }
  )
}

function _Histogram(colorMap, d3, getDateFormat, addTooltips, Plot) {
  return (
    (data, col, type = "continuous") => {
      // Compute color + mean
      const barColor = colorMap.get(type).brighter;
      const mean = d3.mean(data, (d) => d[col]);

      // Formatter for the mean
      const extent = d3.extent(data, (d) => d[col]);
      const format = type === "date" ? getDateFormat(extent) :
        Math.floor(extent[0]) === Math.floor(extent[1]) ? d3.format(",.2f") : d3.format(",.0f");
      const rules = [{ label: "mean", value: mean }];
      return addTooltips(
        Plot.plot({
          height: 55,
          width: 240,
          style: {
            display: "inline-block"
          },
          x: {
            label: "",
            ticks: extent,
            tickFormat: format
          },
          y: {
            axis: null
          },
          marks: [
            Plot.rectY(
              data,
              Plot.binX(
                {
                  y: "count",
                  title: (elems) => {
                    // compute range for the elements
                    const [start, end] = d3.extent(elems, (d) => d[col]);
                    let barFormat;
                    if (type === "date") {
                      barFormat = getDateFormat([start, end]);
                    } else {
                      barFormat = d3.format(
                        Math.floor(start) === Math.floor(end) ? ",.2f" : ",.0f"
                      );
                    }
                    return `${elems.length} rows\n[${barFormat(
                      start
                    )} to ${barFormat(end)}]`;
                  }
                },
                { x: col, fill: barColor }
              )
            ),
            Plot.ruleY([0]),
            Plot.ruleX(rules, {
              x: "value",
              strokeWidth: 2,
              title: (d) => `${d.label} ${col}: ${format(d.value)}`
            })
          ],
          style: {
            marginLeft: -17,
            background: "none",
            overflow: "visible"
          }
        }),
        { opacity: 1, fill: colorMap.get(type).color }
      );
    }
  )
}


function _pct_format(d3) {
  return (
    d3.format(".1%")
  )
}
function _SmallStack(d3, addTooltips, Plot, pct_format) {
  return (
    (categoryData, col, maxCategories = 100) => {
      // Get a horizontal stacked bar
      const label = categoryData.length === 1 ? " category" : " categories";
      let chartData = categoryData;
      let categories = 0;
      if (chartData.length > maxCategories) {
        chartData = categoryData.filter((d, i) => i < maxCategories);
        const total = d3.sum(categoryData, (d) => d.count);
        const otherCount = total - d3.sum(chartData, (d) => d.count);
        let other = {};
        other[col] = "Other categories...";
        other.count = otherCount;
        other.pct = other.count / total;
        chartData.push(other);
      }

      return addTooltips(
        Plot.barX(chartData, {
          x: "count",
          fill: col,
          y: 0,
          title: (d) => d[col] + "\n" + pct_format(d.pct)
        }).plot({
          color: { scheme: "blues" },
          marks: [
            Plot.text([0, 0], {
              x: 0,
              frameAnchor: "bottom",
              dy: 10,
              text: (d) => d3.format(",.0f")(categoryData.length) + `${label}`
            })
          ],
          style: {
            paddingTop: "0px",
            paddingBottom: "15px",
            textAnchor: "start",
            overflow: "visible"
          },
          x: { axis: null },
          color: {
            domain: chartData.map((d) => d[col]),
            scheme: "blues",
            reverse: true
          },
          height: 30,
          width: 205,
          y: {
            axis: null,
            range: [30, 3]
          }
        }),
        { fill: "darkblue" }
      );
    }
  )
}
function _icon_fns(html, colorMap) {
  return (
    {
      ordinal: () => html`<div style="display:inline-block; border-radius:100%; width: 16px; height: 16px; background-color: ${colorMap.get("ordinal").color}; transform: scale(1.3); vertical-align: middle; align-items: center;margin-right:8px;}">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="4" width="2" height="2" fill="white"/>
      <rect x="7" y="4" width="6" height="2" fill="white"/>
      <rect x="4" y="7" width="2" height="2" fill="white"/>
      <rect x="7" y="7" width="6" height="2" fill="white"/>
      <rect x="4" y="10" width="2" height="2" fill="white"/>
      <rect x="7" y="10" width="6" height="2" fill="white"/>
    </svg>
  </div>`,
      date: () => html`<div style="display:inline-block; border-radius:100%; width: 16px; height: 16px; background-color: ${colorMap.get("date").color}; transform: scale(1.3); vertical-align: middle; align-items: center;margin-right:8px;}">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="5" width="8" height="1" fill="white"/>
      <rect x="5" y="4" width="2" height="1" fill="white"/>
      <rect x="9" y="4" width="2" height="1" fill="white"/>
      <rect x="4" y="7" width="8" height="5" fill="white"/>
    </svg>
  </div>`,
      continuous: () => html`<div style="display:inline-block; border-radius:100%; width: 16px; height: 16px; background-color: ${colorMap.get("continuous").color}; transform: scale(1.3); vertical-align: middle; align-items: center;margin-right:8px;}">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="12" width="4" height="2" transform="rotate(-90 4 12)" fill="white"/>
      <rect x="7" y="12" width="6" height="2" transform="rotate(-90 7 12)" fill="white"/>
      <rect x="10" y="12" width="8" height="2" transform="rotate(-90 10 12)" fill="white"/>
    </svg>
  </div>`
    }
  )
}
function _getType() {
  return (
    (data, column) => {
      for (const d of data) {
        const value = d[column];
        if (value == null) continue;
        if (typeof value === "number") return "continuous";
        if (value instanceof Date) return "date";
        return "ordinal"
      }
      // if all are null, return ordinal
      return "ordinal"
    }
  )
}

function _getDateFormat(d3) {
  return (
    (extent) => {
      const formatMillisecond = d3.utcFormat(".%L"),
        formatSecond = d3.utcFormat(":%S"),
        formatMinute = d3.utcFormat("%I:%M"),
        formatHour = d3.utcFormat("%I %p"),
        formatDay = d3.utcFormat("%a %d"),
        formatWeek = d3.utcFormat("%b %d"),
        formatMonth = d3.utcFormat("%B"),
        formatYear = d3.utcFormat("%Y");

      // Test on the difference between the extent, offset by 1

      return extent[1] > d3.utcYear.offset(extent[0], 1) ? formatYear :
        extent[1] > d3.utcMonth.offset(extent[0], 1) ? formatMonth :
          extent[1] > d3.utcWeek.offset(extent[0], 1) ? formatWeek :
            extent[1] > d3.utcDay.offset(extent[0], 1) ? formatDay :
              extent[1] > d3.utcHour.offset(extent[0], 1) ? formatHour :
                extent[1] > d3.utcMinute.offset(extent[0], 1) ? formatMinute :
                  extent[1] > d3.utcSecond.offset(extent[0], 1) ? formatSecond :
                    extent[1] > d3.utcMillisecond.offset(extent[0], 1) ? formatMillisecond :
                      formatDay
    }
  )
}
function _colorMap(d3, _) {
  return (
    new Map([["ordinal", "rgba(78, 121, 167, 1)"],
    ["continuous", "rgba(242, 142, 44, 1)"],
    ["date", "rgba(225,87,89, 1)"]
    ].map(d => {
      const col = d3.color(d[1])
      const color_copy = _.clone(col)
      color_copy.opacity = .6
      return [d[0], { color: col.formatRgb(), brighter: color_copy.formatRgb() }]
    }))
  )
}

export default function define(runtime, observer) {
  const main = runtime.module();



  function toString() { return this.url; }
  // const fileAttachments = new Map([
  //   ["penguins.csv", {url: new URL("./files/715db1223e067f00500780077febc6cebbdd90c151d3d78317c802732252052ab0e367039872ab9c77d6ef99e5f55a0724b35ddc898a1c99cb14c31a379af80a.csv", import.meta.url), mimeType: "text/csv", toString}]
  // ]);
  // const fileAttachments = new Map([

  //   ["penguins.csv", {url: "https://static.observableusercontent.com/files/715db1223e067f00500780077febc6cebbdd90c151d3d78317c802732252052ab0e367039872ab9c77d6ef99e5f55a0724b35ddc898a1c99cb14c31a379af80a", mimeType: "text/csv", toString}]

  // ]);
  // main.builtin("FileAttachment", runtime.fileAttachments(name => fileAttachments.get(name)));
  main.variable(observer()).define(["md"], _1);
  main.variable(observer()).define(["md"], _2);

  main.variable(observer("data")).define("data", ["FileAttachment", "d3", "fetch"], myData);
  main.variable(observer()).define(["Inputs", "data"], _4);
  main.variable(observer()).define(["md"], _5);
  main.variable(observer()).define(["data"], _6);
  main.variable(observer()).define(["Inputs", "data"], _7);
  main.variable(observer()).define(["md"], _8);
  main.variable(observer()).define(["Inputs", "data"], _9);
  main.variable(observer()).define(["md"], _10);
  main.variable(observer("configuration")).define("configuration", configFun);
  main.variable(observer("viewof selection")).define("viewof selection", ["Inputs", "data"], _selection);
  main.variable(observer("viewof selection1")).define("viewof selection1", ["Inputs", "data", "configuration"], _selection1);
  main.variable(observer("selection")).define("selection", ["Generators", "viewof selection"], (G, _) => G.input(_));
  main.variable(observer()).define(["selection"], _12);

  main.variable(observer("search")).define("search", ["Inputs"], search);
  main.variable(observer()).define(["md"], _13);
  main.variable(observer()).define(["md"], _14);
  main.variable(observer()).define(["Inputs", "data"], _15);
  main.variable(observer()).define(["md"], _16);
  main.variable(observer()).define(["Inputs", "data", "sparkbar", "d3"], _17);
  main.variable(observer("sparkbar")).define("sparkbar", ["htl"], _sparkbar);
  main.variable(observer()).define(["md"], _19);
  main.variable(observer()).define(["md"], _20);
  main.variable(observer()).define(["Inputs", "data"], _21);
  main.variable(observer()).define(["md"], _22);
  main.variable(observer()).define(["Inputs", "data"], _23);
  main.variable(observer()).define(["md"], _24);
  main.variable(observer()).define(["md"], _25);
  main.variable(observer("Table")).define("Table", ["Inputs"], _Table);


  //s
  main.variable(observer("colorMap")).define("colorMap", ["d3", "_"], _colorMap);
  main.variable(observer("getDateFormat")).define("getDateFormat", ["d3"], _getDateFormat);
  main.variable(observer("getType")).define("getType", _getType);
  main.variable(observer("icon_fns")).define("icon_fns", ["html", "colorMap"], _icon_fns);
  main.variable(observer("SmallStack")).define("SmallStack", ["d3", "addTooltips", "Plot", "pct_format"], _SmallStack);
  main.variable(observer("pct_format")).define("pct_format", ["d3"], _pct_format);
  main.variable(observer("Histogram")).define("Histogram", ["colorMap", "d3", "getDateFormat", "addTooltips", "Plot"], _Histogram);
  main.variable(observer("SummarizeColumn")).define("SummarizeColumn", ["d3", "getType", "htl", "icon_fns", "SmallStack", "pct_format", "Histogram", "html"], _SummarizeColumn);
  main.variable(observer("viewof col_summary")).define("viewof col_summary", ["SummarizeColumn", "data"], _col_summary);


  const child1 = runtime.module(define1);
  main.import("addTooltips", child1);
  const child2 = runtime.module(define2);
  main.import("dataInput", child2);

  return main;
}
