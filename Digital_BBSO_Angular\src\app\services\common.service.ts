import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'lodash';
import { DataService } from './data.service';
import { FormControl } from '@angular/forms';
import { DatePipe, formatDate } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
// import { TranslationService } from './TranslationService/translation.services';
import { environment } from 'src/environments/environment';
import { LanguageService } from './language.service';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  private toasterSubject = new Subject<any>();
  private settingSubject = new Subject<any>();

  public contrastPass = true;
  public colorMode = 'light';
  public palettecolor1: any = '#ffffff';
  public palettecolor2: any = '#ffffff';
  public palettecolor3: any = '#2e52dd';
  public palettecolor4: any = '#000000';
  public palettecolor5: any = '#2e52dd';
  public palettecolor6: any = '#ff3333';
  public palettecolor7: any = '#ffffff';
  public palettecolor8: any = '#2e52dd';
  public palettecolor9: any = '#ffffff';
  public palettecolor10: any = '#2e52dd';
  public palettecolor11: any = '#2e52dd';
  public userInfo: any = {};
  public applicationInfo: any = {};
  public userSiteList = [];
  public observeList = [];
  public userIntegrationMenu;
  public menuFeatureUserIn = [];
  public userAssignedSite = [];
  public selectedLanguage = 'EN';
  // lang Object
  public labelObject = {
    EN:{}, 
    DE:{},
    IT:{},
    ES:{},
    FR:{},
    PT:{},
    KO:{},
    JA:{},
    ZH:{},
    NL:{}
  };
  public toasterLabelObject = {}
  loaderFlag: boolean;
  userUnitList: any;
  auditNumbers: any=[];
  showLanguageLoading: boolean = false;
  defaultConfigList: any = [];
  notificationGroupData: any;
  constructor(
    private datePipe: DatePipe,
    private dataService: DataService,
    private toastr: ToastrService,
    private translate: TranslateService,
    private languageService: LanguageService,
    // private translationService: TranslationService
  ) {
    console.log('this.commonService.dateFormat common')
  }


  private dataSubject = new BehaviorSubject<any>(null);
  data$ = this.dataSubject.asObservable();

  updateLabelObject (locale: any) {
    this.toasterLabelObject = {
      'toasterNoconfig': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterNoconfig'] || 'toasterNoconfig',
      'toasterUploadreached': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterUploadreached'] || 'toasterUploadreached',
      'toasterImguploadsuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterImguploadsuccess'] || 'toasterImguploadsuccess',
      'toasterImguploadfailed': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterImguploadfailed'] || 'toasterImguploadfailed',
      'toasterUploadsuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterUploadsuccess'] || 'toasterUploadsuccess',
      'toasterFailed': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterFailed'] || 'toasterFailed',
      'toasterPleasefillreqfields': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasefillreqfields'] || 'toasterPleasefillreqfields',
      'toasterSavesuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterSavesuccess'] || 'toasterSavesuccess',
      'toasterNoDataToSave': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterNoDataToSave'] || 'toasterNoDataToSave',
      'toasterActionalreadycreated': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterActionalreadycreated'] || 'toasterActionalreadycreated',
      'toasterActionitemcreatedsuccessfully': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterActionitemcreatedsuccessfully'] || 'toasterActionitemcreatedsuccessfully',
      'toasterPleasefilldetails': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasefilldetails'] || 'toasterPleasefilldetails',
      'toasterUsernotfound': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterUsernotfound'] || 'toasterUsernotfound',
      'toasterPleasechoosecat': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasechoosecat'] || 'toasterPleasechoosecat',
      'toasterSomethingwentwrong': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterSomethingwentwrong'] || 'toasterSomethingwentwrong',
      'toasterFailedtosaveobs': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterFailedtosaveobs'] || 'toasterFailedtosaveobs',
      'toasterNotassociatedrole': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterNotassociatedrole'] || 'toasterNotassociatedrole',
      'toasterPleasechoosecoreprinciple': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasechoosecoreprinciple'] || 'toasterPleasechoosecoreprinciple',
      'toasterPleasechooseprocesstype': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasechooseprocesstype'] || 'toasterPleasechooseprocesstype',
      'toasterPleasechooseobervation': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterPleasechooseobervation'] || 'toasterPleasechooseobervation',
      'toasterRespsuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterRespsuccess'] || 'toasterRespsuccess',
      'toasterRespfailed': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterRespfailed'] || 'toasterRespfailed',
      'toasterSuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterSuccess'] || 'toasterSuccess',
      'toasterFailure': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterFailure'] || 'toasterFailure',
      'toasterOpsuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterOpsuccess'] || 'toasterOpsuccess',
      'toasterFilefailed': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterFilefailed'] || 'toasterFilefailed',
      'toasterAlreadyexists': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterAlreadyexists'] || 'toasterAlreadyexists',
      'toasterNoprocess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterNoprocess'] || 'toasterNoprocess',
      'toasterCompthankyou': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterCompthankyou'] || 'toasterCompthankyou',
      'toasterSomethingwentwrongmail': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterSomethingwentwrongmail'] || 'toasterSomethingwentwrongmail',
      'toasterDontenterduplicatemail': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterDontenterduplicatemail'] || 'toasterDontenterduplicatemail',
      'toasterFillvalidemail': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterFillvalidemail'] || 'toasterFillvalidemail',
      'toasterEmailsent': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterEmailsent'] || 'toasterEmailsent',
      'toasterDisabsuccess': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterDisabsuccess'] || 'toasterDisabsuccess',
      'toasterActionitemcreatefailed': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterActionitemcreatefailed'] || 'toasterActionitemcreatefailed',
      'toasterObsdelete': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterObsdelete'] || 'toasterObsdelete',
      'toasterQualityassessment': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterQualityassessment'] || 'toasterQualityassessment',
      'toasterInfo': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterInfo'] || 'toasterInfo',
      'toasterTurnlandscape': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterTurnlandscape'] || 'toasterTurnlandscape',
      'toasterNodashboardconfig': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterNodashboardconfig'] || 'toasterNodashboardconfig',
      'toasterEnablechecklist': this.labelObject[locale][environment.applicationId+'.'+locale+'.stLabel.'+'toasterEnablechecklist'] || 'toasterEnablechecklist',
    }
  }

  setData(data: any): void {
    this.dataSubject.next(data);
  }
  triggerToast(toaster) {
    // this.toasterSubject.next(toaster);
    if (toaster.type == 'success') {
      this.toastr.success(toaster.msg, toaster.title);
    } else if (toaster.type == 'error') {
      this.toastr.error(toaster.msg, toaster.title);
    } else {
      this.toastr.info(toaster.msg, toaster.title);
    }
  }

  listenToast(): Observable<any> {
    return this.toasterSubject.asObservable();
  }

  dateFormatArray = [
    { name: "dd/MM/yyyy", value: "dd/MM/yyyy", customFormat: "DD/MM/YYYY",local:"en-IN" },
    { name: "MM/dd/yyyy", value: "MM/dd/yyyy", customFormat: "MM/DD/YYYY",local:"en-029" },
    { name: "dd.MM.yyyy", value: "dd.MM.yyyy", customFormat: "DD.MM.YYYY",local:"en-IN" },
    { name: "yyyy/MM/dd", value: "yyyy/MM/dd", customFormat: "YYYY/MM/DD",local:"en-IN" }
    
  ]
  dateFormat = { name: "MM/dd/yyyy", value: "MM/dd/yyyy", customFormat: "MM/DD/YYYY",local:"en-029" };

  triggerSetting(cookie) {
    this.settingSubject.next(cookie);
  }

  listenSetting(): Observable<any> {
    return this.settingSubject.asObservable();
  }

  saveMenuAction(menuActionList) {
    localStorage.setItem('menuActionList', JSON.stringify(menuActionList));
  }

  retrieveMenuAction() {
    return localStorage.getItem('menuActionList');
  }
  removeMenuAction() {
    localStorage.removeItem('menuActionList');
  }

  public tableColumn = [
    {
      name: 'employeeInfo',
      columns: [
        { name: 'employeeID' },
        { name: 'employeeName' },
        { name: 'dateOfBirth' },
        { name: 'mobileNumber' },
        { name: 'designation' },
        { name: 'dateOfJoining' },
        { name: 'employmentStatus' },
        { name: 'gender' },
        { name: 'panNumber' },
        { name: 'uanNumber' },
      ],
    },
    {
      name: 'PayrollData',
      columns: [
        { name: 'employeeIDs' },
        { name: 'employeeName' },
        { name: 'grossPay' },
        { name: 'employeePF' },
        { name: 'employeeESI' },
        { name: 'professionalTax' },
        { name: 'tax' },
        { name: 'totalDeduction' },
        { name: 'netSalary' },
        { name: 'workingDays' },
        { name: 'paidDays' },
      ],
    },
  ];

  public timeDiffCalc(date: any) {
    const dateFuture: any = new Date(date);
    const dateNow: any = new Date();

    let delta = Math.abs(dateFuture - dateNow) / 1000;
    const days = Math.floor(delta / 86400);
    delta -= days * 86400;
    const hours = Math.floor(delta / 3600) % 24;
    delta -= hours * 3600;
    const minutes = Math.floor(delta / 60) % 60;
    delta -= minutes * 60;
    const seconds = delta % 60;
    var returnString = '';
    if (days > 0) {
      returnString = returnString + days + ' days ';
    }
    if (hours > 0 || returnString.length > 0) {
      returnString = returnString + hours + ' hours ';
    }
    if (minutes > 0 || returnString.length > 0) {
      returnString = returnString + minutes + ' minutes ';
    }
    // if (seconds > 0 || returnString.length > 0) {
    //   returnString = returnString + parseInt(seconds + "") + " soconds ";
    // }

    return returnString;
  }

  public allSiteObj = {
    name: 'All',
    externalId: 'All',
    description: 'All',
    siteCode: 'All',
    functionalLocations: {
      items: [
        {
          externalId: 'All',
          name: 'All',
        },
      ],
    },
  };
  public allUnitObj = {
    name: 'All',
    externalId: 'All',
    description: 'All',
    refersTo: {
      items: [
        {
          externalId: 'All',
          name: 'All',
        },
      ],
    },
  };
  public allGeoRegion = {
    name: 'All',
    description: 'All',
    externalId: 'All',
  };
  public allCountry = {
    name: 'All',
    externalId: 'All',
  };
  public regionList = [];
  public countryList = [];
  public siteList = [];
  public unitList = [];
  public businessLineList = [];
  public reportingLocationList = [];

  public processList = [];
  public subProcessList = [];
  public filterListFetched = false;
  public filterListSubject: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  public observeListSubject: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  public siteChanges$ = new Subject<string>();
  siteChangeFun(text: string): void {
    this.siteChanges$.next(text);
  }
  siteChanges(): Observable<string> {
    return this.siteChanges$.asObservable();
  }
  filterListLoaded(value: any) {
    if (value == 'Site') {
      this.filterListFetched = true;
    }
    this.filterListSubject.next(value);
  }

  getProcessConfiguration(siteId, cb) {
    var _this = this;
    var siteFinded = _this.processList.find(
      (e) => e.refSite && e.refSite.externalId == siteId
    );
    if (!siteFinded && siteId) {
      _this.dataService
        .postData(
          { externalId: siteId },
          _this.dataService.NODE_API + '/api/service/listProcessBySite'
        )
        .subscribe((resData: any) => {
          var processItems =
            resData['data']['list' + _this.configuration['typeProcess']][
              'items'
            ];
          _this.processList = _this.processList.concat(
            _.orderBy(processItems, ['name'], ['desc'])
          );
          _this.processList = _.uniqBy(_this.processList, 'externalId');
          _this.filterListLoaded('Process');

          cb(_this.processList);
        });
    } else {
      cb(_this.processList);
    }
  }
  getSiteList() {
    var _this = this;
    _this.dataService
      .postData(
        {},
        _this.dataService.NODE_API +
          '/api/service/listReportingSiteFunctionalLocation'
      )
      .subscribe((resData: any) => {
        if (resData.hasOwnProperty('error')) {
          _this.dataService.login();
        }
        _this.siteList = [];
        // _this.siteList = [_this.allSiteObj];
        var siteItems =
          resData['data']['list' + _this.configuration['typeReportingSite']][
            'items'
          ];
        _this.siteList = _this.siteList.concat(
          _.orderBy(siteItems, ['name'], ['asc'])
        );
        _this.siteList = _.uniqBy(_this.siteList, 'externalId');
        _this.observeListSubject.next(true);
        var pageInfo =
          resData['data']['list' + _this.configuration['typeReportingSite']][
            'pageInfo'
          ];
        if (pageInfo.hasNextPage) {
          nextPageSite(pageInfo);
        } else {
          _this.filterListLoaded('Site');
        }
        function nextPageSite(pageInfo) {
          _this.dataService
            .postData(
              pageInfo,
              _this.dataService.NODE_API +
                '/api/service/listReportingSiteCursor'
            )
            .subscribe((resData: any) => {
              _this.siteList = _this.siteList.concat(
                resData['data'][
                  'list' + _this.configuration['typeReportingSite']
                ]['items']
              );
              _this.siteList = _.orderBy(
                _.uniqBy(_this.siteList, 'externalId'),
                ['name'],
                ['asc']
              );
              var pageInfo =
                resData['data'][
                  'list' + _this.configuration['typeReportingSite']
                ]['pageInfo'];
              if (pageInfo.hasNextPage) {
                nextPageSite(pageInfo);
              } else {
                _this.filterListLoaded('Site');
              }
            });
        }
      });
  }
  
  auditNumberHistory() {
    var _this = this;
    _this.dataService
      .postData({},
        _this.dataService.NODE_API + '/api/service/listSchedule'
      )
      .subscribe((resData: any) => {
        _this.auditNumbers = resData.data.listOFWASchedule.items.map(item => item.auditNumber);
        console.log(_this.auditNumbers)
      });
  }

  getSiteByCountryList(countries, cb) {
    var _this = this;
    _this.dataService
      .postData(
        { countries: countries },
        _this.dataService.NODE_API + '/api/service/listReportingSiteByCountry'
      )
      .subscribe((resData: any) => {
        if (resData.hasOwnProperty('error')) {
          _this.dataService.login();
        }
        // var siteList = [_this.allSiteObj];
        var siteList = [];
        var siteItems =
          resData['data']['list' + _this.configuration['typeReportingSite']][
            'items'
          ];
        siteList = siteList.concat(_.orderBy(siteItems, ['name'], ['asc']));
        siteList = _.uniqBy(siteList, 'externalId');
        _this.filterListLoaded('Site');
        cb(siteList);
      });
  }
  getUnitList1(siteId, cb) {
    var _this = this;
    _this.dataService
      .postData(
        { sites: siteId },
        _this.dataService.NODE_API +
          '/api/service/listReportingUnitFunctionalLocation'
      )
      .subscribe((resData: any) => {
        // _this.unitList = [_this.allUnitObj];
        _this.unitList = [];
        var myUnit = [];
        _.each(
          resData['data']['list' + _this.configuration['typeReportingSite']][
            'items'
          ],
          function (eUnit) {
            _.each(eUnit['reportingUnits']['items'], function (eUnit1) {
              myUnit.push(eUnit1);
            });
          }
        );
        _this.unitList = _this.unitList.concat(
          _.orderBy(myUnit, ['description'], ['asc'])
        );
        _this.unitList = _.uniqBy(_this.unitList, 'externalId');
        _this.filterListLoaded('Unit');
        cb(_this.unitList);
      });
  }

    getUser(email, cb) {
        var _this = this;
        _this.dataService.postData({ email: email }, _this.dataService.NODE_API + "/api/service/listUserAzureAttribute").
            subscribe((resData: any) => {
                if (resData["data"] && resData["data"]["list" + _this.configuration["typeUserAzureAttribute"]]["items"].length > 0) {
                    var userItems = resData["data"]["list" + _this.configuration["typeUserAzureAttribute"]]["items"][0];
                    cb(userItems)
                } else {
                    cb(null)
                }
            });
    }
    
  getApplication() {
    var _this = this;

    _this.dataService
      .postData(
        { externalId: _this.dataService.applicationId },
        _this.dataService.NODE_API + '/api/service/listApplication'
      )
      .subscribe((resData: any) => {
        // _this.regionList = [_this.allGeoRegion]
        var appInfo =
          resData['data']['list' + _this.configuration['typeApplication']][
            'items'
          ][0];
        _this.applicationInfo = appInfo;
        // cb();
      });
  }
  getGeoRegion() {
    var _this = this;

    _this.dataService
      .postData({}, _this.dataService.NODE_API + '/api/service/listGeoRegion')
      .subscribe((resData: any) => {
        // _this.regionList = [_this.allGeoRegion]
        _this.regionList = [];
        _this.regionList = _this.regionList.concat(
          _.orderBy(
            resData['data']['list' + _this.configuration['typeGeoRegion']][
              'items'
            ],
            ['description'],
            ['asc']
          )
        );
        _this.regionList = _.uniqBy(_this.regionList, 'externalId');
        _this.filterListLoaded('Region');
        // cb();
      });
  }

  getCountry(region, cb) {
    var _this = this;

    _this.dataService
      .postData(
        { regions: region },
        _this.dataService.NODE_API + '/api/service/listCountry'
      )
      .subscribe((resData: any) => {
        // _this.countryList = [_this.allCountry]
        _this.countryList = [];
        _this.countryList = _this.countryList.concat(
          _.orderBy(
            resData['data']['list' + _this.configuration['typeCountry']][
              'items'
            ],
            ['name'],
            ['asc']
          )
        );
        _this.countryList = _.uniqBy(_this.countryList, 'externalId');
        _this.filterListLoaded('Country');
        cb(_this.countryList);
      });
  }

  getBusinessLine(units, cb) {
    var _this = this;
    _this.dataService
      .postData(
        { units: units },
        _this.dataService.NODE_API + '/api/service/listBusinessLineByUnit'
      )
      .subscribe((resData: any) => {
        // _this.countryList = [_this.allCountry]
        var myBusinessLine = [];
        _.each(
          resData['data']['list' + this.configuration['typeReportingUnit']][
            'items'
          ],
          function (eUnit) {
            // var myFilterIndex = myBusinessLine.findIndex(({ externalId }) => externalId === eUnit.externalId);
            // if (eUnit['newBusinessLine']) {
            //   myBusinessLine.push(eUnit['newBusinessLine']);
            // }
          }
        );
        _this.businessLineList = _this.businessLineList.concat(
          _.orderBy(myBusinessLine, ['description'], ['asc'])
        );
        _this.businessLineList = _.uniqBy(_this.businessLineList, 'externalId');
        _this.filterListLoaded('BusinessLine');
        cb(_this.businessLineList);
      });
  }
  getReportingLocation() {
    var _this = this;
    _this.dataService
      .postData(
        {},
        _this.dataService.NODE_API + '/api/service/listReportingLocation'
      )
      .subscribe((resData: any) => {
        var myReportingLocation =
          resData['data']['list' + this.configuration['typeReportingLocation']][
            'items'
          ];
        _this.reportingLocationList = _.orderBy(
          myReportingLocation,
          ['description'],
          ['asc']
        );
        _this.reportingLocationList = _.uniqBy(
          _this.reportingLocationList,
          'externalId'
        );
        _this.filterListLoaded('ReportingLocation');
        console.log(_this.reportingLocationList);
      });
  }
  getReportingLocationAll(){
    this.dataService
    .postData(
      {},
      this.dataService.NODE_API + '/api/service/listReportingLocationAll'
    )
    .subscribe((res: any) => {
      console.log(res)
  })}

  setDefaultSiteValue(callback) {
    var _this = this;
    _this.dataService
      .postData(
        { externalId: _this.dataService.siteId },
        _this.dataService.NODE_API + '/api/service/listCommonDataSet'
      )
      .subscribe((resData: any) => {
        return callback(
          resData['data']['list' + _this.configuration['typeReportingSite']][
            'items'
          ][0]
        );
      });
  }

  setDefaultUnitValue(callback) {
    var _this = this;
    _this.dataService
      .postData(
        { externalId: _this.dataService.unitId },
        _this.dataService.NODE_API + '/api/service/listCommonDataSet'
      )
      .subscribe((resData: any) => {
        return callback(
          resData['data']['list' + _this.configuration['typeReportingUnit']][
            'items'
          ][0]
        );
      });
  }
  _siteFilter(value: any): any[] {
    const filterValue = typeof value == 'string' ? value.toLowerCase() : '';
    return this.siteList.filter((option) =>
      option['name'].toLowerCase().includes(filterValue)
    );
  }
  _unitFilter(value: any): any[] {
    const filterValue = typeof value == 'string' ? value.toLowerCase() : '';
    return this.unitList.filter((option) =>
      option['name'].toLowerCase().includes(filterValue)
    );
  }

  getlistUser(email, cb) {
    var _this = this;
    _this.dataService
      .postData(
        { email: email },
        _this.dataService.NODE_API + '/api/service/listUser'
      )
      .subscribe((resData: any) => {
        var userItems =
          resData['data']['search' + _this.configuration['typeUser']]['items'][0];
        _this.userInfo = userItems;
        cb(null);
      });
  }
  getIntegrationUser(keyCall, cb){
    console.log('getIntegrationUser')
    var _this = this;
    var postObj = {
      "application_code": _this.dataService.applicationId
    }
    if(_this.dataService.siteId && _this.dataService.siteId.length > 0){
      postObj["site_code"] = _this.dataService.siteId
    }
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/integrationUser").
    subscribe((resData: any) => {
      console.log('resData====>',resData)
      _this.userUnitList = resData.units
      _this.selectedLanguage = resData.favoriteLanguage?.toUpperCase() || 'EN';
      console.log('Inside CommonService selected Language: ', _this.selectedLanguage)

      // _this.translationService.getLabels(
      //   _this.selectedLanguage.toUpperCase(),
      //   function (data) {
      //     _this.updateLabelObject(_this.selectedLanguage.toUpperCase())
      //   }
      // )
   //   {"detail":"Invalid authorization token: ExpiredSignatureError"}
   if(resData && resData.detail =="Invalid authorization token: ExpiredSignatureError"){
    _this.dataService.login();
   }
   if(resData && resData.message =="Invalid authorization token: ExpiredSignatureError"){
    _this.dataService.login();
   }
     _this.userIntegrationMenu = resData
   
    if(_this.userIntegrationMenu.applications && _this.userIntegrationMenu.applications.length > 0){
      _this.userAssignedSite = _this.userIntegrationMenu.applications[0].userSites
      console.log('_this.userAssignedSite',_this.userAssignedSite)
      if(_this.userAssignedSite.length > 0){
        // console.log('_this.userAssignedSite[0].siteId',_this.userAssignedSite[0].siteId)
         if(keyCall != "header-click"){
          if(_this.userIntegrationMenu.favoriteLanguage){
            _this.selectedLanguage = _this.userIntegrationMenu.favoriteLanguage.toUpperCase();
            console.log('_this.selectedLanguage ',_this.selectedLanguage )
          }
          if( _this.userIntegrationMenu.favoriteReportingSite){
           var findSiteUs = _this.userAssignedSite.find(item => item.siteId == _this.userIntegrationMenu.favoriteReportingSite.siteId)
           console.log('findSiteUs',findSiteUs) 
           if(findSiteUs){
            _this.dataService.siteId =  findSiteUs.siteId
            _this.getIntegrationUser('header-click',function (data) {
            
            })
           }else{
            _this.dataService.siteId = _this.userAssignedSite[0].siteId
           }
          
          }else{
            _this.dataService.siteId = _this.userAssignedSite[0].siteId
          }
            
         }
         if(_this.userIntegrationMenu.applications[0].roles.length>0){
          _this.menuFeatureUserIn = [];
          
          _this.userIntegrationMenu.applications[0].roles.forEach((ele)=>{
           var findSiteRole =  ele.siteCodes.find(item => item == _this.dataService.siteId)
           if(findSiteRole){
            _this.menuFeatureUserIn.push(...ele.features)
           }
          })
       //   _this.userIntegrationMenu.applications[0].roles.forEach(i => _this.menuFeatureUserIn.push(...i.features))
          _this.menuFeatureUserIn  = _.orderBy(_this.menuFeatureUserIn, ['featureAccessLevelCode'], ['asc']);
          console.log('menuFeatureUserIn',_this.menuFeatureUserIn)
         }else{
             _this.menuFeatureUserIn = [];
         }
      }
    }else{
      _this.menuFeatureUserIn = [];
    }
 
   

     if(_this.userIntegrationMenu.applications[0].roles.length==0){
        _this.triggerToast({
            type: 'error',
            title: '',
            msg: _this.toasterLabelObject['toasterNotassociatedrole'],
          });
          
    }
     _this.filterListLoaded("userAccess");
     if(_this.userIntegrationMenu && _this.userIntegrationMenu.applications &&
          _this.userIntegrationMenu.applications[0].roles.length > 0){
        cb(resData)
     }else{
        cb(null)
     }
    
    });
  }
  getDefaultConfig(){
    var _this = this;
    _this.dataService.getJsonFile('assets/processConfig.json').subscribe(
      (data) => {
        _this.defaultConfigList = data;
      },
      (error) => {
        console.error('Error fetching JSON file:', error);
      }
    );
  }
  getIntegrationUserTest(keyCall, cb){
    console.log('getIntegrationUser')
    var _this = this;
    var postObj = {
      "application_code": _this.dataService.applicationId
    }
    if(_this.dataService.siteId && _this.dataService.siteId.length > 0){
      postObj["site_code"] = _this.dataService.siteId
    }
    _this.dataService.postData(postObj, _this.dataService.UserAPI + "/integration/user").
    subscribe((resData: any) => {
      console.log('resData====>',resData)
   //   {"detail":"Invalid authorization token: ExpiredSignatureError"}
  
    });
  }
  getUniqueListBy(arr, key) {
    return [...new Map(arr.map((item) => [item[key], item])).values()];
  }
  isSiteAdmin(){
    console.log(this.userIntegrationMenu)
    if(this.userIntegrationMenu){
      var siteAdminList = this.userIntegrationMenu["applications"][0]["roles"].find(e => e.roleName == "Site Admin");
      if(siteAdminList && siteAdminList.siteCodes.find(e => e == this.dataService.siteId)){
        return true;
      }else{
        return false;
      }
    }else{
      return false;
    }
    
  }
  //listObservation
  getObservationList() {
    var _this = this;
    _this.dataService
      .postData(
        {
          limit: 1000,
          sites: _this.dataService.siteId ? [_this.dataService.siteId] : [],
          startDate: _this.dataService.startDate
            ? formatDate(_this.dataService.startDate, 'yyyy-MM-dd', 'en-US')
            : '',
          endDate: _this.dataService.endDate
            ? formatDate(_this.dataService.endDate, 'yyyy-MM-dd', 'en-US')
            : '',
        },
        _this.dataService.NODE_API + '/api/service/listObservation'
      )
      .subscribe((resData: any) => {
        var userObserve =
          resData['data']['list' + _this.configuration['typeObservation']][
            'items'
          ];
        userObserve.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDays = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear =
            dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        _this.observeList = userObserve;
        _this.observeListSubject.next(true);
      });
  }

  getFieldWalk() {
    var _this = this;
    _this.dataService
      .postData(
        {
          limit: 1000,
          sites: _this.dataService.siteId ? [_this.dataService.siteId] : [],
          startDate: _this.dataService.startDate
            ? formatDate(_this.dataService.startDate, 'yyyy-MM-dd', 'en-US')
            : '',
          endDate: _this.dataService.endDate
            ? formatDate(_this.dataService.endDate, 'yyyy-MM-dd', 'en-US')
            : '',
        },
        _this.dataService.NODE_API + '/api/service/listFieldWalk'
      )
      .subscribe((resData: any) => {
        var userObserve =
          resData['data']['list' + _this.configuration['typeFieldWalk']][
            'items'
          ];
        userObserve.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDays = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear =
            dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        _this.observeList = userObserve;
        _this.observeListSubject.next(true);
      });
  }

  notification(objectNoti: any, notificationType: any) {
    objectNoti.forEach((notificationObject: any) => {
      notificationObject.notificationType = notificationType;
    });
    console.log(objectNoti);
    this.dataService
      .postData(
        objectNoti,
        this.dataService.NODE_API + '/api/service/notificationEvent'
      )
      .subscribe((data) => {
        console.log(data);
      });
  }

  notificationGroup(){
    var _this=this
    _this.dataService
    .postData({},
      _this.dataService.NODE_API+"/api/service/notificationgroup")
      .subscribe((resData: any) => {
      console.log(resData.message)
      _this.notificationGroupData = resData
    })
  }

  toggleAllSelection(control: FormControl, list: any) {
    var selectAll = ['0'];
    if (control.value) {
      if (control.value.length > 0 && control.value[0] == '0') {
        _.each(list, function (eData) {
          selectAll.push(eData.externalId);
        });
        control.setValue(selectAll);
      } else {
        control.setValue([]);
      }
    }
  }

  getSelectedValue(arrayValue) {
    if (arrayValue) {
      var myArray = _.cloneDeep(arrayValue);
      if (myArray.length > 0 && myArray[0] == '0') {
        var retArray = myArray.splice(0, 1);
        return myArray;
      } else {
        return myArray;
      }
    } else {
      return [];
    }
  }
  formatDate(date: Date, format: string): string {
    return this.datePipe.transform(date, format);
  }

  configuration = {};
}
