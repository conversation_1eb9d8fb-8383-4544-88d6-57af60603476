@use '@angular/material' as mat;


// @font-face {
//   font-family: 'Myriad-Regular';
//   src: url('assets/Myriad Pro/Myriad Pro Regular/Myriad Pro Regular.ttf');
// }

// @font-face {
//   font-family: 'Myriad-Black';
//   src: url('assets/Myriad Pro/Myriad Pro Black/Myriad Pro Black.otf');
// }

// @font-face {
//   font-family: 'Myriad-Bold';
//   src: url('assets/Myriad Pro/Myriad Pro Bold/Myriad Pro Bold.ttf');
// }

// @font-face {
//   font-family: 'Myriad-ExtraBold';
//   src: url('assets/Myriad Pro/Myriad Pro Heavy/Myriad Pro Heavy.ttf');
// }

// @font-face {
//   font-family: 'Myriad-Medium';
//   src: url('assets/Myriad Pro/Myriad Pro Regular/Myriad Pro Regular.ttf');
// }

// @font-face {
//   font-family: 'Myriad-SemiBold';
//   src: url('assets/Myriad Pro/Myriad Pro Semibold/Myriad Pro Semibold.ttf');
// }

// $custom-typography: mat.define-typography-config($font-family: "Myriad-Regular"
//   );

@font-face {
  font-family: 'Roboto-Medium';
  src: url('assets/Roboto/Roboto-Medium.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}


@font-face {
  font-family: 'Roboto-black';
  src: url('assets/Roboto/Roboto-Black.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}


@font-face {
  font-family: 'Roboto-bold';
  src: url('assets/Roboto/Roboto-Bold.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}



@font-face {
  font-family: 'Roboto-light';
  src: url('assets/Roboto/Roboto-Light.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}




@font-face {
  font-family: 'Roboto-regular';
  src: url('assets/Roboto/Roboto-Regular.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Roboto-thin';
  src: url('assets/Roboto/Roboto-Thin.woff2') format('woff2'),
    url('assets/Roboto/Roboto-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

$custom-typography: mat.define-typography-config($font-family: "Roboto-regular"
  );

@include mat.core($custom-typography);


@import '_variables.scss';

html,
body {
  height: 100%;
  background-color: #f8f8f8;
}

body {
  margin: 0;
  // font-family: Roboto, "Helvetica Neue", sans-serif;
  font-family: 'Roboto-regular';
  // font-family: 'Kaisei Tokumin', serif;
}

.bold {
  font-family: "Roboto-bold" !important;
}

.regular {
  font-family: "Roboto-regular" !important;
}

.display-4 {
  font-size: $display-4-size !important;
}

.display-3 {
  font-size: $display-3-size !important;
}

.display-2 {
  font-size: $display-2-size !important;
}

.display-1 {
  font-size: $display-1-size !important;
}

.headline {
  font-size: $headline-size !important;
}

.title {
  font-size: $title-size !important;
}

.subheading-2 {
  font-size: $subheading-2-size !important;
}

.subheading-1 {
  font-size: $subheading-1-size !important;
}

.expandHead {
  font-size: $subheading-1-size !important;
}


.body-2 {
  font-size: $body-2-size !important;
}

.body-1 {
  font-size: $body-1-size !important;
}

.caption {
  font-size: $caption-size !important;
}

.palette-color1 {
  color: $palette-color1;
}

.palette-color2 {
  color: $palette-color2;
}

.palette-color3 {
  color: $palette-color3;
}

// .palette-color4:  #000;
// .palette-color5:  #63667B;
// .palette-color6:  #ff0000;

.button-svg-blue {
  fill: $palette-color2;
}

.font-28-bold {
  font-size: 28.83px;
  font-family: "Roboto-bold" !important;

}

.header-bg {
  background-color: rgb(255, 255, 255);
}

.input {
  font-size: $input-size !important;
}

.mat-elevation-z3 {
  position: relative;
  z-index: 2;
}

.fill-remaining-space {
  flex: 1 1 auto;
}

.marginLeftMinus {
  margin-left: -22px;
}

.listIcons {
  color: #4F4F4F;
  font-size: 20px;
  margin-top: 3px;
}

.vap-icon {
  margin: 0 10px;
  color: rgb(240, 93, 12);
}

.treat-head {
  margin-top: 10px;
  margin-left: 5px;
}

.treat-md {
  margin: 10px;
}

.margin_right {
  margin-right: 10px;

}

.inter-font14 {
  font-size: 14px;
  font-family: 'Roboto-Medium';
}

.inter-font12 {
  font-size: 12px;
  font-family: 'Roboto-Medium';
}

.inter-font {
  font-family: 'Roboto-Medium';
}

.icon-box {
  height: 35px;
  width: 35px;
  background-color: rgb(180, 179, 179); //
  border-radius: 6px;
  padding: 5px;
  margin: 5px 5px;
}

.icon-box-light {
  height: 19px;
  width: 20px;
  background-color: #083d5b;
  border-radius: 6px;
  padding: 7px 7px;
  margin: 5px 5px;
}

.dialog_input .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #ffffff !important;
  overflow: hidden !important;
  border-radius: 5px !important;

}

.set-back-color .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #ffffff !important;
  overflow: hidden !important;
  border-radius: 5px !important;


}

.behav-tree-select .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #ffffff !important;
  overflow: hidden !important;
  border-radius: 5px !important;

}

.behav-tree-input .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #ffffff !important;
  overflow: hidden !important;
  border-radius: 5px !important;

}

.head-font {
  font-weight: 600 !important;
  font-family: 'Roboto-Medium' !important;
  font-size: 24px !important;
}

.semi-bold {
  font-family: 'Roboto-Medium' !important;
  font-size: 16px !important;
}

.semi-bold-14 {
  font-family: 'Roboto-Medium' !important;
  font-size: 14px !important;
}

.semi-bold-12 {
  font-family: 'Roboto-Medium' !important;
  font-size: 12px !important;
}

.semi-bold-28 {
  font-family: 'Roboto-Medium' !important;
  font-size: 28.83px !important;
}

.semi-regular-12 {
  font-family: "Roboto-regular" !important;
  font-size: 12px !important;
}

.semi-regular-14 {
  font-family: "Roboto-regular" !important;
  font-size: 14px !important;
}

// .mat-form-field-appearance-outline .mat-form-field-outline-thick {
//   color: #083D5B !important;
// }


.background-highlight {
  background-color: #DBDBDB !important;
  font-size: 12px !important;
  cursor: pointer !important;
}

.background-white {
  background-color: white !important;
  font-size: 12px !important;
  cursor: pointer !important;

}

.links-wrapper>path {
  stroke: rgb(240, 93, 12);
}


.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: rgb(0, 0, 0);
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
  color: rgb(0, 0, 0);
  background-color: rgb(0, 0, 0);
}

.mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  background-color: rgba(0, 0, 0, 0.26);
}


.set-back-color-action .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #ffffff !important;
  overflow: hidden !important;
  border-radius: 5px !important;
  height: 36px;
  margin-top: 3px;
}


.waffle {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  justify-content: center;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  justify-content: center;
}



.block {
  width: 180px;
  height: 110px;
  margin: 5px;
  margin-right: 40px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
}

.dashblock {
  width: 140px;
  height: 110px;
  margin: 5px;
  // margin-right: 40px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
}

.dashblock1 {
  width: 160px;
  height: 110px;
  margin: 5px;
  margin-right: 25px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
}

.grid-card-head {
  background-image: linear-gradient(to bottom, #01112F, #2B3952);
  margin: 20px;
  height: auto;
}

.grid-card {
  width: auto;
  margin: 20px;
  height: auto;
}

.right_box_table tbody tr {
  margin-top: -20px;
}

.risk-matrix .mat-dialog-container {
  border-radius: 7px !important;
  overflow: auto;
  margin-top: 60px;
  margin-left: 90px;
  width: 100vw;
  height: 90vh;
  border-radius: 10px;
  // box-shadow: 0 3px 10px rgb(0 0 0 / 0.2) !important;
  box-shadow:
    0 2.8px 2.2px rgba(0, 0, 0, 0.034),
    0 6.7px 5.3px rgba(0, 0, 0, 0.048),
    0 12.5px 10px rgba(0, 0, 0, 0.06),
    0 22.3px 17.9px rgba(0, 0, 0, 0.072),
    0 41.8px 33.4px rgba(0, 0, 0, 0.086),
    0 100px 80px rgba(0, 0, 0, 0.12) !important;
}

.matrix_tbl {
  margin-top: 29px;
  border-collapse: collapse;
  height: 290px;
  width: 550px;
}

.matrix_tbl td {
  border: 1px solid gray;
  border-collapse: collapse;
  font-size: 12px;
  padding: 5px;
  height: 39px;

}

.matrix_tbl_bottom {
  border-collapse: collapse;
  width: 1302px;
  margin-left: 28px;
}

.matrix_tbl_bottom td {
  border: 1px solid gray;
  border-collapse: collapse;
  font-size: 12px;
  padding: 4px;

}

// .dark_box {
//   height: 30px;
//   width: 30px;
//   border-radius: 5px;
//   background-color: rgb(42, 43, 46);
//   padding-left: 10px;
//   padding-top: 10px;
//   margin-right: 10px;
// }



.custom_taps .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label {
  padding: 0 6px;

  min-width: 40px;
  opacity: 1;
  min-height: 80px;
}

.custom_taps .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-ink-bar {
  background: rgb(240, 80, 0);
  // height: 10px;
}

.custom_taps .mat-tab-header {
  border-bottom: none;
}

.custom_taps {
  margin-top: -10px;
}

.parking-icon-box {
  background-color: rgb(42, 43, 46);
  border-radius: 4px;
  height: 40px;
  width: 40px;
  padding-top: 3px;
  padding-left: 3px;

}
.mat-menu-content:not(:empty) {
  padding-top: 0px !important;
  padding-bottom: 8px;
}


.data_view_container {
  margin: 10px;
  background-color: white;
  border: 1px solid gray;
  border-radius: 8px;
}

.sub_con_text {
  padding-top: 10px;
  padding-left: 10px;
  padding-bottom: 10px;
}




///map
/// 


.circle {
  stroke: #fff;
  fill: #fff;
  fill-opacity: 0.2;
}

.sphere {
  fill: #243772; //243772 //485D94
}

.country {
  fill: #B19F90;
  stroke: black;
  stroke-opacity: 0.1;
}

.map-label {
  font-weight: 600 !important;
  font-family: 'Roboto-Medium' !important;
  // font-size: 16px !important;
  fill: #ffffff
}


//table

@import '~@angular/material/theming';

$dark-text: #000;
$dark-primary-text: #000;
$dark-accent-text: #000;


.mat-icon-button {
  margin: 2px !important;
  margin-left: -12px !important;
}

table {
  width: 100%;
}

th.mat-sort-header-sorted {
  color: black;
}

.mat-dialog-container {
  background: #F2F2F2;
  color: #000;
  max-height: 60vh !important;
}

.model_unit {
  position: absolute;
  left: auto;
  z-index: 1000;
  margin-left: 90px;

}

.model_unit_two {
  position: absolute;
  left: auto;
  z-index: 1000;
  margin-left: 390px;

}


.search-input .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  // background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 5px !important;
  height: 39px !important;
}

.config-search .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  // background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 5px !important;
  height: 39px !important;
}

.location-input {
  width: 300px !important;
  height: 38px !important;
}

.location-input.mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  // background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 4px !important;
}

.search-input-assets {
  width: 341px !important;
  // height: 38px !important;
  display: block;
  margin-top: 6px;
}

.search-input-assets.mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  // background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 4px !important;
}

.config-search-input .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  // background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 5px !important;
}

.config-search-input {
  width: 258px !important;
  font-size: 9.249px !important;
}

.my-title {
  font-family: 'Roboto-Medium' !important;
  font-size: 18px !important;
}

.poly_label {
  font-family: 'Roboto-Medium' !important;
  font-size: 18px !important;
  color: black !important;
  text-align: center !important;
  background: white !important;
  border-radius: 5px !important;
  height: auto !important;
}

.menu_class {
  position: absolute;
  bottom: 20px;
  z-index: 1000;
  background-color: #141414;
  border-radius: 5px;
  width: 100px;
  text-align: center;
  cursor: pointer;
}

.fill-svg {
  fill: #1A2254;
}


.sop-box1 {
  width: 100px;
  height: 100px;
  padding: 3px 3px;
  color: #f9f9f9 !important;
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: #f9f9f9 !important;
  box-shadow: 1px 3px 10px 1px #1A2254;
  border-radius: 10px;
}

.horiz-list {
  max-width: 100%;
  min-height: 60px;
  display: flex;
  flex-direction: row;
  background: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;
}


/* //cdk drag and drop */
.example-list {
  list-style-type: none;
  padding: 0;
}

.example-list li {
  display: table-cell;
  padding: 4px;
}

.example-container {
  display: flex;
  flex-wrap: wrap;
}

// .example-box {
//   width: 290px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   text-align: center;
//   border-radius: 4px;
//   position: relative;
//   z-index: 1;
// }
.padding-right_10 {
  padding-right: 10px;
}

.padding-right_5 {
  padding-right: 5px;
}

.cursor {
  cursor: pointer;
}

.uploadLink {

  color: #005EA2;
  text-decoration: underline;
}

.cdk-drop-list {
  padding: 5px;
  // display: flex;
  // padding-right: 10px;
  // padding-bottom: 10px;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
}

.cdk-drag-placeholder {
  opacity: .3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.widget-dropdown {
  width: 230px;
  padding: 20px 10px;
  background: #f9f9f9;
  box-shadow: -5px -5px 15px #f9f9f9, 5px 5px 15px #f9f9f9;
  /*box-shadow: 0 0 10px rgba(250, 251, 255, 0.6);*/
  border-radius: 20px;
  position: absolute;
  margin-bottom: 30px;
  z-index: 999999;
  right: 15px;
  top: 160px;
  height: 185px;
  overflow-y: auto;
}

.dashboard-box ul {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0px;
  padding: 0;
  list-style: none;
}

.dashboard-box ul li {
  margin-right: 15px;
  list-style: none;
}

.mat-body {
  color: black;
}

.prop-container {
  background: #f9f9f9 !important;
  padding: 0px;
  margin: 0px;
  position: fixed;
  right: 0px;
  top: 100px;
  width: 0px;
  z-index: 1100;
  transition: width 1s;
  border-radius: 10px;
  border: solid 2px orange;
}


.mat-select-arrow-wrapper {
  height: 0px;
}



.close_box {
  height: 35px;
  width: 35px;
  padding-top: 5px;
  padding-left: 5px;
  border-radius: 4px;
}


.setBackGround {
  background-color: #f1f1f1;
}



//new css
.cognite-hover .mat-dialog-container {
  background: #232323da;
  color: white;
  border: 1px solid white;
}

.we-pm-icon {
  cursor: pointer;
}


.layer-input .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 5px !important;

}

.layer-input .mat-form-field-wrapper {
  width: 240px;
}


.dark_box {
  height: 30px;
  width: 30px;
  border-radius: 5px;
  background-color: rgb(42, 43, 46);
  padding-left: 6px;
  padding-top: 4px;
  margin-right: 10px;
}

.label_title {
  font-weight: bold;
}

.multiselect-dropdown {
  font-size: 14px !important;
}

.cognitechild {
  cursor: progress;
}


.expandright {
  left: auto;
  right: 49px;
  /* Button width-1px */
}

// .expandright:hover {
// 	padding: 0 0 0 0px;
// }


.menu-svg-icon {
  // fill: #1A2254;
  fill: #fff;
}

.button-svg-icon {
  fill: #fff;
}

.button-stroke-svg-icon {
  stroke: $palette-color2
}

.button-stroke-svg-icon-3d {
  fill: none;
  stroke: #fff;
  stroke-miterlimit: 10;
  stroke-width: 1.95px;
}

.svg-icon {
  fill: #1A2254;
}

.mat-flat-button.mat-accent,
.mat-raised-button.mat-accent,
.mat-fab.mat-accent,
.mat-mini-fab.mat-accent {
  background-color: #1A2254;
}

.globe_class {
  background-color: #141414;
  border-radius: 5px;
  padding: 10px;
  cursor: pointer;
}

// mat-checkbox {
//   color: #1A2254;
//    .mat-checkbox-background {
//       background-color: #1A2254;
//   }

//   &.mat-checkbox-focused{
//       .mat-ink-ripple{
//           background-color: #93C854;
//       }
//   }
// }

.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
.mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: #1A2254;
}

//add

.list-3dhead {
  font-weight: 600;
  font-size: 20px;
  font-family: 'Roboto-Medium';
}

.list3dboxcard {
  //background-color: #1A2254;
  border: 1px solid #1A2254;
  //  margin-left: 15px;
  //  margin-right: 15px;

  height: 260px;
  width: 260px;
  cursor: pointer;
  // box-shadow: 2px 2px 12px 2px #b990ce;


}

.cardText {
  font-size: 16px;
  font-family: 'Roboto-Medium';
  color: rgb(0, 0, 0);


}


.multiselect-item-checkbox input[type=checkbox]:checked+div:before {
  background: #1A2254 !important;
}

.multiselect-item-checkbox input[type=checkbox]+div:before {
  color: #1A2254 !important;
  border: 2px solid #1A2254 !important;
}

.multiselect-dropdown .dropdown-btn .selected-item-container .selected-item {
  background: #1A2254 !important;
  border: 1px solid #1A2254 !important;
}

.dropdown-btn {
  width: 90% !important;
  ;
}

.disabled {
  pointer-events: none;
  display: none;
}

.mat-form-field-label-wrapper {
  top: -15px !important;
}



.mat-accent .mat-slider-track-fill {
  background-color: #1A2254 !important;
}

.mat-slider-thumb-label {
  background-color: #1A2254 !important;

}

.mat-slider-thumb {
  background-color: #1A2254 !important;
}

.likehood_div {
  display: flex;
  margin: 5px;
}

.likehood-card-head {
  height: 50px;
  min-width: 125px;
  max-width: 125px;
  margin: 3px;
  padding: 5px !important;
  font-size: 12px;
  text-align: left;
}

.likehood-card-desc {
  height: 50px;
  min-width: 350px;
  max-width: 350px;
  margin: 3px;
  padding: 5px !important;
  font-size: 12px;
  text-align: left;
}

.two-line {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.four-line {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.div-center {
  position: absolute;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.con-card-head {
  height: 85px;
  min-width: 425px;
  max-width: 425px;
  margin: 3px;
  padding: 5px !important;
  font-size: 12px;
}

.con-card-desc {
  height: 85px;
  min-width: 130px;
  max-width: 130px;
  margin-top: 5px;
  margin-left: 5px;
  margin-right: 5px;
  padding: 5px !important;
  font-size: 12px;
  text-align: left;
}

.con-card-desc1 {
  height: 35px;
  min-width: 130px;
  max-width: 130px;
  margin-top: 5px;
  margin-left: 5px;
  margin-right: 5px;
  padding: 5px !important;
  font-size: 12px;
}
.mat-slide-toggle.mat-checked .mat-slide-toggle-bar{
  background-color: #839EA6 !important;
}
.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: #083D5B !important;
}

// .mat-ink-bar {
//   background-color: #fcad14 !important;
// }

.c-radius {
  border-radius: 8px !important;
}

.mat-tab-label {
  opacity: 1 !important;
}

.table1 {
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
}

.risk-class {
  color: #000;
}

.risk-class-red {
  color: #fff;
}

.mat-input-element:disabled {
  color: black !important;
}

.mat-radio-button.mat-radio-disabled .mat-radio-label-content {
  color: black !important;
}

.toast-top-right {
  margin-top: 60px !important;
}

.grid_highlight {
  stroke: #1A2254;
  stroke-dasharray: 10, 2;
  stroke-linecap: butt;
  stroke-width: 2
}

.my-menu {
  // font-family: 'Myriad-SemiBold' !important;
  font-size: 14.22px !important;
  color: #fff;
  margin: auto;
}

.menuSelected {
  background-color: #1A2254 !important;
}

.mat-menu-panel {
  max-width: 100% !important;
}

.menu-expand {
  background: #1A2254;
  cursor: pointer;
  height: auto !important;
  position: absolute;
  top: auto;
  bottom: 0;
  margin-left: 26px;
}

.menu-collapse {
  background: #1A2254;
  cursor: pointer;
  height: auto !important;
  float: right;
  top: auto;
  bottom: 0;
  position: absolute;
  left: 190px;
  margin-left: -25px;
}

.as-split-gutter {
  display: none !important;
}

.split-border {
  border-right: #1A2254 3px solid;
}

.scroll {
  margin-bottom: 50px;
}

.matrix-content {
  // color: #fff;
}



//new css
.cognite-hover .mat-dialog-container {
  background: #232323da;
  color: white;
  border: 1px solid white;
  padding: 11px !important;
  // height: 40px;
  overflow: hidden;
}

.cdk-global-overlay-wrapper {

  overflow: hidden !important;

}

.cdk-overlay-container {
  overflow: hidden !important;
  z-index: 99999;
}

.mat-icon-button {
  color: #1A2254 !important;
}

.chart-text {
  letter-spacing: 1px;
  font-weight: bold;
}

.bar {
  fill: #1A2254
}

.mat-list-base .mat-list-item .mat-list-item-content-reverse,
.mat-list-base .mat-list-option .mat-list-item-content-reverse {
  padding: 0px !important;
}



.threadAssetsIcon {
  background-color: #1A2254;
  border-radius: 5px;
  width: 35px;
  height: 35px;
  margin: 2px;
  margin-top: 5px;
  padding-left: 7px;
  padding-top: 7px;
  cursor: pointer;
}

.cognite_menu {
  position: absolute;
  z-index: 1000;
  right: 0;
  top: 25%;
}

.cognite-menu_btn {
  background-color: white;
  color: black;
  width: 40px !important;
  min-width: 10px !important;
  padding: 0px !important;
}

.tree-chart {
  height: auto;
  width: 100% !important;
  background-color: #d7d7d7
}

.text-button {
  border-radius: 5px;
  background-color: #1A2254;
  color: #fff;
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.mat-form-field-wrapper {
  padding-bottom: 0px !important;
}

.menu-small {
  width: calc(100% - 80px) !important;
}

.menu-full {
  width: calc(100% - 190px) !important;
}

.risk_matrix_scroll {
  overflow-x: auto;
  // width:100vw;
}

.overview-head {
  min-width: 145px;
  border-right: 1px solid black;
}

.overview-value {
  text-align: center;
  width: 20%;
  padding: 10px;
}

.overview-total {
  text-align: center;
  width: 100px;
  border-right: 1px solid black;
  padding: 10px;
}

.cdk-overlay-pane {
  width: auto !important;
}

.logout-pop {
  width: 300px;
  margin-left: 10px;
  margin-bottom: 15px;
}


.dashboard-grid {
  width: 257px !important;
  height: 123px !important;
  display: inline-grid !important;
  margin: 19px !important;
  background: transparent linear-gradient(180deg, #1A2254 0%, #050711 100%) 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 0.20000000298023224px solid #FFFDFC !important;
  border-radius: 10px !important;
  opacity: 1 !important;
  padding: 10px !important;
}

.search-input {
  width: 532px !important;

  font-size: 9.249px !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 6px 0 10px 0 !important;
}

.mat-form-field-prefix {
  top: 0px !important;
  margin-left: 5px !important;
  margin-right: 10px !important;
}

.input-field {
  width: 174px !important;
  font-size: 9.249px !important;
}


.observe-color {
  color: $palette-color12;
}


.observation_box {
  height: 182px;
  width: 184px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #0E2340;
  border-radius: 20px;
  opacity: 1;
  margin: 20px;
  color: black;
  cursor: pointer;

}

.observation_box_highlighted {
  width: 184px;
  height: 182px;
  background: transparent linear-gradient(180deg, #1A2254 0%, #050711 100%) 0% 0% no-repeat padding-box;
  border: 1px solid #0E2340;
  border-radius: 20px;
  opacity: 1;
  color: white;
  margin: 20px;
  cursor: pointer;
}


.arrow-box {
  width: 349px;
  height: 104px;
  background: #083d5b 0% 0% no-repeat padding-box;
  border: 1px solid #E4E4E5;
  border-radius: 4px;
  opacity: 1;
  color: #fff;
  padding: 20px;
  position: relative;
  z-index: 1000;
  box-sizing: border-box;


}

.observe-box {
  width: 349px;
  height: 104px;
  background: #F3F4F5 0% 0% no-repeat padding-box;
  border: 1px solid #083d5b;
  opacity: 1;
  border-radius: 4px;
  padding: 20px;
  position: relative;
  margin-left: -3px;
  box-sizing: border-box;

}

.arrow-box-leftside {
  width: 349px;
  height: 104px;
  background: #083d5b 0% 0% no-repeat padding-box;
  border: 1px solid #E4E4E5;
  border-radius: 4px;
  opacity: 1;
  color: #fff;
  padding: 20px;
  position: relative;
  box-sizing: border-box;
}

.arrow-box.arrow-right:after {
  content: " ";
  position: absolute;
  right: -13px;
  top: 40px;
  border-top: 15px solid transparent;
  border-right: none;
  border-left: 15px solid #083d5b;
  border-bottom: 15px solid transparent;
  // z-index: 1000;
}


.arrow-box-leftside.arrow-left:before {
  content: " ";
  position: absolute;
  left: 0px;
  top: 40px;
  border-top: 15px solid transparent;
  border-right: none;
  border-left: 15px solid white;
  border-bottom: 15px solid transparent;
  // z-index: 100;
}


.route-container {
  padding: 65px 44px 20px 92px;
  transition: all .5s;

  app-header {
    position: fixed;
    top: 0;
    right: 0;
    width: calc(100% - 48px);
    transition: all .5s;
    z-index: 99999;
  }

  &.route-container-collaspse {
    padding: 85px 20px 20px 100px;
    transition: all .5s;

    app-header {
      position: fixed;
      top: 0;
      right: 0;
      width: calc(100% - 80px);
      transition: all .5s;
    }
  }

  app-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    height: 100vh;
    z-index: 99999;
  }
}


.bt-icon-section1 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 100%;

  .mat-icon-button {
    width: auto;
    height: auto;
    margin: 0 !important;
    border-radius: 0;
    line-height: inherit !important;
    cursor: pointer;
    .mat-button-wrapper {
      display: flex;
    }
  }

  &:hover {
    background: #083D5B;
    border-bottom: 3px solid #F15C05;
    box-sizing: border-box;

    span.location-text {
      color: #fff;
      font-family: Roboto;
      font-size: 12px;
    }

    .mat-icon-button {
      .mat-button-wrapper {
        svg {

          #search,
          #dark_mode_FILL0_wght400_GRAD0_opsz48,
          #notifications_FILL0_wght400_GRAD0_opsz48,
          #public_FILL0_wght400_GRAD0_opsz48,
          #question_mark_FILL0_wght400_GRAD0_opsz48,
          #location_on_FILL0_wght400_GRAD0_opsz48 {
            fill: #fff;
          }
        }
      }
    }
  }
}

.bt-icon-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 100%;

  .mat-icon-button {
    width: auto;
    height: auto;
    margin: 0 !important;
    border-radius: 0;
    line-height: inherit !important;
    cursor: pointer;
    .mat-button-wrapper {
      display: flex;
    }
  }

  &:hover {
    background: #083D5B;
    border-bottom: 3px solid #F15C05;
    box-sizing: border-box;

    span.location-text {
      color: #fff;
    }

    .mat-icon-button {
      .mat-button-wrapper {
        svg {

          #search,
          #dark_mode_FILL0_wght400_GRAD0_opsz48,
          #notifications_FILL0_wght400_GRAD0_opsz48,
          #question_mark_FILL0_wght400_GRAD0_opsz48,
          #location_on_FILL0_wght400_GRAD0_opsz48 {
            fill: #fff;
          }
        }
      }
    }
  }
}


.profile-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;

  .language-section {
    position: relative;
    display: flex;
    height: 100%;
    align-items: center;

    .mat-icon-button {
      width: auto !important;
      height: auto !important;
      min-width: inherit !important;
      line-height: inherit;
      border-radius: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 !important;

      span.mat-button-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;

        .lan-ico {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 2px;
        }

        .lan-text {
          line-height: 13px;
          font-size: 12px;
          color: #4E7389;
          font-weight: 600;
        }
      }
    }

    .mat-form-field {
      visibility: hidden;
      position: absolute;
      top: 25px;
      width: 100px;
    }
  }
}

// .moon-ico-section{
//   margin-right: 27.48px;
// }

.notification-section {
  margin-right: 1.26px;
}

.help-section {
  margin-right: 1.07px;
}

.search-section {
  margin-right: 16.21px;
}
.location-text {
  cursor: pointer;
  font-size: 14px;
  color: #4E7389;
  margin-right: 8px;
  line-height: 13px;
  font-family: Roboto !important;
    font-size: 12px !important;
}

.loaction.bt-icon-section {
  width: auto;
  padding: 0 8px;
  margin: 0 16px 0 0px;
  cursor: pointer;

  .location-text {
    cursor: pointer;
    font-size: 14px;
    color: #4E7389;
    margin-right: 8px;
    line-height: 13px;
    font-family: Roboto !important;
      font-size: 14px !important;
  }

  .mat-icon-button {
    .mat-button-wrapper {
      margin-top: -3px;
    }
  }
}


.cancel_btn {
  margin: 10px !important;
  width: 114px !important;

  border: 1px solid #0E2340 !important;
  border-radius: 4px !important;
  opacity: 1 !important;
  color: #0E2340 !important;
}

.leftMargin {
  margin-left: 20px;
}

.leftMargin_10 {
  margin-left: 10px;
}

.start-color {
  color: #DF0A0A;
}

.marginTop {
  margin-top: 10px;
}

.marginBottom {
  margin-bottom: 10px;
}

.marginTop-5 {
  margin-top: 5px;
}

.marginTop-20 {
  margin-top: 20px;

}

.marginLeft-5 {
  margin-left: 5px !important;
}

.marginLeft-20 {
  margin-left: 20px;
}

.marginRight-5 {
  margin-right: 5px !important;
}

.marginRight-108 {
  margin-right: 108px;
}

.category_box {
  min-width: 141px;
  width: auto;
  height: 33px;
  background: #073148 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #073148;
  border-radius: 17px;
  opacity: 1;
  color: white;
  cursor: pointer;
  margin: 10px;
  padding-left: 15px;
  padding-right: 15px;
}

.category_box_light {
  min-width: 141px;
  width: auto;
  height: 33px;
  border: 1px solid #073148;
  border-radius: 17px;
  opacity: 1;
  color: black;
  cursor: pointer;
  margin: 10px;
  padding-left: 15px;
  padding-right: 15px;
}

.icon-bg-box {
  width: 25px;
  height: 25px;

  background: #E5E5E5 0% 0% no-repeat padding-box;
  border: 0.5px solid #E6E2E2;
  border-radius: 3px;
  opacity: 1;
  cursor: pointer;

}

.mic-icon-box {
  width: 37px;
  height: 37px;

  /* UI Properties */

  background: #E5E5E5 0% 0% no-repeat padding-box;
  border: 0.5px solid #E6E2E2;
  border-radius: 5px;
  opacity: 1;
}

.add_Icon {
  font-size: 16px;
  margin-top: 7px;
  color: #1A2254;
}

.add-sub-category-popup .mat-dialog-container {
  background: #fff 0% 0% no-repeat padding-box !important;
  border-radius: 10px !important;
}

.add-craft-popup .mat-dialog-container {
  background: #fff 0% 0% no-repeat padding-box !important;
  border-radius: 10px !important;
}


.add_sub_drop {
  width: 341px !important;

  border-radius: 4px !important;
}

.add_sub_drop .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-outline {
  background-color: #F5F5F5 !important;
  overflow: hidden !important;
  border-radius: 5px !important;

}

.sub-category-btn {
  margin: 20px !important;
  width: 114px !important;

}

.sub-category-btn-bg {
  background-color: #00B7DA !important;
}




//material css
.mat-option {
  font-size: $body-1-size !important;
}

//
.icons3d {
  font-size: 25px;
  margin-left: 10px;
  margin-top: 10px;
}

.uploadPhotoBox {
  width: 349px;
  height: 238px;
  margin: 5px;
  /* UI Properties */

  background: transparent radial-gradient(closest-side at 50% 50%, #F9FAFB 0%, #F2F3F4 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 2px #00000029;
  border: 1px solid #E9EAEB;
  border-radius: 4px;
  opacity: 1;
}

.uploadPhotoBoxField {
  width: 349px;
  height: 473px;

  background: transparent radial-gradient(closest-side at 50% 50%, #F9FAFB 0%, #F2F3F4 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 2px #00000029;
  border: 1px solid #E9EAEB;
  border-radius: 4px;
  opacity: 1;
}

.configuration-table thead {
  background-color: $palette-color2 !important;


}

.configuration-table thead th {

  color: $palette-color1 !important;
  font-size: $subheading-1-size !important;
  min-width: 130px;

}

.configuration-table thead td {

  color: #060606 !important;
  font-size: $caption-size !important;

}

.schedule-table thead {
  background-color: $palette-color2 !important;
}

.schedule-table thead th {

  color: $palette-color1 !important;
  font-size: $subheading-1-size !important;
  // min-width: 80px;
}

.schedule-table thead td {

  color: #060606 !important;
  font-size: $caption-size !important;
  padding: 5px !important;

}

.audit-table thead {
  background-color: $palette-color2 !important;
}

.audit-table thead th {

  color: $palette-color1 !important;
  font-size: $subheading-1-size !important;
  // min-width: 60px;
}

.audit-table thead td {

  color: #060606 !important;
  font-size: $caption-size !important;
  padding: 5px !important;

}

.padding-left1 {
  // padding-left: 1% !important; 
  //align-items: end !important;
  //    display: flex !important;
  //  // padding: 21px 0 !important;
  //   justify-content: flex-end !important;

}

.config-table-icon {
  margin: 6px;
  cursor: pointer;
}

.add-question-area {
  width: 100% !important;
  min-width: 300px !important;
  height: 90px !important;
}
.listHeadBox-behaviour{
  min-width: 50vh; /* Fixed width */
  overflow-x: auto !important;
}

.listHeadBox {
  width: 99%;
  height: 54px;

  /* UI Properties */

  background: #083D5B 0% 0% no-repeat padding-box;
  opacity: 1;
  color: white;
}

.blueLightBox {
  width: 99%;
  min-height: 37px;
  height: auto;

  /* UI Properties */

  background: #F0F0F0 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.listHeadBoxSummary {
  width: 99%;
  height: 45px;
  background: #1A2254 0% 0% no-repeat padding-box;
  opacity: 1;
  color: white;
}

.background-none {
  background: none !important;
}
.blueLightBoxSummary {
  width: 99%;
  height: 37px;
  height: auto;
  background: #C5C7D5 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.BoxSummaryRose {
  width: 99%;
  height: 37px;
  height: auto;
  background: #F7617D 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.BoxSummaryYellow {
  width: 99%;
  height: 37px;
  height: auto;
  background: #FECD54 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.BoxSummaryGreen {
  width: 99%;
  height: 37px;
  height: auto;
  background: #2CD889 0% 0% no-repeat padding-box;
  opacity: 0.98;
}
.BoxSummaryRoseHeading {
  width: 99%;
  height: 45px;
  height: auto;
  background: #F7617D 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.BoxSummaryYellowHeading {
  width: 99%;
  height: 45px;
  height: auto;
  background: #FECD54 0% 0% no-repeat padding-box;
  opacity: 0.98;
}

.BoxSummaryGreenHeading {
  width: 99%;
  height: 45px;
  height: auto;
  background: #2CD889 0% 0% no-repeat padding-box;
  opacity: 0.98;
}


.paddingLeft-10 {
  padding-left: 10px;
}
.paddingRight-10 {
  padding-right: 10px;
}

.leftBorder {
  border-left: 2px solid white;
  height: 100% !important;
}

.behaviour-list-tree {
  min-height: 37px;
  height: auto;
  width: 99%;
  background: #F0F0F0 0% 0% no-repeat padding-box;
  padding-left: 10px;

}

.timePickerInput {
  width: 75px;
  height: 39px;
  overflow: hidden;

  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #DEDEDF;
  border-radius: 4px;
  opacity: 1;
  padding-left: 4px;
}

.timepicker {

  .timepicker__header {
    background-color: $palette-color2 !important;
  }

  .clock-face__clock-hand {
    background-color: $palette-color2 !important;

    &::after {
      background-color: $palette-color2 !important;
    }

    &::before {
      border-color: $palette-color2 !important;
    }
  }

  .clock-face__number {
    >span.active {
      background-color: $palette-color2 !important;
    }
  }

  button:not(.timepicker-dial__item) {
    color: $palette-color2 !important;
  }
}

.bahaviour-tabs {
  width: 99% !important;

}

.bahaviour-tabs .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels {
  width: 20% !important;
}

.bahaviour-tabs .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label {

  font-size: 16px;
  font-family: "Roboto-regular";
  margin-left: 0px;
  margin-right: 32px;

}

.bahaviour-tabs .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label-active {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Roboto-Medium';
  margin-left: 0px;
  margin-right: 32px;

}

.add-circle-icon {
  font-size: 18px;
  margin-top: 8px;
  margin-left: -12px;
}

.alert-body {
  background: #AEB1C2 0% 0% no-repeat padding-box;
  border-radius: 4px;
  padding: 13px;
  width: 100%;
  height: auto;
  opacity: 0.96;
}

.alert-row {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  //box-shadow: 0px 8px 6px #EBEBEB;
  border: 0.5px solid #AEB1C2;
  border-radius: 0px 6px 0px 0px;
  opacity: 1;
  min-height: 77px;
  height: auto;
  margin-top: 20px;
}

.success {
  color: #20670E
}

.warning {
  color: #F7617D;
}

.workflow-box {
  width: 257px;
  height: 123px;

  /* UI Properties */

  background: transparent linear-gradient(180deg, #1A2254 0%, #050711 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 0.20000000298023224px solid #FFFDFC;
  border-radius: 10px;
  opacity: 1;
  color: white;
  padding: 14px;
  margin-bottom: 20px;

}

.workflow-card {
  min-height: 141px !important;
  width: 100%;
  background: #F5F5F5 0% 0% no-repeat padding-box !important;
  //background-color: #F5F5F5;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 1px solid #FDFDFD !important;
  border-radius: 14px !important;
  opacity: 1 !important;

}

.workflow-sub-card {
  width: 176px;
  height: 101px;
  cursor: pointer;
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 0.20000000298023224px solid #FFFDFC !important;
  border-radius: 10px !important;
  opacity: 1 !important;
}

.workflow-sub-body-card {
  width: 100%;
  min-height: 349px;
  height: auto;
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029 !important;
  border: 0.20000000298023224px solid #FFFDFC !important;
  border-radius: 10px !important;
  opacity: 1 !important;
  padding: 20px;
}



.schedule-form-width {
  width: 343px;
}

.schedule-form-sub-width {
  width: 163px;
}
.mat-calendar-body-cell:hover {
  .mat-calendar-body-cell-content {
    color: white !important;
  }
}
.mat-calendar-body-selected .mat-calendar-body-today {
  color: white !important;
}
.mat-calendar-body-selected{
  color: white !important;
}

.mat-calendar-body-cell-container {
  .mat-calendar-body-in-range{
     &::before{
        background-color: #F1F4F6;
     }
    .mat-calendar-body-cell-content{
        color: rgba(0, 0, 0, 0.87);
    }
  }
  
  .mat-calendar-body-cell{
    &:hover{
     .mat-calendar-body-cell-content{
       background-color: #1A2254 !important;
       color: #fff !important;
     }
    }
    // .mat-calendar-body-cell-content{
    //  &.mat-calendar-body-today{
    //    background-color: #1A2254 !important;
    //    color: #fff !important;
    //   }
    // }
   
  }

  .mat-calendar-body-active{
     .mat-calendar-body-cell-content{
       //  background-color:  transparent !important;
        &.mat-calendar-body-selected.mat-calendar-body-today{
           background-color: #1A2254 !important;
           color: #fff !important;
        }
        &.mat-calendar-body-today{
         background-color: #1A2254 !important;
         color: #fff !important;
        }
     }
     
  }
}
.circle-icon {
  font-size: 19px;
  margin-top: 5px;
}

.circle-blue {
  color: #00055D;
}

.circle-red {
  color: #DC1F26;
}

.circle-light {
  color: #BEF1F9;
}

.circleTableRed {
  height: 25px !important;
  width: 30px !important;
  background-color: #DC1F26;
  color: white !important;
  border-radius: 50%;

}

.circleTableBlue {
  height: 25px !important;
  width: 30px !important;
  background-color: #00055D;
  color: white !important;
  border-radius: 50%;

}

.circleTableLight {
  height: 25px !important;
  width: 30px !important;
  background-color: #BEF1F9;
  color: white !important;
  border-radius: 50%;

}


.field-walk-popup .mat-dialog-container {
  padding: 0px !important;
  background: #BEF1F9 0% 0% no-repeat padding-box;
}

.field-walk-row {

  height: 36px;
  background: #1A2254 0% 0% no-repeat padding-box;
  border: 1px solid #707070;
  opacity: 1;
  padding: 8px;

}

.field-walk-column {
  width: 360px;
  padding: 30px;
  height: 100%;

}

.field-walk-box1 {
  min-width: 114px;
  width: auto;
  height: 35px;
  background: #00B7DA 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #01112F;
  border-radius: 17px;
  opacity: 1;
  cursor: pointer;
  padding-left: 10px;
  padding-right: 10px;

  margin-bottom: 15px;
}

.field-walk-box2 {
  min-width: 114px;
  width: auto;
  height: 35px;
  background: #1A2254 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #01112F;
  border-radius: 17px;
  color: white;
  margin-bottom: 15px;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
}

.uploadFileBox {
  border: 0.10000000149011612px solid #e9ecef;
  border-radius: 6px;
  opacity: 1;
  width: 100%;
  height: 91px;

}

.minWidth {
  min-width: 150px;
}

.scoreCardBox {
  width: 218px;
  height: 128px;
  background: transparent linear-gradient(180deg, #01112F 0%, #556074 100%) 0% 0% no-repeat padding-box;
  //background-color: #01112F; 
  box-shadow: 0px 3px 6px #272D3B33;
  border-radius: 20px;
  opacity: 1;
  color: white;
  padding: 16px;
}

.dashboardBox {
  width: 218px;
  height: 128px;
  background: transparent linear-gradient(180deg, #01112F 0%, #556074 100%) 0% 0% no-repeat padding-box;
  //background-color: #01112F;
  box-shadow: 0px 3px 6px #272D3B33;
  border-radius: 20px;

  color: white;
  padding: 18px;
}

.chart-Card-main {
  width: 100%;
  padding: 20px;
  box-shadow: 0px 6px 20px #00000045 !important;
  border: 1px solid #FFFFFF !important;
  border-radius: 12px !important;

}

.chart-Card {
  width: 100%;
  padding: 20px;
  box-shadow: 0px 6px 20px #00000045 !important;
  border: 1px solid #FFFFFF !important;
  border-radius: 12px !important;
  opacity: 1;
}

.dateRangePick .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix {
  display: flex !important;
  flex-direction: row !important;
}

.dateRangePick .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix .mat-datepicker-toggle {
  margin-top: -16px !important;
  margin-right: -16px !important;

}



mat-select-filter form {

  padding-left: 15px !important;
  padding-top: 15px !important;
  padding-bottom: 4px !important;
  padding-right: 0px !important;
}

.head-font {
  font-weight: 500 !important;
  font-family: 'Roboto-bold' !important;
  font-size: 20px !important;
  text-transform: uppercase;
  color: #083D5B !important;
  line-height: inherit !important;
}

//header profile section css

.user-profile-section {
  .user-profile {
    width: 32px;
    height: 32px;
    font-size: 18px;
    color: #fff;
    line-height: 24px;
    background: #083D5B;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .user-profile-data {
    display: flex;
    align-items: start;
    justify-content: center;
    flex-flow: column;
    margin: 0px 0 0 12px;

    .name {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.8705882353);
      line-height: 14px;
      font-weight: bold;
      margin: 0 0 10px 0;
    }

    .user-details {
      font-size: 12.64px;
      color: #000000CC;
      line-height: 9px;
    }
  }
}

//====


//sidebar css

.logo {
  display: flex;
  align-items: center;
  justify-content: start;
  padding: 10px;

  span {
    display: flex;
    width: 48px;
    align-items: center;
    justify-content: start;
  }
}

.expand-collapse-section {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
  height: 48px;
  background: #20506B;

  span {
    display: flex;
    cursor: pointer;
    width: 48px;
    align-items: center;
    justify-content: center;

    svg {
      transform: rotate(0deg);
      transition: all .5s;
    }
  }

  &.expand-collapse-section-rotate {
    span {
      svg {
        transform: rotate(180deg);
        transition: all .5s;
      }
    }
  }
}

.sidebar-section {
  width: 48px;
  transition: all .5s;
  background: #083D5B;
  color: white;
  
  .sidebar-menu-section {
    height: calc(100vh - 96px);
    overflow: auto;

    .mat-selection-list {
      padding: 0 !important;
    }

    .mat-list-option {
      .mat-list-text {
        display: flex;
        padding: 0 !important;
        flex-flow: wrap;
        height: 100%;
        align-items: center;

        span.menu-wrap {
          justify-content: center;
          width: 48px !important;

          .menu-svg {
            display: flex;
            align-items: center;
            margin: 0;
            max-width: 100% !important;
            justify-content: center;
          }
        }

      }

      &:hover {
        background-color: #073148 !important;

        &::after {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 3px;
          content: "";
          background: #F15C05;
        }
      }

    }
  }

  &.expand-sidebar {
    width: 294px;
    transition: all .5s;

    .mat-list-text {
      padding-right: 0 !important;
    }

    .sidebar-menu-section {
      .mat-list-option {
        .menu-svg {
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          max-width: 100% !important;
        }
      }
    }
  }
}

.my-menu {
  font-size: 14px !important;
  color: #fff;
  margin: auto;
  width: calc(100% - 48px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'Roboto-regular';
}

.menuSelected {
  background-color: #073148 !important;
  position: relative;

  &::after {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    content: "";
    background: #F15C05;
  }
}

.menu-tooltip {
  margin: 2px 0 0 6px !important;
  padding: 7px 10px !important;
  font-size: 12px;
  background: #424242;
  font-family: "Roboto-light";
  position: relative;
  overflow: visible !important;
}

.menu-tooltip::before {
  position: absolute;
  content: "";
  border-width: 6px;
  border-style: solid;
  border-color: transparent #424242 transparent transparent;
  left: -11px;
  top: 48%;
  transform: translateY(-50%);
}

//=====

// menu overla css

.sideBar-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2117647059);
  top: 0;
  left: 0;
  z-index: 999;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

//===

.cst-sub-header-title {
  letter-spacing: 0.13px;
  color: #0E2340;
  text-transform: uppercase;
  line-height: 24px;
}

.site-unit-search-control-label {
  font: normal normal normal 16px/20px Roboto-regular !important;
  margin: 0 10px 0 5px;
  color: #000;
}

//new
.grid-stl {
  width: 317px !important;
  height: 120px !important;
  flex-flow: wrap;
  display: flex !important;
  margin: 9px 18px 9px 0 !important;
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137) !important;
  border: 1px solid #E7E7E7;
  border-radius: 6px !important;
  box-sizing: border-box !important;
  cursor: pointer;
  position: relative;
  padding: 10px 25px 10px 30px !important;

  &:hover {
    background: #0D213D !important;

    &::after {
      background: #F15C05 !important;
    }

    .grid-stl-heading {
      .subheading-2 {
        color: #fff;
      }
      .subtitle {
        color: #fff;
      }
    }
  }

  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 7px;
    background: #F5F5F5 0% 0% no-repeat padding-box;
    border-radius: 5px 0px 0px 5px;
  }

  .grid-stl-ico {
    margin-right: 20px;

    i {
      line-height: 10px;
    }
  }

  .grid-stl-ico,
  .grid-stl-heading {
    display: flex;
    align-items: center;
  }

  .grid-stl-heading {
    .subheading-2 {
      font-size: 20px !important;
      line-height: 24px;
      color: #083D5B;
      font-family: 'Roboto-bold';
    }
  }

  &.selectedItem {
    background: #0D213D !important;

    &::after {
      background: #F15C05 !important;
    }

    .grid-stl-heading {
      .subheading-2 {
        color: #fff;
      }
    }
  }
}
.process-stl-heading {
  display: flex;
  align-items: center;
}
.process-stl-ico {
  i {
    svg {
      fill: #0095C7;
      width: 35px !important;
      height: 35px !important;

      path,
      ellipse,
      polyline,
      line {
        stroke: #0095C7 !important;
      }
    }
  }
}

.process-subheading-2 {
  font-size: 20px !important;
  line-height: 24px;
  color: #162439;
  font-family: 'Roboto' !important;
  font-weight: 500 !important;
}

.mat-raised-button.cst-btn {
  width: auto;
  height: auto;
  background: #083D5B 0% 0% no-repeat padding-box !important;
  border-radius: 4px;
  color: #fff;
  line-height: 18px;
  padding: 11px 22px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.3764705882);
  font-size: 15px;

  span.mat-button-wrapper {
    line-height: 12px !important;
  }

  &:hover {
    background: #4E7389 !important;
  }
}

.mat-raised-button.cst-btn.cancel {
  border: 1px solid #083D5B;
  background: transparent !important;
  color: #083D5B;
  box-shadow: none;

  &:hover {
    background: #4E73890B 0% 0% no-repeat padding-box;
  }
}

.main-observation {
  .main-observation-section {
    min-height: calc(100vh - 353px);
  }

  .main-obervation-fotter {
    display: flex;
    justify-content: center;

    .mat-raised-button.cst-btn {
      margin: 0 5px;
    }
  }
}


.create-observation {
  .create-observation-section {
    min-height: auto;
  }

  .create-obervation-fotter {
    // display: flex;
    // justify-content: center;
    min-height: 150px;

    .mat-raised-button.cst-btn {
      margin: 0 5px;
    }
  }
}

.behaviour-checklist-section {
  .behaviour-checklist-fotter {
    margin-top: 10px;
    display: flex;
    justify-content: right;
    // margin: 20px 0;

    .cst-btn {
      margin: 0 5px;
    }
  }
}

.create-schedular-fotter {
  display: flex;
  justify-content: right;
  margin-top: 20px;
  // margin: 20px 0;

  .cst-btn {
    margin: 0 5px;
  }
}

.unit-search-section {
  margin: 20px 0;
}

.overflow-section {
  overflow-x: auto;
  height: calc(100vh - 120px);
  overflow-y: auto;
  padding-right: 5px;
}
.overflow-sectionscreen{
  overflow-x: auto;
  height: calc(100vh - 50px);
  overflow-y: auto;
  padding-right: 5px;
}

.overflow-sectionaudit {
  overflow-x: auto;
  height: calc(100vh - 202px);
  overflow-y: auto;
  padding-right: 5px;
}
.overflow-sectiontest {
  overflow-x: auto;
  height: 68vh;
  overflow-y: auto;
  padding-right: 5px;
}

.audit-plan-unit-section {
  display: flex;
  
  justify-content: space-between;
  margin: 0px 0;
  align-items: center;

  .audit-head-icon {
    display: flex;
    // width: 100%;
    justify-content: end;

    .icon-bg-box {
      width: 30px;
      height: 30px;
      background: #083D5B !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      margin: 0 2px;

      .button-stroke-svg-icon {
        stroke: #fff !important;
      }

      .button-svg-blue {
        fill: #fff !important;
      }
    }
  }
}

.audit-plan-unit-sectiondashboard {
  // display: flex;
  
  justify-content: space-between;
  margin: 0px 0;
  align-items: center;

  .audit-head-icon {
    display: flex;
    // width: 100%;
    justify-content: end;

    .icon-bg-box {
      width: 30px;
      height: 30px;
      background: #083D5B !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      margin: 0 2px;

      .button-stroke-svg-icon {
        stroke: #fff !important;
      }

      .button-svg-blue {
        fill: #fff !important;
      }
    }
  }
}
.audit-section {
  display: flex;
  flex-flow: column;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h4 {
      margin-bottom: 0px;
    }
  }
}

.dateRangePick.set-back-color-action {
  height: 49px;
}

.audit-plan-unit-section.dashboard-unit-section {
  justify-content: start;

  .dashboard-filter {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
}
.outerbox{
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #0000003B;
  border-radius: 8px;
  width: calc(100% - 10px) !important;
  padding: 30px 30px;
  box-sizing: border-box;
  margin-top: 5px;
}

.dashboard-section{
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #0000003B;
  border-radius: 8px;
  width: calc(100% - 10px) !important;
  padding: 30px 30px;
  box-sizing: border-box;
  margin-top: 5px;
   h4{
       margin-bottom: 0px ;
   }
   .chart-Card-main{
    display: flex;
    flex-flow: column;
    box-sizing: border-box;
    box-shadow: none !important;
    h4{
         font-size: 18px !important;  
    }
    .chart-filter{
      display: flex;
      align-items: center;
      justify-content: space-between;
     
    }
   }

     .half-mat-card {
       display: flex;
  
       .half-chart-Card {
         margin: 0 10px 20px 10px;
         max-width: calc(49.5% - 20px);
         box-shadow: none !important;
         h4 {
           font-size: 18px !important;
         }
  
         .chart-filter {
           display: flex;
           align-items: center;
           justify-content: space-between;
  
         }
       }
     }
     .half-mat-card-split {
      display: flex;
 
      .half-chart-Card {
        margin: 0 10px 20px 10px;
        max-width: calc(100% - 20px);
        box-shadow: none !important;
        h4 {
          font-size: 18px !important;
        }
 
        .chart-filter {
          display: flex;
          align-items: center;
          justify-content: space-between;
 
        }
      }
    }
     }

.dashboard-card-section{
  display: flex;
  margin: 20px 0;
}

.dashboard-cst-card {
  background: rgba(0, 149, 199, 0.05);
  margin-right: 23px;
  width: 230px;
  min-height: 131px;
  border-radius: 8px;
  padding: 20px 21px;
  position: relative;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: pointer;
  margin-bottom: 5px;

  .left-part {
      display: flex;
      align-items: self-start;
      flex-flow: column;
      justify-content: center;
      display: flex;
  
      span.unit {
        font-size: 60px;
        line-height: 44px;
        color: #083D5B;
        font-family: "Roboto-light";
        letter-spacing: -3px;
        display: flex;
  
        .unit-key {
          color: #083D5B;
          opacity: 0.6;
          font-size: 24px;
          line-height: 24px;
          height: 100%;
          display: flex;
          margin: -3px 0 0 3px;
          font-family: "Roboto-regular";
        }
      }
  
      span.bt-text {
        color: #585D6B;
        letter-spacing: 0.09px;
        font-size: 15px;
        line-height: 24px;
        font-family: "Roboto-regular";
        margin-top: 2px;
      }
    }

  .right-part {
    display: flex;
    align-items: center;
  }
}

.observation-list-filter-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .observation-list-filter {
    display: flex;
    align-items: center;
  }

  h4 {
    margin-bottom: 0px;
  }
}

.schedular-tracker-section {
  .icon-bg-box.schedular-tracker-back-icon {
    .mat-icon {
      font-size: 17px;
      margin-left: 10px;
      margin-top: 6px;
      color: #fff;
    }

  }
}

.configuration-section.ovservation-list-section {
  .audit-plan-unit-section {
    h4 {
      margin: 0 !important;
    }

    .audit-head-icon {
      .icon-bg-box {
        .mat-icon {
          color: #fff;

          &.configuration_node_add {
            font-size: 20px;
            margin-left: 5px;
            margin-top: 2px;
          }

          &.configuration_list {
            font-size: 20px;
            margin-left: 4px;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .configuration-site-icon {
    line-height: 10px;

    svg {
      fill: rgb(8, 61, 91) !important;
    }
  }

  .confiq-sub-heading {
    margin-bottom: 0 !important;
    font-size: 16px !important;
  }

  .site-icon-text {
    margin: 0 10px;
  }

  .site-icon {
    line-height: 10px;
  }

  .icon-bg-box.schedular-tracker-back-icon {
    .mat-icon {
      font-size: 17px;
      margin-left: 10px;
      margin-top: 6px;
      color: #fff;
    }
  }
}

.icon-bg-box {
  width: 30px;
  height: 30px;
  background: #083D5B !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  margin: 0 2px;

  .button-stroke-svg-icon {
    stroke: #fff !important;
  }

  .button-svg-blue {
    fill: #fff !important;
  }

  .icon-arrow {
    font-size: 17px;
    margin-left: 9px;
    margin-top: 5px;
    color: #fff;
  }
}

.form-cofiq-filter-section,
.action-section-unit-section,
.alert-section-unit-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0 0 0;

  h4 {
    margin: 0 !important;
  }
}

.units-filter {
  display: flex;
  align-items: center;
}

.btn-section {
  display: flex;
  justify-content: right;
  align-items: center;

  .cst-btn {
    margin: 0 5px;
  }
}

.workflow {
  display: flex;

  .shared-dash-card {
    display: flex;
    width: 257px;
    height: 123px;
    box-shadow: 0px 3px 6px #00000029;
    // border: 0.20000000298023224px solid #FFFDFC;
    border-radius: 10px;
    //background: transparent linear-gradient(180deg, #1A2254 0%, #050711 100%) 0% 0% no-repeat padding-box;
    box-sizing: border-box;
    padding: 16px 18px 16px 18px;
    margin: 20px 38px 20px 0px;

    .ico-section {
      display: flex;
      align-items: center;

      svg {
        path {
          fill: #0095C7;
        }
      }
    }

    .content-sec {
      width: calc(100% - 30px);
      display: flex;
      flex-flow: column;
      padding-left: 25px;
      box-sizing: border-box;

      h6.shared-dash-heading {
        letter-spacing: 0px;
        font-family: 'Roboto-bold';
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0px;
        color: #083D5B;
        margin-bottom: 18px;
      }

      .createdBy {
        color: #000;
        font-size: 12.65px;
        font-family: 'Roboto-regular';
        font-weight: 300;
        line-height: 17px;
        letter-spacing: 0.3px;

        span {
          &:last-child {
            margin-left: 5px;
          }
        }
      }

      .bot-fot {
        display: flex;
        justify-content: end;
        margin-top: 12px;

        a {
          color: #000;
          font-size: 12.65px;
          font-family: "Roboto-regular";
          font-weight: 300;
          line-height: 20px;
          cursor: pointer;
          margin: 0 3px;
        }
      }
    }

    &:hover {
      .ico-section {
        svg {
          path {
            fill: #fff;
          }
        }
      }

      .content-sec {
        h6.shared-dash-heading {
          color: #fff;
        }

        .createdBy {
          color: #fff;
        }

        .bot-fot {
          a {
            color: #fff;
          }
        }
      }
    }

  }
}

.board {
  display: flex;
}

.workflow-section {
  .grid-stl {
    &:hover {
      .grid-stl-ico {
        svg {
          path {
            fill: #F15C05;
          }
        }
      }
    }
  }
}

.main-observation,
.create-observation {
  .grid-stl {
    .grid-stl-ico {
      i {
        svg {
          fill: #0095C7;
          width: 35px !important;
          height: 35px !important;

          path,
          ellipse,
          polyline,
          line {
            stroke: #0095C7 !important;
          }
        }
      }
    }

    &:hover {
      .grid-stl-ico {
        i {
          svg {
            fill: #fff;

            path,
            ellipse,
            polyline,
            line {
              stroke: #fff !important;
            }
          }
        }
      }
    }

    &.selectedItem {
      .grid-stl-ico {
        i {
          svg {
            fill: #fff !important;

            path,
            ellipse,
            polyline,
            line {
              stroke: #fff !important;
            }
          }
        }
      }
    }
  }

  .grid-stl-disable {
    .grid-stl-ico {
      i {
        svg {
          fill: #0095C7;
          width: 35px !important;
          height: 35px !important;

          path,
          ellipse,
          polyline,
          line {
            stroke: #0095C7 !important;
          }
        }
      }
    }

    &.selectedItem {
      .grid-stl-ico {
        i {
          svg {
            fill: #fff !important;

            path,
            ellipse,
            polyline,
            line {
              stroke: #fff !important;
            }
          }
        }
      }
    }
  }

}

.width165 {
  width: 165px;
}

.mat-icon-button {
  width: auto;
  height: auto;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 12px !important;
}

.quality-section {
  .quality-mat-tree {
    .mat-tree-node {
      .mat-icon-button {
        .mat-button-wrapper {
          svg {
            path {
              fill: #083D5B;
            }
          }
        }
      }

      span.semi-bold {
        min-width: 80px;
        font-size: 14px !important;
        line-height: 17px !important;
        font-family: "Roboto-regular" !important;
        font-weight: normal !important;
        margin: 0;
        letter-spacing: 0.3px;
      }
    }
  }
}

.quality-section {
  width: 100%;
  max-width: 70%;

  &.audit-plan-section {
    max-width: 100%;
  }

  .quality-upload-section {
    .upload-section {
      .subheading-1 {
        font-size: 14px !important;
        line-height: 17px !important;
        font-family: "Roboto-regular" !important;
        font-weight: normal !important;
        margin: 0;
        letter-spacing: 0.3px;
      }
    }
  }
}

.scorecard-section {
  .scorecard-card {
    .dashboard-cst-card {
      width: 18% !important;
    }
  }

  .caption {
    font-size: 14px !important;
    line-height: 17px !important;
    font-family: "Roboto-regular" !important;
    font-weight: normal !important;
    margin: 0;
    letter-spacing: 0.3px;
  }
}

.summary-section {
  .listHeadBoxSummary {
    background: #083D5B 0% 0% no-repeat padding-box;
  }

  .blueLightBoxSummary,
  .listHeadBoxSummary {
    .caption {
      font-size: 14px !important;
      line-height: 17px !important;
      font-family: "Roboto-regular" !important;
      font-weight: normal !important;
      margin: 0;
      letter-spacing: 0.3px;
    }
  }
}


mat-datepicker-toggle {
  .mat-icon-button {
    margin-top: 4px !important;
  }
}

.my-dateicon {
  .mat-icon-button {
    height: auto;
    width: auto;
    top: 7px;
    right: 3px;
  }
}

mat-dialog-actions {
  .cst-btn {
    margin: 0 5px;
  }
}

.category-pop-up {
  margin-top: 40px !important;

  .cst-btn {
    margin: 0 5px;
  }
}

//scroll css
::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar {
  width: 11px;
  background-color: rgb(189 189 189 / 48%);
  border-radius: 8px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgb(8 61 91)
}

//common padding classes
.cursor-pointor {
  cursor: pointer;
}

.d-flex {
  display: flex;
}

.flex-row {
  flex-flow: row;
}

.align-item-center {
  align-items: center;
}

.justify-center {
  justify-content: center !important;
}

.justify-end {
  justify-content: end !important;
}

.justify-start {
  justify-content: start !important;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-flow-wrap {
  flex-flow: wrap;
}

.p-l-10 {
  padding-left: 10px;
}

.p-r-8 {
  padding-right: 8px;
}

.p-l-8 {
  padding-left: 8px;
}

.p-l-20 {
  padding-left: 20px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-150 {
  margin-top: 150px !important;
}

.mt-40 {
  margin-top: 40px;
}

.mt-5 {
  margin-bottom: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-10 {
  margin-left: 10px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mt-50 {
  margin-top: 50px;
}

.mt-6 {
  margin-top: 6px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-12 {
  margin-top: 12px;
}

.p-r-65 {
  padding-right: 65px;
}

.p-l-65 {
  padding-left: 65px;
}

.mt-50 {
  margin-top: 40px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-0 {
  margin-bottom: 0px !important;
}

span.mlr-2 {
  margin: 0 1px;
}

h2 {
  font: normal normal bold 20px/25px Roboto-regular !important;
  color: #333333;
  margin: 0 !important;
}

h3 {
  font: normal normal bold 18px/22px Roboto-regular !important;
  color: #333333;
  margin: 0 !important;
}

h4 {
  font-size: 20px !important;
  font-family: "Roboto-Medium" !important;
  font-weight: 400 !important;
  color: #9E9E9E !important;
  line-height: 32px !important;

  &.headline {
    font-size: 20px !important;
  }
}

span.headline {
  display: flex;
  align-items: center;
  font-size: 20px !important;
  font-family: "Roboto-Medium";
  font-weight: 400 !important;
  color: #9E9E9E !important;
  line-height: 32px;
}
.mat-form-field.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  color: #083D5B;
}


h4.heading {
  display: flex;
  align-items: center;
  font-size: 22px !important;
  font-family: "Roboto" !important;
  font-weight: 500 !important;
  color: #083D5B !important;
  line-height: 32px;
}

h4 {
  i {
    margin-right: 10px;
    line-height: 10px
  }
}

.width165 {
  width: 165px !important;
}

mat-label.mail-sub-heading {
  font-size: 20px !important;
  font-family: "Roboto-Medium";
  font-weight: 400 !important;
  line-height: 20px;
  color: #000;
  margin-bottom: 5px;
  display: flex;
}

h6.mail-heading {
  font: normal normal normal 20px/33px Roboto-regular !important;
  color: #FFFFFF;
  margin-bottom: 5px;
}

h2.app-sub-heading {
  font: normal normal bold 16px/23px Roboto-regular !important;
  letter-spacing: 0px;
  color: #000000;
  text-shadow: 0px 3px 6px #00000029;
}

h2.event-text {
  font: normal normal normal 20px/24px Roboto-regular !important;
  color: #E1660E;
  margin: 15px 0 15px 0 !important;
}

h4.heading.maintaince-heading {
  font-size: 18px !important;
  font-family: Roboto-regular !important;
  margin: 22px 0 6px 0;
}

h6.heading.unit-heading {
  font-size: 14.22px !important;
  color: #333333;
  line-height: 18px !important;
  font-family: 'Roboto-regular' !important;
  font-weight: normal !important;
}

h6.heading.box-heading {
  color: #FFFFFF;
  font-size: 14px !important;
  line-height: 17px !important;
  font-family: 'Roboto-regular' !important;
  font-weight: normal !important;
  margin: 0;
  letter-spacing: 0.3px;
}

.strict {
  margin-left: 2px;
  color: red
}

.width100 {
  width: 100%;
}

mat-label.form-label {
  font: normal normal normal 14px/18px Roboto-regular !important;
  letter-spacing: 0px;
}



@media only screen and (max-width: 820px) {
  .expandHead {
    font-size: $caption-size !important;
  }

  .head-font {

    font-size: 14px !important;
  }
}

@media only screen and (max-width: 400px) {


  .search-input {
    width: 150px !important;
  }
}

@media only screen and (max-width: 768px) {


  .search-input {
    width: 300px !important;
  }

  .arrow-box {
    // max-width: 349px;
    // min-width: 300px;
    width: 250px;



  }

  .observe-box {
    // max-width: 349px;
    // min-width: 300px;
    width: 250px;



  }

  .arrow-box-leftside {
    // max-width: 349px;
    // min-width: 300px;
    width: 250px;



  }
}


.proofImage {
  margin-bottom: 0px !important;
  height: 80%;
  width: 100%;
}

.mat-select-panel {
  // min-width: 200px !important;
  overflow-x: hidden !important;
  max-width: 100% !important;
  margin-top: 29px;
}

.iFrameTable {
  width: 100% !important;
  height: 60vh !important;
  border: 1px solid rgb(207, 205, 205) !important;
}

.cdk-overlay-pane {
  // top: 184.2px !important;
  //left: 424.375px !important;
  // margin-top: 30px !important;
  margin-left: 6px !important;


}
.iFrameTablebar {
  width: 100% !important;
  height: 47vh !important;
  border: 1px solid rgb(207, 205, 205) !important;
}

.spinner-body {
  background: #ffffff50;
  height: 100vh;
  width: 100vw;
  z-index: 1000;
  position: absolute;
}

.spinner {
  margin-left: 42vw !important;
  top: 25vh;
  z-index: 10000;
}

.spinnerbar {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
}

.spinner-bodybar {
  background: #ffffff50;
  height: 59%;
  width: 83%;
  z-index: 1000;
  position: absolute;
  left: 50%;
  top: 54%;
  transform: translate(-49%, -47%);
}
.spinneractionpopup{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
}
.spinner-actionpopup {
  background: #ffffff50;
  height: 108%;
  width: 100%;
  z-index: 1000;
  position: absolute;
  left: 50%;
  top: 54%;
  transform: translate(-49%, -47%);
}

.mat-calendar-body-in-range::before{
  background: rgba(63,81,181,.2) !important;
}
.mat-calendar-body-in-range::after{
  color: #fff;
}
.mat-calendar-body-cell::after {
  color: #fff;
}
.mat-calendar-body-cell::before {
  color: #fff;
}
// .mat-calendar-body-cell-content, .mat-date-range-input-separator{
//   color: #fff;
// }
.urgent-label-font{
  font-size: 13px;
  margin-left: 15px;
}


.confirmation-dialog.cdk-overlay-pane {
  width: 100% !important;
  max-width: 384px !important;
  box-sizing: border-box;
  padding: 30px 20px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 21px;

  .mat-dialog-container {
    box-shadow: none;
    background: #FFFFFF;

    h6.confirmation-heading {
      font-size: 18px;
      color: #060606;
      font-family: roboto-regular;
      line-height: 26px;
      text-align: center;
      margin-bottom: 15px;
    }
  }

  .confirmation-fotter {
    display: flex;
    align-items: center;
    justify-content: center;

    .cst-btn {
      margin: 0 5px;

      &.cancel {
        background: #fff !important;

        .mat-button-focus-overlay {
          opacity: 0;
        }
      }
    }
  }
}
.clear{
  height: 100px;
}

.range-filter{
  cursor: pointer;
  border: 0.5px solid #083d5b;
  padding: 4px;
}
.range-filter-select{
  cursor: pointer;
  color: #fff;
  border: 0.5px solid #083d5b;
  background: #083d5b; 
  padding: 4px;
}

.tab-height {
  min-height: 60vh;
}

.assets-close-btn   {                     
  height: 38px;
  margin-top: 6px;
  margin-left: -7.9px;
  border: 1px solid #adadad;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  mat-icon{
   color: #adadad;
  }
}
.loading-text{
  margin: 13px;
}

.timepicker__actions {
  gap: 0.75rem !important;
}
.mat-form-field.dashboard-announcement-select {
  .mat-form-field-infix {
    border: 1px solid #D7DBEC;
    width: 180px;
    height: 35px;
    box-sizing: border-box;
    background: #F5F5F5 0% 0% no-repeat padding-box;
    border-radius: 6px;
    padding: 9px;
  }

  .mat-form-field-label-wrapper {
    display: none;
  }

  .mat-select-value-text {
    font: normal normal normal 14px/20px Roboto-regular;
    letter-spacing: 0px;
    color: #000000;
  }

  .mat-select-arrow-wrapper {
    background: url('./assets/icons/drop-down-arrow.svg');
    background-repeat: no-repeat;
    width: 15px;
    height: 8px;

    .mat-select-arrow {
      border: 0;
    }
  }
}

.mat-form-field.form-full-width-drop-down {
  width: 100%;

  .mat-form-field-infix {
    background: transparent;
    height: 38px;
  }

  .mat-select-arrow-wrapper {
    background: url('./assets/icons/caret-down.svg');
    width: 8px;
    height: 5px;
    background-repeat: no-repeat;
    background-position: center center;
  }
}

.mat-form-field.dashboard-announcement-select.search-select-box{
  .mat-form-field-label-wrapper{
    display: block;
    top: -9px !important;
    left: 10px;
  }
} 
.mat-form-field.dashboard-announcement-select.search-select-box.mat-form-field-should-float {
  .mat-form-field-label-wrapper{
    display: none;
  }
}

.action-radio-section {
  display: flex;
  align-items: center;
  position: relative;

  .mat-radio-button {
    &:nth-of-type(2) {
      margin-right: 197px;
    }
  }

  .teams {
    position: absolute;
    left: 216px;
  }

  .site {
    margin-left: 5px;
  }

  .both-action-drop {
    margin-top: 1px;

    .mat-form-field.form-full-width-drop-down {
      width: 166px;

      .mat-form-field-infix {
        height: 28px;
        padding: 0px 7px;
        line-height: 28px;

        .mat-select-value-text {
          text-align: left;
          font: normal normal normal 13px/15px Roboto-regular;
          letter-spacing: 0px;
          color: #333333DE;
        }
      }
    }
  }
}
.occurrence-section{
  .occurrence-text{
    margin: 8px 0 0 0;
    padding: 0;
  }
}
.cdk-overlay-pane.set-resource{
  width: 600px !important;
  .mail-popup{
    padding: 25px;
    background: #fff;
    .mail-body{
      padding: 20px 0;
      display: flex;
      flex-flow: wrap;
      .half-width{
        width: 50%;
        box-sizing: border-box;
      }
      .weeks{
        margin-top: 20px;
        display: flex;
        width: 100%;
        flex-flow: column;
        ul{
             padding: 0;
            margin: 0;
            list-style-type: none;
            display: flex;
            li{
              margin: 5px;
              a{
                width: 25px;
                height: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 100%;
                cursor: pointer;
                line-height: 25px;
                &.selected{
                  background: #083D5B !important;
                  color: #fff;
                }
              }
            }
        }
      }
      .monthly{
        position: relative;
        margin-top: 20px;
        width: 100%;
        .mat-radio-group{
          display: flex;
          flex-flow: column;
          .mat-radio-button{
            margin: 10px 0;
            .mat-radio-label{
              .mat-radio-container{
                width: 15px;
                height: 15px;
                .mat-radio-outer-circle{
                  width: 15px;
                  height: 15px;
                }
                .mat-radio-inner-circle{
                  width: 15px;
                  height: 15px;
                }
              }
              .mat-radio-label-content{
                font-size: 14px;
              }
            }
          }
        }
        common-lib-input{
          position: absolute;
          top: 5px;
          left: 75px;
          .mat-form-field-infix{
            border: 1px solid #D7DBEC;
            padding: 6px 10px;
            border-radius: 4px;
            width: 20px;
          }
        }
        commom-select-box{
          position: absolute;
          top: 44px;
          left: 75px;
          .resource-month-drop-down {
            .mat-form-field-infix{
              width: 100px;
            }
          }
          &:last-child{
            left: 185px;
          }
        }
       
      }
    }
    
  }
}
.mat-form-field-type-mat-date-range-input .mat-form-field-infix{
  width: 214px !important;
}

.cdk-overlay-pane.mail-pop-up-dashbard {
  width: 427px !important;
  max-width: 100% !important;
  margin-top: 200px;
  margin-left: -400px;

  .mat-dialog-container {
    background: transparent;
    box-shadow: none;

    .mail-popup {
      background: #1A2254;
      border-radius: 21px;
      padding: 25px 35px;
    }
  }
}

.mail-fotter {
  margin-top: 56px;
  display: flex;
  align-items: center;
  justify-content: center;

  .mail-cancel {
    margin-right: 15px;
  }

}

.mat-form-field.form-date-picker {
  width: 100%;

  .mat-form-field-infix {
    padding: 0;
    margin: 0;
    display: flex;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDEDF;
    border-radius: 4px;
    height: 38px;
    box-sizing: border-box;

    .mat-datepicker-input {
      padding: 0 10px;
      font: normal normal normal 14px/18px Roboto-regular;
      letter-spacing: 0px;
      color: #090909;
    }

    .mat-datepicker-toggle {
      .mat-focus-indicator.mat-icon-button {
        width: 38px;
        height: 36px;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box;
        line-height: 36px;
        background: url('./assets/icons/calendar-alt.svg');
        background-repeat: no-repeat;
        background-position: center center;

        .mat-button-wrapper {
          svg {
            display: none;
          }
        }
      }
    }
  }
}
.mat-form-field-underline {
  width: 0% !important; 
}

.mat-icon-color{
  color: #adadad;
  cursor: pointer;
 
}
.log-margin{
  margin: 3px;
}

.split-border {
  border-right: #083d5b 3px solid;
}

.log-list span{
  justify-content: space-between;
}


.disabled {
  opacity: 0.5 !important;
  pointer-events: none !important;
}

//new
.grid-stl-config {
  width: auto !important;
  height: 45px !important;
  min-width: 200px !important;
  flex-flow: wrap;
  display: flex !important;
  margin: 9px 18px 9px 0 !important;
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137) !important;
  border: 1px solid #E7E7E7;
  border-radius: 6px !important;
  box-sizing: border-box !important;
  cursor: pointer;
  position: relative;
  padding: 10px 25px 10px 30px !important;

  &:hover {
    background: #0D213D !important;

    &::after {
      background: #F15C05 !important;
    }

    .grid-stl-heading {
      .subheading-2 {
        color: #fff;
      }
    }
  }

  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 7px;
    background: #F5F5F5 0% 0% no-repeat padding-box;
    border-radius: 5px 0px 0px 5px;
  }

  .grid-stl-ico {
    margin-right: 20px;

    i {
      line-height: 10px;
    }
  }

  .grid-stl-ico,
  .grid-stl-heading {
    display: flex;
    align-items: center;
  }

  .grid-stl-heading {
    .subheading-2 {
      font-size: 16px !important;
      line-height: 24px;
      color: #083D5B;
      font-family: 'Roboto-bold';
    }
  }

  &.selectedItem {
    background: #0D213D !important;

    &::after {
      background: #F15C05 !important;
    }

    .grid-stl-heading {
      .subheading-2 {
        color: #fff;
      }
    }
  }
}
.config-margin {
  margin-left: 9px;
  margin-top: 8px;
}


.chip {
  display: flex;
  align-items: center;
  background-color: #e0e0e0;
  border-radius: 16px;
  padding: 8px 16px;
  margin: 5px;
  font-size: 14px;
  color: #555;

}

.chip-name {
  margin-right: 8px;
}

.chip-close {
  font-size: 16px;
  cursor: pointer;
  color: #888;
}

.chip-close:hover {
  color: #555;
}


.mail-dialog.cdk-overlay-pane {
  width: 100% !important;
  max-width: 784px !important;
  height: 450px;
  box-sizing: border-box;
  padding: 30px 20px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 21px;

  .mat-dialog-container {
    box-shadow: none;
    background: #FFFFFF;

    h6.confirmation-heading {
      font-size: 18px;
      color: #060606;
      font-family: roboto-regular;
      line-height: 26px;
      text-align: center;
      margin-bottom: 15px;
    }
  }

  .confirmation-fotter {
    display: flex;
    align-items: center;
    justify-content: center;

    .cst-btn {
      margin: 0 5px;

      &.cancel {
        background: #fff !important;

        .mat-button-focus-overlay {
          opacity: 0;
        }
      }
    }
  }
}

.mail-dialogdashboard.cdk-overlay-pane {
  width: 100% !important;
  max-width: 1084px !important;
  height: 650px;
  box-sizing: border-box;
  padding: 30px 20px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 21px;

  .mat-dialog-container {
    box-shadow: none;
    background: #FFFFFF;

    h6.confirmation-heading {
      font-size: 18px;
      color: #060606;
      font-family: roboto-regular;
      line-height: 26px;
      text-align: center;
      margin-bottom: 15px;
    }
  }

  .confirmation-fotter {
    display: flex;
    align-items: center;
    justify-content: center;

    .cst-btn {
      margin: 0 5px;

      &.cancel {
        background: #fff !important;

        .mat-button-focus-overlay {
          opacity: 0;
        }
      }
    }
  }
}
.userMailIcon{
  color: #555;
  cursor: pointer;
}
// .apexcharts-svg{
//   height: 400px !important;
// }
.active-class{
  cursor: not-allowed;
  background-color: rgba(239, 239, 239, 0.3);
  color: rgb(84, 84, 84);
  border-color: rgba(118, 118, 118, 0.3);
}

.grid-stl-disable {
  width: 317px !important;
  height: 120px !important;
  flex-flow: wrap;
  // display: none !important;
  margin: 9px 18px 9px 0 !important;
  background: rgba(239, 239, 239, 0.3) 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137) !important;
  border: 1px solid #E7E7E7;
  border-radius: 6px !important;
  box-sizing: border-box !important;
  cursor: not-allowed;
  position: relative;
  padding: 10px 25px 10px 30px !important;
  color: rgb(84 84 84 / 60%) !important;
  display: flex !important;
  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 7px;
    background: #F5F5F5 0% 0% no-repeat padding-box;
    border-radius: 5px 0px 0px 5px;
  }

  .grid-stl-ico {
    margin-right: 20px;

    i {
      line-height: 10px;
    }
  }

  .grid-stl-ico,
  .grid-stl-heading {
    display: flex;
    align-items: center;
  }

  .grid-stl-heading {
    .subheading-2 {
      font-size: 20px !important;
      line-height: 24px;
      color: #545454ab;
      font-family: 'Roboto-bold';
    }
  }

  &.selectedItem {
    background: #0D213D !important;

    &::after {
      background: #F15C05 !important;
    }

    .grid-stl-heading {
      .subheading-2 {
        color: #fff;
      }
    }
  }
}

.welcome-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  text-align: center;
  width: 400px;
  max-width: 90%;
}

/* Close button styles */
.close-button {
  background-color: #4caf50;
  border: none;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.close-button:hover {
  background-color: #45a049; /* Darker shade of green on hover */
}

.info-button {
  position: relative;
  display: inline-block;
  cursor: pointer;
  vertical-align: middle; /* Aligns the icon with the text */
}

.info-button .tooltip {
  visibility: hidden;
  width: 200px; /* Adjust width as needed */
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}
.info-button:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.custom-tooltip-style {
  width: 200px; /* Adjust width as needed */
  // background-color: #333;
  // color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 8px;
}

.custom-tooltip {
  background-color: #333;
  color: #fff;
  text-align: left;
  font-size: 12px;
  border-radius: 4px;
  padding: 8px;
  opacity: 1;
  // width: 800px !important;
  max-width: 800px !important;
  min-width: 600px !important;
}

.filter-popup {
  padding: 20px;
  max-width: 600px;
}

.filter-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-field {
  flex: 1 1 45%; /* Allows items to fit two in a row */
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h2 {
  margin: 0;
}

.mat-tab-label .mat-tab-label-content {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: pre-wrap !important;
}
.display-flex  {
  display: flex;
  flex-wrap: wrap;
}
.width-category{
  width: 33%;
}
.mat-tree-node{
  min-height: auto;
}