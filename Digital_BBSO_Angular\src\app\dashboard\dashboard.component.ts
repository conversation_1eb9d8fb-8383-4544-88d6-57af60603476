import {
  Component,
  OnInit,
  <PERSON>ement<PERSON>ef,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectorRef,
  ViewChild,
  ChangeDetectionStrategy,
  Inject,
  AfterViewInit,
} from '@angular/core';
import {  MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import ApexCharts from 'apexcharts';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { BarchartdetailsComponent } from '../barchartdetails/barchartdetails.component';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import {ObservationSendMailComponent } from '../observation-module/observation-send-mail/observation-send-mail.component';
import * as _ from 'lodash';
import { NgZone } from '@angular/core';
import {
  ChartComponent,
  ApexAxisChartSeries,
  ApexChart,
  ApexFill,
  ApexYAxis,
  ApexTooltip,
  ApexTitleSubtitle,
  ApexXAxis,
  ApexDataLabels,
  ApexPlotOptions,
  ApexStroke,
  ApexLegend,
  ApexResponsive,
  ApexTheme,
  ApexNonAxisChartSeries,
} from 'ng-apexcharts';
import { ColumnChartService } from '../services/column-chart.service';
import { ChartOptions } from '../modals/chart.modal';
import { CommonService } from '../services/common.service';
import { DataService } from '../services/data.service';
import { formatDate } from '@angular/common';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatDateFormats } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../services/TranslationService/translation.services';
import { LanguageService } from '../services/language.service';
import { environment } from 'src/environments/environment';

export type ChartOptionsMetrics = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  plotOptions: ApexPlotOptions;
  grid: ApexGrid;
  yaxis: ApexYAxis;
  xaxis: ApexXAxis;
  legend: ApexLegend;
  tooltip: ApexTooltip;
  stroke: ApexStroke;
};


export type ChartOptionsOne = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis | ApexYAxis[];
  title: ApexTitleSubtitle;
  labels: string[];
  stroke: any; // ApexStroke;
  dataLabels: any; // ApexDataLabels;
  fill: ApexFill;
  tooltip: ApexTooltip;
  colors;
};
export type ChartOptionsAction = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  stroke: ApexStroke;
  tooltip: ApexTooltip;
  dataLabels: ApexDataLabels;
};
export type ChartOptionsTwo = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  plotOptions: ApexPlotOptions;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  stroke: ApexStroke;
  title: ApexTitleSubtitle;
  tooltip: ApexTooltip;
  fill: ApexFill;
  legend: ApexLegend;
  colors: string[];
};
export type chartOptionsweek = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  plotOptions: ApexPlotOptions;
  responsive: ApexResponsive[];
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  title: ApexTitleSubtitle;
  legend: ApexLegend;
  fill: ApexFill;
};

export type chartOptionUnsafe = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  plotOptions: ApexPlotOptions;
  responsive: ApexResponsive[];
  title: ApexTitleSubtitle;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  legend: ApexLegend;
  fill: ApexFill;
  stroke: ApexStroke;
  tooltip: ApexTooltip;
  colors;
  
};

export type ChartOptionsThree = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  title: ApexTitleSubtitle;
  plotOptions: ApexPlotOptions;
  legend: ApexLegend;
};

export type ChartOptionsRiskSection = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  title: ApexTitleSubtitle;
  plotOptions: ApexPlotOptions;
  legend: ApexLegend;
};

export type ChartOptionsCategory = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  title: ApexTitleSubtitle;
  plotOptions: ApexPlotOptions;
  legend: ApexLegend;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  stroke: ApexStroke;
  tooltip: ApexTooltip;
  fill: ApexFill;
  colors;
};

export type ChartOptionsPercentage = {
  series: ApexNonAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  title: ApexTitleSubtitle;
  plotOptions: ApexPlotOptions;
  legend: ApexLegend;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  stroke: ApexStroke;
  tooltip: ApexTooltip;
  fill: ApexFill;
  responsive: ApexResponsive[];
  theme:ApexTheme,
  labels:any,
  colors:any
};


export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},]
})
export class DashboardComponent implements OnInit, AfterViewInit {
  siteControl: FormControl = new FormControl('');
  filteredSiteOptions: Observable<any[]>;
  siteList = [];
  filteredSiteList = [];
  locVal = [];

  filteredUnitOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl('');
  locationObserve: FormControl = new FormControl('');
  categoryOpt: FormControl = new FormControl("");
  categoryList: { name: string; externalId: string[]; }[];
  
  filteredCategoryList: any;
  unitList = [];
  filteredUnitList = [];

  startDateControl: FormControl = new FormControl();
  endDateControl: FormControl = new FormControl();

  locationStartDateControl: FormControl = new FormControl();
  locationEndDateControl: FormControl = new FormControl();

  filterFlag = '';
  DateControl: FormControl = new FormControl(new Date());
  userAccessMenu: any;


  //processControl: FormControl = new FormControl("Observation");
  process = [
    {
      name: 'Observation',
    },
    {
      name: 'Field Walk',
    },
    {
      name: 'Audit',
    },
  ];

  processControl: FormControl = new FormControl('');
  filteredProcessOptions: Observable<any[]>;
  subProcessControl: FormControl = new FormControl('');
  filteredSubProcessOptions: Observable<any[]>;
  // process = []
  deparmentControl: FormControl = new FormControl('');
  processList = [];
  filteredProcessList = [];

  subProcessList = [];
  filteredSubProcessList = [];

  corePrinciplesControl: FormControl = new FormControl();
  corePrinciplesList = [];
  filteredCorePrinciplesList = [];

  observationControl: FormControl = new FormControl('Behaviour');
  observation = [
    {
      name: 'Behaviour',
    },
    {
      name: 'Hazards',
    },
    {
      name: 'Incidents',
    },
  ];

  categoryControl: FormControl = new FormControl('PPE');
  category = [
    {
      name: 'PPE',
    },
    {
      name: 'Tools & Equipment',
    },
    {
      name: 'Work Environment',
    },
  ];

  dashboardArr = [
    {
      key: "Observation",
      name: 'DASHBOARD.CARDS.TITLE_OBSERVATION',
      isEnable:false,
      value: '0',
      unit: '',
    },
    {
      key: "Field Walk",
      name: 'DASHBOARD.CARDS.FIELD_WALKS',
      isEnable:false,
      value: '0',
      unit: '',
    },
    {
      key: "Audit",
      name: 'MENU.AUDIT',
      isEnable:false,
      value: '0',
      unit: '',
    },
    {
      key: "Hazards",
      name: 'DASHBOARD.CARDS.HAZARD',
      isEnable:false,
      value: '0',
      unit: '',
    },
    {
      key: "Action",
      name: 'DASHBOARD.CARDS.ACTION',
      isEnable:false,
      value: '0',
      unit: '',
    },
  ];

  widgetList:any = []

  rangeFlag = "";
  labels = {};

  @ViewChild("chart") chartMetrics: ChartComponent;
  public chartOptionsMetrics: Partial<ChartOptionsMetrics>;

  @ViewChild('chart') chart: ChartComponent;
  public chartOptions: Partial<ChartOptions>;

  @ViewChild('chartOne') chartOne: ChartComponent;
  public chartOptionsUnit: Partial<ChartOptions>;

  @ViewChild("chartAction") chartAction: ChartComponent;
  public chartOptionsAction: Partial<ChartOptions>;

  // public chartOptions: Partial<ChartOptionsOne>;
  @ViewChild('chartDept') chartDept: ChartComponent;
  public chartOptionsDept: Partial<ChartOptions>;

  @ViewChild('chartTwo') chartTwo: ChartComponent;
  public chartOptionsTwo: Partial<ChartOptionsTwo>;

  public chartOptionsweek: Partial<chartOptionsweek>;
  
  @ViewChild('chartUnsafe') chartUnsafe: ChartComponent;
  public chartOptionsUnsafe: Partial<chartOptionUnsafe>;

  @ViewChild("chartRistSection") chartRistSection: ChartComponent;
  public chartOptionsRiskSection: Partial<ChartOptionsRiskSection>;

  @ViewChild("chartCategory") chartCategory: ChartComponent;
  public chartOptionsCategory: Partial<ChartOptionsCategory>;

  @ViewChild("chartPercentage") chartPercentage: ChartComponent;
  public chartOptionsPercentage: Partial<ChartOptionsPercentage>;




  @ViewChild('treemap') chartThree: ChartComponent;
  public chartOptionsThree: Partial<ChartOptionsThree>;

  public chartOptionsFour: ChartOptions;
  public capacityChartOptions: any;
  series: ApexAxisChartSeries = [];
  capacityChartSeries: ApexAxisChartSeries = [];

  reportingLocationList: any = [];

currentDate: any;
dateInPastWeek: any;
settingData:any;

  observationProcess:any;
  fieldWalkProcess:any;
  auditProcess:any;
  hazardsProcess:any;
  initFlag: boolean;
  loaderFlag: boolean;
  selectedWidget: any;
  selectedProcess: any;
  maxDate = new Date();
  listType: any;
  loadingcount: number=0;
  showSecondButton: boolean=false;
  selectedSite: any;
  dashboardConfigDetail: any;
  convertedColors: any;
  categoryRef: boolean= true;

  constructor(
    private dialog: MatDialog,private ngZone: NgZone,
    private router: Router,
    private cd: ChangeDetectorRef,
    private dataService: DataService,
    private commonService: CommonService,
    private columnChartService: ColumnChartService,
    @Inject(MAT_DATE_FORMATS) private dateFormats,
    private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string,
    private translate: TranslateService,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private changeDetector: ChangeDetectorRef
  ) {
    // this.ObservationsbyLocation()
    // this.ObservationsbyLocationweek()

    this.labels = {
      'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
      'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
      'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
      'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
      'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
      'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
      'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
      'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
        '1w': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1w'] || '1w',
        '1m': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1m'] || '1m',
        '3m': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'3m'] || '3m',
        '1y': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1y'] || '1y',
        'ytd': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'ytd'] || 'ytd',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'buttonDownloadpdf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonDownloadpdf'] || 'buttonDownloadpdf',
        'dashboardTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dashboardTitle'] || 'dashboardTitle',
        'subheadingCharttitle4': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subheadingCharttitle4'] || 'subheadingCharttitle4',
        'byLocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byLocation'] || 'byLocation',
        'byUnit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byUnit'] || 'byUnit',
        'trends': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'trends'] || 'trends',
        'byDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byDepartment'] || 'byDepartment',
        'sitewideParticipationLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sitewideParticipationLast7Days'] || 'sitewideParticipationLast7Days',
        'riskBehavioursAtLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskBehavioursAtLast7Days'] || 'riskBehavioursAtLast7Days',
        'atRiskBySection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'atRiskBySection'] || 'atRiskBySection',
        'percentageParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'percentageParticipation'] || 'percentageParticipation',
        'chartsParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsParticipation'] || 'chartsParticipation',
        'chartsNonparticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsNonparticipation'] || 'chartsNonparticipation',
        'toasterNoprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterNoprocess'] || 'toasterNoprocess',
        'chartsCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsCount'] || 'chartsCount',
        'chartsPeriod': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsPeriod'] || 'chartsPeriod',
        'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
        'toasterInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterInfo'] || 'toasterInfo',
        'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
        'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'] || 'observations',
        'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
        'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
        'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
        'metricsByChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'metricsByChecklist'] || 'metricsByChecklist',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
      }

    var _this =this;
    
    this.setDateFormat();

    this.chartOptionsTwo = {
      series: [
        {
          name: 'AT RISK',
          data: [],
        },
        {
          name: 'SAFE',
          data: [],
        },
      ],
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
      },
      colors: ['#035D7B', '#80D3EF'],
      plotOptions: {
        bar: {
          horizontal: true,
        },
      },
      stroke: {
        width: 1,
        colors: ['#fff'],
      },
      // title: {
      //   text: "Observations by Categories"
      // },
      xaxis: {
        categories: [],
        labels: {
          formatter: function (val) {
            return val + 'K';
          },
        },
      },
      yaxis: {
        title: {
          text: undefined,
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + 'K';
          },
        },
      },
      fill: {
        opacity: 1,
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: 40,
      },
    };

    this.chartOptionsThree = {
      series: [
        {
          data: [],
        },
      ],
      legend: {
        show: false,
      },
      chart: {
        height: 350,
        type: 'treemap',
        fontFamily: 'Myriad-Regular',
      },
      // title: {
      //   text: "Treemap with color scale"
      // },
      dataLabels: {
        enabled: true,
        offsetY: -3,
      },
      plotOptions: {
        treemap: {
          enableShades: true,
          reverseNegativeShade: true,
          colorScale: {
            ranges: [
              {
                from: -10,
                to: 0,
                color: '#FF2A38',
              },
              {
                from: 0.001,
                to: 10,
                color: '#3C436D',
              },
            ],
          },
        },
      },
    };
       _this.dataService.postData({ "dataSetType": "Dashboardcolor" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
         var Dashboardcolor = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
         console.log('Dashboardcolor', Dashboardcolor)
         this.convertedColors = Dashboardcolor?.reduce((acc, item) => {
          acc[item.name] = item.value;
          return acc;
      }, {});
       })

  }

  setPercentageChartOptions(locVal) {
    var _this = this;
    // var locKey = [
    //   _this.translationService.translate('DASHBOARD.CHARTS.PARTICIPATION'),
    //   _this.translationService.translate('DASHBOARD.CHARTS.NON_PARTICIPATION'),
    // ]
    var locKey = [
      _this.labels['chartsParticipation'],
      _this.labels['chartsNonparticipation'],
    ]
    _this.chartOptionsPercentage = {
      series: locVal,
      chart: {
        height: 350,
        type: "pie"
      },
      labels: locKey,
      // theme: {
      //   monochrome: {
      //     enabled: true
      //   }
      // },
      colors: ['#008FFB', '#aeaeae'],
      title: {
        // text: "Number of leads"
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200
            },
            legend: {
              position: "bottom"
            }
          }
        }
      ],
      tooltip: {
        y: {
          formatter: function(val, { seriesIndex }) {
            return locKey[seriesIndex] + ': ' + val;
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        show: true,
        position: 'bottom',
        formatter: function(seriesName, opts) {
          return locKey[opts.seriesIndex];
        }
      }
    };
  }
  onCorePrincipleSelected(i: any): void {
    var _this=this;
    
    _this.loadingfalse()
  
   
  }
  
  configProcess(id){
    var _this = this;
    console.log('configProcess', id)
    if(id){
    _this.loaderFlag = true;
    this.dataService.postData({ "externalId":  id }, this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess")
    .subscribe((resData: any) => {
      _this.loaderFlag = false;
        _this.loadingfalse()
      if (resData["data"]["list" + this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
        const processConfig = resData["data"]["list" + this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
        this.dashboardConfigDetail = processConfig["dashboardConfig"];
        console.log('dashboardConfigDetail', this.dashboardConfigDetail)
        
        _this.cd.detectChanges()
        
      }else{
        var processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.selectedProcess.name)
        this.dashboardConfigDetail = processConfig["dashboardConfig"];
        _this.cd.detectChanges()
      }
    });
  }
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
          'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
            'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
            'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
            'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
            'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
            'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
            '1w': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1w'] || '1w',
            '1m': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1m'] || '1m',
            '3m': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'3m'] || '3m',
            '1y': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'1y'] || '1y',
            'ytd': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'ytd'] || 'ytd',
            'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
            'buttonDownloadpdf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonDownloadpdf'] || 'buttonDownloadpdf',
            'dashboardTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dashboardTitle'] || 'dashboardTitle',
            'subheadingCharttitle4': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subheadingCharttitle4'] || 'subheadingCharttitle4',
            'byLocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byLocation'] || 'byLocation',
            'byUnit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byUnit'] || 'byUnit',
            'trends': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'trends'] || 'trends',
            'byDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byDepartment'] || 'byDepartment',
            'sitewideParticipationLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sitewideParticipationLast7Days'] || 'sitewideParticipationLast7Days',
            'riskBehavioursAtLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskBehavioursAtLast7Days'] || 'riskBehavioursAtLast7Days',
            'atRiskBySection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'atRiskBySection'] || 'atRiskBySection',
            'percentageParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'percentageParticipation'] || 'percentageParticipation',
            'chartsParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsParticipation'] || 'chartsParticipation',
            'chartsNonparticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsNonparticipation'] || 'chartsNonparticipation',
            'toasterNoprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterNoprocess'] || 'toasterNoprocess',
            'chartsCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsCount'] || 'chartsCount',
            'chartsPeriod': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chartsPeriod'] || 'chartsPeriod',
            'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
            'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
            'toasterInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterInfo'] || 'toasterInfo',
            'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
            'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'] || 'observations',
        'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
        'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
        'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
        'metricsByChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'metricsByChecklist'] || 'metricsByChecklist',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
      
          }

        _this.chartOptionsCategory = {
          ... _this.chartOptionsCategory,
          yaxis:{
            ..._this.chartOptionsCategory.yaxis,
            title:{
              text: _this.labels['chartsCount'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          }
        }
        // By Location
        _this.chartOptions = {
          ... _this.chartOptions,
          yaxis:{
            ... _this.chartOptions.yaxis,
            title:{
              text: _this.labels['chartsCount'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          }
        }
        // By Unit
        _this.chartOptionsUnit = {
          ... _this.chartOptionsUnit,
          yaxis:{
            ..._this.chartOptionsDept.yaxis,
            title:{
              text: _this.labels['chartsCount'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          }
        }
        // By Department
        _this.chartOptionsDept = {
          ... _this.chartOptionsDept,
          xaxis:{
            ..._this.chartOptionsDept.xaxis,
            title:{
              text: _this.labels['chartsCount'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          }
        }
        // Sitewide
        _this.chartOptionsweek = {
          ... _this.chartOptionsweek,
          xaxis:{
            ..._this.chartOptionsweek.xaxis,
            title:{
              text: _this.labels['chartsPeriod'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          },
          yaxis:{
            ..._this.chartOptionsweek.yaxis,
            title:{
              text: _this.labels['chartsCount'],
              style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
              }
            }
          }
        }
        // Risk
        _this.chartOptionsUnsafe = {
          ..._this.chartOptionsUnsafe,
          xaxis: {
            ..._this.chartOptionsUnsafe.xaxis,
            type: "category",
            // categories: xvalues,
            title: {
                text: _this.labels['chartsPeriod'],
                style: {
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#D3D3D3'
                }
            }
          },
          yaxis: {
            ..._this.chartOptionsUnsafe.yaxis,
              title: {
                  text: _this.labels['chartsCount'],
                  style: {
                      fontSize: '14px',
                      fontWeight: 'bold',
                      color: '#D3D3D3'
                  }
              },
              labels: {
                  formatter: function (value) {
                      return value.toFixed(0); // Ensure values are integers
                  }
              }
          },
        }
        // Trends
        _this.capacityChartOptions.xaxis = {
          ..._this.capacityChartOptions.xaxis,
          title: {
              text: _this.labels['chartsPeriod'],
              style: {
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#D3D3D3'
              }
          }
        };
        _this.capacityChartOptions.yaxis = {
            ..._this.capacityChartOptions.yaxis,
            title: {
                text: _this.labels['chartsCount'],
                style: {
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#D3D3D3'
                }
            },
          }
        // Participation Pie chart
        var locKey = [
          _this.labels['chartsParticipation'],
          _this.labels['chartsNonparticipation'],
        ]
        _this.setPercentageChartOptions(_this.locVal)
        console.log("update title: ",_this.chartOptionsCategory.yaxis.title.text)
        console.log('Update Labels: ', _this.chartOptionsPercentage.labels)
        _this.cd.detectChanges();

        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    this.triggerAltKey()

      // this.dateFormats.display.dateInput = "YYYY, DD MMM";
    
      loadCorePrinciples();
      function loadCorePrinciples(){
        setTimeout(()=>{
          var processList = _this.commonService.processList.filter((e) => {
            return (
              e.processType == 'Core Principles' &&
              (e.refSite && e.refSite.externalId) == _this.siteControl.value
            );
          });
          _this.corePrinciplesList = processList;
          _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
          if(_this.corePrinciplesList.length == 0){
            loadCorePrinciples()
          }
        },2000)
      }
      
        
    _this.userAccessMenu = _this.commonService.userIntegrationMenu;
    if(_this.userAccessMenu){
      _this.getUserMenuConfig();
    }
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          _this.getUserMenuConfig();
        }
      }
    })

    _this.corePrinciplesControl.valueChanges.subscribe(process => {
      var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == process);
      })
      _this.processList = childrenProcess;
      _this.filteredProcessList = _this.processList.slice();
      if(_this.processList.length < 1 && _this.siteControl.value && _this.initFlag && _this.corePrinciplesControl.value){
        _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.labels['toasterNoprocess'] });
        _this.cd.detectChanges();
      }
_this.changeCall()
    })
    _this.unitControl.valueChanges.subscribe(process => {
      _this.changeCall()
    })
    _this.locationObserve.valueChanges.subscribe(process => {
      _this.changeCall()})
    
    _this.subProcessControl.valueChanges.subscribe(process => {
    _this.categoryOpt.reset()
      if(process && process.length>0){
        this.categoryRef=true;
        _this.configProcess(process);
        _this.chartRefresh();
        // _this.categoryRefresh();
      }
    });
    _this.categoryOpt.valueChanges.subscribe(process => {
      if(process && process.length>0){
        this.categoryRef=false;
        // _this.configProcess(process);
        _this.chartRefresh();
        // _this.categoryRefresh();
      }
    });
    
    this.capacityChartSeries = [
      {
        name: 'Observation',
        data: [],
      },
    ];

    // this.chartOptionsFour = this.columnChartService.getConfigureColumnChartOptions('100%', false,
    //     350, "bar", false, false, 0, "smooth", [], [], 1, false, true, true, ["Actual", "Expected"],
    //     ["#2CD889", "#775DD0"], [], false);

    this.capacityChartOptions =
      this.columnChartService.getConfigureColumnChartOptions(
        '100%',
        false,
        350,
        'area',
        false,
        true,
        5,
        'smooth',
        ['#2CD889', '#FECF5C', '#F7617D'],
        ['#2CD889', '#FECF5C', '#F7617D'],
        1,
        false,
        false,
        false,
        [],
        [],
        [],
        true
      );
      _this.rengeFilter('Week')
    var processList = _this.commonService.processList.filter((e) => {
      return (
        e.processType == 'Core Principles' &&
        e.refSite['externalId'] == _this.siteControl.value
      );
    });
    _this.corePrinciplesList = processList;
    _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();

    // this.endDateControl.valueChanges.subscribe((res) => {
    //   if(this.startDateControl.value && this.endDateControl.value){
    //   _this.applyFilter();
    //   }
    // });

  }

  changeCall(){
    var _this=this;
    _this.categoryOpt.reset()

    _this.widgetList = [];
    var obIndex = _this.dashboardArr.findIndex(e => e.key == "Observation");
    var fwIndex = _this.dashboardArr.findIndex(e => e.key == "Field Walk");
    var auIndex = _this.dashboardArr.findIndex(e => e.key == "Audit");
    var haIndex = _this.dashboardArr.findIndex(e => e.key == "Hazards");
    var acIndex = _this.dashboardArr.findIndex(e => e.key == "Action");

    _this.selectedProcess = undefined;
    _this.selectedWidget = undefined;
    _this.observationProcess = undefined;
    _this.fieldWalkProcess = undefined;
    _this.auditProcess = undefined;
    _this.hazardsProcess = undefined;
    _this.dashboardArr[obIndex]["value"] = "0";
    _this.dashboardArr[obIndex]["isEnable"] = false;
    _this.dashboardArr[fwIndex]["value"] = "0";
    _this.dashboardArr[fwIndex]["isEnable"] = false;
    _this.dashboardArr[auIndex]["value"] = "0";
    _this.dashboardArr[auIndex]["isEnable"] = false;
    _this.dashboardArr[haIndex]["value"] = "0";
    _this.dashboardArr[haIndex]["isEnable"] = false;
    _this.dashboardArr[acIndex]["value"] = "0";
    _this.dashboardArr[acIndex]["isEnable"] = true;
    _this.selectedWidget = undefined;
    var observationProcess = _this.processList.find(f => f.name == "Observation");
    if (observationProcess) {
      _this.observationProcess = observationProcess;
      _this.selectedProcess = observationProcess;
      _this.dashboardArr[obIndex]["isEnable"] = true;
      _this.dashboardArr[obIndex]["isSelected"] = true;
      _this.selectedWidget = _this.dashboardArr[obIndex];
      _this.widgetList.push(_this.dashboardArr[obIndex]);
    }
    var fieldWalkProcess = _this.processList.find(f => f.name == "Field Walk");
    if (fieldWalkProcess) {
      _this.fieldWalkProcess = fieldWalkProcess;
      _this.dashboardArr[fwIndex]["isEnable"] = true;
      _this.widgetList.push(_this.dashboardArr[fwIndex]);
      if(!_this.selectedWidget){
        _this.selectedProcess = fieldWalkProcess;
        _this.selectedWidget = _this.dashboardArr[fwIndex];
      }
    }
    var auditProcess = _this.processList.find(f => f.name == "Audit");
    if (auditProcess) {
      _this.auditProcess = auditProcess;
      _this.dashboardArr[auIndex]["isEnable"] = true;
      _this.widgetList.push(_this.dashboardArr[auIndex]);
      if(!_this.selectedWidget){
        _this.selectedProcess = auditProcess;
        _this.selectedWidget = _this.dashboardArr[auIndex];
      }
    }
    var hazardsProcess = _this.processList.find(f => f.name == "Hazards");
    if (hazardsProcess) {
      _this.hazardsProcess = hazardsProcess;
      _this.dashboardArr[haIndex]["isEnable"] = true;
      _this.widgetList.push(_this.dashboardArr[haIndex]);
      if(!_this.selectedWidget){
        _this.selectedProcess = hazardsProcess;
        _this.selectedWidget = _this.dashboardArr[haIndex];
      }
    }
    _this.widgetList.push(_this.dashboardArr[acIndex]);      
    if(_this.siteControl.value && _this.initFlag){
      _this.applyFilter();
    }
    _this.chartRefresh();

  }
  enableButtonAfterDelay() {
    var _this=this;
    setTimeout(() => {
      _this.showSecondButton = true;
      _this.cd.detectChanges();
    }, 2000);
    this.cd.detectChanges();
  }

  widgetClick(obj){
    var _this = this;

    _this.subProcessControl.setValue('');
    _this.subProcessControl.reset();
   
    if(obj.key == "Action"){
      // _this.goPage("action/action-list");
      // return;
      this.listType="Action"
    }
    
    if(obj.key == "Observation"){
      _this.selectedProcess = _this.observationProcess;
      this.listType="Observation"
    }
    if(obj.key == "Field Walk"){
      _this.selectedProcess = _this.fieldWalkProcess;
      this.listType="Field Walk"
    }
    if(obj.key == "Audit"){
      _this.selectedProcess = _this.auditProcess;
      this.listType="Audit"
    }
    if(obj.key == "Hazards"){
      _this.selectedProcess = _this.hazardsProcess;
      this.listType="Hazards"
    }
    _this.selectedWidget = obj;
    _this.widgetList.forEach(element => {
      if(element.key == obj.key){
        element["isSelected"] = true;
        
      }else{
        element["isSelected"] = false;
      }
    });
    _this.loadSubProcess();
    // _this.chartRefresh();
    if(obj.key == "Action"){
      _this.chartRefresh();
    }
  }
  getUserMenuConfig(){
    var _this = this
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
      setTimeout(()=>{
        _this.siteChanged();
      },1000)
    }
  }

  getDateFormat(site){
    var _this = this;
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData({ "sites": site }, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
      var listSetting = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
      if (listSetting.length > 0) {
        var settingData = listSetting[0];
          var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
          
          if(dateFormat){
            _this.commonService.dateFormat = dateFormat;
            _this._locale = dateFormat.local;
            _this._adapter.setLocale(_this._locale);
            _this.setDateFormat();
            _this.cd.detectChanges();
          }
        }else{
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
        if(dateFormat){
          _this.commonService.dateFormat = dateFormat;
          _this._locale = dateFormat.local;
          _this._adapter.setLocale(_this._locale);
          _this.setDateFormat();
          _this.cd.detectChanges();
        }
       
      }
      _this.loadingcount--
    _this.loadingfalse()
    });
  }
  onDateRangeChange(): void {
    var _this=this
      // _this.rangeFlag = "";
      _this.applyFilter();
      _this.cd.detectChanges();
    }

  actionWidget() {
    var _this = this;
    var observationIndex = _this.widgetList.findIndex(e => e.key == "Action");
    const itemsOfInterest = ['Hazards', 'Observation', 'FieldWalk', 'OFWAAudit'];
    if (observationIndex > -1) {
    _this.loadingcount++
    _this.loadingfalse()
      _this.dataService.postData({
        objectType: itemsOfInterest,
        reportingSite: this.siteControl.value ? [this.siteControl.value] : [],
        reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
        reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
        applicationId: _this.dataService.applicationId,
        startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
      },
        _this.dataService.NODE_API + '/api/service/aggregateAction').subscribe((data) => {
          _this.widgetList[observationIndex]["value"] = data[0].count.externalId;
          _this.cd.detectChanges();
          _this.loadingcount--
          _this.loadingfalse()
        });
    }
  }

  observationWidget() {
    var _this = this;

    _this.cd.detectChanges();
    var observationIndex = _this.widgetList.findIndex(e => e.key == "Observation");
    if(observationIndex > -1 && _this.observationProcess) {
      // _this.loaderFlag = true;
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData({
          reportingSite: _this.siteControl.value ? [_this.siteControl.value] : [],
          reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
          reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
          process: [{
            externalId:_this.observationProcess.externalId,
            space:_this.observationProcess.space,
          }],
          isActive: true,
          startDate: _this.startDateControl.value ? formatDate(_this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: _this.endDateControl.value ? formatDate(_this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/aggregateObservation' ).subscribe((data) => {
        _this.widgetList[observationIndex]["value"] = data[0].count.externalId;
        // _this.loaderFlag = false;
        _this.cd.detectChanges();
    _this.loadingcount--
    _this.loadingfalse()
      });
    }
  }

  hazardsWidget() {
    var _this = this;
    var hazardsIndex = _this.widgetList.findIndex(e => e.key == "Hazards");
    if(hazardsIndex > -1 && _this.hazardsProcess) {
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData({
          reportingSite: _this.siteControl.value ? [_this.siteControl.value] : [],
          reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
          reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
          process: [{
            externalId:_this.hazardsProcess.externalId,
            space:_this.hazardsProcess.space,
          }],
          isActive: true,
          startDate: _this.startDateControl.value ? formatDate(_this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: _this.endDateControl.value ? formatDate(_this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/aggregateObservation' ).subscribe((data) => {
        _this.widgetList[hazardsIndex]["value"] = data[0].count.externalId;
        _this.cd.detectChanges();
        _this.loadingcount--
    _this.loadingfalse()
      });
    }
  }

  fieldWalkWidget() {
    var _this = this;
    var fieldIndex = _this.widgetList.findIndex(e => e.key == "Field Walk");
    if(fieldIndex > -1 && _this.fieldWalkProcess) {
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData({
          reportingSite: _this.siteControl.value ? [_this.siteControl.value] : [],
          reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
          reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
          process: [{
            externalId:_this.fieldWalkProcess.externalId,
            space:_this.fieldWalkProcess.space,
          }],
          isActive: true,
          startDate: _this.startDateControl.value ? formatDate(_this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: _this.endDateControl.value ? formatDate(_this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/aggregateFieldWalk' ).subscribe((data) => {
        _this.widgetList[fieldIndex]["value"] = data[0].count.externalId;
        _this.cd.detectChanges();
        _this.loadingcount--
    _this.loadingfalse()
      });
    }
  }
  auditWidget() {
    var _this = this;
    var auditIndex = _this.widgetList.findIndex(e => e.key == "Audit");
    if(auditIndex > -1 && _this.auditProcess) {
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData({
          reportingSite: _this.siteControl.value ? [_this.siteControl.value] : [],
          reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
          reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
          process: [{
            externalId:_this.auditProcess.externalId,
            space:_this.auditProcess.space,
          }],
          startDate: _this.startDateControl.value ? formatDate(_this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: _this.endDateControl.value ? formatDate(_this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/aggregateAudit' ).subscribe((data) => {
        _this.widgetList[auditIndex]["value"] = data[0].count.externalId;
        _this.cd.detectChanges();
        _this.loadingcount--
    _this.loadingfalse()
      });
    }
  }
  
  rengeFilter(filter){
  
    this.categoryRef=true;
    this.categoryOpt.reset();
    if(filter =="Week"){
      this.endDateControl.setValue( new Date());
      this.startDateControl.setValue(new Date(new Date().setDate(new Date().getDate()-7)));
    }
    if(filter =="Month"){
      this.endDateControl.setValue( new Date());
      this.startDateControl.setValue(new Date(new Date().setMonth(new Date().getMonth()-1)));
    }
    if(filter =="Quarter"){
      this.endDateControl.setValue( new Date());
      this.startDateControl.setValue( new Date(new Date().setMonth(new Date().getMonth()-3)));
    }
    if(filter =="Year"){
      this.endDateControl.setValue( new Date());
      this.startDateControl.setValue(new Date(new Date().setFullYear(new Date().getFullYear()-1)));
    }
    if(filter =="YTD"){
      this.endDateControl.setValue( new Date());
      this.startDateControl.setValue(new Date(new Date().getFullYear(), 0, 1));
    }
    this.rangeFlag = filter;

switch(this.listType) {
  case "Observation":
    this.listObservation(this.listType);
    this.listLast7DaysObservation(this.listType);
    this.checklistMetics(this.listType);
    break;
  case "Hazards":
    this.listObservation(this.listType);
    this.listLast7DaysObservation(this.listType);
    this.checklistMetics(this.listType);
    break;
  case "Audit":
    this.listAudit();
    this.listLast7DaysAudit();
    break;
  case "Field Walk":
    this.listFieldWalk();
    this.listLast7DaysFieldWalk();
    break;
}
this.applyFilter();
// this.hazardsWidget()
//     this.observationWidget()
//     this.fieldWalkWidget()
//     this.auditWidget()
//     this.actionWidget()
   
  }

  loadingfalse(){
    var _this=this
    this.setDateFormat();
    console.log("Loading LOG count",_this.loadingcount)
    if (_this.loadingcount<=1){
      console.log("loading false")
      _this.loaderFlag=false
      _this.cd.detectChanges()
    }
    else {
      _this.loaderFlag=true
      _this.showSecondButton = false;
      _this.cd.detectChanges();
    }
  }
  ngAfterViewInit(): void {
    var _this = this;
    
    _this.translate.use(this.commonService.selectedLanguage);
    _this.ngZone.run(() => {
      // _this.translate.onLangChange.subscribe(() => { 
      //   // By Categories
      //   _this.chartOptionsCategory = {
      //     ... _this.chartOptionsCategory,
      //     yaxis:{
      //       title:{
      //         text: _this.labels['chartsCount'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     }
      //   }
      //   // By Location
      //   _this.chartOptions = {
      //     ... _this.chartOptions,
      //     yaxis:{
      //       title:{
      //         text: _this.labels['chartsCount'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     }
      //   }
      //   // By Unit
      //   _this.chartOptionsUnit = {
      //     ... _this.chartOptionsUnit,
      //     yaxis:{
      //       title:{
      //         text: _this.labels['chartsCount'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     }
      //   }
      //   // By Department
      //   _this.chartOptionsDept = {
      //     ... _this.chartOptionsDept,
      //     xaxis:{
      //       ..._this.chartOptionsDept.xaxis,
      //       title:{
      //         text: _this.labels['chartsCount'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     }
      //   }
      //   // Sitewide
      //   _this.chartOptionsweek = {
      //     ... _this.chartOptionsweek,
      //     xaxis:{
      //       title:{
      //         text: _this.labels['chartsPeriod'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     },
      //     yaxis:{
      //       title:{
      //         text: _this.labels['chartsCount'],
      //         style: {
      //           fontSize: '14px',
      //           fontWeight: 'bold',
      //           color: '#D3D3D3'
      //         }
      //       }
      //     }
      //   }
      //   // Risk
      //   _this.chartOptionsUnsafe = {
      //     ..._this.chartOptionsUnsafe,
      //     xaxis: {
      //       type: "category",
      //       // categories: xvalues,
      //       title: {
      //           text: _this.labels['chartsPeriod'],
      //           style: {
      //               fontSize: '14px',
      //               fontWeight: 'bold',
      //               color: '#D3D3D3'
      //           }
      //       }
      //     },
      //     yaxis: {
      //         title: {
      //             text: _this.labels['chartsCount'],
      //             style: {
      //                 fontSize: '14px',
      //                 fontWeight: 'bold',
      //                 color: '#D3D3D3'
      //             }
      //         },
      //         labels: {
      //             formatter: function (value) {
      //                 return value.toFixed(0); // Ensure values are integers
      //             }
      //         }
      //     },
      //   }
      //   // Trends
      //   _this.capacityChartOptions.xaxis = {
      //     ..._this.capacityChartOptions.xaxis,
      //     title: {
      //         text: _this.labels['chartsPeriod'],
      //         style: {
      //             fontSize: '14px',
      //             fontWeight: 'bold',
      //             color: '#D3D3D3'
      //         }
      //     }
      //   };
      //   _this.capacityChartOptions.yaxis = {
      //       ..._this.capacityChartOptions.yaxis,
      //       title: {
      //           text: _this.labels['chartsCount'],
      //           style: {
      //               fontSize: '14px',
      //               fontWeight: 'bold',
      //               color: '#D3D3D3'
      //           }
      //       },
      //     }
      //   // Participation Pie chart
      //   var locKey = [
      //     _this.labels['chartsParticipation'],
      //     _this.labels['chartsNonparticipation'],
      //   ]
      //   _this.setPercentageChartOptions(_this.locVal)
      //   console.log("update title: ",_this.chartOptionsCategory.yaxis.title.text)
      //   console.log('Update Labels: ', _this.chartOptionsPercentage.labels)
      //   _this.cd.detectChanges();
      // })
    })

    setTimeout(()=>{
      if(!this.initFlag){
        _this.applyFilter();
      }
    },1000)
    // setTimeout(()=>{
    //   _this.loaderFlag = false;
     
    // },10000)
    if (_this.commonService.filterListFetched) {
      _this.filterInit('Site');
      _this.filterInit('Unit');
    }
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      _this.filterInit(fiterType);
    });

    _this.commonService.observeListSubject.subscribe((obType: any) => {
      //   _this.filterInit(fiterType);
      var site = _this.commonService.siteList.find(
        (e) => e.externalId == _this.dataService.siteId
      );

      if (site && site['reportingLocations']['items'].length > 0) {
        _this.reportingLocationList = _.orderBy(
          site['reportingLocations']['items'],
          ['description'],
          ['asc']
        );
        _this.reportingLocationList = _.uniqBy(
          _this.reportingLocationList,
          'externalId'
        );
        // _this.filteredReportingLocationList = _this.reportingLocationList.slice();
        var observeLoc = [];
        
        if (_this.commonService.observeList.length > 0) {
          _this.reportingLocationList.forEach((element, i) => {
            var obFind = _this.commonService.observeList.filter(
              (item) =>
                item.refReportingLocation &&
                item.refReportingLocation.externalId == element.externalId
            );
            if (obFind.length > 0) {
              var obj = {
                x: element.description,
                y: obFind.length,
              };
              observeLoc.push(obj);
            }
          });
        }

        _this.chartOptionsThree = {
          series: [
            {
              data: observeLoc,
            },
          ],
          legend: {
            show: false,
          },
          chart: {
            height: 350,
            type: 'treemap',
            fontFamily: 'Myriad-Regular',
          },
          // title: {
          //   text: "Treemap with color scale"
          // },
          dataLabels: {
            enabled: true,
            offsetY: -3,
          },
          plotOptions: {
            treemap: {
              enableShades: true,
              reverseNegativeShade: true,
              colorScale: {
                ranges: [
                  {
                    from: -10,
                    to: 0,
                    color: '#FF2A38',
                  },
                  {
                    from: 0.001,
                    to: 10,
                    color: '#3C436D',
                  },
                ],
              },
            },
          },
        };
        _this.cd.detectChanges();
      }
    });
    _this.siteControl.valueChanges.subscribe((xArray) => {
      _this.dataService.siteId = xArray;
      // _this.commonService.getObservationList();
      _this.selectedWidget = undefined;
      _this.selectedProcess = undefined;
      _this.corePrinciplesControl.setValue(undefined);
      _this.getDateFormat(_this.dataService.siteId);
      _this.loadCorePrinciple(xArray);
      _this.processLoad(() => { });
      _this.siteChanged();
    });

    _this.unitControl.valueChanges.subscribe(value => {
      console.log('Value:', value);
    
      _this.processLoad(() => { });
    
      let tempArray = [];
      
      // Check if value is an array or a single value
      if (Array.isArray(value)) {
        // If value is an array, filter for each element in the array
        tempArray = _this.tempLocation.filter(item => 
          value.includes(item.reportingUnit.externalId)
        );
      } else {
        // If value is a single value, filter normally
        tempArray = _this.tempLocation.filter(item => 
          item.reportingUnit.externalId === value
        );
      }
    
      console.log('tempArray:', tempArray);
    
      _this.reportingLocationList = tempArray;
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
    
      console.log('_this.reportingLocationList:', _this.reportingLocationList);
    });
    
    if (_this.dataService.siteId) {
      _this.siteControl.setValue(_this.dataService.siteId);
      _this.getDateFormat(_this.dataService.siteId);
      _this.siteChanged();
    }
  }
  loadCorePrinciple(siteId) {
    var _this = this;
    // _this.loaderFlag = false;
    _this.commonService.getProcessConfiguration(siteId, function (data) {
        var processList = _this.commonService.processList.filter((e) => {
          return (
            e.processType == 'Core Principles' &&
            (e.refSite && e.refSite.externalId) == siteId
          );
        });
        _this.corePrinciplesList = processList;
        _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
        if(_this.corePrinciplesList.length>0){
          _this.corePrinciplesControl.setValue(_this.corePrinciplesList[0].externalId);
        }
  
    });
    if(_this.commonService.processList.length>0){
      var processList = _this.commonService.processList.filter((e) => {
        return (
          e.processType == 'Core Principles' &&
          (e.refSite && e.refSite.externalId) == siteId
        );
      });
      _this.corePrinciplesList = processList;
      _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
      if(_this.corePrinciplesList.length>0){
        _this.corePrinciplesControl.setValue(_this.corePrinciplesList[0].externalId);
      }
      
    }
  }

  async location(){
    this.dataService
    .postData(
      {},
      this.dataService.NODE_API + '/api/service/listReportingLocationAll'
    )
    .subscribe((res: any) => {
    })
  }

  filterInit(fiterType) {
    var _this = this;

    if (fiterType == 'Site') {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.commonService.siteList.slice();
      _this.commonService.observeListSubject.next(true);
    }
    if (fiterType == 'Unit') {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.commonService.unitList.slice();
    }
    //  _this.userData = _this.commonService.userInfo
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]['value'] = '';

      _this.filteredSiteList = _this.siteList.slice();
      _this.filteredUnitList = _this.unitList.slice();
      _this.filteredProcessList = _this.processList.slice();
    }
  }
  unitChanged() {
    var _this = this;

    var selectedUnit = _this.commonService.getSelectedValue(
      _this.unitControl.value
    );
    if (selectedUnit.length > 0) {
      let result = Array.isArray(selectedUnit);
      if (result == true) {
        var mySite = [];
        var finalSite = _this.commonService.siteList.filter(function (e) {
          var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
            if (selectedUnit.indexOf(f.externalId) > -1) {
              mySite.push(e['externalId']);
              return true;
            }
          });
          return siteFiltered.length > 0;
        });

        _this.filterFlag = 'Unit';
        if(mySite.length>0){
          _this.siteControl.setValue(mySite[0]);
        }
      } else {
        var mySit;

        var finalSite = _this.commonService.siteList.filter(function (e) {
          var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
            if (f.externalId == selectedUnit) {
              mySit = e.externalId;
              return true;
            }
          });
          return siteFiltered.length > 0;
        });

        _this.filterFlag = 'Unit';
        if(mySit){
          _this.siteControl.setValue(mySit);
        }
      }
    } else {
      selectedUnit = _this.unitList.map((eItem) => eItem.externalId);
    }
  }
  siteChanged() {
    var _this = this;
    var selectedSite = _this.commonService.getSelectedValue(
      _this.siteControl.value
    );
    _this.unitList = [];
    _this.filteredUnitList = _this.unitList.slice();

    if (selectedSite.length > 0) {
      var myCountries = [];
      var myRegions = [];
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e['country']) {
            myCountries.push(e['country']['externalId']);
            if (e['country']['parent'])
              myRegions.push(e['country']['parent']['externalId']);
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
      _this.filterFlag = 'Site';
    } else {
      selectedSite = _this.siteList.map((eItem) => eItem.externalId);
    }

    if (selectedSite.length > 0) {
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (
            e['reportingUnits']['items'] &&
            e['reportingUnits']['items'].length > 0
          ) {
            _this.unitList = _this.unitList.concat(
              _.orderBy(e['reportingUnits']['items'], ['name'], ['asc'])
            );
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
    } else {
      if (_this.siteList.length > 0) {
        _this.unitList = _this.commonService.unitList;
        _this.filteredUnitList = _this.unitList.slice();
      }
    }

  }

  // filterClick() {
  //   var _this = this;
  //   var filter = document.getElementsByClassName('mat-filter-input');
  //   if (filter.length > 0) {
  //     filter[0]["value"] = "";
  //     _this.filteredProcessList = _this.processList.slice();
  //    // _this.filteredSiteList = _this.siteList.slice();
  //   }
  // }
  clearFilter(filter) {
    var _this = this;
    
    _this.categoryRef=true;
    if (filter == 'All') {
      // _this.siteControl.reset();
      // _this.unitControl.reset();
      _this.rengeFilter('Week')

      // _this.startDateControl.reset();
      // _this.endDateControl.reset();
      // _this.dataService.startDate = '';
      // _this.dataService.endDate = '';
      // // _this.locationStartDateControl.reset();
      // // _this.locationEndDateControl.reset();
      // _this.rangeFlag = ""
      // _this.applyFilter();
    }

    if (filter == 'Country') {
      _this.siteControl.reset();
    }
    if (filter == 'Site') {
      _this.unitControl.reset();
    }

    if (filter == 'Unit') {
    }
    
    this.categoryOpt.reset();
    this.locationObserve.reset();
    this.unitControl.reset();
  }
  applyFilter() {
    var _this = this;
    _this.categoryRef=true;
    // setTimeout(()=>{
    //   _this.loaderFlag = false;
    // },5000)
    _this.loadSubProcess()
    _this.initFlag = true;
    _this.observationWidget();
    _this.hazardsWidget();
    _this.fieldWalkWidget();
    _this.auditWidget();
    _this.actionWidget();
   
    if(_this.selectedWidget){
      // _this.chartRefresh();
    }
    // setTimeout(()=>{
    //   _this.loaderFlag = false;
    //   console.log(_this.widgetList)
    //   console.log(_this.selectedWidget)
      
    // },8000)
  }
  chartRefresh(){
    var _this = this;
    _this.getUnits()
    _this.categoryRef=true;
    if(_this.selectedWidget.key == "Observation" || _this.selectedWidget.key == "Hazards" ){
      _this.listObservation(_this.selectedWidget.key);
      _this.listLast7DaysObservation(_this.selectedWidget.key);
      _this.checklistMetics(_this.selectedWidget.key);
      if(_this.selectedWidget.key == "Observation"){
        _this.onCorePrincipleSelected( _this.filteredProcessList.find(item => item.name === "Observation")?.externalId);
      }
      else if(_this.selectedWidget.key == "Hazards"){
        _this.onCorePrincipleSelected(_this.filteredProcessList.find(item => item.name === "Hazards")?.externalId);
      }
    }
    if(_this.selectedWidget.key == "Field Walk"){
      _this.listFieldWalk();
      _this.listLast7DaysFieldWalk();
      _this.onCorePrincipleSelected(_this.filteredProcessList.find(item => item.name === "Field Walk")?.externalId);
    }
    if(_this.selectedWidget.key == "Audit"){
      _this.onCorePrincipleSelected(_this.filteredProcessList.find(item => item.name === "Audit")?.externalId);
      _this.listAudit();
      _this.listLast7DaysAudit();
    }
    if(_this.selectedWidget.key == "Action"){
      console.log("actions here")
      _this.listActions();
    }
  }
  
  listActions(){
    var _this = this;
    var observationIndex = _this.widgetList.findIndex(e => e.key == "Action");
    const itemsOfInterest = ['Hazards', 'Observation', 'FieldWalk', 'OFWAAudit'];
    if (observationIndex > -1) {
    _this.loadingcount++
    _this.loadingfalse()
      _this.dataService.postData({
        objectType: itemsOfInterest,
        reportingSite: this.siteControl.value ? [this.siteControl.value] : [],
        reportingUnit: _this.unitControl.value ? _this.unitControl.value : [],
        reportingLocation: _this.locationObserve.value ? [_this.locationObserve.value] : [],
        applicationId: _this.dataService.applicationId,
        startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
      },
        _this.dataService.NODE_API + '/api/service/listAction').subscribe((resData) => {
          // _this.widgetList[observationIndex]["value"] = data[0].count.externalId;
          let graphDataAction=resData['data']['listAction']['items'];
          _this.listActionsGraph(graphDataAction)
          _this.cd.detectChanges();
          _this.loadingcount--
          _this.loadingfalse()
        });
    }

  }
  listActionsGraph(graphDataAction: any) {
    console.log(graphDataAction, this.rangeFlag);
    const itemsOfInterest = ['Hazards', 'Observation', 'FieldWalk', 'OFWAAudit']; // Replace with your actual item types

    const dates = new Set<string>();
    const objectTypeCounts: Record<string, Record<string, number>> = {};
  
    // Process the data
    graphDataAction.forEach(({ assignmentDate: date, objectType }) => {
      if (date && objectType && itemsOfInterest.includes(objectType)) { // Filter based on items of interest
        dates.add(date);
        objectTypeCounts[objectType] = objectTypeCounts[objectType] || {};
        objectTypeCounts[objectType][date] = (objectTypeCounts[objectType][date] || 0) + 1;
      }
    });
  
    const allDates = Array.from(dates).sort();
    const categories = this.getCategories(allDates);
  
    const seriesData = Object.keys(objectTypeCounts).map(objectType => ({
      name: objectType,
      data: categories.map(category => 
        this.rangeFlag === 'Week' || this.rangeFlag === 'Month' 
          ? objectTypeCounts[objectType][category] || 0 
          : this.getCountForMonth(objectTypeCounts[objectType], category)
      )
    }));
  
    this.chartOptionsAction = {
      series: seriesData,
      chart: {
        height: 350,
        type: "area"
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: "smooth"
      },
      xaxis: {
        categories,
        type: this.rangeFlag === 'Week' || this.rangeFlag === 'Month' ? "datetime" : "category"
      },
      tooltip: {
        x: {
          format: this.rangeFlag === 'Week' || this.rangeFlag === 'Month' ? "dd/MM/yy" : "MMM"
        }
      }
    };
  
    this.cd.detectChanges();
  }
  
  getCategories(dates: string[]): string[] {
    if (this.rangeFlag === 'Week' || this.rangeFlag === 'Month') {
      return dates;
    }
  
    const months = new Set<string>();
    dates.forEach(date => {
      const month = new Date(date).toLocaleString('default', { month: 'short' });
      months.add(month);
    });
  
    return Array.from(months).sort(
      (a, b) => new Date(Date.parse(`${a} 1, 2021`)).getMonth() - new Date(Date.parse(`${b} 1, 2021`)).getMonth()
    );
  }
  
  getCountForMonth(countsByDate: Record<string, number>, month: string): number {
    const monthIndex = new Date(Date.parse(`${month} 1, 2021`)).getMonth();
    return Object.entries(countsByDate).reduce((total, [date, count]) => {
      return new Date(date).getMonth() === monthIndex ? total + count : total;
    }, 0);
  }
  
  
  
  

  //Observation Hazards
  listObservation(listType){
    var _this = this;
    // _this.loaderFlag = true;
    this.listType=listType
    console.log("HIIIIIIIIIIIIIIIII",listType,this.siteControl.value,this.unitControl.value)
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          listType:listType,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          category:this.categoryOpt?.value,
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/listObservation'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeObservation']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        // _this.loaderFlag = false;
        _this.observationTrends(observationList);
        _this.observationRiskBySection(observationList);
        _this.observationbyDepartment(observationList);
        _this.observationbyLocation(observationList);
        _this.observationbyCategory(observationList);
        _this.listUserRoleSite(observationList);
        _this.observationByUnit(observationList);
        _this.loadingcount--
        _this.categoryRefresh(observationList);
        _this.loadingfalse()
      });
  }

  categoryRefresh(observationList: any) {
    console.log(observationList[0]?.refCategory?.items);
    if (this.categoryRef == true) {
      const categoryMap = new Map<string, { name: string, externalId: string[] }>();
  
      observationList?.forEach((item: any) => {
        if (item?.refCategory?.items?.length > 0) {
          item.refCategory.items.forEach((category: any) => {
            const categoryName = category.name;
            const categoryExternalId = category.externalId;
  
            if (categoryName) {
              if (!categoryMap.has(categoryName)) {
                categoryMap.set(categoryName, { name: categoryName, externalId: categoryExternalId });
              }
            }
          });
        }
      });
  
      this.categoryList = Array.from(categoryMap.values());
  
      // If categoryList is empty, push a default "No items" entry
      if (this.categoryList.length === 0) {
        this.categoryList = [{ name: 'No items', externalId: [] }];
      }
  
      this.filteredCategoryList = this.categoryList;
      this.cd.detectChanges();
    }
  }
  

  listUserRoleSite(observationList) {
    var _this = this;
    _this.dataService.postData({
      sites: this.siteControl.value ? [this.siteControl.value] : [],
    }, _this.dataService.NODE_API + '/api/service/listUserRoleSite'
    ).subscribe((resData: any) => {
      var userRoleList = resData;
      _this.observationbyPercentage(observationList,userRoleList)
    });
  }
 
  listLast7DaysObservation(listType){
    var _this = this;
    var lastcurrentDate=new Date();
    var lastdateInPastWeek =new Date();
    lastdateInPastWeek.setDate((lastcurrentDate.getDate() - 7))
    var lEndDate =formatDate(lastcurrentDate, 'yyyy-MM-dd', 'en-US')
    var lStartDate=formatDate(lastdateInPastWeek, 'yyyy-MM-dd', 'en-US')
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          listType:listType,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          category:this.categoryOpt?.value,
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: formatDate(lStartDate, 'yyyy-MM-dd', 'en-US'),
          endDate: formatDate(lEndDate, 'yyyy-MM-dd', 'en-US'),
        }, _this.dataService.NODE_API + '/api/service/listObservation'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeObservation']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        _this.observationRiskBehaviur(observationList);
        _this.observationSiteParticipation(observationList);
    _this.loadingcount--
    _this.loadingfalse()
      });
  }
  observationByUnit(observationList) {
    var _this = this;

// Step 1: Filter observationList by unitList
var filteredObservationList = observationList.filter(obs => 
  _this.unitList.some(unit => unit.externalId === obs.refUnit?.externalId)
);

// Step 2: Group the filtered data by refunit.description
var groupedData = _.groupBy(filteredObservationList, 'refUnit.description');

// Step 3: Prepare data for the chart
var unitKey = [];
var unitVal = [];

Object.keys(groupedData).forEach(key => {
  if (key !== 'undefined') {
    unitKey.push(key);
    unitVal.push(groupedData[key].length);
  }
});
_this.generateUniqueColors(unitKey.length, unitKey);
console.log(_this.colors);
const unitColors = {};
unitKey.forEach((key, index) => {
  unitColors[key] = _this.colors[index]; // Map colors to unit keys
});

console.log(groupedData);
    _this.chartOptionsUnit = {
      series: [
        {
          name: "Count",
          data: unitVal
        }
      ],
      chart: {
        type: "bar",
        events: {
          dataPointSelection: (event: any, chartContext: any, config: any) => {
            _this.handleBarClick("observationbyUnit", config,groupedData);
          },
          dataPointMouseEnter: function(event) {
            event.target.style.cursor = "pointer";
        }
        },
        height: 350,
        stacked: true
      },
      colors: unitKey.map(key => unitColors[key]), // Use mapped colors
      legend: {
        position: "top",
        horizontalAlign: "center",
        offsetX: 40
    },
    plotOptions: {
        bar: {
            horizontal: false,
            distributed: true,
            dataLabels: {
                position: 'top' 
            }
        }
    },
    dataLabels: {
        enabled: true,
        offsetY: -20, 
        style: {
            colors: ['#000'], 
            fontSize: '12px',
            fontWeight: 'bold'
        },
        
    },
      stroke: {
        width: 1,
        colors: ["#fff"]
      },
      title: {
        // text: "Observation count"
      },
      xaxis: {
        title: {
          text: undefined,
          style: {
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#D3D3D3'
          }
      },
        categories: unitKey,
        labels: {
          formatter: function(val) {
            return val.length > 10 ? val.substring(0, 22) + '...' : val;
          },
          style: {
            cssClass: 'apexcharts-xaxis-label',  // Apply custom class to labels
          },
        }
      },
      yaxis: {
        title: {
          text: _this.labels['chartsCount'],
          style: {
            fontSize: '14px',
            fontWeight: 'bold',
            color: '#D3D3D3'
        }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + "";
          }
        }
      },
      fill: {
        opacity: 1
      },

    };
    _this.unitColors = unitColors;
    _this.cd.detectChanges();
    console.log('unitColors',unitColors)
    _this.observationbyLocation(observationList)

  }
  unitColors: any;
  colors: any = [];
  generateUniqueColors(count,key) {
    this.colors = [];

  const usedColors = new Set(); // To track used colors

  // Function to generate a unique random color
  const getUniqueRandomColor = () => {
      let color;
      do {
          color = this.getRandomColor();
      } while (usedColors.has(color)); // Retry if color is already used
      usedColors.add(color); // Add to used colors
      return color;
  };

  // Generate colors
  console.log('key', key);
  for (let i = 0; i < count; i++) {
      const currentKey = key[i];
      if (this.convertedColors && this.convertedColors[currentKey]) {
          // Use predefined color if it exists and hasn't been used
          const color = this.convertedColors[currentKey];
          if (!usedColors.has(color)) {
              this.colors.push(color);
              usedColors.add(color);
          } else {
              this.colors.push(getUniqueRandomColor()); // Generate unique fallback color
          }
      } else {
          // Generate a unique random color for unspecified keys
          this.colors.push(getUniqueRandomColor());
      }
  }
  console.log('Generated Colors:', this.colors);
}
  
  // Function to generate a random color in hex format
  getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    console.log('color',color);
    return color;
  }
  
  observationTrends(observationList) {
    var _this = this;
    observationList = _.orderBy(observationList, ['obMonthYear'], ['asc']);
    const groupedOb = _.groupBy(observationList, (car) => car.obMonthYear);
    var obTrendArray = [];
    var obTrendMonth = [];
    var myGroupList = [];
    for (var key of Object.keys(groupedOb)) {
      var mySplit = key.split("-");
      var obj = { year : parseInt(mySplit[0]), month : parseInt(mySplit[1]), value: groupedOb[key].length }
      myGroupList.push(obj);
    }
    const sortedData = _.orderBy(myGroupList, ['year', 'month'], ['asc', 'asc']);
    _.each(sortedData, function (eData) {
      obTrendMonth.push(eData.year + "-" + eData.month);
      obTrendArray.push(eData.value);
    })
    _this.capacityChartSeries = [{
      name: _this.selectedWidget.key,
      data: obTrendArray,
    }];
    _this.capacityChartOptions =
      _this.columnChartService.getConfigureColumnChartOptions(
        '100%',
        false,
        350,
        'area',
        false,
        true,
        5,
        'smooth',
        ['#2CD889', '#FECF5C', '#F7617D'],
        ['#2CD889', '#FECF5C', '#F7617D'],
        1,
        false,
        false,
        false,
        [],
        [],
        obTrendMonth,
        true
      );


      _this.capacityChartOptions.xaxis = {
        categories: obTrendMonth,
        title: {
            text: _this.labels['chartsPeriod'],
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
            }
        }
    };
    _this.capacityChartOptions.yaxis = {
      min: 0,
      max: Math.max(...obTrendArray) + 9,
      tickAmount: 10,
        title: {
            text: _this.labels['chartsCount'],
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
            }
        },
        labels: {
          formatter: function (value) {
              return Math.floor(value);
          }
      }
  };
    _this.cd.detectChanges();
  }
  observationRiskBySection(observationList) {
    var _this = this;
    var newob = []
    observationList.forEach((element) => {
      if (element.refOFWAChecklist && element.refOFWAChecklist.items.length > 0) {
        newob = newob.concat(element.refOFWAChecklist.items)
      }
    });
    const groups = newob.reduce((groups, game) => {
      const category = game.refOFWACategory && game.refOFWACategory.name ? game.refOFWACategory.name : ''
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(game);
      return groups;
    }, {});

    // Edit: to add it in the array format instead
    var groupArrays = Object.keys(groups).map((exId) => {
      return {
        exId,
        games: groups[exId]
      };
    });
    var riskSection = []
    groupArrays.forEach((ele) => {
      var obj = {
        x: ele.exId
      }
      var unSafe = 0;
      ele.games.forEach(element => {
        if (element.isUnSafe == true) {
          unSafe++;
        }
      });
      obj['y'] = unSafe
      riskSection.push(obj)
    })
    _this.chartOptionsRiskSection = {
      series: [
        {
          data: riskSection
        }
      ],
      legend: {
        show: false
      },
      chart: {
        height: 350,
        type: "treemap"
      },
      // title: {
      //   text: "Treemap with color scale"
      // },
      dataLabels: {
        enabled: true,

        offsetY: -3
      },
      plotOptions: {
        treemap: {
          enableShades: true,
          shadeIntensity: 0.5,
          reverseNegativeShade: true,
          colorScale: {
            ranges: [
              {
                from: -6,
                to: 0,
                color: "#CD363A"
              },
              {
                from: 0.001,
                to: 6,
                color: "#52B12C"
              }
            ]
          }
        }
      }
    };
    _this.cd.detectChanges();
  }
  observationbyDepartment(observationList) {
    var _this = this;
    function groupBy(xs, f) {
      return xs.reduce((r, v, i, a, k = f(v)) => ((r[k] || (r[k] = [])).push(v), r), {});
    }
    const result = groupBy(observationList, (c) => c.refDeparment && c.refDeparment.description);
    var daptName = [];
    var deptVal = []
    for (const [key, value] of Object.entries(result)) {
      var valueArr: any = value
      if (key != 'null') {
        daptName.push(key);
        deptVal.push(valueArr.length);
      }
    }
    _this.chartOptionsDept = {
      series: [
        {
          name: "Type1",
          data: deptVal
        }
      ],
      chart: {
        type: "bar",
        events: {
          dataPointSelection: (event: any, chartContext: any, config: any) => {
            this.handleBarClick("observationbyDepartment", config, result);
          },
          dataPointMouseEnter: function(event) {
            event.target.style.cursor = "pointer";
        }
        },
        height: 350,
        stacked: true
      },
      plotOptions: {
        bar: {
          horizontal: true
        }
      },
      stroke: {
        width: 1,
        colors: ["#fff"]
      },
      title: {},
      xaxis: {
        title: {
          text: _this.labels['chartsCount'],
          style: {
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#D3D3D3'
          }
      },
        categories: daptName,
        labels: {
          formatter: function (val) {
            return val;
          }
        }
      },
      yaxis: {
        title: {
          text: undefined
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + "";
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        position: "top",
        horizontalAlign: "left",
        offsetX: 40
      }
    };
    _this.cd.detectChanges();

  }
  observationbyLocation(observationList) {
    var _this = this;
    console.log("HIIIIIIIIIIIIIIII", observationList)
    var groupedData = _.groupBy(observationList, 'refReportingLocation.description');
    var locKey = [];
    var locVal = [];
    Object.keys(groupedData).forEach(key => {
      if(key!='undefined'){
        locKey.push(key)
        locVal.push(groupedData[key].length)
      }
    });
    console.log('color', _this.unitColors)
    console.log(groupedData)
    if(_this.unitColors){
      console.log("color")
    var barColors = locKey.map(key => {
      const refUnitDesc = groupedData[key][0]?.refUnit?.description; // Get `refUnit.description` of the first element
      return _this.unitColors[refUnitDesc] || '#000'; // Use the mapped color or a default
    });
    console.log('color', barColors)
  }
    _this.chartOptions = {
      series: [
        {
          name: "Type1",
          data: locVal
        }
      ],
      chart: {
        type: "bar",
        events: {
          dataPointSelection: (event: any, chartContext: any, config: any) => {
            _this.handleBarClick("observationbyLocation", config,groupedData);
          },
          dataPointMouseEnter: function(event) {
            event.target.style.cursor = "pointer";
        }
        },
        height: 350,
        stacked: true
      },
      legend: {
        position: "top",
        horizontalAlign: "center",
        offsetX: 40
    },
    colors: barColors,
    plotOptions: {
        bar: {
            horizontal: false,
            distributed: true,
            dataLabels: {
                position: 'top' // Position data labels at the top of the bars
            }
        }
    },
    dataLabels: {
        enabled: true,
        offsetY: -20, // Adjust this value to move the labels above the bars
        style: {
            colors: ['#000'], // Color of the data labels
            fontSize: '12px',
            fontWeight: 'bold'
        },
        
    },
      stroke: {
        width: 1,
        colors: ["#fff"]
      },
      title: {
        // text: "Observation count"
      },
      xaxis: {
        title: {
          text: undefined,
          style: {
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#D3D3D3'
          }
      },
        categories: locKey,
        labels: {
          formatter: function(val) {
            return val;
          }
        }
      },
      yaxis: {
        title: {
          text: _this.labels['chartsCount'],
          style: {
            fontSize: '14px',
            fontWeight: 'bold',
            color: '#D3D3D3'
        }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + "";
          }
        }
      },
      fill: {
        opacity: 1
      },

    };
    // capacityChartOptions
    _this.cd.detectChanges();

  }

  getDescriptionById(id: string, list: { externalId: string; description: string }[]): string {
    const item = list.find(el => el.externalId === id);
    return item ? item.description : '';
  }

  // Method to get name by ID
  getNameById(id: string, list: { externalId: string; name: string }[]): string {
    const item = list.find(el => el.externalId === id);
    return item ? item.name : '';
  }
  downloadPdf() {
    // Check if the screen is in portrait mode
    if (window.matchMedia("(orientation: portrait)").matches) {
        // alert('Please turn your device to landscape mode for better viewing.');
        this.dialog.open(DialogContentComponent, {
          data: { message: 'toasterTurnlandscape' }
        });
        this.loaderFlag = false;
        return;
    }

    this.loaderFlag = true;

    // Desired screen size for PDF generation
    const targetWidth = 1920;
    const targetHeight = 1200;

    // Create an off-screen container with the desired resolution
    const container = document.createElement('div');
    container.style.position = 'fixed';
    container.style.left = '-9999px';
    container.style.top = '0';
    container.style.width = `${targetWidth}px`;
    container.style.height = `${targetHeight}px`;
    container.style.overflow = 'hidden';
    container.style.zIndex = '-1'; // Ensure it's out of the visible stack
    document.body.appendChild(container);

    // Clone the dashboard content into this container
    const dashboardElement = document.getElementById('dashboard');
    if (dashboardElement) {
        const clonedDashboard = dashboardElement.cloneNode(true) as HTMLElement;
        container.appendChild(clonedDashboard);

        // Retrieve all chart instances from the cloned content
        const chartElements = clonedDashboard.querySelectorAll('apx-chart');

        // Update chart options to fit the new container
        chartElements.forEach((chartElement: HTMLElement) => {
            this.ngZone.run(() => {
                const chartId = chartElement.querySelector('.apexcharts-canvas')?.getAttribute('id');
                if (chartId) {
                    const chartInstance = ApexCharts.getChartByID(chartId);
                    if (chartInstance) {
                        // Resize chart to fit the new resolution
                        chartInstance.updateOptions({
                            chart: {
                                width: targetWidth,
                                height: targetHeight,
                            },
                        });
                    }
                }
            });
        });
        this.cd.detectChanges();

        // Give the charts some time to resize and render in the new container
        setTimeout(() => {
            html2canvas(clonedDashboard, { scale: 2 }).then((canvas) => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');

                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();

                // Calculate the aspect ratio of the image
                const imgProps = pdf.getImageProperties(imgData);
                const imgAspectRatio = imgProps.width / imgProps.height;

                // Calculate the height of the image to fit the PDF page width
                let imgHeight = pdfWidth / imgAspectRatio;

                // Define header content with controlled spacing
                const headerMargin = 8; // Margin from the top of the page
                const lineSpacing = 7;   // Line spacing for headers
                const imageMarginTop = headerMargin + lineSpacing * 1.2 + 2; // Space between header and image
              const bottomMargin = -6; // Margin from the bottom of the page

                if (imgHeight > pdfHeight - imageMarginTop - bottomMargin) {
                  console.log("Image height")
                    imgHeight = pdfHeight - imageMarginTop - bottomMargin;
                }

              // Add the header content
              const siteId = this.siteControl.value;
              const corePrincipleId = this.corePrinciplesControl.value;
              const startDate = this.startDateControl.value ? (this.startDateControl.value._d ? this.startDateControl.value._d.toLocaleDateString() : this.startDateControl.value.toLocaleDateString()) : null;
              const endDate = this.endDateControl.value ? (this.endDateControl.value._d ? this.endDateControl.value._d.toLocaleDateString() : this.endDateControl.value.toLocaleDateString()) : null;
              const siteDescription = this.getDescriptionById(siteId, this.siteList);
              const corePrincipleDescription = this.labels['cards' + this.getNameById(corePrincipleId, this.corePrinciplesList)];

              pdf.setFontSize(12);
              pdf.text(this.labels['site'] + ': ' + siteDescription, 10, headerMargin);
              pdf.text( this.labels['corePrinciple'] + ': ' + corePrincipleDescription, 105, headerMargin);
              if (startDate && endDate) {
                  pdf.text(this.labels['startDate'] + ': ' + startDate, 10, headerMargin + lineSpacing);
                  pdf.text(this.labels['endDate'] + ': ' + endDate, 105, headerMargin + lineSpacing);
              }
              pdf.addImage(imgData, 'PNG', 0, imageMarginTop, pdfWidth, imgHeight);

                pdf.save('dashboard.pdf');

                this.loaderFlag = false;
                this.cd.detectChanges();

                // Clean up: Remove the container
                document.body.removeChild(container);
            });
        }, 1000);

    } else {
        console.error('Dashboard element not found!');
    }
}


  observationRiskBehaviur(observationList) {
    var _this = this;
    var newob = [];
    
    observationList.forEach((observation) => {
      if (observation.refOFWAChecklist && observation.refOFWAChecklist.items.length > 0) {
        observation.refOFWAChecklist.items.forEach(item => {
          newob.push({
            ...item,
            observation: observation // Keep reference to the source observation
          });
        });
      }
    });
  
    newob.forEach(element => {
      element.createdDate = formatDate(element.lastUpdatedTime, 'yyyy-MM-dd', 'en-US');
    });
  
    const groups = newob.reduce((groups, item) => {
      const date = item.createdDate;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(item);
      return groups;
    }, {});
  
    // Ensure all 7 days are present
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const formattedDate = date.toISOString().split('T')[0]; // Assuming date format is 'YYYY-MM-DD'
  
      if (!groups[formattedDate]) {
        groups[formattedDate] = [];
      }
  
      last7Days.push(formattedDate);
    }
  
    var groupArrays = last7Days.map((date) => {
      const games = groups[date];
      const listdata = [...new Set(games.map(game => game.observation))].filter((item: any) => item.observationStatus === 'Unsafe');
  
      var unsafeCount = 0;
      listdata.forEach((observation: {observationStatus: any}) => {
        if (observation.observationStatus === 'Unsafe') {
          unsafeCount++;
        }
      });
  
      return {
        date,
        games,
        listdata,
        unSafeCount: unsafeCount
      };
    });
  
    groupArrays = groupArrays.sort(function compare(a, b) {
      var dateA: any = new Date(a.date);
      var dateB: any = new Date(b.date);
      return dateA - dateB;
    });
  
    console.log("groupArrays", groupArrays, observationList);
    
    var xvalues = groupArrays.map(item => item['date']);
    var yvalues = groupArrays.map(item => item['unSafeCount']);
  
    console.log("X values:", xvalues);
    console.log("Y values:", yvalues);

    var maxy=10+Math.max(...yvalues);
    _this.chartOptionsUnsafe = {
      series: [
        {
          name: "At Risk",
          data: yvalues
        }
      ],
      chart: {
        type: "bar",
        events: {
          dataPointSelection: (event, chartContext, config) => {
            this.handleBarClick("observationRiskBehaviur", config, groupArrays);
          },
          dataPointMouseEnter: function(event) {
            event.target.style.cursor = "pointer";
          }
        },
        height: 350,
        stacked: true,
        toolbar: {
          show: true
        },
        zoom: {
          enabled: true
        }
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              position: "bottom",
              offsetX: -10,
              offsetY: 0
            }
          }
        }
      ],
      plotOptions: {
        bar: {
          horizontal: false
        }
      },
      xaxis: {
        type: "category",
        categories: xvalues,
        title: {
          text: _this.labels['chartsPeriod'],
          style: {
            fontSize: '14px',
            fontWeight: 'bold',
            color: '#D3D3D3'
          }
        }
      },
      yaxis: {
        min: 0,
        max: maxy,
        tickAmount: 10,
        title: {
            text: _this.labels['chartsCount'],
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#D3D3D3'
            }
        },
        labels: {
          formatter: function (value) {
            return value.toFixed(0); // Ensure values are integers
          }
        }
      },
      legend: {
        position: "right",
        offsetY: 40
      },
      fill: {
        opacity: 1
      }
    };
  
    _this.cd.detectChanges();
  }
  


observationSiteParticipation(observationList: any) {
  var _this = this;
  var newob = [];
  console.log("newob", observationList);

  // Copy elements and ensure date is set correctly
  observationList.forEach((element) => {
    const newElement = { ...element };
    newElement.date = element.date ? element.date : element.createdTime;
    newob.push(newElement);
  });

  console.log("newob", newob);

  // Group data by date and count occurrences
  const groupedData = newob.reduce((acc, cur) => {
    if (!acc[cur.date]) {
      acc[cur.date] = { count: 1, items: [cur] };
    } else {
      acc[cur.date].count++;
      acc[cur.date].items.push(cur);
    }
    return acc;
  }, {});

  // Ensure all 7 days are present
  const last7Days = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const formattedDate = date.toISOString().split('T')[0]; // Assuming date format is 'YYYY-MM-DD'

    if (!groupedData[formattedDate]) {
      groupedData[formattedDate] = { count: 0, items: [] };
    }

    last7Days.push(formattedDate);
  }

  // Map grouped data to the format needed for the chart
  const datadate = last7Days.map((date) => ({
    date: date,
    count: groupedData[date].count
  }));
  const maxcountvalue = Math.max(...Object.values(groupedData).map((item: { count: number }) => item.count));
  var maxy=10+maxcountvalue;

  // Set chart options
  _this.chartOptionsweek = {
    series: [
      {
        name: _this.selectedWidget.key,
        data: datadate.map((item) => item.count)
      }
    ],
    chart: {
      type: "bar",
      events: {
        dataPointSelection: (event: any, chartContext: any, config: any) => {
          this.handleBarClick("observationSiteParticipation", config, groupedData);
        },
        dataPointMouseEnter: function (event) {
          event.target.style.cursor = "pointer";
        }
      },
      height: 350,
      stacked: true,
      toolbar: {
        show: true
      },
      zoom: {
        enabled: true
      }
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          legend: {
            position: "bottom",
            offsetX: -10,
            offsetY: 0
          }
        }
      }
    ],
    plotOptions: {
      bar: {
        horizontal: false
      }
    },
    xaxis: {
      type: "category",
      categories: datadate.map((item) => item.date),
      title: {
        text: 'Period',
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          color: '#D3D3D3'
        }
      }
    },
    yaxis: {
      min: 0,
      max: maxy,
      tickAmount: 10,
      title: {
        text: 'Count',
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          color: '#D3D3D3'
        }
      },
      labels: {
        // formatter: function (value) {
        //   return value.toFixed(0);
        // }
      }
    },
    legend: {
      position: "right",
      offsetY: 40
    },
    fill: {
      opacity: 1
    }
  };

  _this.cd.detectChanges();
}


  //Field Walk
  listFieldWalk(){
    var _this = this;
    // _this.loaderFlag = true;
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          category:this.categoryOpt?.value,
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/listFieldWalk'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeFieldWalk']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        // _this.loaderFlag = false;
        _this.observationTrends(observationList);
        // _this.observationRiskBySection(observationList); // Not Applicaple
        // _this.observationbyDepartment(observationList); // Not Applicaple
        _this.observationbyLocation(observationList);
        _this.listUserRoleSite(observationList);
        _this.categoryRefresh(observationList);
        _this.loadingcount--
        _this.loadingfalse()
      });
  }

  listLast7DaysFieldWalk(){
    var _this = this;
    var lastcurrentDate=new Date();
    var lastdateInPastWeek =new Date();
    lastdateInPastWeek.setDate((lastcurrentDate.getDate() - 7))
    var lEndDate =formatDate(lastcurrentDate, 'yyyy-MM-dd', 'en-US')
    var lStartDate=formatDate(lastdateInPastWeek, 'yyyy-MM-dd', 'en-US')
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          category:this.categoryOpt?.value,
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: formatDate(lStartDate, 'yyyy-MM-dd', 'en-US'),
          endDate: formatDate(lEndDate, 'yyyy-MM-dd', 'en-US'),
        }, _this.dataService.NODE_API + '/api/service/listFieldWalk'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeFieldWalk']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.date);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        // _this.observationRiskBehaviur(observationList); // Not Applicaple
        _this.observationSiteParticipation(observationList);
        _this.loadingcount--
        _this.loadingfalse()
      });
  }


  //Audit
  listAudit(){
    var _this = this;
    // _this.loaderFlag = true;
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
          endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
        }, _this.dataService.NODE_API + '/api/service/listAudit'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeAudit']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.createdTime);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        // _this.loaderFlag = false;
        _this.observationTrends(observationList);
        // _this.observationRiskBySection(observationList); // NA (No Risk checklist)
        // _this.observationbyDepartment(observationList); // NA (No Department)
        // _this.observationbyLocation(observationList); // NA (No Reporting Location)
        _this.observationbyCategoryAudit(observationList);
        _this.listUserRoleSite(observationList);
        _this.loadingcount--
        _this.loadingfalse()
      });
  }

  listLast7DaysAudit(){
    var _this = this;
    var lastcurrentDate=new Date();
    var lastdateInPastWeek =new Date();
    lastdateInPastWeek.setDate((lastcurrentDate.getDate() - 7))
    var lEndDate =formatDate(lastcurrentDate, 'yyyy-MM-dd', 'en-US')
    var lStartDate=formatDate(lastdateInPastWeek, 'yyyy-MM-dd', 'en-US')
    _this.loadingcount++
    _this.loadingfalse()
    _this.dataService.postData( {
          limit: 1000,
          sites: this.siteControl.value ? [this.siteControl.value] : [],
          units: this.unitControl.value ? this.unitControl.value : [],
          location: this.locationObserve.value ? this.locationObserve.value : "",
          process: [_this.selectedProcess.externalId],
          subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
          startDate: formatDate(lStartDate, 'yyyy-MM-dd', 'en-US'),
          endDate: formatDate(lEndDate, 'yyyy-MM-dd', 'en-US'),
        }, _this.dataService.NODE_API + '/api/service/listAudit'
      ).subscribe((resData: any) => {
        var observationList = resData['data']['list' + _this.commonService.configuration['typeAudit']]['items'];
        observationList.forEach((ele) => {
          var dataVal = new Date(ele.createdTime);
          ele.obDate = dataVal.getDate();
          ele.obMonth = dataVal.getMonth() + 1;
          ele.obYear = dataVal.getFullYear();
          ele.obMonthYear = dataVal.getFullYear() + '-' + (dataVal.getMonth() + 1);
        });
        // _this.observationRiskBehaviur(observationList); (NA)
        _this.observationSiteParticipation(observationList);
        _this.loadingcount--
        _this.loadingfalse()
      });
  }

  observationbyCategory(observationList: any) {
    
    var _this = this;
    var myChecklist = [];
    var listdata = {}; // Changed to an object to store categories as keys

    observationList.forEach((element) => {
        if (element.refOFWAChecklist && element.refOFWAChecklist.items.length > 0) {
            element.refOFWAChecklist.items.forEach((element2) => {
                if (element2 && element2.refOFWACategory) { // Ensure refOFWACategory is not null
                    var categoryFilter = myChecklist.find(e => e.observationId == element.externalId && element2.refOFWACategory.name == e.refOFWACategory.name)
                    if (!categoryFilter) {
                        element2["observationId"] = element.externalId;
                        myChecklist.push(element2);
                    }
                }
            });
            myChecklist = myChecklist.concat(element.refOFWAChecklist.items);

            // Group observations by category name
            element.refOFWAChecklist.items.forEach((item) => {
              const categoryName = item.refOFWACategory ? item.refOFWACategory.name : 'Unknown';
              if (!listdata[categoryName]) {
                  listdata[categoryName] = [];
              }
              if (!listdata[categoryName].find(e => e.externalId === element.externalId)) {
                  listdata[categoryName].push(element);
              }
          });
        }
    });

    myChecklist = _.orderBy(myChecklist, ['refOFWACategory.name'], ['asc']);
    console.log(myChecklist);
    console.log("Dictionary of categories and observations:", listdata);

    const groupedData = _.groupBy(myChecklist, item => item.refOFWACategory ? item.refOFWACategory.name : 'Unknown');
    var locKey = [];
    var locVal = [];
    console.log("HIIIIIIIIIIIIIII", groupedData);
    Object.keys(listdata).forEach(key => {
        if (key !== 'undefined') {
            locKey.push(key);
            locVal.push(listdata[key].length);
        }
    });
    
    _this.chartOptionsCategory = {
        series: [
            {
                name: "Count",
                data: locVal
            }
        ],
        chart: {
            type: "bar",
            events: {
                dataPointSelection: (event: any, chartContext: any, config: any) => {
                    this.handleBarClick("observationbyCategory", config, listdata);
                },
                dataPointMouseEnter: function(event) {
                  event.target.style.cursor = "pointer";
              }
            },
            height: 350
        },
        xaxis: {
            categories: locKey,
        // tickPlacement: "on",
            labels: {
                rotate: -45 // Rotate labels by -45 degrees
            }
        },
        yaxis: {
            title: {
                text: _this.labels['chartsCount'],
                style: {
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#D3D3D3'
                }
            }
        },
        stroke: {
            width: 1,
            colors: ["#fff"]
        },
        title: {
            // text: "Observation count"
        },
      
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + "";
                }
            }
        },
        fill: {
            opacity: 1
        },
        legend: {
            position: "top",
            horizontalAlign: "left",
            offsetX: 40
        },
        plotOptions: {
            bar: {
                horizontal: false,
                dataLabels: {
                    position: 'top' // Position data labels at the top of the bars
                }
            }
        },
        dataLabels: {
            enabled: true,
            offsetY: -20, // Adjust this value to move the labels above the bars
            style: {
                colors: ['#000'], // Color of the data labels
                fontSize: '12px',
                fontWeight: 'bold'
            },
            
        }
    };
    
    _this.cd.detectChanges();
}
  observationbyCategoryAudit(observationList: any) {
    
    var _this = this;
    var myChecklist=[]
       observationList.forEach((element) => {
        if(element.refOFWASchedule && element.refOFWASchedule.refOFWACategory){
          myChecklist.push(element.refOFWASchedule.refOFWACategory);
        }
       });
       myChecklist = _.orderBy(myChecklist, ['name'], ['asc']);
       const groupedData = _.groupBy(myChecklist, 'name');
       var locKey = [];
       var locVal = [];
       Object.keys(groupedData).forEach(key => {
         if(key!='undefined'){
           locKey.push(key)
           locVal.push(groupedData[key].length)
         }
       });
       
    _this.chartOptionsCategory = {
      series: [
        {
          name: "Count",
          data: locVal
        }
      ],
      chart: {
        type: "bar",
        events: {
          dataPointSelection: (event: any, chartContext: any, config: any) => {
            this.handleBarClick("observationbyCategoryAudit", config);
          },
          dataPointMouseEnter: function(event) {
            event.target.style.cursor = "pointer";
        }
        },
        height: 350
      },
      xaxis: {
        categories: locKey,
        // tickPlacement: "on",
        labels: {
          rotate: -45 // Rotate labels by -45 degrees
        }
      },
      yaxis: {
        title: {
          text: undefined
        }
      },
      stroke: {
        width: 1,
        colors: ["#fff"]
      },
      title: {
        // text: "Observation count"
      },
      
      tooltip: {
        y: {
          formatter: function(val) {
            return val + "";
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        position: "top",
        horizontalAlign: "left",
        offsetX: 40
      }
    };
    
    _this.cd.detectChanges();
  }

  observationbyPercentage(observationList: any,userRoleList:any) {
    
    var _this = this;
    var locKey = [
      _this.labels['chartsParticipation'],
      _this.labels['chartsNonparticipation'],
    ]
    const groupedData = _.groupBy(observationList, 'createdBy');
    const keys = Object.keys(groupedData);
      //  const percentage = (keys.length / 50) * 100;
       _this.locVal = [keys.length,userRoleList.length];
      //  set%(locVal) declar locVal as global variable and call the same on lang change

      _this.setPercentageChartOptions(_this.locVal);
  
    _this.chartOptionsPercentage = {
      series: _this.locVal,
      chart: {
        height: 350,
        type: "pie"
      },
      labels: locKey,
      // theme: {
      //   monochrome: {
      //     enabled: true
      //   }
      // },
      colors: ['#008FFB', '#aeaeae'],
      title: {
        // text: "Number of leads"
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200
            },
            legend: {
              position: "bottom"
            }
          }
        }
      ],
      tooltip: {
        y: {
          formatter: function(val, { seriesIndex }) {
            return locKey[seriesIndex] + ': ' + val;
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        show: true,
        position: 'bottom',
        formatter: function(seriesName, opts) {
          return locKey[opts.seriesIndex];
        }
      }
    };
    _this.enableButtonAfterDelay()
    _this.cd.detectChanges();
  }
  goPage(page) {
    this.router.navigate([page]);
  }
  handleBarClick(chartName: string, config: any, groupedData?: any) {
    var _this=this;
    const dataPointIndex = config.dataPointIndex;
    const seriesIndex = config.seriesIndex;
    
    if (dataPointIndex === undefined) {
      console.error('dataPointIndex is undefined');
      return;
    }
    let value:any
    let yAxisValue: string;
    let data: string;
  
    switch (chartName) {
      case 'observationbyLocation':
        console.log(groupedData,this.chartOptions.xaxis.categories)
        yAxisValue = this.chartOptions.xaxis.categories[dataPointIndex];
        value= this.chartOptions.series[seriesIndex].data[dataPointIndex];
        console.log(groupedData[yAxisValue])
        data=groupedData[yAxisValue]
        break;
        case 'observationbyUnit':
          console.log(groupedData,this.chartOptions.xaxis.categories)
          yAxisValue = this.chartOptionsUnit.xaxis.categories[dataPointIndex];
          value= this.chartOptionsUnit.series[seriesIndex].data[dataPointIndex];
          data=groupedData[yAxisValue]
          break;
      case 'observationbyCategory':
        yAxisValue = this.chartOptionsCategory.xaxis.categories[dataPointIndex];
        value= this.chartOptionsCategory.series[seriesIndex].data[dataPointIndex];
        data=groupedData[yAxisValue]
        break;
      case 'observationSiteParticipation':
        yAxisValue = this.chartOptionsweek.xaxis.categories[dataPointIndex];
        value= this.chartOptionsweek.series[seriesIndex].data[dataPointIndex];
        data=groupedData[yAxisValue].items
        break;
      case 'observationRiskBehaviur':
        console.log(groupedData)
        yAxisValue=groupedData[config.dataPointIndex].date
        value=groupedData[config.dataPointIndex].unSafeCount
        data= groupedData[config.dataPointIndex].listdata
        break;
      case 'observationbyDepartment':
        yAxisValue = this.chartOptionsDept.xaxis.categories[dataPointIndex];
        value= this.chartOptionsDept.series[seriesIndex].data[dataPointIndex];
        data=groupedData[yAxisValue]
        break;
      default:
        console.error('Unknown chart name:', chartName);
        return;
    }
  
    console.log('sending this to listview', yAxisValue,value,data);
    // setTimeout(() => {
    //   // this.openPopup("yAxisValue",10);
    // }, 1000);
    this.openPopup(yAxisValue,value,data);
  }
  getUnits(){

    var _this=this
    _this.loadingcount++
    _this.loadingfalse()
    console.log(this.siteControl.value)
    var site = _this.commonService.siteList.find(e => e.externalId == this.siteControl.value)
    _this.selectedSite = site;
    _this.unitList=site["reportingUnits"]["items"]
    console.log('unitList',_this.unitList)
    _this.loadingcount--
    _this.loadingfalse()
  }
  
  loadSubProcess(){
    var _this = this;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.selectedProcess.externalId);
    })
    _this.subProcessList = childrenProcess;
    _this.filteredSubProcessList = _this.subProcessList.slice();
    if(_this.subProcessList.length < 1 && _this.siteControl.value && _this.initFlag && _this.selectedProcess.externalId){
      _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.labels['toasterNoprocess'] });
      _this.cd.detectChanges();
    }
  }
  
  openPopup(yAxisValue:any, value: any, tabledata?: any): void {
    this.ngZone.run(() => {
    var _this=this
    _this.triggerAltKey()
    var listType=this.listType
    _this.cd.detectChanges();
     if (listType != "Audit" ) {
    console.log(listType)
    const dialogRef =  this.dialog.open(BarchartdetailsComponent, {
       panelClass: 'mail-dialogdashboard', data: {yAxisValue,value,tabledata,listType}
   });
   _this.triggerAltKey()
   dialogRef.afterClosed().subscribe(result => {
    _this.cd.detectChanges();
    console.log(result)
    _this.triggerAltKey();
   })
   }})
}
  

  triggerAltKey(): void {
    const altKeyEvent = new KeyboardEvent('keydown', {
      key: 'Alt',
      code: 'AltLeft',
      keyCode: 18,
      charCode: 18,
      bubbles: true,
      cancelable: true,
      view: window
    });

    document.dispatchEvent(altKeyEvent);
  }
  setDateFormat() {
    console.log("this.commonService.dateFormat>>>>>>>>>>>>>>>>>>>>")
    console.log(this.commonService.dateFormat)
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }

  
  items: { name: string; count: number; externalIds: string[]; }[];
  createdByList: { name: string; externalIds: string[]; }[];
  filteredCreatedByList: any;
  

  checklistMetics(listType:any): void {
    var _this=this
    var lastcurrentDate=new Date();
    var lastdateInPastWeek =new Date();
    lastdateInPastWeek.setDate((lastcurrentDate.getDate() - 7))
    var lEndDate =formatDate(lastcurrentDate, 'yyyy-MM-dd', 'en-US')
    var lStartDate=formatDate(lastdateInPastWeek, 'yyyy-MM-dd', 'en-US')
    if (listType == "Observation" || listType == "Harzard") {
    var api= '/api/service/listObservation'
    }
    else if(listType == "Audit"){
     var api= '/api/service/listAudit'
    }
    _this.loadingcount++
    _this.loadingfalse()
       _this.dataService.postData({
         limit: 1000,
         listType:listType,
         sites: this.siteControl.value ? [this.siteControl.value] : [],
         units: this.unitControl.value ? this.unitControl.value : [],
         location: this.locationObserve.value ? this.locationObserve.value : "",
         category:this.categoryOpt?.value,
         process: [_this.selectedProcess.externalId],
         subProcess: _this.subProcessControl.value ? [_this.subProcessControl.value] : undefined,
         startDate: this.startDateControl.value ? formatDate(this.startDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
         endDate: this.endDateControl.value ? formatDate(this.endDateControl.value, 'yyyy-MM-dd', 'en-US') : '',
           }, _this.dataService.NODE_API + api
           ).subscribe((resData: any) => {
             
    _this.loadingcount--
    _this.loadingfalse()
    if(listType == "Observation" || listType == "Harzard"){
     var observationList = resData['data']['list' + _this.commonService.configuration['typeObservation']]['items'];
    }
    else if(listType == "Audit"){
     var observationList = resData['data']['list' + _this.commonService.configuration['typeAudit']]['items'];
    }
             // var observationList = resData['data']['list' + _this.commonService.configuration['typeObservation']]['items'];
             _this.commonService.loaderFlag = false;
             const groupedItems: any = {};
             if (observationList.length > 0) {
               var myObservation = observationList.filter(e => e.refOFWAChecklist.items.length > 0);
               console.log("myObservation", myObservation)
               _.each(myObservation, function (eData) {
                 _.each(eData.refOFWAChecklist.items, function (eChecklist) {
                   const key = _this.getDescription(eChecklist);
                   if (!groupedItems[key]) {
                     groupedItems[key] = {
                       description: key,
                       subCategoryName: eChecklist.refOFWASubCategory ? eChecklist.refOFWASubCategory.name : '',
                       categoryName: eChecklist.refOFWACategory ? eChecklist.refOFWACategory.name : '',
                       Safe: 0,
                       UnSafe: 0,
                       NotObserved: 0,
                       items: []
                     };
                   }
                   if (eChecklist.isSafe) groupedItems[key].Safe++;
                   if (eChecklist.isUnSafe) groupedItems[key].UnSafe++;
                   if (eChecklist.isNotObserved) groupedItems[key].NotObserved++;
                   groupedItems[key].items.push(eChecklist);
                 })
               })
               console.log(groupedItems)
               const categories = Object.keys(groupedItems);
               categories.sort((a, b) => a.length - b.length);
   const seriesNames = ["Safe", "UnSafe", "NotObserved"];
   const series = seriesNames.map(name => ({
     name,
     data: categories.map(category => groupedItems[category][name] || 0)
   }));
   _this.chartOptionsMetrics = {
     series,
     chart: {
       type: "bar",
       height: (200 + (categories.length * 40)),
       toolbar: {
         show: false
       }
     },
     plotOptions: {
       bar: {
        horizontal: true,
        barHeight: "10px",
         dataLabels: {
           position: "top"
         }
       }
     },
     dataLabels: {
       enabled: true,
       style: {
        fontSize: "8px",
         colors: ["#000"] // Set numbers to black color
       }
     },
     stroke: {
       show: true,
       width: 1,
       colors: ["#fff"]
     },
     xaxis: {
       categories,
       labels: {
         formatter: function (value) {
           return value.length > 22 ? value.substring(0, 22) + "..." : value;
         }
       }
     },
     legend: {
       position: "top",
       offsetX: 0,
       offsetY: 0
     },
   };
   
   
   console.log(_this.chartOptionsMetrics);
   _this.cd.detectChanges();
             }
             
           })
   }
   getDescription(item: any): string {
   
     if (item.refOFWAQuestion && item.refOFWAQuestion.description) {
       return item.refOFWAQuestion.description;
     }
     else if (item.refOFWASubCategory && item.refOFWASubCategory.name) {
       return item.refOFWASubCategory.name;
     }
     else if (item.refOFWACategory && item.refOFWACategory.name) {
       return item.refOFWACategory.name;
     }
     else {
       return ""; 
     }
   }

   site: any;
  filteredReportingLocationList: any;
  tempLocation: any;
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    console.log(_this.site)
    if (_this.site && _this.site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(_this.site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      console.log('reportingLocationList',_this.reportingLocationList)
    }
    _this.cd.detectChanges();
    cb();
  }



}


@Component({
  selector: 'dialog-content',
  template: `
    <h1 mat-dialog-title>{{ labels['toasterInfo'] }}</h1>
    <span class="subheading-1">{{ labels[data.message]  }}</span>
    <!-- <div mat-dialog-content>{{data.message}}</div> -->
    <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonOk'] }}" (buttonAction)="cancelClick()"></common-lib-button>
  `
})
export class DialogContentComponent implements OnInit {
  labels = {}
  constructor(
    private cd: ChangeDetectorRef,
    public dialogRef: MatDialogRef<DialogContentComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { message: string },
    public translationService: TranslationService,
    private commonService: CommonService,
    private languageService: LanguageService,
    private ngZone: NgZone,
    private changeDetector: ChangeDetectorRef,
  ) {
    this.labels = {
      'toasterInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterInfo'] || 'toasterInfo',
      'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
      'toasterTurnlandscape': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterTurnlandscape'] || 'toasterTurnlandscape',
    }
  }

  ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'toasterInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterInfo'] || 'toasterInfo',
          'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
          'toasterTurnlandscape': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterTurnlandscape'] || 'toasterTurnlandscape',
        }
        console.log('commonService label', _this.labels)
        
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
  }

  cancelClick(): void {
    this.dialogRef.close();
    console.log("Dialog closed");
    this.cd.detectChanges(); // This may not be necessary, but you can keep it if you want to ensure changes are detected.
  }
}
