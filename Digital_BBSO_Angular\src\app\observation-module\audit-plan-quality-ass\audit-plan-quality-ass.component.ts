import { FlatTreeControl } from '@angular/cdk/tree';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
import _ from "lodash";
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-audit-plan-quality-assessment',
  templateUrl: './audit-plan-quality-ass.component.html',
  styleUrls: ['./audit-plan-quality-ass.component.scss']
})
export class AuditPlanQualityAssComponent implements OnInit {

  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");
  checklistSubCategoryArray: any = [];
  observedForm: FormGroup;
  form1: FormGroup;
  labels = {}

  private _transformer = (node: any, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      bold: node.bold,
      id: node.id,
      question: node.question,
      level: level,
      type:node.type,
      formGroup: node.formGroup,
      checkListId: node.externalId,
      checkListSpace: node.space
    };
  };

  treeControl = new FlatTreeControl<any>(
    node => node.level,

    node => node.expandable,
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children,
  );


  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  hasChild = (_: number, node: any) => node.expandable;
  observation: any;
  selectedSite: any;
  checkListArray: any = [];
  questionList: any = [];
  auditData: any;
  loaderFlag: boolean;
  scoreData:any = [];
  scheduleCategory: any;
  checklistFlex: string="20";
  processConfig: any;
  configDetail: any;
  newConfigDetail: { safe: any; notsafe: any; notobserved: any; };
  defaultItem: any;

  constructor(private fb: FormBuilder, private dataService: DataService, private router: Router, private commonService: CommonService, private translate: TranslateService, private languageService:LanguageService, private ngZone: NgZone, private changeDetector: ChangeDetectorRef) {
    this.form1 = this.fb.group({
      generalNote: [""],
    })
    this.labels = {
      'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
      'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
      'tableheaderSatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatisfactory'] || 'tableheaderSatisfactory',
      'tableheaderUnsatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatisfactory'] || 'tableheaderUnsatisfactory',
      'tableheaderOfi': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfi'] || 'tableheaderOfi',
      'treeheaderSupplierselfcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderSupplierselfcomments'] || 'treeheaderSupplierselfcomments',
      'tableheaderOnsitevisual': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOnsitevisual'] || 'tableheaderOnsitevisual',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
      'buttonExportPDF': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonExportPDF'] || 'buttonExportPDF',
      'generalNote': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'generalNote'] || 'generalNote',
       }
    var _this = this;
    _this.loaderFlag = true;
  }

  ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
          'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
          'tableheaderSatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatisfactory'] || 'tableheaderSatisfactory',
          'tableheaderUnsatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatisfactory'] || 'tableheaderUnsatisfactory',
          'tableheaderOfi': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfi'] || 'tableheaderOfi',
          'treeheaderSupplierselfcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderSupplierselfcomments'] || 'treeheaderSupplierselfcomments',
          'tableheaderOnsitevisual': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOnsitevisual'] || 'tableheaderOnsitevisual',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
          'buttonExportPDF': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonExportPDF'] || 'buttonExportPDF',
        'generalNote': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'generalNote'] || 'generalNote',
       }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
    _this.observedForm = this.fb.group({
      locationObserve: [""],
      behalf: [""],
      datetime: ["", Validators.required],
      startTime: ["", Validators.required],
      endTime: ["", Validators.required]
    });

   }
  ngAfterViewInit(): void {

    var _this = this;
    console.log(history.state)
    if (history.state.externalId) {
      
      _this.getScheduleData(history.state.externalId)
    } else {
      _this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
      // _this.router.navigateByUrl('observations/audit-plan-quality');
    }


  }
  goPage(page) {
    this.router.navigate([page]);
  }

  goAudit(){
    this.router.navigate(['observations/list'], { state: {  listType: "Audit" } });
  }
  getScheduleData(id) {
    var _this = this;
    console.log(id)
    _this.loaderFlag = true;
    _this.dataService.postData({ externalId: id }, _this.dataService.NODE_API + "/api/service/listAudit").subscribe(data => {
      _this.auditData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
      console.log(_this.auditData)
      _this.dataService.postData({ "externalId": _this.auditData.refSubProcess.externalId }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess")
      .subscribe((resData: any) => {
        console.log(resData)
        var processConfig;
      if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
        processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
        const hardCodedConfig = _this.commonService.defaultConfigList.find(e => e.refProcess === _this.auditData.refProcess.name);
          if (hardCodedConfig) {
            const mergedConfig = _this.mergeConfigs(processConfig, hardCodedConfig);
            console.log(mergedConfig);
            _this.processConfig = mergedConfig;
          }
          _this.processConfig = processConfig;
          console.log(_this.processConfig);
          console.log(_this.commonService.defaultConfigList.find(e => e.refProcess == _this.auditData.refProcess.name))
      }else{
        processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.auditData.refProcess.name)
      }
        var configDetail = processConfig["configDetail"];
        const { high, medium, low } = configDetail;
        const { safe, notsafe, notobserved } = configDetail;
        _this.newConfigDetail = { safe, notsafe, notobserved };
        _this.defaultItem = Object.values(_this.newConfigDetail).find(item => item.isDefault);
        if (_this.defaultItem) {
          if (_this.defaultItem.field === "Safe") {
            _this.defaultItem.value = "satisfactory";
          } else if (_this.defaultItem.field === "Not Safe") {
            _this.defaultItem.value = "ofi";
          } else if (_this.defaultItem.field === "Not Observed") {
            _this.defaultItem.value = "unSatisfactory";
          }
      }
      
      // Alter true/false to "Yes"/"No"
      // for (const key in _this.defaultItem) {
      //   if(_this.defaultItem[key] =="null"){
      //     console.log("hii")
      //   }
      //     else if (_this.defaultItem[key] === true) {
      //       _this.defaultItem[key] = "Yes";
      //     } else if (_this.defaultItem[key] === false) {
      //       _this.defaultItem[key] = "No";
      //     }
      //     // else if(_this.defaultItem[key] == "null"){
      //     //   _this.defaultItem[key] = "null";
      //     // }
      // }

        _this.configDetail = processConfig["configDetail"];
      })
      _this.form1.get("generalNote").setValue(_this.auditData?.generalNote);
           
      _this.dataService.postData({ auditId: id}, _this.dataService.NODE_API + "/api/service/listScoreCard").subscribe(scoredata => {
        _this.scoreData = scoredata["data"]["list" + _this.commonService.configuration["typeScoreCard"]]["items"];
      });
      _this.dataService.postData({ schedules: [_this.auditData["refOFWASchedule"]["externalId"]] }, _this.dataService.NODE_API + "/api/service/listChecklist").subscribe(data1 => {
        _this.observation = data1["data"]["list" + _this.commonService.configuration["typeChecklist"]]["items"];
        _this.commonService.getProcessConfiguration(_this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"], function (data2) {
          var processList = _this.commonService.processList.filter(e => {
            return (e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.auditData["refOFWASchedule"].refOFWACategory["externalId"];
          })
          _this.scheduleCategory = _this.commonService.processList.find(e => e.externalId == _this.auditData["refOFWASchedule"]["refOFWACategory"]["externalId"])["refOFWAProcess"]
          if(_this.scheduleCategory && _this.scheduleCategory.name == 'Internal Audit'){
            _this.checklistFlex = "42";
          }
          
          var site = _this.commonService.siteList.find(e => e.externalId == _this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
          _this.selectedSite = site;

          _this.dataService.postData({ "externalId": _this.auditData["refOFWASchedule"].refOFWACategory["externalId"] }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
            var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
            _this.questionList = _.concat(_this.questionList, myQuestionList)
            console.log("D")
            _this.loadQuestion(processList)
          })

          // _this.loaderFlag = false;
          // _.each(processList, function (eData) {
          //   _this.selectSubCategory(eData);
          // })
        });
      })

    })
  }

  loadQuestion(processList) {
    var _this = this;
           console.log("Load Question",processList)
    _.each(processList, function (eData, index1) {
      
      if (index1 == (processList.length - 1)) {
        console.log("1")
        _this.selectSubCategory(eData, true);
        // _this.loaderFlag = false;
      } else {
        console.log("2")
        _this.selectSubCategory(eData, false);
      }
    })
  }

  selectSubCategory(item,isFinal) {
    var _this = this;
    if (item["selected"]) {
      item["selected"] = false;
    } else {
      item["selected"] = true;
    }

    if (!_this.checkListArray.find(e => e.id == item["refOFWAProcess"]["externalId"]) && item.selected == true) {
      console.log("HIIIIIIIIIIIIIIIIII",_this.defaultItem)
      var categoryFormGroup = new FormGroup({
        comment: new FormControl(''),
        note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) == false }),
        isSatisfactory: new FormControl(_this.defaultItem ? _this.defaultItem.value  : "")
      });
      var parentObj = {
        name: item["refOFWAProcess"]["name"],
        bold: true,
        id: item["refOFWAProcess"]["externalId"],
        children: [],
        formGroup: categoryFormGroup
      }
      console.log("parentObj",parentObj)
      console.log("item",item)
      _this.checkListArray.push(parentObj);
      // _this.dataSource.data.push(parentObj);
      var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == item["refOFWAProcess"]["externalId"]);
      if (myQuestions.length == 0) {
        _this.dataService.postData({ "externalId": item["refOFWAProcess"]["externalId"] }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
          var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
          _this.questionList = _.concat(_this.questionList, myQuestionList)
          _this.checkListUpdate(item,isFinal);
        })
      } else {
        _this.checkListUpdate(item,isFinal);
        _this.dataSource.data = _this.checkListArray;
      }
    } else {
      _this.checkListUpdate(item,isFinal);
    }
  }

  getColor(node: any): string {
    if (node.bold) {
      return; // Example: Red for bold and level 0
    }
    return '#017BA4'; // Default color
  }

  onCheckboxChange(a:any) {
    var _this = this;
    console.log("HIIIIIIIIIIIII",_this.newConfigDetail, a.value)
    // SAFE
    if (a.value.isSatisfactory === 'satisfactory') {
      if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
    // NOT SAFE
    else if (a.value.isSatisfactory === 'ofi') {
      if(_this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
    // NOT OBSERVED
    else if (a.value.isSatisfactory === 'unSatisfactory') {
      if(_this.newConfigDetail.notobserved.isNotes===false || _this.newConfigDetail.notobserved.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
  }

  checkListUpdate(subCategory: any,isFinal) {
    var _this = this;
    var parentIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    var myIndex = _this.checkListArray.findIndex(e => e.id == subCategory.externalId);
    if (myIndex == -1 && subCategory.selected == true) {
      var subCategoryFormGroup;
      console.log("HIIIIIIIIIIIIIIIIII",_this.observation.length)
      if (_this.observation && _this.observation.length > 0) {
        var subCategoryAns = _this.observation.find(e => e.refOFWASubCategory.externalId == subCategory.externalId && !e.refOFWAQuestion);
        console.log(subCategoryAns)
        var isSatisfactory = "";
        if (subCategoryAns) {
          if (subCategoryAns.isSatisfactory) {
            isSatisfactory = "satisfactory";
          }
          if (subCategoryAns.isOfi) {
            isSatisfactory = "ofi";
          }
          if (subCategoryAns.isUnsatisfactory) {
            isSatisfactory = "unSatisfactory";
          }
          subCategoryFormGroup = new FormGroup({
            comment: new FormControl(subCategoryAns.comment ? subCategoryAns.comment : ""),
            note: new FormControl(subCategoryAns.note),
            isSatisfactory: new FormControl(isSatisfactory)
          });
        } else {
          subCategoryFormGroup = new FormGroup({
            comment: new FormControl(''),
            note: new FormControl(""),
            isSatisfactory: new FormControl(isSatisfactory)
          });
        }
      } else {
        subCategoryFormGroup = new FormGroup({
          comment: new FormControl(''),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) == false }),
          isSatisfactory: new FormControl(_this.defaultItem ? _this.defaultItem.value  : "")
        });
      }

      _this.checkListArray[parentIndex].children.push({
        name: subCategory.name,
        bold: false,
        type:"SubCategory",
        id: subCategory.externalId,
        checkListId: subCategoryAns ? subCategoryAns.externalId:"",
        checkListSpace: subCategoryAns ? subCategoryAns.space :"",
        children: [],
        formGroup: subCategoryFormGroup
      })
      _this.checklistQuestion(subCategory,isFinal);
    }else{
      _this.checklistQuestion(subCategory,isFinal);
    }
    _this.dataSource.data = _this.checkListArray;
    // this.dataSource.data = this.checkListArray;
  }

  checklistQuestion(subCategory: any,isFinal) {
    var _this = this;
    console.log("isFinal",isFinal)
    var categoryIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    if (categoryIndex != -1) {
      var subCategoryIndex = _this.checkListArray[categoryIndex].children.findIndex(e => e["id"] == subCategory["externalId"]);
      var myQuestion = _this.questionList.filter(e => e["refOFWASubCategory"] && e["refOFWASubCategory"]["externalId"] == subCategory["externalId"]);
      if (myQuestion.length > 0 && subCategoryIndex != -1) {
        if (_this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var subCategoryFormGroup;
            var isSatisfactory = "";
            
      console.log("HIIIIIIIIIIIIIIIIII",_this.observation.length)
            if (_this.observation && _this.observation.length > 0) {
              var questionAns = _this.observation.find(e => (e.refOFWASubCategory.externalId == subCategory.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              console.log(questionAns)
              if (questionAns) {

                if (questionAns.isSatisfactory) {
                  isSatisfactory = "satisfactory";
                }
                if (questionAns.isOfi) {
                  isSatisfactory = "ofi";
                }
                if (questionAns.isUnsatisfactory) {
                  isSatisfactory = "unSatisfactory";
                }

                subCategoryFormGroup = new FormGroup({
                  comment: new FormControl(questionAns.comment ? questionAns.comment : ""),
                  note: new FormControl(questionAns.note),
                  isSatisfactory: new FormControl(isSatisfactory)
                });
              } else {
                subCategoryFormGroup = new FormGroup({
                  comment: new FormControl(''),
                  note: new FormControl(""),
                  isSatisfactory: new FormControl(isSatisfactory)
                });
              }

            } else {
              subCategoryFormGroup = new FormGroup({
                comment: new FormControl(''),
                note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) == false }),
                isSatisfactory: new FormControl(_this.defaultItem ? _this.defaultItem.value  : "")
              });
            }



            _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].push({
              name: question["name"],
              question: question["description"],
              bold: false,
              type:"Question",
              id: question["externalId"],
              formGroup: subCategoryFormGroup,
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
            })

          })
          _this.dataSource.data = this.checkListArray;
          if (isFinal) {
            _this.loaderFlag = false;
          }
        } else {
          _this.dataSource.data = this.checkListArray;
          if (isFinal) {
            _this.loaderFlag = false;
          }
        }

      } else {
        _this.dataSource.data = this.checkListArray;
        if (isFinal) {
          _this.loaderFlag = false;
        }
      }
    } else {
      _this.dataSource.data = this.checkListArray;
      if (isFinal) {
        _this.loaderFlag = false;
      }
    }

  }

  nextClick() {
    console.log(this.dataSource.data)

    var _this = this;

    _this.loaderFlag = true;
    var questionArray = []
    _.each(this.dataSource.data, function (eCatergory) {
      var catergoryId = eCatergory.id;
      var category = _this.commonService.processList.find(e => e.externalId == catergoryId);
      _.each(eCatergory.children, function (eSubCatergory) {


        var subCategoryId = eSubCatergory.id;
        var subCategory = _this.commonService.processList.find(e => e.externalId == subCategoryId);
        var subCategoryValue = eSubCatergory.formGroup.value;
        _this.checklistSubCategoryArray.push(subCategory);
        var subCategoryAnswer = {};
        if (eSubCatergory.checkListId && eSubCatergory.checkListId.length > 0) {
          subCategoryAnswer["externalId"] = eSubCatergory.checkListId;
        }
        subCategoryAnswer["refOFWACategory"] = {
          space: category["space"],
          externalId: category["externalId"]
        }
        subCategoryAnswer["refOFWASubCategory"] = {
          space: subCategory["space"],
          externalId: subCategory["externalId"]
        }
        subCategoryAnswer["refOFWASchedule"] = {
          space: _this.auditData["refOFWASchedule"]["space"],
          externalId: _this.auditData["refOFWASchedule"]["externalId"]
        }

        subCategoryAnswer["isSatisfactory"] = false;
        subCategoryAnswer["isOfi"] = false
        subCategoryAnswer["isUnsatisfactory"] = false
        if (subCategoryValue.isSatisfactory == "satisfactory") {
          subCategoryAnswer["isSatisfactory"] = true;
        }
        if (subCategoryValue.isSatisfactory == "ofi") {
          subCategoryAnswer["isOfi"] = true;
        }
        if (subCategoryValue.isSatisfactory == "unSatisfactory") {
          subCategoryAnswer["isUnsatisfactory"] = true;
        }
        subCategoryAnswer["note"] = subCategoryValue.note;
        subCategoryAnswer["comment"] = subCategoryValue.comment;

        // questionArray.push(subCategoryAnswer);

        _.each(eSubCatergory.children, function (eQuestion) {
          var questionAnswer = {};

          var questionId = eQuestion.id;
          var question = _this.questionList.find(e => e.externalId == questionId);
          if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
            questionAnswer["externalId"] = eQuestion.checkListId
          }
          questionAnswer["refOFWACategory"] = {
            space: category["space"],
            externalId: category["externalId"]
          }
          questionAnswer["refOFWASubCategory"] = {
            space: subCategory["space"],
            externalId: subCategory["externalId"]
          }
          questionAnswer["refOFWAQuestion"] = {
            space: question["space"],
            externalId: question["externalId"]
          }
          questionAnswer["refOFWASchedule"] = {
            space: _this.auditData["refOFWASchedule"]["space"],
            externalId: _this.auditData["refOFWASchedule"]["externalId"]
          }
          var questionValue = eQuestion.formGroup.value;

          questionAnswer["isSatisfactory"] = false;
          questionAnswer["isOfi"] = false
          questionAnswer["isUnsatisfactory"] = false
          if (questionValue.isSatisfactory == "satisfactory") {
            questionAnswer["isSatisfactory"] = true;
          }
          if (questionValue.isSatisfactory == "ofi") {
            questionAnswer["isOfi"] = true;
          }
          if (questionValue.isSatisfactory == "unSatisfactory") {
            questionAnswer["isUnsatisfactory"] = true;
          }
          questionAnswer["comment"] = questionValue.comment;
          questionAnswer["note"] = questionValue.note;
          questionArray.push(questionAnswer);
        })
      })
    })
    _this.createAuditScore(questionArray);
    var postObjChecklist = {
      "type": _this.commonService.configuration["typeChecklist"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": questionArray
    }
    console.log(postObjChecklist)
    _this.dataService.postData(postObjChecklist, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = false;
      if (data["items"].length > 0) {
        _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
        _this.checklistEdgeCreation(data["items"], _this.auditData);
        // _this.router.navigate(['observations/audit-plan-file'], { state: { data: this.auditData } });

        if(_this.scheduleCategory && _this.scheduleCategory.name == 'Internal Audit'){
          _this.dataService["auditData"] = _this.auditData;
          _this.router.navigate(['observations/upload-doc'], { state: { data: this.auditData } });
        }else{
          _this.router.navigate(['observations/audit-plan-file'], { state: { data: this.auditData } });
        }
      } else {
         _this.router.navigate(['observations/list'], { state: { data: this.auditData, listType: "Observation" } });
        _this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterSomethingwentwrong'] });
      }

      // this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    })

  }

  mergeConfigs(apiConfig: any, defaultConfig: any): any {
    // Recursively merge objects
    const mergeObjects = (target: any, source: any) => {
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null) {
            if (!target[key] || typeof target[key] !== 'object') {
              target[key] = Array.isArray(source[key]) ? [] : {};
            }
            mergeObjects(target[key], source[key]);
          } else if (target[key] === undefined) {
            target[key] = source[key];
          }
        }
      }
    };
  
    const result = { ...apiConfig };
    mergeObjects(result, defaultConfig);
    if (!apiConfig.dashboardConfig || Object.keys(apiConfig.dashboardConfig).length === 0) {
      result.dashboardConfig = { ...defaultConfig.dashboardConfig };
    }
  
    if (!apiConfig.columnConfig || Object.keys(apiConfig.columnConfig).length === 0) {
      result.columnConfig = { ...defaultConfig.columnConfig };
    }
  
    return result;
  }

  createAuditScore(questionArray){
    var _this = this;

    var summaryAudit = _this.scoreData.find(e=> e.isSummaryRecord);
    var totalAudit = _this.scoreData.find(e=> !e.isSummaryRecord);
console.log(questionArray)
    var scoreObj = {
      "refOFWAAudit":{
        space: _this.auditData["space"],
        externalId: _this.auditData["externalId"]
      },
      "isSummaryRecord": true,
      "satisfactory": questionArray.filter(e=>e.isSatisfactory).length,
      "unsatisfactory": questionArray.filter(e=>e.isUnsatisfactory).length,
      "opportunityForImprovement": questionArray.filter(e=>e.isOfi).length  
    }
    // questionArray.filter(e=>e.isOfi).length/4
    if(summaryAudit){
      scoreObj["externalId"]= summaryAudit["externalId"];
    }
   
    var scoreObjNew = {
      "refOFWAAudit":{
        space: _this.auditData["space"],
        externalId: _this.auditData["externalId"]
      },
      "isSummaryRecord": false,
      "satisfactory": questionArray.filter(e=>e.isSatisfactory).length,
      "unsatisfactory": questionArray.filter(e=>e.isUnsatisfactory).length,
      "opportunityForImprovement":  questionArray.filter(e=>e.isOfi).length
    }
    if(totalAudit){
      scoreObjNew["externalId"]= totalAudit["externalId"];
    }
    var postObjScorecard = {
      "type": _this.commonService.configuration["typeScoreCard"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [scoreObj,scoreObjNew]
    }
    _this.dataService.postData(postObjScorecard, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if(data["items"].length>0){
        var auditObj = {
          "externalId":this.auditData["externalId"],
          "generalNote": _this.form1?.value?.generalNote,
          "refOFWAScorecard":{
            space: data["items"][0]["space"],
            externalId: data["items"][0]["externalId"]
          }
        }
        var postAudit = {
          "type": _this.commonService.configuration["typeAudit"],
          "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
          "unitCode": _this.commonService.configuration["allUnitCode"],
          "items": [auditObj]
        }
        _this.dataService.postData(postAudit, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      
        });
      }
    })

  }
  checklistEdgeCreation(checkListObj, fdmObj) {
    var _this = this;
    var myCheckListArray = [];
    _.each(checkListObj, function (eCheckList) {
      var edgeObj = {
        "instanceType": "edge",
        "space": fdmObj["space"],
        "externalId": fdmObj["externalId"] + "-" + eCheckList["externalId"],
        "type": {
          "space": _this.commonService.configuration["DataModelSpace"],
          "externalId": _this.commonService.configuration["typeAudit"] + ".refOFWAChecklist"
        },
        "startNode": {
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"]
        },
        "endNode": {
          "space": eCheckList["space"],
          "externalId": eCheckList["externalId"]
        }
      }
      myCheckListArray.push(edgeObj);
    });

    _this.dataService.postData(myCheckListArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

    });
  }

  // exportToPDF() {
  //   this.commonService.loaderFlag = true;
  //   const exportableElement = document.getElementById('exportable');
  //   const headingElement = document.getElementById('exportable_heading');

  //   if (!exportableElement || !headingElement) {
  //     console.error('Element not found!');
  //     return;
  //   }

  //   const pdf = new jsPDF('p', 'mm', 'a4');
  //   const pageWidth = pdf.internal.pageSize.getWidth();
  //   const pageHeight = pdf.internal.pageSize.getHeight();

  //   html2canvas(exportableElement, { scale: 2 }).then((canvas) => {
  //     const contentHeight = canvas.height * (190 / canvas.width); // Scale to fit A4 width
  //     const totalPages = Math.ceil(contentHeight / pageHeight);
  //     const imgData = canvas.toDataURL('image/png');

  //     for (let i = 0; i < totalPages; i++) {
  //       if (i > 0) pdf.addPage();

  //       // Add heading to each page
  //       html2canvas(headingElement).then((headingCanvas) => {
  //         const headingImgData = headingCanvas.toDataURL('image/png');
  //         pdf.addImage(headingImgData, 'PNG', 10, 10, pageWidth - 20, 20); // Adjust size/position as needed
  //       });

  //       const offset = i * pageHeight;
  //       pdf.addImage(
  //         imgData,
  //         'PNG',
  //         10,
  //         30, // Position below the heading
  //         pageWidth - 20,
  //         (canvas.height * (pageWidth - 20)) / canvas.width, // Maintain aspect ratio
  //         undefined,
  //         'FAST'
  //       );
  //     }

  //     pdf.save('checklist.pdf');
  //   });
  // }

  // exportToPDF() {
  //   this.commonService.loaderFlag = true;
  //   this.changeDetector.detectChanges();
  
  //   const heading = document.getElementById('exportable_heading');
  //   const content = document.getElementById('exportable_content');
  
  //   if (heading && content) {
  //     // Apply dynamic styles for rendering
  //     const treeNodes = document.getElementById('exportable_content2');
  //     treeNodes.style.alignItems = 'stretch';

  
  //     // Generate heading canvas
  //     html2canvas(heading).then((canvasHeading) => {
  //       const imgWidth = 208; // PDF width in mm
  //       const pageHeight = 290; // PDF height in mm
  //       const headingImgHeight = (canvasHeading.height * imgWidth) / canvasHeading.width;
  //       const headingDataURL = canvasHeading.toDataURL('image/png');
  
  //       const pdf = new jsPDF('p', 'mm', 'a4');
  
  //       // Add the heading to the first page
  //       pdf.addImage(headingDataURL, 'PNG', 0, 15, imgWidth, headingImgHeight);
  
  //       // Generate content canvas and handle pagination
  //       html2canvas(content).then((canvasContent) => {
  //         let contentHeightLeft = canvasContent.height;
  //         const contentImgHeight = (canvasContent.height * imgWidth) / canvasContent.width;
  //         let contentPosition = 0;
  
  //         while (contentHeightLeft > 0) {
  //           const pageCanvas = document.createElement('canvas');
  //           pageCanvas.width = canvasContent.width;
  //           pageCanvas.height = Math.min(
  //             canvasContent.height - contentPosition,
  //             canvasContent.width * (pageHeight - headingImgHeight - 10) / imgWidth
  //           );
  
  //           const pageContext = pageCanvas.getContext('2d');
  //           if (pageContext) {
  //             pageContext.drawImage(
  //               canvasContent,
  //               0,
  //               contentPosition,
  //               canvasContent.width,
  //               pageCanvas.height,
  //               0,
  //               0,
  //               pageCanvas.width,
  //               pageCanvas.height
  //             );
  
  //             const contentDataURL = pageCanvas.toDataURL('image/png');
  
  //             if (contentPosition === 0) {
  //               // First page content
  //               pdf.setFontSize(10);
  //               pdf.text(this.labels['qualityassessmentTitle'], 5, 10);
  //               pdf.addImage(
  //                 contentDataURL,
  //                 'PNG',
  //                 0,
  //                 headingImgHeight + 16,
  //                 imgWidth,
  //                 (pageCanvas.height * imgWidth) / pageCanvas.width
  //               );
  //             } else {
  //               // Subsequent pages
  //               pdf.addPage();
  //               pdf.setFontSize(10);
  //               pdf.text(this.labels['qualityassessmentTitle'], 5, 10);
  //               pdf.addImage(
  //                 headingDataURL,
  //                 'PNG',
  //                 0,
  //                 15,
  //                 imgWidth,
  //                 headingImgHeight
  //               );
  //               pdf.addImage(
  //                 contentDataURL,
  //                 'PNG',
  //                 0,
  //                 headingImgHeight + 16,
  //                 imgWidth,
  //                 (pageCanvas.height * imgWidth) / pageCanvas.width
  //               );
  //             }
  
  //             contentPosition += pageCanvas.height;
  //             contentHeightLeft -= pageCanvas.height;
  //           }
  //         }
  
  //         // Save the final PDF
  //         pdf.save('exported-file-with-heading-and-content.pdf');
  
  //         // Reset loader
  //         this.commonService.loaderFlag = false;
  //         this.changeDetector.detectChanges();
  //       });
  //     });
  //   }
  // }
  exportToPDF() {
    this.commonService.loaderFlag = true;
    this.changeDetector.detectChanges();
  
    const heading = document.getElementById('exportable_heading');
    const content = document.getElementById('exportable_content');
  
    if (heading && content) {
      const leftMargin = 10; // Left margin in mm
      const rightMargin = 10; // Right margin in mm
      const pageWidth = 210; // A4 page width in mm
      const usableWidth = pageWidth - leftMargin - rightMargin; // Width for content after margins
      const pageHeight = 297; // A4 page height in mm
  
      html2canvas(heading).then((canvasHeading) => {
        const headingImgHeight = (canvasHeading.height * usableWidth) / canvasHeading.width;
        const headingDataURL = canvasHeading.toDataURL('image/png');
  
        const pdf = new jsPDF('p', 'mm', 'a4');
  
        html2canvas(content).then((canvasContent) => {
          let contentHeightLeft = canvasContent.height;
          let contentPosition = 0;
  
          const contentImgHeight = (canvasContent.height * usableWidth) / canvasContent.width;
  
          while (contentHeightLeft > 0) {
            const pageCanvas = document.createElement('canvas');
            pageCanvas.width = canvasContent.width;
            pageCanvas.height = Math.min(
              canvasContent.height - contentPosition,
              canvasContent.width * (pageHeight - headingImgHeight - 25) / usableWidth // Account for title and margins
            );
  
            const pageContext = pageCanvas.getContext('2d');
            if (pageContext) {
              pageContext.drawImage(
                canvasContent,
                0,
                contentPosition,
                canvasContent.width,
                pageCanvas.height,
                0,
                0,
                pageCanvas.width,
                pageCanvas.height
              );
  
              const contentDataURL = pageCanvas.toDataURL('image/png');
  
              if (contentPosition === 0) {
                // First page
                pdf.setFontSize(12);
                pdf.text(this.labels['qualityassessmentTitle'], leftMargin, 10); // Add title at the top
                pdf.addImage(headingDataURL, 'PNG', leftMargin, 15, usableWidth, headingImgHeight);
                pdf.addImage(
                  contentDataURL,
                  'PNG',
                  leftMargin,
                  headingImgHeight + 15, // Align content directly below the heading
                  usableWidth,
                  (pageCanvas.height * usableWidth) / pageCanvas.width
                );
              } else {
                // Subsequent pages
                pdf.addPage();
                pdf.setFontSize(12);
                pdf.text(this.labels['qualityassessmentTitle'], leftMargin, 10); // Add title at the top
                pdf.addImage(
                  contentDataURL,
                  'PNG',
                  leftMargin,
                  15, // Start content at the top for subsequent pages
                  usableWidth,
                  (pageCanvas.height * usableWidth) / pageCanvas.width
                );
              }
  
              contentPosition += pageCanvas.height;
              contentHeightLeft -= pageCanvas.height;
            }
          }
  
          // Save the PDF
          pdf.save('exported-quality-assessment.pdf');
  
          // Reset loader
          this.commonService.loaderFlag = false;
          this.changeDetector.detectChanges();
        });
      });
    }
  }
  
  
}
