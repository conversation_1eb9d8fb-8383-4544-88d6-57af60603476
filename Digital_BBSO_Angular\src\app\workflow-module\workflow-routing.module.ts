import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainWorkflowComponent } from './main-workflow/main-workflow.component';
import { CreateWorkflowComponent } from './create-workflow/create-workflow.component';



const routes: Routes = [
    {
        path: '',
        component: MainWorkflowComponent
    },

    {
        path: 'create-workflow', component: CreateWorkflowComponent
    },

];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class WorkflowRoutingModule { }