<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="quality-section audit-plan-section">
          
            <div class="action-section-unit-section">
                <commom-label labelText="{{ labels['qualityassessmentTitle'] }}" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>
                    <div fxFlex="none" fxLayoutAlign="end">
                        <common-lib-button [className]="'cst-btn marginLeft-5'" text="{{labels['buttonExportPDF']}}" (click)="exportToPDF()"></common-lib-button>
                    </div>
            </div>

            <div id="exportable" class="quality-mat-tree">
                <div  id="exportable_heading">
                <div fxLayout="row" *ngIf="scheduleCategory" fxLayoutAlign="start center" class="listHeadBox marginTop">
                    <div [fxFlex]="checklistFlex" class="paddingLeft-10" fxLayoutAlign="start center">
                        <div class="expandHead">
                            {{ labels['formcontrolsBehaviourchecklist'] }}
                        </div>
                    </div>
                    <div fxFlex="12" class="paddingLeft-10 padding-right_10 leftBorder " fxLayoutAlign="center center">
                        <div class="expandHead">
                            <!-- {{ labels['tableheaderSatisfactory'] }} -->
                            {{configDetail && configDetail.safe.displayName  }}
                        </div>
                    </div>
                    <div fxFlex="12" class="paddingLeft-10 padding-right_10 leftBorder " fxLayoutAlign="center center">
                        <div class="expandHead">
                            <!-- {{ labels['tableheaderOfi'] }} -->
                                    {{configDetail && configDetail.notsafe.displayName }} 
                        </div>
                    </div>
                    <div fxFlex="12" class="paddingLeft-10 padding-right_10 leftBorder " fxLayoutAlign="center center">
                        <div class="expandHead">
                            <!-- {{ labels['tableheaderUnsatisfactory'] }} -->
                                    {{configDetail && configDetail.notobserved.displayName }}
                        </div>
                    </div>
                    <div fxFlex="22" *ngIf="scheduleCategory && scheduleCategory.name != 'Internal Audit'" class="paddingLeft-10 padding-right_10 leftBorder " fxLayoutAlign="center center">
                        <div class="expandHead">
                            {{ labels['treeheaderSupplierselfcomments'] }}
                        </div>
                    </div>
                    <div fxFlex="22" class="paddingLeft-10 padding-right_10 leftBorder " fxLayoutAlign="center center">
                        <div class="expandHead">
                            {{ labels['tableheaderOnsitevisual'] }}
                        </div>
                    </div>


                </div></div>
                <div id="exportable_content">
                <div *ngIf="!loaderFlag && scheduleCategory">
                    <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
                        <!-- This is the tree node template for leaf nodes -->
                        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding matTreeNodePaddingIndent="0">
                            <!-- use a disabled button to provide padding for tree leaf -->
                            <div [formGroup]="node.formGroup" fxLayout="row" fxLayoutAlign="start center"
                                [ngClass]="{'behaviour-list-tree':  node.bold == false }">

                                <div style="width: 52vw;" fxLayout="row" fxLayoutAlign="start center">
                                    <button mat-icon-button disabled></button>
                                    <span id="exportable_content2"[ngClass]="node.bold == true ? 'semi-bold' : 'caption'"
                                        style="min-width: 43px;">
                                        {{node.name}}
                                    </span>
                                    <span *ngIf="node.question" class="caption" style="padding: 4px; 
                               text-justify: inter-word; word-wrap: break-word;">
                                        {{node.question}}
                                    </span>
                                </div>

                                <!-- <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}" [checked]="true"></mat-radio-button>
                                </div>
                                <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div> -->
                                <div fxFlex="36" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-group *ngIf="node.type=='Question'" formControlName="isSatisfactory" fxFlex="100" (change)="onCheckboxChange(node.formGroup)">
                                        <div fxFlex="33" fxLayoutAlign="center center">
                                            <mat-radio-button value="satisfactory" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                        <div fxFlex="33" fxLayoutAlign="center center" >
                                            <mat-radio-button value="ofi" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                        <div fxFlex="33" fxLayoutAlign="center center">
                                            <mat-radio-button value="unSatisfactory" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                    </mat-radio-group>
                                </div>

                                <div fxFlex="22" *ngIf="node.bold == false && (scheduleCategory && scheduleCategory.name != 'Internal Audit')" fxLayoutAlign="center center">
                                    <mat-form-field *ngIf="node.type=='Question'" appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <textarea matInput formControlName="comment" cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>
                                <div fxFlex="22" class="marginLeft-5" *ngIf="node.bold == false"
                                    fxLayoutAlign="center center">
                                    <mat-form-field *ngIf="node.type=='Question'" appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <textarea matInput formControlName="note" cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>


                        </mat-tree-node>
                        <!-- This is the tree node template for expandable nodes -->
                        <mat-tree-node *matTreeNodeDef="let node;when: hasChild" matTreeNodePadding
                            matTreeNodePaddingIndent="0">
                            <div [formGroup]="node.formGroup" fxLayout="row" fxLayoutAlign="start center"
                                [ngClass]="{'behaviour-list-tree':  node.bold == false}">

                                <div style="width: 11vw;" fxLayout="row" fxLayoutAlign="start center">
                                    <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                                        <!-- <mat-icon class="mat-icon-rtl-mirror">
                           {{treeControl.isExpanded(node) ? 'expand_circle_down' : 'chevron_right'}}
                         </mat-icon> -->
                                        <div *ngIf="treeControl.isExpanded(node)">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                                viewBox="0 0 24 24" style="enable-background:new 0 0 512 512"
                                                xml:space="preserve" class="">
                                                <g>
                                                    <path
                                                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5.707 9.707-5 5a1 1 0 0 1-1.414 0l-5-5a1 1 0 0 1 1.414-1.414L12 13.586l4.293-4.293a1 1 0 0 1 1.414 1.414z"
                                                        data-name="Layer 2" fill="#1A2254" data-original="#000000"
                                                        class=""></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div *ngIf="!treeControl.isExpanded(node)">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                                viewBox="0 0 512 512" style="enable-background:new 0 0 512 512"
                                                xml:space="preserve" class="">
                                                <g>
                                                    <path
                                                        d="M256 0C114.837 0 0 114.837 0 256s114.837 256 256 256 256-114.837 256-256S397.163 0 256 0zm79.083 271.083L228.416 377.749A21.275 21.275 0 0 1 213.333 384a21.277 21.277 0 0 1-15.083-6.251c-8.341-8.341-8.341-21.824 0-30.165L289.835 256l-91.584-91.584c-8.341-8.341-8.341-21.824 0-30.165s21.824-8.341 30.165 0l106.667 106.667c8.341 8.341 8.341 21.823 0 30.165z"
                                                        fill="#1A2254" data-original="#000000" class=""></path>
                                                </g>
                                            </svg>
                                        </div>

                                    </button>

                                    <span [ngClass]="node.bold == true ? 'semi-bold' : 'caption'"
                                        style="min-width: 40px;" [ngStyle]="{'color': getColor(node)}">
                                        {{node.name}}
                                    </span>
                                </div>
                                <div fxFlex="36" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <!-- <mat-radio-group formControlName="isSatisfactory" fxFlex="100">
                                        <div fxFlex="33" fxLayoutAlign="center center">
                                            <mat-radio-button value="satisfactory" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                        <div fxFlex="33" fxLayoutAlign="center center" >
                                            <mat-radio-button value="ofi" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                        <div fxFlex="33" fxLayoutAlign="center center">
                                            <mat-radio-button value="unSatisfactory" name="{{node.id}}"></mat-radio-button>
                                        </div>
                                    </mat-radio-group> -->
                                </div>
                                <!-- <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}" [checked]="true"></mat-radio-button>
                                </div>
                                <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="12" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div> -->

                                <div fxFlex="22" *ngIf="node.bold == false " fxLayoutAlign="center center">

                                    <mat-form-field *ngIf="node.type=='Question'" appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <textarea formControlName="comment" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>

                                </div>
                                <div class="marginLeft-5" fxFlex="22" *ngIf="node.bold == false"
                                    fxLayoutAlign="center center">
                                    <mat-form-field *ngIf="node.type=='Question'" appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <textarea formControlName="note" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>


                        </mat-tree-node>
                    </mat-tree>
                </div></div>
                <form [formGroup]="form1">

                <div style="width: 100%; margin-top: 20px">
                    <div>
                        <mat-form-field appearance="outline" style="width: 100%;height: 90px;"
                            class="behav-tree-select">
                            <mat-label>{{labels['generalNote']}} </mat-label>
                            <textarea matInput id="ob_textarea" formControlName="generalNote"
                                cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                        </mat-form-field>

                    </div></div></form>

                <div class="btn-section marginTop-20">
                    <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                        (buttonAction)="goAudit()"></common-lib-button>
                    <common-lib-button [className]="'cst-btn'" text="{{ labels['next'] }}"
                        (click)="nextClick()"></common-lib-button>
                </div>
                <div fxLayout="row" class="clear"> </div>
            </div>
        </div>





    </div>
</div>
