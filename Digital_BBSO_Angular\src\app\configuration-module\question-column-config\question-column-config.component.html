<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="form-config-section">
            <div class="form-cofiq-filter-section">
                <commom-label [labelText]="'CONFIGURATION.QUESTION_CONFIG.TITLE'" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>

                 <div class="units-filter">
                    <div  class="icon-bg-box" (click)="goPage('configuration/form-list')">
                       <mat-icon class="icon-arrow">arrow_back_ios</mat-icon>
                    </div>
                 </div>
            </div>
            <div class="marginTop-20">
                <div class="marginTop"  fxLayout="row" >
                    <span class="subheading-1 regular">
                       {{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.OBSERVATION_TYPE' | translate }}
                    </span>
                 
                  </div>        
                <mat-form-field appearance="outline" >
                    
                    <mat-select [formControl]="observationForm" >
                      <mat-option value="{{item.name}}" *ngFor="let item of observationDropDown">{{item.name}}</mat-option>
                    
                    </mat-select>
                  </mat-form-field>
            </div>
            <form   [formGroup]="form" >
                <div formArrayName="rows">
                    <div  fxLayout="row wrap" fxLayoutGap="20px"  fxLayoutAlign="start center" class="leftMargin_10 marginTop-20" *ngFor="let row of form['controls']['rows']['controls']; let i = index" [formGroupName]="i">
                        <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="start center">
                            <div>
                                <span class="subheading-1">
                                    {{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.COLUMN_NAME' | translate }}
                                </span>
                            </div>
                            <div>
                
                                <mat-form-field   appearance="outline" style="width: 100%;" class="behav-tree-input">
                         
                                    <input formControlName="name" type="text" class="input"  placeholder=""  matInput >
                         
                                   </mat-form-field>
                            </div>
                        </div>
                        <div>
                            <mat-radio-group aria-label="Select an option" formControlName="status">
                                <mat-radio-button value="1" >&nbsp;&nbsp;{{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.CHECK_BOX' | translate }}</mat-radio-button>&nbsp;&nbsp;
                                <mat-radio-button value="2" >&nbsp;&nbsp;{{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.RADIO' | translate }}</mat-radio-button>&nbsp;&nbsp;            
                                <mat-radio-button value="3" >&nbsp;&nbsp;{{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.DROP_DOWN' | translate }}</mat-radio-button>&nbsp;&nbsp;
                                <mat-radio-button value="4" >&nbsp;&nbsp;{{ 'CONFIGURATION.QUESTION_CONFIG.FORM_CONTROLS.INPUT_BOX' | translate }}</mat-radio-button>&nbsp;&nbsp;
                              </mat-radio-group>
                              
                        </div>
                     
                        <common-lib-button *ngIf="i!=0" [className]="'cst-btn marginLeft-5'" [text]="'BUTTON.REMOVE'"
                              [icon]="'minus'" (buttonAction)="onDeleteRow(i)"></common-lib-button>
                           
                     </div>
        
                </div>
               
        
             </form>
          
             <div fxLayout="row" class="leftMargin_10 marginTop-20" >
                <div>
                    <common-lib-button [className]="'cst-btn marginLeft-5'" [text]="'BUTTON.ADD_MORE'"
                    [icon]="'add'" (buttonAction)="initGroup()"></common-lib-button>
                </div>
             </div>
        
             
             <div fxLayout="row" class="marginTop-20 btn-section">
                <div fxFlex="100" fxLayoutAlign="center center">
                    <common-lib-button [className]="'cst-btn cancel'" [text]="'BUTTON.CANCEL'"
                     (buttonAction)="goPage('form-list')"></common-lib-button>
                     <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.SUBMIT'"
                     (buttonAction)="saveQuestion()"></common-lib-button>
                  
                </div>
              </div>
        </div>
        

        
   

    


    </div>
  </div>

