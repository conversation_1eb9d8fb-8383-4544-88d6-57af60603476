[{"configDetail": {"art": {"field": "Art", "isEnabled": true, "displayName": "Art", "isMandatory": false, "translateKey": "art"}, "low": {"field": "Low", "isEnabled": true, "displayName": "Low", "isMandatory": false, "translateKey": "low"}, "date": {"field": "Date", "isEnabled": true, "displayName": "Date", "isMandatory": false, "translateKey": "date"}, "high": {"field": "High", "isEnabled": true, "displayName": "High", "isMandatory": false, "translateKey": "high"}, "risk": {"field": "Risk", "isEnabled": true, "displayName": "Risk", "isMandatory": false, "translateKey": "risk"}, "safe": {"field": "Safe", "notes": true, "isNotes": true, "isDefault": false, "isEnabled": true, "displayName": "Safe ", "translateKey": "safe", "isNotesMandatory": false, "isInjuryPotential": true}, "unit": {"field": "Unit", "isEnabled": true, "displayName": "Unit", "isMandatory": false, "translateKey": "unit"}, "week": {"field": "Week", "isEnabled": true, "displayName": "Week", "isMandatory": false, "translateKey": "week"}, "cause": {"field": "Cause", "isEnabled": true, "displayName": "Cause", "isMandatory": false, "translateKey": "cause"}, "floor": {"field": "Floor", "isEnabled": true, "displayName": "Floor", "isMandatory": false, "translateKey": "floor"}, "month": {"field": "Month", "isEnabled": true, "displayName": "Month", "isMandatory": false, "translateKey": "month"}, "notes": {"field": "Notes", "isEnabled": true, "displayName": "Notes", "isMandatory": false, "translateKey": "notes"}, "shift": {"field": "Shift", "isEnabled": true, "displayName": "Shift", "isMandatory": false, "translateKey": "shift"}, "assets": {"field": "Assets", "isEnabled": true, "displayName": "Test Assets", "isMandatory": false, "translateKey": "assets"}, "crafts": {"field": "Crafts", "isEnabled": true, "displayName": "Additional Observations", "isMandatory": false, "translateKey": "crafts"}, "medium": {"field": "Medium", "isEnabled": true, "displayName": "Medium", "isMandatory": false, "translateKey": "medium"}, "endTime": {"field": "End Time", "isEnabled": true, "displayName": "End Time", "isMandatory": false, "translateKey": "endTime"}, "measure": {"field": "Measure/Activity", "isEnabled": true, "displayName": "Measure/Activity", "isMandatory": false, "translateKey": "measureActivity"}, "notsafe": {"field": "Not Safe", "notes": true, "isNotes": true, "isDefault": true, "isEnabled": true, "displayName": "Not safe", "translateKey": "notSafe", "isNotesMandatory": false, "isInjuryPotential": true}, "problem": {"field": "Problem", "isEnabled": true, "displayName": "Problem", "isMandatory": false, "translateKey": "problem"}, "urgency": {"field": "Urgency", "isEnabled": true, "displayName": "Urgency", "isMandatory": false, "translateKey": "urgency"}, "activity": {"field": "Activity", "isEnabled": true, "displayName": "Activity", "isMandatory": false, "translateKey": "activity"}, "feedback": {"field": "<PERSON><PERSON><PERSON>", "isEnabled": true, "displayName": "<PERSON><PERSON><PERSON>", "isMandatory": false, "translateKey": "feedback"}, "solution": {"field": "Solution", "isEnabled": true, "displayName": "Solution", "isMandatory": false, "translateKey": "solution"}, "eventType": {"field": "Event Type", "isEnabled": true, "displayName": "Event Type", "isMandatory": false, "translateKey": "eventType"}, "signature": {"field": "Signature", "isEnabled": true, "displayName": "Signature", "isMandatory": false, "translateKey": "signature"}, "startTime": {"field": "Start Time", "isEnabled": true, "displayName": "Start Time", "isMandatory": false, "translateKey": "startTime"}, "contractor": {"field": "Contractor", "isEnabled": true, "displayName": "Contractor", "isMandatory": false, "translateKey": "contractor"}, "department": {"field": "Department", "isEnabled": true, "displayName": "Department", "isMandatory": false, "translateKey": "department"}, "notobserved": {"field": "Not Observed", "notes": true, "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Not Observed", "translateKey": "notObserved", "isNotesMandatory": false, "isInjuryPotential": false}, "projectName": {"field": "Project Name", "isEnabled": true, "displayName": "Project Name", "isMandatory": false, "translateKey": "projectName"}, "riskyAction": {"field": "Risky Action", "isEnabled": true, "displayName": "Risky Action", "isMandatory": false, "translateKey": "riskyAction"}, "timeCapture": {"field": "TimeCapture", "isEnabled": true, "displayName": "TimeCapture", "isMandatory": false, "translateKey": "timeCapture"}, "observedCraft": {"field": "Craft Observed", "isEnabled": true, "displayName": "Craft Observed", "isMandatory": false, "translateKey": "observeredCraft"}, "riskAgreement": {"field": "Risk Agreement", "isEnabled": true, "displayName": "Risk Agreement", "isMandatory": false, "translateKey": "riskAgreement"}, "safeBehaviour": {"field": "Safe Behaviour", "isEnabled": true, "displayName": "Safe Behaviour", "isMandatory": false, "translateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "injuryPotential": {"field": "Injury <PERSON>", "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Injury <PERSON>", "isMandatory": false, "translateKey": "injuryPotential", "isNotesMandatory": false}, "reasonForAction": {"field": "Reason for Action", "isEnabled": true, "displayName": "Reason for Action", "isMandatory": false, "translateKey": "reasonForAction"}, "contractorDetail": {"field": "Contractor <PERSON><PERSON>", "isEnabled": true, "displayName": "Contractor <PERSON><PERSON>", "isMandatory": false, "translateKey": "contractorDetail"}, "locationObserved": {"field": "Location Observed", "isEnabled": true, "displayName": "Location Observed", "isMandatory": false, "translateKey": "locationObserved"}, "shortDescription": {"field": "Short Description", "isEnabled": true, "displayName": "Short Description", "isMandatory": false, "translateKey": "shortDescription"}, "eventDesccription": {"field": "Event Description", "isEnabled": true, "displayName": "Event Description", "isMandatory": false, "translateKey": "eventDescription"}, "stopWorkAuthority": {"field": "Stop Work Authority", "isEnabled": true, "displayName": "Stop Work Authority", "isMandatory": false, "translateKey": "stopWorkAuthority"}, "suggestedSolution": {"field": "Suggested Solution", "isEnabled": true, "displayName": "Suggested Solution", "isMandatory": false, "translateKey": "suggestedSolution"}, "observedOnBehalfOf": {"field": "Observed on behalf of", "isEnabled": true, "displayName": "Observed on behalf of", "isMandatory": false, "translateKey": "observedOnBehalfOf"}, "operationalLearning": {"field": "Operational Learning", "isEnabled": true, "displayName": "Do you have any opportunity for Operational Learning", "isMandatory": false, "translateKey": "doYouHaveAnyOpportunityForOperationalLearning"}, "correctiveActionFlag": {"field": "Did corrective actions occur specific to the stop work event ?", "isEnabled": true, "displayName": "Did corrective actions occur specific to the stop work event ?", "isMandatory": false, "translateKey": "didCorrectiveActionsOccurSpecificToTheStopWorkEvent"}, "describeTheObservations": {"field": "Describe the Observations", "isEnabled": true, "displayName": "Describe the Observations", "isMandatory": false, "translateKey": "describeTheObservations"}, "descripeCorrectiveActionTaken": {"field": "Describe corrective action taken", "isEnabled": true, "displayName": "Describe corrective action taken", "isMandatory": false, "translateKey": "describeCorrectiveActionTaken"}, "operationalLearningDescription": {"field": "Operational Learning Description", "isEnabled": true, "displayName": "Describe the Operational Learning opportunities you found", "isMandatory": false, "translateKey": "describeTheOperationalLearningOpportunitiesYouFound"}, "additionalCommentsOnObservation": {"field": "Additional comments on Observation", "isEnabled": true, "displayName": "Additional comments on Observation", "isMandatory": false, "translateKey": "additionalCommentsOnObservation"}, "workOrderNumber": {"field": "Work Order Number", "isEnabled": true, "displayName": "Work Order Number", "isMandatory": false, "translateKey": "workOrderNumber"}, "observedOnBehalfOfSec": {"field": "Secondary Observed on behalf of", "isEnabled": true, "displayName": "Secondary Observed on behalf of", "isMandatory": false, "translateKey": "observedOnBehalfOfSec"}, "shiftIdentifier": {"field": "Shift Identifier", "isEnabled": true, "displayName": "Shift Identifier", "isMandatory": false, "translateKey": "shiftIdentifier"}, "checklistEnableValue": {"isEnabled": true}, "notificationEnableValue": {"isEnabled": true}, "notificationGroup": {"field": "Notification Group", "groupName": "NTFAPPGP-MADURAI", "displayName": "Notification Group", "translateKey": "notificationGroup"}}, "dashboardConfig": {"ByUnit": {"field": "By Unit", "isEnabled": true, "displayName": "By Unit", "isMandatory": false, "translateKey": "byUnit"}, "Trends": {"field": "Trends", "isEnabled": true, "displayName": "Trends", "isMandatory": false, "translateKey": "trends"}, "ByCategory": {"field": "By Category", "isEnabled": true, "displayName": "By Category", "isMandatory": false, "translateKey": "byCategory"}, "ByLocation": {"field": "By Location", "isEnabled": true, "displayName": "By Location", "isMandatory": false, "translateKey": "byLocation"}, "ByDepartment": {"field": "By Department", "isEnabled": true, "displayName": "By Department", "isMandatory": false, "translateKey": "byDepartment"}, "AtRiskbySection": {"field": "At Risk by Section", "isEnabled": true, "displayName": "At Risk by Section", "isMandatory": false, "translateKey": "atRiskBySection"}, "PercentageParticipation": {"field": "Percentage Participation", "isEnabled": true, "displayName": "Percentage Participation", "isMandatory": false, "translateKey": "percentageParticipation"}, "RiskBehavioursatlast7days": {"field": "Risk Behaviours at last 7 days", "isEnabled": true, "displayName": "Risk Behaviours at last 7 days", "isMandatory": false, "translateKey": "riskBehavioursAtLast7Days"}, "Sitewideparticipationlast7days": {"field": "Sitewide participation last 7 days", "isEnabled": true, "displayName": "Sitewide participation last 7 days", "isMandatory": false, "translateKey": "sitewideParticipationLast7Days"}, "MetricsByChecklist": {"field": "Metrics By Checklist", "isEnabled": false, "displayName": "Metrics By Checklist", "isMandatory": false, "translateKey": "metricsByChecklist"}}, "columnConfig": {"art": {"field": "Art", "isEnabled": true, "translateKey": "art", "sequence": 1, "displayName": "Art"}, "date": {"field": "Date", "isEnabled": true, "translateKey": "date", "sequence": 2, "displayName": "Date"}, "site": {"field": "Site", "isEnabled": true, "translateKey": "site", "sequence": 3, "displayName": "Site"}, "unit": {"field": "Unit", "isEnabled": true, "translateKey": "unit", "sequence": 4, "displayName": "Unit"}, "week": {"field": "Week", "isEnabled": true, "translateKey": "week", "sequence": 5, "displayName": "Week"}, "cause": {"field": "Cause", "isEnabled": true, "translateKey": "cause", "sequence": 6, "displayName": "Cause"}, "crafts": {"field": "Craft", "isEnabled": true, "translateKey": "craft", "sequence": 7, "displayName": "Craft"}, "projectName": {"field": "ProjectName", "isEnabled": true, "translateKey": "projectName", "sequence": 8, "displayName": "ProjectName"}, "measure": {"field": "Measure", "isEnabled": true, "translateKey": "measure", "sequence": 9, "displayName": "Measure"}, "problem": {"field": "Problem", "isEnabled": true, "translateKey": "problem", "sequence": 10, "displayName": "Problem"}, "category": {"field": "Category", "isEnabled": true, "translateKey": "category", "sequence": 11, "displayName": "Category"}, "solution": {"field": "Solution", "isEnabled": true, "translateKey": "solution", "sequence": 12, "displayName": "Solution"}, "createdBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "created<PERSON>y", "sequence": 13, "displayName": "<PERSON><PERSON>"}, "externalId": {"field": "Id", "isEnabled": true, "translateKey": "id", "sequence": 14, "displayName": "Id"}, "modifiedBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedBy", "sequence": 15, "displayName": "<PERSON><PERSON>"}, "createdTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "createdOn", "sequence": 16, "displayName": "<PERSON><PERSON>"}, "description": {"field": "Description", "isEnabled": true, "translateKey": "description", "sequence": 17, "displayName": "Description"}, "subCategory": {"field": "Subcategory", "isEnabled": true, "translateKey": "subCategory", "sequence": 18, "displayName": "Subcategory"}, "lastUpdatedTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedOn", "sequence": 19, "displayName": "<PERSON><PERSON>"}, "observationType": {"field": "ObsType", "isEnabled": true, "translateKey": "observationType", "sequence": 20, "displayName": "ObsType"}, "shortDescription": {"field": "ShortDesc", "isEnabled": true, "translateKey": "shortDescription", "sequence": 21, "displayName": "ShortDesc"}, "observationStatus": {"field": "Status", "isEnabled": true, "translateKey": "status", "sequence": 22, "displayName": "Status"}, "isOperationalLearning": {"field": "OperationalLearning", "isEnabled": true, "translateKey": "operationalLearning", "sequence": 23, "displayName": "OperationalLearning"}, "operationalLearningDescription": {"field": "OperationalLearningDescription", "isEnabled": true, "translateKey": "operationalLearningDescription", "sequence": 24, "displayName": "OperationalLearningDescription"}, "location": {"field": "Location", "isEnabled": true, "translateKey": "location", "sequence": 25, "displayName": "Location"}, "workOrderNumber": {"field": "WorkOrderNumber", "isEnabled": true, "translateKey": "workOrderNumber", "sequence": 26, "displayName": "WorkOrderNumber"}, "actions": {"field": "Actions", "isEnabled": true, "translateKey": "actions", "sequence": 27, "displayName": "Actions"}}, "refProcess": "Observation"}, {"configDetail": {"art": {"field": "Art", "isEnabled": true, "displayName": "Art", "isMandatory": false, "translateKey": "art"}, "low": {"field": "Low", "isEnabled": true, "displayName": "Low", "isMandatory": false, "translateKey": "low"}, "workOrderNumber": {"field": "Work Order Number", "isEnabled": true, "displayName": "Work Order Number", "isMandatory": false, "translateKey": "workOrderNumber"}, "date": {"field": "Date", "isEnabled": true, "displayName": "Date", "isMandatory": false, "translateKey": "date"}, "high": {"field": "High", "isEnabled": true, "displayName": "High", "isMandatory": false, "translateKey": "high"}, "risk": {"field": "Risk", "isEnabled": true, "displayName": "Risk", "isMandatory": false, "translateKey": "risk"}, "safe": {"field": "Safe", "notes": true, "isNotes": true, "isDefault": false, "isEnabled": true, "displayName": "Safe ", "translateKey": "safe", "isNotesMandatory": false, "isInjuryPotential": true}, "unit": {"field": "Unit", "isEnabled": true, "displayName": "Unit", "isMandatory": false, "translateKey": "unit"}, "week": {"field": "Week", "isEnabled": true, "displayName": "Week", "isMandatory": false, "translateKey": "week"}, "cause": {"field": "Cause", "isEnabled": true, "displayName": "Cause", "isMandatory": false, "translateKey": "cause"}, "floor": {"field": "Floor", "isEnabled": true, "displayName": "Floor", "isMandatory": false, "translateKey": "floor"}, "month": {"field": "Month", "isEnabled": true, "displayName": "Month", "isMandatory": false, "translateKey": "month"}, "notes": {"field": "Notes", "isEnabled": true, "displayName": "Notes", "isMandatory": false, "translateKey": "notes"}, "shift": {"field": "Shift", "isEnabled": true, "displayName": "Shift", "isMandatory": false, "translateKey": "shift"}, "assets": {"field": "Assets", "isEnabled": true, "displayName": "Test Assets", "isMandatory": false, "translateKey": "assets"}, "crafts": {"field": "Crafts", "isEnabled": true, "displayName": "Additional Observations", "isMandatory": false, "translateKey": "crafts"}, "medium": {"field": "Medium", "isEnabled": true, "displayName": "Medium", "isMandatory": false, "translateKey": "medium"}, "endTime": {"field": "End Time", "isEnabled": true, "displayName": "End Time", "isMandatory": false, "translateKey": "endTime"}, "measure": {"field": "Measure/Activity", "isEnabled": true, "displayName": "Measure/Activity", "isMandatory": false, "translateKey": "measureActivity"}, "notsafe": {"field": "Not Safe", "notes": true, "isNotes": true, "isDefault": true, "isEnabled": true, "displayName": "Not safe", "translateKey": "notSafe", "isNotesMandatory": false, "isInjuryPotential": true}, "problem": {"field": "Problem", "isEnabled": true, "displayName": "Problem", "isMandatory": false, "translateKey": "problem"}, "urgency": {"field": "Urgency", "isEnabled": true, "displayName": "Urgency", "isMandatory": false, "translateKey": "urgency"}, "activity": {"field": "Activity", "isEnabled": true, "displayName": "Activity", "isMandatory": false, "translateKey": "activity"}, "feedback": {"field": "<PERSON><PERSON><PERSON>", "isEnabled": true, "displayName": "<PERSON><PERSON><PERSON>", "isMandatory": false, "translateKey": "feedback"}, "solution": {"field": "Solution", "isEnabled": true, "displayName": "Solution", "isMandatory": false, "translateKey": "solution"}, "eventType": {"field": "Event Type", "isEnabled": true, "displayName": "Event Type", "isMandatory": false, "translateKey": "eventType"}, "signature": {"field": "Signature", "isEnabled": true, "displayName": "Signature", "isMandatory": false, "translateKey": "signature"}, "startTime": {"field": "Start Time", "isEnabled": true, "displayName": "Start Time", "isMandatory": false, "translateKey": "startTime"}, "contractor": {"field": "Contractor", "isEnabled": true, "displayName": "Contractor", "isMandatory": false, "translateKey": "contractor"}, "department": {"field": "Department", "isEnabled": true, "displayName": "Department", "isMandatory": false, "translateKey": "department"}, "notobserved": {"field": "Not Observed", "notes": true, "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Not Observed", "translateKey": "notObserved", "isNotesMandatory": false, "isInjuryPotential": false}, "projectName": {"field": "Project Name", "isEnabled": true, "displayName": "Project Name", "isMandatory": false, "translateKey": "projectName"}, "riskyAction": {"field": "Risky Action", "isEnabled": true, "displayName": "Risky Action", "isMandatory": false, "translateKey": "riskyAction"}, "timeCapture": {"field": "TimeCapture", "isEnabled": true, "displayName": "TimeCapture", "isMandatory": false, "translateKey": "timeCapture"}, "observedCraft": {"field": "Craft Observed", "isEnabled": true, "displayName": "Craft Observed", "isMandatory": false, "translateKey": "observeredCraft"}, "riskAgreement": {"field": "Risk Agreement", "isEnabled": true, "displayName": "Risk Agreement", "isMandatory": false, "translateKey": "riskAgreement"}, "safeBehaviour": {"field": "Safe Behaviour", "isEnabled": true, "displayName": "Safe Behaviour", "isMandatory": false, "translateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "injuryPotential": {"field": "Injury <PERSON>", "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Injury <PERSON>", "isMandatory": false, "translateKey": "injuryPotential", "isNotesMandatory": false}, "reasonForAction": {"field": "Reason for Action", "isEnabled": true, "displayName": "Reason for Action", "isMandatory": false, "translateKey": "reasonForAction"}, "contractorDetail": {"field": "Contractor <PERSON><PERSON>", "isEnabled": true, "displayName": "Contractor <PERSON><PERSON>", "isMandatory": false, "translateKey": "contractorDetail"}, "locationObserved": {"field": "Location Observed", "isEnabled": true, "displayName": "Location Observed", "isMandatory": false, "translateKey": "locationObserved"}, "shortDescription": {"field": "Short Description", "isEnabled": true, "displayName": "Short Description", "isMandatory": false, "translateKey": "shortDescription"}, "eventDesccription": {"field": "Event Description", "isEnabled": true, "displayName": "Event Description", "isMandatory": false, "translateKey": "eventDescription"}, "stopWorkAuthority": {"field": "Stop Work Authority", "isEnabled": true, "displayName": "Stop Work Authority", "isMandatory": false, "translateKey": "stopWorkAuthority"}, "suggestedSolution": {"field": "Suggested Solution", "isEnabled": true, "displayName": "Suggested Solution", "isMandatory": false, "translateKey": "suggestedSolution"}, "observedOnBehalfOf": {"field": "Observed on behalf of", "isEnabled": true, "displayName": "Observed on behalf of", "isMandatory": false, "translateKey": "observedOnBehalfOf"}, "operationalLearning": {"field": "Operational Learning", "isEnabled": true, "displayName": "Do you have any opportunity for Operational Learning", "isMandatory": false, "translateKey": "doYouHaveAnyOpportunityForOperationalLearning"}, "correctiveActionFlag": {"field": "Did corrective actions occur specific to the stop work event ?", "isEnabled": true, "displayName": "Did corrective actions occur specific to the stop work event ?", "isMandatory": false, "translateKey": "didCorrectiveActionsOccurSpecificToTheStopWorkEvent"}, "describeTheObservations": {"field": "Describe the Observations", "isEnabled": true, "displayName": "Describe the Observations", "isMandatory": false, "translateKey": "describeTheObservations"}, "descripeCorrectiveActionTaken": {"field": "Describe corrective action taken", "isEnabled": true, "displayName": "Describe corrective action taken", "isMandatory": false, "translateKey": "describeCorrectiveActionTaken"}, "operationalLearningDescription": {"field": "Operational Learning Description", "isEnabled": true, "displayName": "Describe the Operational Learning opportunities you found", "isMandatory": false, "translateKey": "describeTheOperationalLearningOpportunitiesYouFound"}, "additionalCommentsOnObservation": {"field": "Additional comments on Observation", "isEnabled": true, "displayName": "Additional comments on Observation", "isMandatory": false, "translateKey": "additionalCommentsOnObservation"}, "observedOnBehalfOfSec": {"field": "Secondary Observed on behalf of", "isEnabled": true, "displayName": "Secondary Observed on behalf of", "isMandatory": false, "translateKey": "observedOnBehalfOfSec"}, "checklistEnableValue": {"isEnabled": true}, "notificationEnableValue": {"isEnabled": true}, "notificationGroup": {"field": "Notification Group", "groupName": "NTFAPPGP-MADURAI", "displayName": "Notification Group", "translateKey": "notificationGroup"}}, "dashboardConfig": {"ByUnit": {"field": "By Unit", "isEnabled": true, "displayName": "By Unit", "isMandatory": false, "translateKey": "byUnit"}, "Trends": {"field": "Trends", "isEnabled": true, "displayName": "Trends", "isMandatory": false, "translateKey": "trends"}, "ByCategory": {"field": "By Category", "isEnabled": true, "displayName": "By Category", "isMandatory": false, "translateKey": "byCategory"}, "ByLocation": {"field": "By Location", "isEnabled": true, "displayName": "By Location", "isMandatory": false, "translateKey": "byLocation"}, "ByDepartment": {"field": "By Department", "isEnabled": true, "displayName": "By Department", "isMandatory": false, "translateKey": "byDepartment"}, "AtRiskbySection": {"field": "At Risk by Section", "isEnabled": true, "displayName": "At Risk by Section", "isMandatory": false, "translateKey": "atRiskBySection"}, "PercentageParticipation": {"field": "Percentage Participation", "isEnabled": true, "displayName": "Percentage Participation", "isMandatory": false, "translateKey": "percentageParticipation"}, "RiskBehavioursatlast7days": {"field": "Risk Behaviours at last 7 days", "isEnabled": true, "displayName": "Risk Behaviours at last 7 days", "isMandatory": false, "translateKey": "riskBehavioursAtLast7Days"}, "Sitewideparticipationlast7days": {"field": "Sitewide participation last 7 days", "isEnabled": true, "displayName": "Sitewide participation last 7 days", "isMandatory": false, "translateKey": "sitewideParticipationLast7Days"}, "MetricsByChecklist": {"field": "Metrics By Checklist", "isEnabled": false, "displayName": "Metrics By Checklist", "isMandatory": false, "translateKey": "metricsByChecklist"}}, "columnConfig": {"art": {"field": "Art", "isEnabled": true, "translateKey": "art", "sequence": 1, "displayName": "Art"}, "date": {"field": "Date", "isEnabled": true, "translateKey": "date", "sequence": 2, "displayName": "Date"}, "site": {"field": "Site", "isEnabled": true, "translateKey": "site", "sequence": 3, "displayName": "Site"}, "unit": {"field": "Unit", "isEnabled": true, "translateKey": "unit", "sequence": 4, "displayName": "Unit"}, "week": {"field": "Week", "isEnabled": true, "translateKey": "week", "sequence": 5, "displayName": "Week"}, "cause": {"field": "Cause", "isEnabled": true, "translateKey": "cause", "sequence": 6, "displayName": "Cause"}, "crafts": {"field": "Craft", "isEnabled": true, "translateKey": "craft", "sequence": 7, "displayName": "Craft"}, "projectName": {"field": "ProjectName", "isEnabled": true, "translateKey": "projectName", "sequence": 8, "displayName": "ProjectName"}, "measure": {"field": "Measure", "isEnabled": true, "translateKey": "measure", "sequence": 9, "displayName": "Measure"}, "problem": {"field": "Problem", "isEnabled": true, "translateKey": "problem", "sequence": 10, "displayName": "Problem"}, "category": {"field": "Category", "isEnabled": true, "translateKey": "category", "sequence": 11, "displayName": "Category"}, "solution": {"field": "Solution", "isEnabled": true, "translateKey": "solution", "sequence": 12, "displayName": "Solution"}, "createdBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "created<PERSON>y", "sequence": 13, "displayName": "<PERSON><PERSON>"}, "externalId": {"field": "Id", "isEnabled": true, "translateKey": "id", "sequence": 14, "displayName": "Id"}, "modifiedBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedBy", "sequence": 15, "displayName": "<PERSON><PERSON>"}, "createdTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "createdOn", "sequence": 16, "displayName": "<PERSON><PERSON>"}, "description": {"field": "Description", "isEnabled": true, "translateKey": "description", "sequence": 17, "displayName": "Description"}, "subCategory": {"field": "Subcategory", "isEnabled": true, "translateKey": "subCategory", "sequence": 18, "displayName": "Subcategory"}, "lastUpdatedTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedOn", "sequence": 19, "displayName": "<PERSON><PERSON>"}, "observationType": {"field": "ObsType", "isEnabled": true, "translateKey": "observationType", "sequence": 20, "displayName": "ObsType"}, "shortDescription": {"field": "ShortDesc", "isEnabled": true, "translateKey": "shortDescription", "sequence": 21, "displayName": "ShortDesc"}, "observationStatus": {"field": "Status", "isEnabled": true, "translateKey": "status", "sequence": 22, "displayName": "Status"}, "isOperationalLearning": {"field": "OperationalLearning", "isEnabled": true, "translateKey": "operationalLearning", "sequence": 23, "displayName": "OperationalLearning"}, "operationalLearningDescription": {"field": "OperationalLearningDescription", "isEnabled": true, "translateKey": "operationalLearningDescription", "sequence": 24, "displayName": "OperationalLearningDescription"}, "location": {"field": "Location", "isEnabled": true, "translateKey": "location", "sequence": 25, "displayName": "Location"}, "workOrderNumber": {"field": "WorkOrderNumber", "isEnabled": true, "translateKey": "workOrderNumber", "sequence": 26, "displayName": "WorkOrderNumber"}, "actions": {"field": "Actions", "isEnabled": true, "translateKey": "actions", "sequence": 27, "displayName": "Actions"}}, "refProcess": "Hazards"}, {"configDetail": {"unit": {"field": "Unit", "isEnabled": true, "displayName": "unit", "isMandatory": false, "translateKey": "unit"}, "assets": {"field": "Assets", "isEnabled": true, "displayName": "Assets", "isMandatory": false, "translateKey": "assets"}, "workOrderNumber": {"field": "Work Order Number", "isEnabled": true, "displayName": "Work Order Number", "isMandatory": false, "translateKey": "workOrderNumber"}, "endTime": {"field": "End Time", "isEnabled": true, "displayName": "End Time", "isMandatory": false, "translateKey": "endTime"}, "attendee": {"field": "Attendees", "isEnabled": true, "displayName": "Attendees", "isMandatory": false, "translateKey": "attendees"}, "datetime": {"field": "Date and Time", "isEnabled": true, "displayName": "Date and Time", "isMandatory": false, "translateKey": "dateAndTime"}, "signature": {"field": "Signature", "isEnabled": true, "displayName": "Signature", "isMandatory": false, "translateKey": "signature"}, "startTime": {"field": "Start Time", "isEnabled": true, "displayName": "Start Time", "isMandatory": false, "translateKey": "startTime"}, "whatWentWell": {"field": "What went well", "isEnabled": true, "displayName": "What went well", "isMandatory": false, "translateKey": "whatWentWell"}, "overallSummary": {"field": "Overall Summary", "isEnabled": true, "displayName": "Overall Summary", "isMandatory": false, "translateKey": "overallSummary"}, "checklistOption": "Un Safe", "locationObserve": {"field": "Location Observed", "isEnabled": true, "displayName": "Location Observed", "isMandatory": false, "translateKey": "locationObserved"}, "whatNeedsAttention": {"field": "What needs attention", "isEnabled": true, "displayName": "What needs attention", "isMandatory": false, "translateKey": "whatNeedsAttention"}, "operationalLearning": {"field": "Operational Learning", "isEnabled": true, "displayName": "Do you have any opportunity for Operational Learning", "isMandatory": false, "translateKey": "doYouHaveAnyOpportunityForOperationalLearning"}, "whatDidYouDoAboutIt": {"field": "What did you do about it", "isEnabled": true, "displayName": "What did you do about it", "isMandatory": false, "translateKey": "whatDidYouDoAboutIt"}, "defaultInjuryPotential": "No", "operationalLearningDescription": {"field": "Operational Learning Description", "isEnabled": true, "displayName": "Describe the Operational Learning opportunities you found", "isMandatory": false, "translateKey": "describeTheOperationalLearningOpportunitiesYouFound"}, "projectName": {"field": "Project Name", "isEnabled": true, "displayName": "Project Name", "isMandatory": false, "translateKey": "projectName"}, "title": {"field": "Title", "isEnabled": true, "displayName": "Title", "isMandatory": false, "translateKey": "title"}, "checklistEnableValue": {"isEnabled": true}, "notificationEnableValue": {"isEnabled": true}, "notificationGroup": {"field": "Notification Group", "groupName": "NTFAPPGP-MADURAI", "displayName": "Notification Group", "translateKey": "notificationGroup"}}, "dashboardConfig": {"ByUnit": {"field": "By Unit", "isEnabled": true, "displayName": "By Unit", "isMandatory": false, "translateKey": "byUnit"}, "Trends": {"field": "Trends", "isEnabled": true, "displayName": "Trends", "isMandatory": false, "translateKey": "trends"}, "ByLocation": {"field": "By Location", "isEnabled": true, "displayName": "By Location", "isMandatory": false, "translateKey": "byLocation"}, "PercentageParticipation": {"field": "Percentage Participation", "isEnabled": true, "displayName": "Percentage Participation", "isMandatory": false, "translateKey": "percentageParticipation"}, "Sitewideparticipationlast7days": {"field": "Sitewide participation last 7 days", "isEnabled": true, "displayName": "Sitewide participation last 7 days", "isMandatory": false, "translateKey": "sitewideParticipationLast7Days"}}, "columnConfig": {"date": {"field": "Observationdate", "isEnabled": true, "translateKey": "observationDate", "sequence": 1, "displayName": "Observationdate"}, "site": {"field": "Site", "isEnabled": true, "translateKey": "site", "sequence": 2, "displayName": "Site"}, "unit": {"field": "Unit", "isEnabled": true, "translateKey": "unit", "sequence": 3, "displayName": "Unit"}, "projectName": {"field": "ProjectName", "isEnabled": true, "translateKey": "projectName", "sequence": 4, "displayName": "ProjectName"}, "createdBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "created<PERSON>y", "sequence": 5, "displayName": "<PERSON><PERSON>"}, "externalId": {"field": "Id", "isEnabled": true, "translateKey": "id", "sequence": 6, "displayName": "Id"}, "modifiedBy": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedBy", "sequence": 7, "displayName": "<PERSON><PERSON>"}, "createdTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "createdOn", "sequence": 8, "displayName": "<PERSON><PERSON>"}, "lastUpdatedTime": {"field": "<PERSON><PERSON>", "isEnabled": true, "translateKey": "updatedOn", "sequence": 9, "displayName": "<PERSON><PERSON>"}, "isOperationalLearning": {"field": "OperationalLearning", "isEnabled": true, "translateKey": "operationalLearning", "sequence": 10, "displayName": "OperationalLearning"}, "operationalLearningDescription": {"field": "OperationalLearningDescription", "isEnabled": false, "translateKey": "operationalLearningDescription", "sequence": 11, "displayName": "OperationalLearningDescription"}, "workOrderNumber": {"field": "WorkOrderNumber", "isEnabled": true, "translateKey": "workOrderNumber", "sequence": 12, "displayName": "WorkOrderNumber"}, "corePrinciple": {"field": "CorePrinciple", "isEnabled": true, "translateKey": "corePrinciple", "sequence": 13, "displayName": "CorePrinciple"}, "subProcess": {"field": "SubProcess", "isEnabled": true, "translateKey": "subProcess", "sequence": 14, "displayName": "SubProcess"}, "title": {"field": "Title", "isEnabled": true, "translateKey": "title", "sequence": 15, "displayName": "Title"}, "actions": {"field": "Actions", "isEnabled": true, "translateKey": "actions", "sequence": 16, "displayName": "Actions"}}, "refProcess": "Field Walk"}, {"configDetail": {"WBA": {"field": "Who is being Audited", "isEnabled": true, "displayName": "Who is being Audited", "isMandatory": false, "translateKey": "whoIsBeingAudited"}, "low": {"field": "Low", "isEnabled": true, "displayName": "Low", "isMandatory": false, "translateKey": "low"}, "WABA": {"field": "Which Activity is being Audited", "isEnabled": true, "displayName": "Which Activity is being Audited", "isMandatory": false, "translateKey": "whichActivityIsBeingAudited"}, "date": {"field": "Date", "isEnabled": true, "displayName": "Date and Time", "isMandatory": false, "translateKey": "date"}, "high": {"field": "High", "isEnabled": true, "displayName": "High", "isMandatory": false, "translateKey": "high"}, "risk": {"field": "Risk", "isEnabled": true, "displayName": "Risk", "isMandatory": false, "translateKey": "risk"}, "safe": {"field": "Safe", "notes": true, "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Proceed", "translateKey": "safe", "isNotesMandatory": false, "isInjuryPotential": false}, "unit": {"field": "Unit", "isEnabled": true, "displayName": "Unit", "isMandatory": false, "translateKey": "unit"}, "week": {"field": "Week", "isEnabled": true, "displayName": "Week", "isMandatory": false, "translateKey": "week"}, "floor": {"field": "Floor", "isEnabled": true, "displayName": "Floor", "isMandatory": false, "translateKey": "floor"}, "month": {"field": "Month", "isEnabled": true, "displayName": "Month", "isMandatory": false, "translateKey": "month"}, "notes": {"field": "Notes", "isEnabled": true, "displayName": "Notes", "isMandatory": false, "translateKey": "notes"}, "shift": {"field": "Shift", "isEnabled": true, "displayName": "Shift", "isMandatory": false, "translateKey": "shift"}, "assets": {"field": "Assets", "isEnabled": true, "displayName": "Test Assets", "isMandatory": false, "translateKey": "assets"}, "crafts": {"field": "Crafts", "isEnabled": true, "displayName": "Additional Observations", "isMandatory": false, "translateKey": "crafts"}, "medium": {"field": "Medium", "isEnabled": true, "displayName": "Medium", "isMandatory": false, "translateKey": "medium"}, "endTime": {"field": "End Time", "isEnabled": true, "displayName": "End Time", "isMandatory": false, "translateKey": "endTime"}, "notsafe": {"field": "Not Safe", "notes": true, "isNotes": true, "isDefault": false, "isEnabled": true, "displayName": "Do not proceed", "translateKey": "notSafe", "isNotesMandatory": false, "isInjuryPotential": true}, "urgency": {"field": "Urgency", "isEnabled": true, "displayName": "Urgency", "isMandatory": false, "translateKey": "urgency"}, "activity": {"field": "Activity", "isEnabled": true, "displayName": "Activity", "isMandatory": false, "translateKey": "activity"}, "feedback": {"field": "<PERSON><PERSON><PERSON>", "isEnabled": true, "displayName": "<PERSON><PERSON><PERSON>", "isMandatory": false, "translateKey": "feedback"}, "priority": {"field": "Priority", "isEnabled": true, "displayName": "Priority", "isMandatory": false, "translateKey": "priority"}, "auditedBy": {"field": "AuditedBy", "isEnabled": true, "displayName": "AuditedBy", "isMandatory": false, "translateKey": "auditedBy"}, "eventType": {"field": "Event Type", "isEnabled": true, "displayName": "Event Type", "isMandatory": false, "translateKey": "eventType"}, "signature": {"field": "Signature", "isEnabled": true, "displayName": "Signature", "isMandatory": false, "translateKey": "signature"}, "startTime": {"field": "Start Time", "isEnabled": true, "displayName": "Start Time", "isMandatory": false, "translateKey": "startTime"}, "contractor": {"field": "Contractor", "isEnabled": true, "displayName": "Contractor", "isMandatory": false, "translateKey": "contractor"}, "department": {"field": "Department", "isEnabled": true, "displayName": "Department", "isMandatory": false, "translateKey": "department"}, "workPermit": {"field": "Work permits", "isEnabled": true, "displayName": "Work permits", "isMandatory": false, "translateKey": "workPermits"}, "notobserved": {"field": "Not Observed", "notes": true, "isNotes": false, "isDefault": false, "isEnabled": true, "displayName": "Not Observed", "translateKey": "notObserved", "isNotesMandatory": false, "isInjuryPotential": false}, "riskyAction": {"field": "Risky Action", "isEnabled": true, "displayName": "Risky Action", "isMandatory": false, "translateKey": "riskyAction"}, "timeCapture": {"field": "TimeCapture", "isEnabled": true, "displayName": "TimeCapture", "isMandatory": false, "translateKey": "timeCapture"}, "nameOfCompany": {"field": "Name of Company", "isEnabled": true, "displayName": "Name of Company", "isMandatory": false, "translateKey": "nameOfCompany"}, "observedCraft": {"field": "Observered Craft", "isEnabled": true, "displayName": "Observered Craft", "isMandatory": false, "translateKey": "observeredCraft"}, "riskAgreement": {"field": "Risk Agreement", "isEnabled": true, "displayName": "Risk Agreement", "isMandatory": false, "translateKey": "riskAgreement"}, "safeBehaviour": {"field": "Safe Behaviour", "isEnabled": true, "displayName": "Safe Behaviour", "isMandatory": false, "translateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "discussionPoint": {"field": "Discussion Point", "isEnabled": true, "displayName": "Discussion Point", "isMandatory": false, "translateKey": "discussionPoint"}, "injuryPotential": {"field": "Injury <PERSON>", "isNotes": false, "isEnabled": true, "displayName": "Injury <PERSON>", "isMandatory": false, "translateKey": "injuryPotential", "isNotesMandatory": false}, "reasonForAction": {"field": "Reason for Action", "isEnabled": true, "displayName": "Reason for Action", "isMandatory": false, "translateKey": "reasonForAction"}, "contractorDetail": {"field": "Contractor <PERSON><PERSON>", "isEnabled": true, "displayName": "Contractor <PERSON><PERSON>", "isMandatory": false, "translateKey": "contractorDetail"}, "locationObserved": {"field": "Location Observed", "isEnabled": true, "displayName": "Location Observed", "isMandatory": false, "translateKey": "locationObserved"}, "shortDescription": {"field": "Short Description", "isEnabled": true, "displayName": "Short Description", "isMandatory": false, "translateKey": "shortDescription"}, "eventDesccription": {"field": "Event Description", "isEnabled": true, "displayName": "Event Description", "isMandatory": false, "translateKey": "eventDescription"}, "stopWorkAuthority": {"field": "Stop Work Authority", "isEnabled": true, "displayName": "Stop Work Authority", "isMandatory": false, "translateKey": "stopWorkAuthority"}, "suggestedSolution": {"field": "Suggested Solution", "isEnabled": true, "displayName": "Suggested Solution", "isMandatory": false, "translateKey": "suggestedSolution"}, "workReleaseNumber": {"field": "Work Release Number", "isEnabled": true, "displayName": "Work Release Number", "isMandatory": false, "translateKey": "workReleaseNumber"}, "observedOnBehalfOf": {"field": "Observed on behalf of", "isEnabled": true, "displayName": "Observed on behalf of", "isMandatory": false, "translateKey": "observedOnBehalfOf"}, "operationalLearning": {"field": "Operational Learning", "isEnabled": true, "displayName": "Do you have any opportunity for Operational Learning", "isMandatory": false, "translateKey": "doYouHaveAnyOpportunityForOperationalLearning"}, "correctiveActionFlag": {"field": "Did corrective actions occur specific to the stop work event ?", "isEnabled": true, "displayName": "Did corrective actions occur specific to the stop work event ?", "isMandatory": false, "translateKey": "didCorrectiveActionsOccurSpecificToTheStopWorkEvent"}, "describeTheObservations": {"field": "Describe the Observations", "isEnabled": true, "displayName": "Describe the Observations", "isMandatory": false, "translateKey": "describeTheObservations"}, "descripeCorrectiveActionTaken": {"field": "Describe corrective action taken", "isEnabled": true, "displayName": "Describe corrective action taken", "isMandatory": false, "translateKey": "describeCorrectiveActionTaken"}, "operationalLearningDescription": {"field": "Operational Learning Description", "isEnabled": true, "displayName": "Describe the Operational Learning opportunities you found", "isMandatory": false, "translateKey": "describeTheOperationalLearningOpportunitiesYouFound"}, "additionalCommentsOnObservation": {"field": "Additional comments on Observation", "isEnabled": true, "displayName": "Additional comments on Observation", "isMandatory": false, "translateKey": "additionalCommentsOnObservation"}, "checklistEnableValue": {"isEnabled": true}, "notificationEnableValue": {"isEnabled": true}, "notificationGroup": {"field": "Notification Group", "groupName": "NTFAPPGP-MADURAI", "displayName": "Notification Group", "translateKey": "notificationGroup"}}, "dashboardConfig": {"Trends": {"field": "Trends", "isEnabled": true, "displayName": "Trends", "isMandatory": false, "translateKey": "trends"}, "ByCategory": {"field": "By Category", "isEnabled": true, "displayName": "By Category", "isMandatory": false, "translateKey": "byCategory"}, "PercentageParticipation": {"field": "Percentage Participation", "isEnabled": true, "displayName": "Percentage Participation", "isMandatory": false, "translateKey": "percentageParticipation"}, "Sitewideparticipationlast7days": {"field": "Sitewide participation last 7 days", "isEnabled": true, "displayName": "Sitewide participation last 7 days", "isMandatory": false, "translateKey": "sitewideParticipationLast7Days"}}, "columnConfig": {"date": {"field": "Date", "isEnabled": false, "translateKey": "date", "sequence": 1, "displayName": "Date"}, "year": {"field": "Year", "isEnabled": false, "translateKey": "year", "sequence": 2, "displayName": "Year"}, "month": {"field": "Month", "isEnabled": false, "translateKey": "month", "sequence": 3, "displayName": "Month"}, "shift": {"field": "Shift", "isEnabled": false, "translateKey": "shift", "sequence": 4, "displayName": "Shift"}, "title": {"field": "Title", "isEnabled": false, "translateKey": "title", "sequence": 5, "displayName": "Title"}, "status": {"field": "Status", "isEnabled": false, "translateKey": "status", "sequence": 6, "displayName": "Status"}, "workReleaseNumber": {"field": "workReleaseNumber", "isEnabled": true, "translateKey": "workReleaseNumber", "sequence": 7, "displayName": "workReleaseNumber"}, "quarter": {"field": "Quarter", "isEnabled": false, "translateKey": "quarter", "sequence": 8, "displayName": "Quarter"}, "refUnit": {"field": "Unit", "isEnabled": false, "translateKey": "unit", "sequence": 9, "displayName": "Unit"}, "priority": {"field": "Priority", "isEnabled": false, "translateKey": "priority", "sequence": 10, "displayName": "Priority"}, "createdBy": {"field": "<PERSON><PERSON>", "isEnabled": false, "translateKey": "created<PERSON>y", "sequence": 11, "displayName": "<PERSON><PERSON>"}, "externalId": {"field": "Id", "isEnabled": false, "translateKey": "id", "sequence": 12, "displayName": "Id"}, "modifiedBy": {"field": "<PERSON><PERSON>", "isEnabled": false, "translateKey": "updatedBy", "sequence": 13, "displayName": "<PERSON><PERSON>"}, "createdTime": {"field": "<PERSON><PERSON>", "isEnabled": false, "translateKey": "createdOn", "sequence": 14, "displayName": "<PERSON><PERSON>"}, "lastUpdatedTime": {"field": "<PERSON><PERSON>", "isEnabled": false, "translateKey": "updatedOn", "sequence": 15, "displayName": "<PERSON><PERSON>"}, "observationEndDate": {"field": "EndDate", "isEnabled": false, "translateKey": "endDate", "sequence": 16, "displayName": "EndDate"}, "observedOnBehalfOf": {"field": "Observedby", "isEnabled": false, "translateKey": "observedBy", "sequence": 17, "displayName": "Observedby"}, "observationStartDate": {"field": "StartDate", "isEnabled": false, "translateKey": "startDate", "sequence": 18, "displayName": "StartDate"}, "refReportingLocation": {"field": "Location", "isEnabled": false, "translateKey": "location", "sequence": 19, "displayName": "Location"}, "isOperationalLearning": {"field": "OperationalLearning", "isEnabled": true, "translateKey": "operationalLearning", "sequence": 20, "displayName": "OperationalLearning"}, "operationalLearningDescription": {"field": "OperationalLearningDescription", "isEnabled": false, "translateKey": "operationalLearningDescription", "sequence": 21, "displayName": "OperationalLearningDescription"}, "corePrinciple": {"field": "CorePrinciple", "isEnabled": true, "translateKey": "corePrinciple", "sequence": 22, "displayName": "CorePrinciple"}, "subProcess": {"field": "SubProcess", "isEnabled": true, "translateKey": "subProcess", "sequence": 23, "displayName": "SubProcess"}, "actions": {"field": "Actions", "isEnabled": false, "translateKey": "actions", "sequence": 24, "displayName": "Actions"}}, "refProcess": "Audit"}]