<!-- <p>You clicked on the bar with category: {{ data.yAxisValue }} and value: {{ data.value }}</p>
<div class="behaviour-checklist-fotter" fxLayout="row">
  <common-lib-button [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'" (buttonAction)="cancelClick()"></common-lib-button>
</div> -->

<!-- <button mat-button class="close-icon" [mat-dialog-close]="true">
    <mat-icon>close</mat-icon>
</button> -->
<div *ngIf="loaderFlag" class="spinner-bodybar">
    <mat-spinner class="spinnerbar"></mat-spinner>
  </div>
  <div fxLayout="row" >
    <div fxFlex="100">
      <div class="audit-plan-unit-sectiondashboard">

<div *ngIf="listType=='Observation' || listType=='Hazards'" class="ovservation-list-section">
    <div class="audit-plan-unit-section">
    <div class="observation-list-filter-wrapper">
      <commom-label *ngIf="listType=='Observation'" labelText="{{labels['observations']}}"
        [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
      <commom-label *ngIf="listType=='Hazards'" labelText="{{labels['cardsHazards'] }}"
        [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
      <div class="observation-list-filter">
        <!-- <mat-form-field class="marginLeft-5 marginRight-5" appearance="outline" >
          <mat-select   [formControl]="processControl"  >
            <mat-option *ngFor="let item of process" value="{{item.name}}" >{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- <mat-form-field class="marginLeft-5 marginRight-5 width165" appearance="outline" >
          <mat-select   [formControl]="observationControl"  >
            <mat-option *ngFor="let item of observation" value="{{item.name}}" >{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="marginLeft-5 marginRight-5 width165"appearance="outline" >
          <mat-select   [formControl]="categoryControl"  >
            <mat-option *ngFor="let item of category" value="{{item.name}}" >{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field> -->

      </div>

    </div>
    <div class="audit-head-icon">
        <div class="icon-bg-box" (click)="excelExport()" matTooltip="{{ labels['reacttableExport'] }}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" width="17" height="17" fill="white">
            <path
              d="m648-140 112-112v92h40v-160H640v40h92L620-168l28 28Zm-448 20q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v268q-19-9-39-15.5t-41-9.5v-243H200v560h242q3 22 9.5 42t15.5 38H200Zm0-120v40-560 243-3 280Zm80-40h163q3-21 9.5-41t14.5-39H280v80Zm0-160h244q32-30 71.5-50t84.5-27v-3H280v80Zm0-160h400v-80H280v80ZM720-40q-83 0-141.5-58.5T520-240q0-83 58.5-141.5T720-440q83 0 141.5 58.5T920-240q0 83-58.5 141.5T720-40Z" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="summaryClick()" matTooltip="{{ labels['reacttableColsummary'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-stroke-svg-icon"
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="settingClick()" matTooltip="{{ labels['reacttableColselection'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
              d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
          </svg>
        </div>
        <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}" style="margin-left: 10px;" (buttonAction)="cancelClick()"></common-lib-button>

      </div>
    </div>
    <div fxLayout="row" fxLayoutAlign="end center" style="margin-right: 10px;">
      <div *ngIf="!expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
        style="cursor: pointer;float: right;" matTooltip="{{labels['expand'] }}">
        <mat-icon style="color: white;margin: -2px;" aria-hidden="false">first_page</mat-icon>
      </div>
      <div *ngIf="expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
        style="cursor: pointer;float: right;" matTooltip="{{labels['collapse'] }}">
        <mat-icon style="color: white;margin: -2px;" aria-hidden="false">last_page</mat-icon>
      </div>
    </div>
    <div fxLayout="row" style="margin-left: 5px;">
      <as-split direction="horizontal">
        <as-split-area [ngClass]="showhide ? 'split-border' : ''" [size]="x"
          [minSize]="minSize">


          <div class="marginTop d-flex">
            <iframe id="iFrameObservationList" class="iFrameTablebar" [src]="url| safe" title="description">
            </iframe>
          </div>

        </as-split-area>
        <as-split-area *ngIf="showhide==true" [size]="y" style="overflow: hidden;">
          <div fxLayout="row" class="threat-dialog-box" style="padding:10px;" fxLayoutAlign="space-between center">
            <div fxLayout="row" style="align-items: center;">

              <div class="semi-bold">
                {{ labels['logInfo'] }}
              </div>
            </div>
            <div>
              <mat-icon (click)="closeClick()" style="cursor: pointer;" aria-hidden="false"
                aria-label="Example home icon">close</mat-icon>

            </div>


          </div>
          <div style="margin: 8px;">
            <mat-card style="margin: 8px;">
              <div fxLayout="row" fxLayoutAlign="space-between center"  style="font-weight: bold;">
                <div fxFlex="40">
                  <span>
                    {{ labels['tablecolsName'] }}
                  </span>
                
                </div>
                <div fxFlex="20">
                  <span>
                    {{ labels['type'] }}
                  </span>
                
                </div>
                <div fxFlex="40">
                  <span>
                    {{ labels['dateAndTime'] }}
                  </span>
                 
                </div>

              </div>


            </mat-card>
            <mat-card  style="margin: 8px;" *ngFor="let item of logList">
              <div fxLayout="row" fxLayoutAlign="space-between center" >
                <div fxFlex="40">
                  <span>
                    {{item.name }}
                  </span>
                 
                </div>
                <div fxFlex="20">
                  <span>
                    {{item.logType }}
                  </span>
                 
                </div>
                <div fxFlex="40">
                  <span>
                    {{item.createdTime | date : "medium" }}
                  </span>
                
                </div>

              </div>


            </mat-card>
          
          </div>
      

          <div *ngIf="errorSpinner == false">
            <br>
            <br>
            <br>
            <br>

            <div fxLayout="row" fxLayoutAlign="center center">
              <mat-spinner></mat-spinner>
            </div>

          </div>


        </as-split-area>
      </as-split>
    </div>


  </div>

  <div *ngIf="listType=='Field Walk'" class="ovservation-list-section">
    <div class="audit-plan-unit-section">
      <div class="observation-list-filter-wrapper">
        <commom-label labelText="{{ labels['fieldWalk'] }}"
          [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <div class="observation-list-filter">
  
        </div>
      </div>
      <!-- <app-common-filter [siteControl]="siteControl"></app-common-filter> -->
      <div class="audit-head-icon">
        <div class="icon-bg-box" (click)="summaryClick()" matTooltip="{{ labels['reacttableColsummary'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-stroke-svg-icon"
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="settingClick()" matTooltip="{{ labels['reacttableColselection'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
              d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
          </svg>
        </div>
        <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}" style="margin-left: 10px;" (buttonAction)="cancelClick()"></common-lib-button>

      </div>
    </div>
    

    <!-- <div class="marginTop d-flex">
      <iframe id="iFrameFieldWalk111" class="iFrameTable" [src]="url3 | safe" title="description"></iframe>
    </div> -->
    <div fxLayout="row" fxLayoutAlign="end center" style="margin-right: 10px;">
      <div *ngIf="!expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
        style="cursor: pointer; float: right;" matTooltip="{{ labels['expand'] }}">
        <mat-icon style="color: white; margin: -2px;" aria-hidden="false">first_page</mat-icon>
      </div>
      <div *ngIf="expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
        style="cursor: pointer; float: right;" matTooltip="{{ labels['collapse'] }}">
        <mat-icon style="color: white; margin: -2px;" aria-hidden="false">last_page</mat-icon>
      </div>
    </div>
    <div fxLayout="row" style="margin-left: 5px;">
      <as-split direction="horizontal">
        <as-split-area style="height: 100vh;" [ngClass]="showhide ? 'split-border' : ''" [size]="x"
          [minSize]="minSize">
          <div class="marginTop d-flex">
            <iframe id="iFrameFieldWalk111" class="iFrameTablebar" [src]="url3 | safe" title="description"></iframe>
          </div> 
        </as-split-area>

        <as-split-area *ngIf="showhide==true" [size]="y" style="overflow: hidden;">
          <div fxLayout="row" class="threat-dialog-box" style="padding:10px;" fxLayoutAlign="space-between center">
            <div fxLayout="row" style="align-items: center;">
              <div class="semi-bold">
                {{ labels['logInfo'] }}
              </div>
            </div>
            <div>
              <mat-icon (click)="closeClick()" style="cursor: pointer;" aria-hidden="false"
                aria-label="Example home icon">close</mat-icon>
            </div>
          </div>

          <div style="margin: 8px;">
            <mat-card style="margin: 8px;">
              <div fxLayout="row" fxLayoutAlign="space-between center" style="font-weight: bold;">
                <div fxFlex="40">
                  <span>
                    {{ labels['tablecolsName'] }}
                  </span>
                </div>
                <div fxFlex="20">
                  <span>
                    {{ labels['type'] }}
                  </span>
                </div>
                <div fxFlex="40">
                  <span>
                    {{ labels['dateAndTime'] }}
                  </span>
                </div>
              </div>
            </mat-card>
            <mat-card style="margin: 8px;" *ngFor="let item of logList">
              <div fxLayout="row" fxLayoutAlign="space-between center">
                <div fxFlex="40">
                  <span>
                    {{item.name }}
                  </span>
                </div>
                <div fxFlex="20">
                  <span>
                    {{item.logType }}
                  </span>
                </div>
                <div fxFlex="40">
                  <span>
                    {{item.createdTime | date : "medium" }}
                  </span>
                </div>
              </div>
            </mat-card>
          </div>

          <div *ngIf="errorSpinner == false">
            <br><br><br><br>
            <div fxLayout="row" fxLayoutAlign="center center">
              <mat-spinner></mat-spinner>
            </div>
          </div>
        </as-split-area>
      </as-split>
    </div>
  </div>

  <div *ngIf="listType=='Audit'" class="ovservation-list-section">
    <div class="observation-list-filter-wrapper">
       <commom-label labelText="{{ labels['audit'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>

       
   </div>
   
  <div class="marginTop d-flex">
    <iframe id="iFrameAudit" class="iFrameTablebar"  [src]="url| safe" title="description">
    </iframe>
  </div>

 </div>

  </div>    </div>  
