<!-- <div fxLayout="row" class="header">
  <div  class="sidebar-section" [ngClass]="!menuFlag ? 'expand-sidebar' : ''">
    <div class="logo">
         <span>
          <svg xmlns="http://www.w3.org/2000/svg" width="27.915" height="27.915" viewBox="0 0 27.915 27.915">
            <path id="Path_9126" data-name="Path 9126" d="M.5,14.458a14.444,14.444,0,0,0,.216,2.483C4.6,7.14,16.759,4.055,22.592,3.115A13.958,13.958,0,0,0,.5,14.458ZM25.438,5.842c-4.189.517-14.361,3.518-16.3,9.245-1.353,4.026,2,5.3,7.74,3.706a28.957,28.957,0,0,0,6.472-2.652v4.684a25.886,25.886,0,0,1-8.1,3.527,25.72,25.72,0,0,1-9.75.818A13.958,13.958,0,0,0,25.438,5.842Z" transform="translate(-0.5 -0.5)" fill="#fff"/>
          </svg>
         </span>
    </div>
    
    <div>
     
    </div>
  </div>
</div> -->

<div fxLayout="row" class="header">
  <div  class="sidebar-section" [ngStyle]="{'height': vendorFlag ? '48px' : null }" [ngClass]="!menuFlag ? 'expand-sidebar' : ''">
    <div class="logo">
         <span>
          <svg xmlns="http://www.w3.org/2000/svg" width="27.915" height="27.915" viewBox="0 0 27.915 27.915">
            <path id="Path_9126" data-name="Path 9126" d="M.5,14.458a14.444,14.444,0,0,0,.216,2.483C4.6,7.14,16.759,4.055,22.592,3.115A13.958,13.958,0,0,0,.5,14.458ZM25.438,5.842c-4.189.517-14.361,3.518-16.3,9.245-1.353,4.026,2,5.3,7.74,3.706a28.957,28.957,0,0,0,6.472-2.652v4.684a25.886,25.886,0,0,1-8.1,3.527,25.72,25.72,0,0,1-9.75.818A13.958,13.958,0,0,0,25.438,5.842Z" transform="translate(-0.5 -0.5)" fill="#fff"/>
          </svg>
         </span>
    </div>
    <div *ngIf="!vendorFlag" class="expand-collapse-section" [ngClass]="!menuFlag ? 'expand-collapse-section-rotate' : ''"   (click)="expandClick()">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="12.41" height="12" viewBox="0 0 12.41 12">
          <g id="Group_14352" data-name="Group 14352" transform="translate(-17.387 -66)">
            <path id="_Color" data-name=" ↳Color" d="M6,0,0,6,1.41,7.41,6,2.83l4.59,4.58L12,6Z" transform="translate(24.797 66) rotate(90)" fill="#90acbb"/>
            <path id="_Color-2" data-name=" ↳Color" d="M6,0,0,6,1.41,7.41,6,2.83l4.59,4.58L12,6Z" transform="translate(29.797 66) rotate(90)" fill="#90acbb"/>
          </g>
        </svg>
      </span>
    </div>
    <div *ngIf="!vendorFlag" class="sidebar-menu-section">
  
      <mat-selection-list #menu [multiple]="false">
        <ng-container *ngFor="let menuItem of menuList;">
          <mat-list-option *ngIf="menuItem.visible==true" [ngClass]="currentRoute.url.includes(menuItem.url) ? 'menuSelected' : ''"
          (click)="menuClick(menuItem)" [value]="menuItem" [matTooltip]="menuFlag ? (labels[menuItem.name]) : ''" [matTooltipClass]="'menu-tooltip'" [matTooltipPosition]="'right'">
          <span class="menu-wrap">
            <i class="menu-svg"
              [innerHTML]="menuItem.icon | safeHtml">{{menuItem.name}}</i>
          </span>
          <span *ngIf="!menuFlag" class="my-menu">
            {{ labels[menuItem.name] }}
          </span>
          </mat-list-option>
        </ng-container>
       </mat-selection-list>
    </div>
    <div>
     
    </div>
  </div>
</div>
