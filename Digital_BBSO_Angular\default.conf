server {
    listen       4900 ;
    server_name  localhost;
    large_client_header_buffers 4 64k;
    ssl_certificate /usr/share/nginx/html/ssl/cert.pem;
    ssl_certificate_key /usr/share/nginx/html/ssl/key.pem;

    location / {
        root   /usr/share/nginx/html/;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }

   
    # redirect server error pages to the static page /50x.html
    
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html/;
    }

 
}
