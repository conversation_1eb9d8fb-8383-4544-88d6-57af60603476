
import { Injectable } from '@angular/core';
import  dayjs from 'dayjs'
import { firstValueFrom } from 'rxjs';

// Custom imports
import { LanguageItem } from './LanguageItem';
import { environment } from 'src/environments/environment';
import { DataService } from '../data.service';
import { CommonService } from '../common.service';
import { TokenService } from '../token.service';

@Injectable({
  providedIn: 'root',
})


export class TranslationService {
    private TranslationCacheName = 'TranslationData';
    private TranslationExpirationCacheName = 'TranslationExpiration'
    private translationAppId = environment.applicationId // APP-DPW
    // private translationAppId =  'APP-DPW'
    public labelsTemp: any = {}
    public availableLanguages: LanguageItem[] = [
        {
          language: 'English',
          code: 'EN'
        },
        {
          language: 'French',
          code: 'FR'
        },
        {
          language: 'Spanish',
          code: 'ES'
        },
        {
          language: 'Portuguese',
          code: 'PT'
        },
        {
          language: 'Italian',
          code: 'IT'
        },
        {
          language: 'Mandarin',
          code: 'ZH'
        },
        {
          language: 'Japanese',
          code: 'JA'
        },
        {
          language: 'Korean',
          code: 'KO'
        },
        {
          language: 'Dutch',
          code: 'NL'
        },
        {
          language: 'German',
          code: 'DE'
        },
    ]

    private APP_ID: string = environment.applicationId


    constructor(
        public dataService:DataService,
        public commonService: CommonService,
        public tokenService: TokenService
    ) {
      
    }

    GetTranslationCacheName(locale:string) {
        return this.translationAppId + this.TranslationCacheName + locale.toUpperCase();
    }

    GetTranslationExpirationCacheName() {
        return this.translationAppId + this.TranslationExpirationCacheName
    }
      
    isTranslationInCache(locale: string): boolean {
        const cacheName = this.GetTranslationCacheName(locale)
      
        return (
          typeof window !== 'undefined' &&
          window.localStorage.getItem(cacheName) !== null &&
          window.localStorage.getItem(cacheName) !== '0'
        )
    }

    isTranslationExpired(): boolean {
        const cacheName = this.GetTranslationExpirationCacheName()
        const expiredLocal = window.localStorage.getItem(cacheName)
        const expired =
          typeof window === 'undefined' ||
          expiredLocal === null ||
          expiredLocal === '0' ||
          Number(expiredLocal) < dayjs().valueOf()
        return expired
    }

    // Get the translation from the Data Model (Calls the Apps Translation /static API endpoint)
    // This method is similar to getMessages of Celanese-SDK
    async getLabels(locale: string, cb: any) {
        var _this = this
        const token = _this.tokenService.getToken()
        const translationCacheName = _this.GetTranslationCacheName(locale)
        const translationExpirationCacheName = _this.GetTranslationExpirationCacheName()
        let labels:any = undefined

        // If the translation is in cache, return it
        // if (_this.isTranslationInCache(locale)) {
        //     labels = JSON.parse(window.localStorage.getItem(translationCacheName)!)
        // }

        if (!labels) {
            const dataLabels = await this.getStaticTranslationData(
                token,
                this.translationAppId,
                'LAN-' + locale.toUpperCase()
            );
            console.log('Data Labels:', dataLabels);

            if (dataLabels) {
                // window.localStorage.setItem(translationCacheName, JSON.stringify(labels))
                // window.localStorage.setItem(translationExpirationCacheName, dayjs().add(1, 'day').valueOf().toString())
                _this.commonService.labelObject[locale] = dataLabels // {'EN': {menu.home:''}}
                cb(true)
                console.log('Labels:', _this.commonService.labelObject);
                
                console.log('Toaster Labels:', _this.commonService.toasterLabelObject);
            }
            else {
              cb(null)
            }
        }


    }

    async getStaticTranslationData(
        token: string,
        app: string = this.APP_ID,
        lang: string = 'LAN-EN'
    ) {
      var _this = this;
        console.log('token translation:', token);

        // Fetch the translation data and convert the Observable to a Promise
        const downloadedLabels:any = await firstValueFrom(this.dataService.postData(
            {
                application: app,
                language: lang
            },
            this.dataService.NODE_API + '/api/service/staticTranslation'
        ));

        if(downloadedLabels && downloadedLabels.detail =="Invalid authorization token: ExpiredSignatureError"){
          _this.dataService.login();
         }
         if(downloadedLabels && downloadedLabels.message =="Invalid authorization token: ExpiredSignatureError"){
          _this.dataService.login();
         }

        console.log("Translation data:", downloadedLabels); // Log the downloaded labels
        return downloadedLabels; // Ensure to return the data
    }

    translate(label: string) { //category
        var _this = this;
        const locale = _this.commonService.selectedLanguage.toUpperCase();

        const key = _this.translationAppId + '.' + locale + '.' +'stLabel.' + label //APP-OFWA.EN.tableCols.category
        console.log('Label Key:', locale);
        console.log('Language Labels:', _this.commonService.labelObject[locale]);
        // let keyValue = null
        // if (_this.commonService.labelObject && _this.commonService.labelObject[locale] && _this.commonService.labelObject[locale][key]) {
        //   keyValue = _this.commonService.labelObject[locale][key] // Get the key value from the commonservice
        // }

        // console.log('Key Value:', keyValue);

        // return keyValue != null && keyValue != '' ? keyValue : label
        return _this.commonService.labelObject[locale][key] ? _this.commonService.labelObject[locale][key] : label


    }

}

