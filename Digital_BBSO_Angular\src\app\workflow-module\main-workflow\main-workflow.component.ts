import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-main-workflow',
  templateUrl: './main-workflow.component.html',
  styleUrls: ['./main-workflow.component.scss']
})
export class MainWorkflowComponent implements OnInit {

  workFlowArray = []

  constructor(  private router: Router,private dataService:DataService) { 
   this.workFlowArray=  this.dataService.workFlowList
  }

  ngOnInit(): void {
  }

  goPage(page) {
    this.router.navigate([page]);
  }

}
