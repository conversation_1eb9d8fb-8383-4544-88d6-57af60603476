import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { UserInterDailogComponent } from 'src/app/shared/user-integration-dialog/userInter-dailog.component';
import { TokenService } from 'src/app/services/token.service';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';

@Component({
  selector: 'app-schedule-details',
  templateUrl: './schedule-details.component.html'
})
export class ScheduleDetailsComponent implements OnInit {


  siteControl: FormControl = new FormControl("");
  siteList = [];
  filteredSiteList = [];
  loaderFlag: boolean;
  userAccessMenu: any;
  displayedColumns: any = [];
  labels = {}

  allColumns = [

    { name: 'externalId', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name: 'title', displayName: "Title", key: "title", activeFlag: true, summary: false },
    { name: 'corePrinciple', displayName: "Core Principle", key: "corePrinciple", activeFlag: false, summary: false },
    { name: 'process', displayName: "Process", key: "process", activeFlag: false, summary: false },
    { name: 'observationType', displayName: "Observation Type", key: "observationType", activeFlag: false, summary: false },
    { name: 'occurrence', displayName: "Occurrence", key: "occurrence", activeFlag: true, summary: false },
    { name: 'observationStartDate', displayName: "Start Date", key: "startDate", activeFlag: true, summary: false },
    { name: 'observationEndDate', displayName: "Due Date", key: "tablecolsDuedate", activeFlag: true, summary: false },
    { name: 'year', displayName: "Year", key: "year", activeFlag: true, summary: false },
    { name: 'quarter', displayName: "Quarter", key: "quarter", activeFlag: true, summary: false },
    { name: 'observer', displayName: "Observer", key: "observer", activeFlag: true, summary: false },
    { name: 'priority', displayName: "Priority", key: "priority", activeFlag: false, summary: false },
    { name: 'status', displayName: "Status", key: "status", activeFlag: true, summary: false },
    { name: 'createdTime', displayName: "Created Time", key: "tablecolsCreatedtime", activeFlag: true, summary: false },
    { name: 'createdBy', displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: 'lastUpdatedTime', displayName: "Updated On", key: "updatedOn", activeFlag: false, summary: false },
    { name: 'modifiedBy', displayName: "Updated By", key: "updatedBy", activeFlag: false, summary: false },
    { name: 'actions', displayName: "Actions", key: "actions", activeFlag: true, summary: false },

 


  ]
  url: any = "";
  url2: any = "";
  listScheduleId: any;
  constructor(private changeDetector: ChangeDetectorRef,  
    private tokenService: TokenService,
    private dialog: MatDialog, 
    private cdRef: ChangeDetectorRef, private dataService: DataService, 
    private router: Router, private commonService: CommonService, private translate: TranslateService,
    private ngZone:NgZone, private languageService: LanguageService
  ) {
  this.url2 = this.dataService.React_API + "/scheduleDetails";
  if(history.state.data){
    this.listScheduleId = history.state.data;
    console.log(' this.listScheduleId', this.listScheduleId);
  

  }
    this.labels = {
    'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
    'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
    'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
    'schedulelistCancelmsg': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'schedulelistCancelmsg'] || 'schedulelistCancelmsg',
    }

   }

  ngOnInit(): void {
    var _this =this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
          'schedulelistCancelmsg': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'schedulelistCancelmsg'] || 'schedulelistCancelmsg',
          }
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      console.log("fiterType>",fiterType)
      if (fiterType) {
      
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
     
          _this.getUserMenuConfig();
        }
       
       
      }
    })

    _this.getUserMenuConfig();
    var iframe = document.getElementById('iFrameAuditScheduleList');
    console.log('iframe', iframe)
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
   
    _this.applyFilter();

    window.onmessage = function (e) {
   
      if (e.data.type && e.data.action && e.data.type == "AuditPlan") {
        if (e.data.action == "Edit") {
          _this.router.navigateByUrl('schedule-list/create-schedule', {
            state: { "data": e.data.data, "action": "Edit" }
          });
        } 
        else if ( e.data.action == "Loading") {
          _this.loaderFlag = false
        }
        else if ( e.data.action == "LangError") {
          _this.applyFilter();
        }
        else if(e.data.action =="disableClick"){
         console.log('disableClick',e.data.data)
        _this.alertMesg(e.data.data);
        }
        else if( e.data.action == "observationClick"){
          _this.router.navigateByUrl('observations/observation', {
            state: { "data": e.data.data, "action": "Schedule" }
          });
        }
        else if (e.data.action == "View") {
          _this.router.navigateByUrl('schedule-list/create-schedule', {
            state: { "data": e.data.data, "action": "View" }
          });
        }
        else if(e.data.action == "scheduleDetails"){
          console.log(' e.data.data', e.data.data)
          _this.router.navigateByUrl('schedule-list/schedule-details', {
            state: { "data": e.data.data, "action": "View" }
          });
          
        }

      };

    }


  }
  alertMesg(data){
    var _this =this
    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {  title:_this.labels['schedulelistCancelmsg'] }
   });
   dialogRef.afterClosed().subscribe(result => {
     if(result == 'YES'){
      var postObjDetail = {
        externalId:data.externalId,
        status: "Disabled",
        isEnable:false
      }
      var instanceAuditObj = {
        "type": _this.commonService.configuration["typeOFWAScheduleDetail"],
        "siteCode": data.space?data.space.split('-')[1]:"COR",
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          postObjDetail
        ]
        }
        console.log('instanceAuditObj',instanceAuditObj);
        _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
          _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterDisabsuccess'] });
          _this.applyFilter() 
        })
      }
   })
  }
  
  applyFilter() {
    var _this = this;
    console.log('_this.listScheduleId',_this.listScheduleId)
    if(_this.listScheduleId == undefined){
      _this.goPage('schedule-list')
    }
    setTimeout(function () {
    var iframe = document.getElementById('iFrameAuditScheduleList');
    console.log('iframe', iframe)
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    console.log(_this.siteControl.value)
    var sites = []
    if (_this.siteControl.value) {
      sites = [_this.siteControl.value]
    }
    iWindow?.postMessage({ "type": "AuditPlan", "action": "Filter", "scheduleId":_this.listScheduleId?_this.listScheduleId.externalId:null,"subCategory": "",sites: sites, LanguageCode: `${_this.commonService.selectedLanguage}`, }, '*');
    _this.getUserMenuConfig();
  }, 1000);


  }
  goPage(page) {
    this.router.navigate([page]);
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
     console.log('_this.displayedColumns',_this.displayedColumns);
      // _this.loaderFlag = true
      var iframe = document.getElementById('iFrameAuditScheduleList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "AuditPlan", "action": "Column", "data": _this.displayedColumns }, '*');
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    // this.columnSummarysubject.next(summaryCol);
    console.log('summaryCol',summaryCol);
    var iframe = document.getElementById('iFrameAuditScheduleList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "AuditPlan", "action": "Summary", "data": summaryCol }, '*');
 
  }

  getUserMenuConfig(){
    var _this = this
    
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
      if(_this.commonService.menuFeatureUserIn.length>0){
      var ScheduleView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleView);
      var ScheduleEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleEdit);
      var ScheduleTracker = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleTracker);
       var ScheduleCreateAudit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleAuditSchedule);
      var ScheduleCreateFieldWalk = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateFieldWalk);
      var ScheduleCreateObservation = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateObservation);
      var iframe = document.getElementById('iFrameAuditScheduleList');
           if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "AuditPlan", "action": "AccessMenu", "data": {ScheduleView:ScheduleView,ScheduleEdit:ScheduleEdit} }, '*');
        
      }else{
        var iframe = document.getElementById('iFrameAuditScheduleList');
        if (iframe == null) return;
   var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
   iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
   iWindow?.postMessage({ "type": "AuditPlan", "action": "AccessMenu", "data": {ScheduleView:{},ScheduleEdit:{}} }, '*');
     
      }
    
  }

}
