import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ParentToChildService } from 'src/app/broadcast/parent-to-child.service';
import { MatTabGroup } from '@angular/material/tabs';
import { AddCraftComponent } from '../../configuration-module/add-craft/add-craft.component';
import _ from "lodash";
import { TokenService } from 'src/app/token.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {



  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  selectedIndexBinding = 0;
  searchControl: FormControl = new FormControl("");
  labels = {};
  siteControl: FormControl = new FormControl("");
  siteList = [];
  filteredSiteList = [];

  dateControl: FormControl = new FormControl();
  dateList = [ ];
  filteredDateList = this.dateList.slice();

  timeControl: FormControl = new FormControl();
  hourControl:  FormControl = new FormControl();
  timeList = [
    { name: "HH:mm:ss", value: "HH:mm:ss" },
    { name: "hh:mm:ss a", value: "hh:mm:ss a" },
    { name: "HH:mm", value: "HH:mm" },
    { name: "hh:mm a", value: "hh:mm a" }
  ];
  filteredTimeList = this.timeList.slice();

  hourList = [
    { name: "12Hrs", value: "12" },
    { name: "24Hrs", value: "24" }
  ];
  filteredTimeFormat = this.hourList.slice();




  ifSite = false;

  processConfig: any;
  loaderFlag: boolean;
  userAccessMenu: any;
  configurationList: any;

  craftList: any = [];
  craftExternalId: any;
  contractorList: any = [];
  contractorExternalId: any;
  settingData: any;
  searchTerm: string = '';
  filteredContractorList: string[] = [];
  constructor(
    private dataService: DataService,
    public dialog: MatDialog,
    private commonService: CommonService,
    private parentchildService: ParentToChildService,
    private router: Router,
    private tokenService: TokenService,
    private translate: TranslateService,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone,
    private changeDetector: ChangeDetectorRef
  ) {

    this.labels = {
      'menuSettings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSettings'] || 'menuSettings',
      'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
      'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'settingsDatetimeconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsDatetimeconfig'] || 'settingsDatetimeconfig',
      'settingsDateformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsDateformat'] || 'settingsDateformat',
      'settingsTimeformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsTimeformat'] || 'settingsTimeformat',
      'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
      'buttonAddcrafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonAddcrafts'] || 'buttonAddcrafts',
      'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
      'contractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractors'] || 'contractors',
      'settingsHourformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsHourformat'] || 'settingsHourformat',
      'addContractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addContractors'] || 'addContractors',
      'searchContractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'searchContractors'] || 'searchContractors',
      'typeToSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'typeToSearch'] || 'typeToSearch',
    }

    var _this = this;
    _this.dateList = commonService.dateFormatArray;
    _this.filteredDateList = this.dateList.slice();
    _this.loaderFlag = true;


  }

  initialDataFlag = 0;
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'menuSettings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSettings'] || 'menuSettings',
          'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'settingsDatetimeconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsDatetimeconfig'] || 'settingsDatetimeconfig',
          'settingsDateformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsDateformat'] || 'settingsDateformat',
          'settingsTimeformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsTimeformat'] || 'settingsTimeformat',
          'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
          'buttonAddcrafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonAddcrafts'] || 'buttonAddcrafts',
          'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
          'contractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractors'] || 'contractors',
          'settingsHourformat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'settingsHourformat'] || 'settingsHourformat',
          'addContractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addContractors'] || 'addContractors',
          'searchContractors': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'searchContractors'] || 'searchContractors',
          'typeToSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'typeToSearch'] || 'typeToSearch'
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    console.log(_this.tabGroup)
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.loaderFlag = false;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId)
      _this.getSetting();
      _this.ifSite = true;
      _this.loaderFlag = false;
    }
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {

        if (fiterType == "userAccess") {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Core Principles") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
        }
        if (fiterType == "Site") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.loaderFlag = false;
        }
        console.log(_this.initialDataFlag)
        if (_this.initialDataFlag > 0) {
          _this.siteControl.setValue(_this.dataService.siteId);
          _this.getSetting();
        }
      }
    })


    this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.craftExternalId = undefined;
      _this.contractorExternalId = undefined;
      _this.ifSite = true;
      _this.getSetting();
      var mySite = _this.commonService.siteList.find(e => e.externalId == value);

    });

  }
  configList: any = [];
  yesNoOption: any = [{ "name": "Yes", "value": true }, { "name": "No", "value": false }]
  ngAfterViewInit(): void {
    var _this = this;
  }

  selectTab(event) {
    var _this = this;
    console.log(event)
    if (event == 1) {
    }
  }

  getSetting() {
    var _this = this;
    _this.loaderFlag = true;
    _this.dataService.postData({ sites: [_this.siteControl.value] }, _this.dataService.NODE_API + '/api/service/listSetting')
      .subscribe((resData: any) => {
        var settingList = resData['data']['list' + _this.commonService.configuration['typeSetting']]['items'];
        console.log(settingList)
        if (settingList.length > 0) {
          _this.settingData = settingList[0];
          console.log(_this.settingData)
          _this.dateControl.setValue(_this.settingData.dateFormat);
          _this.timeControl.setValue(_this.settingData.timeFormat);
          _this.hourControl.setValue(_this.settingData.hourFormat);
          _this.craftExternalId =_this.settingData["externalId"];
          _this.craftList = _this.settingData["craft"] ? _this.settingData["craft"] :[];
          _this.contractorExternalId = _this.settingData["externalId"];
          _this.contractorList = _this.settingData["contractors"] ? _this.settingData["contractors"] : [];
          _this.filteredContractorList = this.contractorList;
        } else {
          _this.settingData = undefined;
          _this.craftExternalId = undefined;
          _this.craftList = [];
          _this.contractorExternalId = undefined;
          _this.contractorList = [];
        }
        _this.loaderFlag = false;
      })
  }
  onSearchChange() {
    this.filteredContractorList = this.contractorList.filter(item =>
      item.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }
  getUserMenuConfig() {
    var _this = this

    if (_this.siteControl.value != _this.dataService.siteId) {
      _this.siteControl.setValue(_this.dataService.siteId)
    }
    if (_this.commonService.menuFeatureUserIn.length > 0) {
      _this.configurationList = {};
      _this.configurationList["dateAndTime"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.dateAndTime);
      _this.configurationList["crafts"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.crafts);
      _this.configurationList["contractors"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.contractors);

    console.log(_this.configurationList)
    console.log(_this.configurationList["dateAndTime"])
    } else {
      _this.configurationList = {}
    }
  }

  goPage(page) {
    this.router.navigate([page]);
  }

  saveConfig() {
    var _this = this;
    _this.loaderFlag = true;
    console.log(_this.dateControl.value)
    console.log(_this.timeControl.value)
    console.log(_this.configList)

    var myObj = {
      externalId: _this.processConfig["externalId"],
      configDetail: {}
    }
    Object.entries(_this.processConfig.configDetail).forEach(([key, value]) => {
      if (key == "dateFormat") {
        if (_this.dateControl.value) {
          myObj["configDetail"][key] = _this.dateControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key == "timeFormat") {
        if (_this.timeControl.value) {
          myObj["configDetail"][key] = _this.timeControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key == "hourFormat") {
        if (_this.hourControl.value) {
          myObj["configDetail"][key] = _this.hourControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key != "dateFormat" && key != "timeFormat" && key != "hourFormat") {
        if (typeof value == "string") {
          myObj["configDetail"][key] = value;
        }
      }

      if (typeof value == "object") {
        myObj["configDetail"][key] = value;
        var findVal = _this.configList.find(e => e.key == key);
        if (findVal) {
          myObj["configDetail"][key]["displayName"] = findVal["displayNameControl"].value;
          myObj["configDetail"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          myObj["configDetail"][key]["isMandatory"] = findVal["isMandatoryControl"].value;
        }

      }
    });
    var postObj = {
      "type": _this.commonService.configuration["typeProcessConfiguration"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        myObj
      ]
    }
    console.log("postObj")
    console.log(postObj)
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })
  }
  formConfig() {
    var _this = this;

    this.router.navigateByUrl('configuration/form-list', {
      state: {}
    });

  }

  addCraft() {
    var _this = this;
    var dataPass = { type: "craft" }
    const dialogRef = this.dialog.open(AddCraftComponent, {
      data: dataPass,
      height: '220px',
      width: '427px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result && result.name && result.name.length > 0) {
        _this.createCraft(result);
      }
    });
  }
  onEditCraft(index) {
    var _this = this;
    var dataPass = { index: index, name: _this.craftList[index], type: "craft" }
    const dialogRef = this.dialog.open(AddCraftComponent, {
      data: dataPass,
      height: '220px',
      width: '427px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result && result.name && result.name.length > 0) {
        result["index"] = index;
        _this.createCraft(result);
      }
    });
  }
  createCraft(result: any) {
    var _this = this;
    console.log(result)
    if (_this.craftList.filter(e => e == result.name).length == 0) {
      _this.loaderFlag = true;
      var site = _this.siteList.find(e => e.externalId == _this.siteControl.value);
      var myObj = {
        "refSite": {
          "externalId": site["externalId"],
          "space": site["space"]
        }
      };
      if (_this.craftExternalId) {
        myObj["externalId"] = _this.craftExternalId;
        var craftList = _.clone(_this.craftList);
        console.log(_this.craftList)
        console.log(result.index)
        if (result.index > -1) {
          craftList[result.index] = result.name;
          myObj["craft"] = craftList;
        } else {
          craftList.push(result.name);
          myObj["craft"] = craftList;
        }
      } else {
        myObj["craft"] = [result.name]
      }
      var postObj = {
        "type": _this.commonService.configuration["typeSetting"],
        "siteCode": _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          myObj
        ]
      }
      _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
        _this.loaderFlag = false;
        if (data["items"] && data["items"].length > 0) {
          _this.craftExternalId =  data["items"][0]["externalId"]
          _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
          if (result.index > -1) {
            _this.craftList[result.index] = result.name;
          } else {
            _this.craftList.push(result.name);
            myObj["name"] = craftList;
          }
        } else {
          _this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterFailed'] });
        }
      })
    } else {
      _this.commonService.triggerToast({ type: 'info', title: "", msg: this.commonService.toasterLabelObject['toasterAlreadyexists'] });
    }
  }

  addContractor() {
    var _this = this;
    var dataPass = {type: "contractor"}
    const dialogRef = this.dialog.open(AddCraftComponent, {
      data: dataPass,
      height: '220px',
      width: '427px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result && result.name && result.name.length > 0) {
        _this.createContractor(result);
      }
    });
  }
  onEditContractor(index) {
    var _this = this;
    var dataPass = { index: index, name: _this.contractorList[index],type: "contractor" }
    const dialogRef = this.dialog.open(AddCraftComponent, {
      data: dataPass,
      height: '220px',
      width: '427px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result && result.name && result.name.length > 0) {
        result["index"] = index;
        _this.createContractor(result);
      }
    });
  }
  createContractor(result: any) {
    var _this = this;
    console.log(result)
    if (_this.contractorList.filter(e => e == result.name).length == 0) {
      _this.loaderFlag = true;
      var site = _this.siteList.find(e => e.externalId == _this.siteControl.value);
      var myObj = {
        "refSite": {
          "externalId": site["externalId"],
          "space": site["space"]
        }
      };
      if (_this.contractorExternalId) {
        myObj["externalId"] = _this.contractorExternalId;
        var contractorList = _.clone(_this.contractorList);
        console.log(_this.contractorList)
        console.log(result.index)
        if (result.index > -1) {
          contractorList[result.index] = result.name;
          myObj["contractors"] = contractorList;
        } else {
          contractorList.push(result.name);
          myObj["contractors"] = contractorList;
        }
      } else {
        myObj["contractors"] = [result.name]
      }
      var postObj = {
        "type": _this.commonService.configuration["typeSetting"],
        "siteCode": _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          myObj
        ]
      }
      _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
        _this.loaderFlag = false;
        if (data["items"] && data["items"].length > 0) {
          _this.contractorExternalId =  data["items"][0]["externalId"]
          _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
          if (result.index > -1) {
            _this.contractorList[result.index] = result.name;
          } else {
            _this.contractorList.push(result.name);
            myObj["name"] = contractorList;
          }
        } else {
          _this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterFailed'] });
        }
      })
    } else {
      _this.commonService.triggerToast({ type: 'info', title: "", msg: this.commonService.toasterLabelObject['toasterAlreadyexists'] });
    }
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  saveSetting() {
    var _this = this;
    _this.loaderFlag = true
    var site = _this.siteList.find(e => e.externalId == _this.siteControl.value);
    var myObj = {
      "refSite": {
        "externalId": site["externalId"],
        "space": site["space"]
      },
      "dateFormat": _this.dateControl.value,
      "timeFormat": _this.timeControl.value,
      "hourFormat": _this.hourControl.value
    }
    if (_this.settingData) {
      myObj["externalId"] = _this.settingData.externalId;
    }
    var postObj = {
      "type": _this.commonService.configuration["typeSetting"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        myObj
      ]
    }
    console.log("postObj")
    console.log(postObj)
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = false;
      _this.settingData = { ...myObj, ...data["items"][0] }
      _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })
  }

}

