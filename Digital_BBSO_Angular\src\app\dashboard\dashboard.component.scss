.charts-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Gap between chart cards */
  }
  
  .chart-Card {
    flex: 1 1 calc(50% - 20px); /* Two charts per row with space between them */
    box-sizing: border-box; /* Ensures padding and borders are included in the width */
    margin-bottom: 20px; /* Space below each card */
    max-width: calc(50% - 20px); /* Ensure that each card is at most 50% of the container width minus gaps */
    min-height: 400px; /* Minimum height for the chart card to increase the background box size */
  }

  .chart-Card2{
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Shadow properties: offset-x, offset-y, blur-radius, color */
    border-radius: 8px; /* Optional: rounded corners for a modern look */
    padding: 16px; /* Optional: padding inside the card */
    margin-bottom: 20px; /* Space below the card */
    background-color: #ffffff; /* Optional: card background color */
  }
  
  /* Optional: Ensure the chart fits within the card */
  #chartCategory2 {
    width: 100%; /* Full width of the chart container */
    height: 100%; /* Full height of the chart container */
  }

  #heading{
    color:red
  }
  .apexcharts-xaxis-label {
    white-space: normal !important;
    text-align: center;
    word-wrap: break-word;
  }
  .hide-card {
    box-shadow: none;
    border-radius: 0;
    padding: 0;
    margin-bottom: 0;
    background-color: transparent;
}
  
  /* Adjustments for smaller screens */
  @media (max-width: 768px) {
    .chart-Card {
      flex: 1 1 100%; /* Stacks charts vertically on smaller screens */
      max-width: 100%; /* Ensure full width on smaller screens */
    }
  }
  
  /* Ensure the charts fit within their cards */
  #chart, #chartDept, #chartCategory, #chartPercentage, #chartRistSection {
    width: 100%; /* Full width of the chart container */
    height: 100%; /* Full height of the chart container */
  }
  