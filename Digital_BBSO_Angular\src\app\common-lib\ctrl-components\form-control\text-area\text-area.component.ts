import { ChangeDetectionStrategy, Component, Input, OnInit } from "@angular/core";


@Component({
    selector: 'common-lib-text-area',
    templateUrl: './text-area.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class TextAreaComponent implements OnInit {
    @Input() placeHolder:string = '';
    @Input() value:string = '';
    @Input() type:string = '';
    @Input() className:string = '';
    constructor() {}
    ngOnInit(): void {
        
    }
}