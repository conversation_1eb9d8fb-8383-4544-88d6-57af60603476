import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Options } from "src/app/modals/home.modal";

@Component({
    selector: 'commom-radio-box',
    templateUrl: './radio-button.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class CommonRadioButtonComponent implements OnInit {
 @Input() className:string= '';
 @Input() radioOptionList:string[] = [];
 @Input() selectedItem: string;
 @Output() radioInput: EventEmitter<any> = new EventEmitter<any>();
  constructor(){}

   ngOnInit(): void {
       
   }

   radioChange(event):void {
     this.radioInput.emit(event.value);
   }
}