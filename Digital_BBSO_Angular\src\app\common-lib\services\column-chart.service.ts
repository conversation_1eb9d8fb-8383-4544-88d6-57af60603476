import { Injectable } from "@angular/core";
import {
    ApexAxisChartSeries,
    ApexChart, ApexDataLabels, ApexFill, ApexForecastDataPoints, ApexGrid, ApexLegend, ApexMarkers, ApexPlotOptions, ApexStroke,
    ApexTitleSubtitle, ApexTooltip, ApexXAxis, ApexYAxis, ChartType
} from "ng-apexcharts";
import { ChartOptions } from "src/app/modals/chart.modal";

@Injectable()

export class ColumnChartService {
    constructor() { }

    getConfigureColumnChartOptions(
        columnWidth?: string | number, dataLabelsEnabled?: boolean,
        chartHeight?: string | number, chartType?: ChartType, toolbar?: boolean,
        showStroke?: boolean, strokeWidth?: number | number[], strokeCurve?: any, strokeColors?: string[],
        fillColors?: any[], opacity?: number | number[], yTooltip?: boolean,
        legendShow?: boolean, legendshowForSingleSeries?: boolean, customLegendItems?: string[],
        legendMarkersFillColors?: string[], xCategories?: string[], isYLabelFortmated?: boolean, horizontalBar?:boolean, lineCap?:any,
        markerSize?:number, markerWidth?:number | number[],  markerHeight?:number | number[], markerColors?: string[], markerStrokeColors?:string | string[], markerStrokeWidth?: number | number[],
        markerStrokeOpacity?:number | number[], markerFillOpacity?:number | number[], markerHoverSize?:number
        ): ChartOptions {
        let chartOptions: ChartOptions;

        chartOptions = {
            plotOptions: {
                bar: {
                    columnWidth: columnWidth ? columnWidth : '100%',
                    horizontal: horizontalBar ? horizontalBar : horizontalBar,
                }
            },
            dataLabels: {
                enabled: dataLabelsEnabled ? dataLabelsEnabled : false
            },
            chart: {
                height: chartHeight ? chartHeight : 350,
                type: chartType ? chartType : 'bar',
                toolbar: {
                    show: toolbar ? toolbar : false
                }
            },
            stroke: {
                show: showStroke ? showStroke : false,
                width: strokeWidth ? strokeWidth : 0,
                colors: strokeColors && strokeColors.length ? strokeColors : [],
                curve: strokeCurve,
                lineCap: lineCap ? lineCap : "square"
            },
            fill: {
                colors: fillColors && fillColors.length ? fillColors : [],
                opacity: opacity ? opacity : 0
            },
            tooltip: {
                y: yTooltip ? {
                    formatter: function (val) {
                        return "$ " + val + " thousands";
                    }
                } : {}
            },
            legend: {
                show: legendShow ? legendShow : false,
                showForSingleSeries: legendshowForSingleSeries ? legendshowForSingleSeries : false,
                customLegendItems: customLegendItems && customLegendItems.length ? customLegendItems : [],
                markers: {
                    fillColors: legendMarkersFillColors && legendMarkersFillColors.length ? legendMarkersFillColors : []
                }
            },
            yaxis: {
                labels: {
                    style: {
                        fontSize: '12.65px',
                        fontFamily: 'Roboto-regular',
                        fontWeight: 400,
                        colors: ["#080808"],
                        cssClass: 'apexcharts-yaxis-label',
                    },
                    formatter: function (val) {
                        if (isYLabelFortmated) {
                            return val + "%";
                        }
                        return val + '';
                    }
                }
            },
            xaxis: {
                categories: xCategories && xCategories.length ? xCategories : [],
                labels: {
                    style: {
                        fontSize: '12.65px',
                        fontFamily: 'Roboto-regular',
                        fontWeight: 400,
                        colors: ["#080808"],
                        cssClass: 'apexcharts-yaxis-label',
                    }
                }
            },
            colors: ["#008FFB"],
            grid: {
                row: {
                  colors: ["#f3f3f3", "transparent"], // takes an array which will be repeated on columns
                  opacity: 0.5
                }
            },
            markers: {
                            size: markerSize ? markerSize : 0,
                            width: markerWidth ?  markerWidth : [],
                            height: markerHeight ?  markerHeight : [],
                            colors: markerColors? markerColors : [],
                            strokeColors: markerStrokeColors ?  markerStrokeColors : [],
                            strokeWidth: markerStrokeWidth ? markerStrokeWidth : [],
                            strokeOpacity: markerStrokeOpacity ? markerStrokeOpacity : [], 
                            fillOpacity: markerFillOpacity? markerFillOpacity : [],
                            hover: {
                              size:  markerHoverSize ? markerHoverSize : 0
                            }
                    },
        }
        return chartOptions;
    }


    getAngleConfigureOptions(chartHeight:number, chartType:ChartType, circleColors:string[], labels?:string[], legendShow?:boolean, seriesData?:any[]){
        let chartOptions: ChartOptions;

        chartOptions = {
            chart: {
                height: chartHeight ? chartHeight : 350,
                type: chartType ? chartType : 'bar',
            },
            series: seriesData && seriesData.length ? seriesData : [],
            plotOptions: {
                radialBar: {
                    offsetY: 0,
                    startAngle: 0,
                    endAngle: 270,
                    hollow: {
                      margin: 5,
                      size: "30%",
                      background: "transparent",
                      image: undefined
                    },
                    dataLabels: {
                      name: {
                        show: false
                      },
                      value: {
                        show: false
                      }
                    }
                  }
            },

            colors: circleColors,
            labels: labels && labels.length ?  labels : [],
            legend: {
                show: legendShow ? legendShow : false,
                floating: true,
                fontSize: "16px",
                position: "left",
                offsetX: 50,
                offsetY: 10,
                labels: {
                  useSeriesColors: true
                },
                formatter: function(seriesName, opts) {
                  return seriesName + ":  " + opts.w.globals.series[opts.seriesIndex];
                },
                itemMargin: {
                  horizontal: 1
                }
              },
            responsive: [
            {
              breakpoint: 480,
              options: {
                legend: {
                  show: true
                }
              }
            }
             ]
        }
        return chartOptions;
     
    }

    getRangeAreaConfigureOptions(chartHeight:number, chartType:ChartType, animationSpeed:number,
        circleColors:string[], fillOpactiy:number[], strokeType:any, strokeWidth: number | number[], legendShow:boolean, 
        customLegendItem?:string[], legentInverseOrder?:boolean, titleText?:string, dataLabelsEnabled?:boolean){
        
        let chartOptions: ChartOptions;
        chartOptions = {
            chart: {
                height: chartHeight ? chartHeight : 350,
                type: chartType ? chartType : 'bar',
                animations: {
                    speed: animationSpeed
                  },
                  
            },
            colors: circleColors && circleColors.length ? circleColors : [],
            fill: {
                opacity: fillOpactiy && fillOpactiy.length ? fillOpactiy : [],
            },
            forecastDataPoints: {
                count: 2,
                dashArray: 4
              },
              stroke: {
                curve: strokeType ? strokeType : "straight",
                width: strokeWidth 
              },

            legend: {
                show: legendShow,
                customLegendItems: customLegendItem && customLegendItem.length ? customLegendItem : [],
                inverseOrder: legentInverseOrder
              },
              title: {
                text: titleText
              },
              markers: {
                hover: {
                  sizeOffset: 5
                }
              },
              dataLabels: {
                enabled: dataLabelsEnabled ? dataLabelsEnabled : false
            },
         
        }
        return chartOptions;
     
    }
}