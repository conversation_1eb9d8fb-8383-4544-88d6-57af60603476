<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">

        <div class="ovservation-list-section configuration-section">
            <div class="audit-plan-unit-section">
                <commom-label labelText="{{ labels['menuSettings']}}" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>
               
            </div>
            <!-- <div class="marginTop">
                <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                    <div fxLayout="row auto" fxLayoutAlign="start center">
                        <div class="treat-md">
                            <span class="semi-bold">{{ labels['site'] }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()"
                                    placeholder="{{ labels['commonfilterChoosesite'] }}"
                                    [formControl]="siteControl" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]=" labels['commonfilterNoresults'] " [placeholder]=" labels['commonfilterSearch'] "
                                        [displayMember]="'description'" [array]="siteList"
                                        (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>

            </div> -->

            <br>
            <mat-tab-group class="outerbox" mat-stretch-tabs  *ngIf="ifSite"
                [(selectedIndex)]="selectedIndexBinding" #tabGroup (selectedIndexChange)="selectTab($event)">
               
                <mat-tab *ngIf="configurationList && configurationList.dateAndTime" label="{{ labels['settingsDatetimeconfig'] }}">
                    <div class="tab-height">
                        <br>
                        <div>
                           
                            <div fxLayout="row wrap">
                                <!-- <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="marginTop">
                                        <commom-label labelText="Date Configuration" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                </div> -->
                                <br>
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="d-flex align-item-center mr-10">
                                        <span class="subheading-1 site-icon-text">{{ labels['settingsDateformat'] }}</span>
                                        <mat-form-field appearance="outline" class="set-back-color">
                                            <mat-select (click)="filterClick()" placeholder="{{ labels['settingsDateformat'] }}"
                                                [formControl]="dateControl" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]=" labels['commonfilterNoresults'] " placeholder="{{ labels['settingsDateformat'] }}" [displayMember]="'value'"
                                                    [array]="dateList"
                                                    (filteredReturn)="filteredDateList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredDateList"
                                                    [value]="item.value">
                                                    {{item.name }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="d-flex align-item-center mr-10">
                                        <span class="subheading-1 site-icon-text">{{ labels['settingsTimeformat'] }}</span>
                                        <mat-form-field appearance="outline" class="set-back-color">
                                            <mat-select (click)="filterClick()" placeholder="{{ labels['settingsTimeformat'] }}"
                                                [formControl]="timeControl" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]=" labels['commonfilterNoresults'] " placeholder="{{ labels['settingsTimeformat'] }}" [displayMember]="'value'"
                                                    [array]="timeList"
                                                    (filteredReturn)="filteredTimeList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredTimeList"
                                                    [value]="item.value">
                                                    {{item.name }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="d-flex align-item-center mr-10">
                                        <span class="subheading-1 site-icon-text">{{ labels['settingsHourformat'] }}</span>
                                        <mat-form-field appearance="outline" class="set-back-color">
                                            <mat-select (click)="filterClick()" placeholder="{{ labels['settingsHourformat'] }}"
                                                [formControl]="hourControl" disableOptionCentering>
                                                <!-- <mat-select-filter [noResultsMessage]=" labels['commonfilterNoresults'] " placeholder="{{ labels['settingsTimeformat'] }}" [displayMember]="'value'"
                                                    [array]="timeList"
                                                    (filteredReturn)="filteredTimeList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredTimeFormat"
                                                    [value]="item.value">
                                                    {{item.name }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <br>
                                <div fxFlex="100" fxLayoutAlign="end">
                                    <div class="create-schedular-fotter">
                                        <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                                            (buttonAction)="saveSetting();"></common-lib-button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.crafts" label="{{ labels['crafts'] }}">
                    <div class="tab-height">
                        <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                            <div *ngFor="let item of craftList;  let i = index" fxLayout="row"
                                fxLayoutAlign="center center">
                                <div class="category_box_light" fxLayoutAlign="center center">
                                    <div fxFlexAlign="center">
                                        <span>
                                            {{item}}
                                        </span>
                                    </div>
                                </div>
                                <button mat-icon-button (click)="onEditCraft(i)" style="width: auto;">
                                    <mat-icon>edit</mat-icon>
                                </button>
                            </div>
                            <div fxLayout="row" class="category_box_light" (click)="addCraft()"
                                fxLayoutAlign="center center">
                                <div fxLayout="row" style="align-items: center;">
                                    <mat-icon class="add_Icon">add_circle</mat-icon>
                                    <span>
                                        {{ labels['buttonAddcrafts'] }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>

                <mat-tab *ngIf="configurationList && configurationList.contractors" label="{{ labels['contractors'] }}">
                    <div class="tab-height" style="margin-top: 10px;">
                      <!-- Search input to filter the contractor list -->
                      <mat-form-field appearance="outline" class="search-field">
                        <mat-label>{{ labels['searchContractors'] }}</mat-label>
                        <input matInput [(ngModel)]="searchTerm" (input)="onSearchChange()" placeholder="{{ labels['typeToSearch'] }}"/>
                      </mat-form-field>
                  
                      <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                        <div *ngFor="let item of filteredContractorList; let i = index" fxLayout="row" fxLayoutAlign="center center">
                          <div class="category_box_light" fxLayoutAlign="center center">
                            <div fxFlexAlign="center">
                              <span>{{ item }}</span>
                            </div>
                          </div>
                          <button mat-icon-button (click)="onEditContractor(i)" style="width: auto;">
                            <mat-icon>edit</mat-icon>
                          </button>
                        </div>
                        
                        <div fxLayout="row" class="category_box_light" (click)="addContractor()" fxLayoutAlign="center center">
                          <div fxLayout="row" style="align-items: center;">
                            <mat-icon class="add_Icon">add_circle</mat-icon>
                            <span>{{ labels['addContractors'] }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </mat-tab>
                  
            </mat-tab-group>

        </div>
    </div>
</div>
