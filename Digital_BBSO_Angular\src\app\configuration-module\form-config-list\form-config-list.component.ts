import { AfterViewInit, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';

@Component({
  selector: 'app-form-config-list',
  templateUrl: './form-config-list.component.html',
  styleUrls: ['./form-config-list.component.scss']
})
export class FormConfigListComponent implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: any = [];

  allColumns = [
    { key: 'id', displayName: "ID", name: "id", activeFlag: true, summary: false },
    { key: 'observationType', displayName: "Observation Type", name: "observationType", activeFlag: true, summary: false },
    { key: 'columnName', displayName: "Column Name", name: "columnName", activeFlag: true, summary: false },
    { key: 'columnType', displayName: "Column Type", name: "columnType", activeFlag: true, summary: false },
    { key: 'date', displayName: "Date", name: "date", activeFlag: true, summary: false },
    { key: 'actions', displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  ];
  observeHistory: any;

  url: any = "";
  constructor(private router: Router, private dataService: DataService, private dialog: MatDialog,private tokenService:TokenService) {
    this.url = this.dataService.React_API + "/formConfig";

    console.log(history.state)
    this.observeHistory = history.state
  }

  ngOnInit(): void {
    var _this = this;
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "FormConfig") {
        console.log(e.data.action)
        if (e.data.action == "FormView") {
          _this.router.navigateByUrl('question-config', {
            state: this.observeHistory
          });
          console.log('FormView-->')
        } else if (e.data.action == "FormEdit") {

          console.log('FormEdit-->')
          _this.router.navigateByUrl('question-config', {
            state: this.observeHistory
          });

        }
      };


    }
  }
  ngAfterViewInit(): void {

    var _this = this;
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FormConfig", "action": "Column", "data": this.displayedColumns }, '*');
    // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');

  }
  ngOnDestroy(): void {

  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    console.log('_this.displayedColumns,', _this.displayedColumns,)
    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol', summaryCol)
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FormConfig", "action": "Summary", "data": summaryCol }, '*');
  }
  goFormConfig() {
    this.router.navigateByUrl('configuration/question-config', {
      state: this.observeHistory
    });
  }


  goPage(page) {
    this.router.navigate([page]);
  }
  siteSelected(region) {
  }
  processSelected(process) {

  }
}
