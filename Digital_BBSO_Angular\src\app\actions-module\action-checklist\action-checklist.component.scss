/* Styles for the background */
.background-button {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.5)),
    url("https://images.pexels.com/photos/4827/nature-forest-trees-fog.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260");
  background-size: cover;
  background-position: center;
}
.test1{
  display: flex !important;
  justify-content: space-between !important;
}
/* Container for modals */
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

/* Base styles for modal boxes */
.modalbox {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  border-radius: 2px;
  background: white;
  padding: 25px 25px 15px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  max-width: 90%;
  margin: 0 auto;
  position: relative;
}

/* Success modal specific styles */
.modalbox.success {
  display: none; 
}

.modalbox.error {
  display: none; 
}

/* Success popup container */
.success-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #1F8248;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  text-align: center;
  width: 400px;
  max-width: 90%;
}

.failure-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #D32F2F;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  text-align: center;
  width: 400px;
  max-width: 90%;
}

/* Success popup icon styles */
.popup-icon {
  background-color: #4caf50;
  border-radius: 50%;
  height: 100px;
  width: 100px;
  margin: 0 auto;
  margin-top: -75px; /* Adjust as needed */
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup2-icon {
  background-color: #e90707;
  border-radius: 50%;
  height: 100px;
  width: 100px;
  margin: 0 auto;
  margin-top: -75px; /* Adjust as needed */
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-icon .glyphicon {
  font-size: 4em;
  color: white;
}

.popup2-icon .glyphicon {
  font-size: 4em;
  color: white;
}

/* Success popup message styles */
.popup-message {
  margin-bottom: 20px;
}

.popup-message h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  color: #4caf50; /* Green color */
  margin-bottom: 10px;
}

.popup-messages h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  color: #e90707;
  margin-bottom: 10px;
}

.popup-message p {
  font-family: "Open Sans", sans-serif;
  font-size: 18px;
  color: #333;
}

.popup-messages p {
  font-family: "Open Sans", sans-serif;
  font-size: 18px;
  color: #333;
}

/* Close button styles */
.close-button {
  background-color: #4caf50;
  border: none;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.close-button:hover {
  background-color: #45a049; /* Darker shade of green on hover */
}

.red-button:hover {
  background-color: #e90707; /* Darker shade of green on hover */
}

.red-button {
  background-color: #e90707;
  border: none;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}


.marginTop {
  margin-top: 10px;
}



.uploadPhotoBox {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.image-container {
  position: relative;
  height: 100px;
  width: 100px;
}

.proofImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.add-photo-container {
  height: 100px;
  width: 100px;
  cursor: pointer;
}

.add-photo-container img {
  border: 1px dashed #ccc;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
}

.remove-btn, .view-btn {
  position: absolute;
  top: 5px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.remove-btn {
  right: 5px;
  background: red;
  color: white;
}

#close{
  position: absolute;
  right: -2px;
  font-size: 12px;
  top: 3px;
  
}

.view-btn {
  right: 30px;
  background: #037f16;
  color: white;
}

.remove-btn:hover, .view-btn:hover {
  opacity: 1;
}

.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.9);
}

.image-modal.show {
  display: block;
}

.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 700px;
  max-height: 80%;
  object-fit: contain;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 35px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
}

.uploadPhotoBox.multiple-rows {
  max-height: unset;
}


.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.9);
  cursor: pointer;
}

.image-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  display: block;
  width: auto;
  height: auto;
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  cursor: default;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 35px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
}

.close-modal:hover {
  color: #bbb;
}
.view-btn {
  right: 30px;
  background: #037f16;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn svg {
  width: 14px;
  height: 14px;
}

.view-icon path {
  fill: #ffffff; 
}
.fullscreen-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(18, 17, 17, 0.9);
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
}

.close-modal {
  position: absolute;
  top: 60px;
  right: 35px;
  color: #f7f1f1;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
  background: none; 
  border: none; 
  padding: 0;   
}


.close-modal:hover {
  color: #f00; 
}


.save-btn {
  margin-left: 10px;
  margin-top: 10px;
}


.selected_border {
  border: 2px solid #3498db; 
  border-radius: 5px; 
  
}


.video-container {
  position: relative;
  display: inline-block;
  margin: 5px;
  width: 100px; /* Adjust width as needed */
  height: 100px; /* Adjust height as needed */
  overflow: hidden;
}

.proofVideo {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures the video fits within the container */
  display: block;
}
.last_selected {
  background-color: #3498db; /* Example background color for last selected item */
  color: #fff; /* Example text color for last selected item */
  /* Add any other styles for last selected item */
}

.pending_deselection {
  background-color: #ffcccc; /* Light red background for pending deselection */
}



.subheading-1.regular {
  font-weight: bold;
  color: #333; /* Adjust color as needed */
}

.clickable-text {
  text-decoration: none;
  color: #000; /* Black text color */
  cursor: pointer;
  padding-left: 10px; /* Space between checkbox and text */
  position: relative; /* Needed for pseudo-element positioning */
  word-wrap: break-word; /* Handle long words */
  word-break: break-word; /* Handle long words */
  overflow-wrap: break-word; /* Ensure long words break properly */
  display: inline-block; /* Ensure text aligns properly with checkbox */
  vertical-align: middle; /* Align text vertically in the middle */
}

.clickable-text::after {
  content: "";
  display: block;
  width: 80%; /* Adjust underline width if needed */
  height: 4px; /* Adjust underline height */
  background: #052d57; /* Adjust underline color */
  position: absolute;
  bottom: -10px; /* Adjust space between text and underline */
  left: 10px; /* Adjust position relative to text */
  transition: transform 0.3s;
  transform: scaleX(0);
  transform-origin: left;
}

.clickable-text:hover::after {
  transform: scaleX(1);
}

.custom-checkbox {
  margin-right: 10px; /* Reduced margin to create less space between checkbox and text */
}

.mat-checkbox-label {
  font-size: 1rem;
  color: #333; /* Adjust color as needed */
}

.item-spacing {
  margin-right: 10px; /* Reduced margin */
}

.select-all-checkbox {
  font-weight: bold;
  margin-left: 15px;
}

.category-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Adjust number of items per row */
  gap: 20px; /* General gap between items */
  row-gap: 30px; /* Increased gap between rows */
}

.category-item {
  display: flex;
  align-items: center; /* Align items vertically center */
  max-width: 400px; /* Optional: Set a max width */
  height: auto; /* Allow height to adjust based on content */
  padding: 10px 0; /* Add padding to increase spacing between rows */
  overflow: hidden; /* Hide overflow content */
  word-wrap: break-word; /* Ensure long words wrap correctly */
  word-break: break-word; /* Ensure long words break correctly */
}

.category-item mat-checkbox {
  margin-right: 10px; /* Adjust margin between checkbox and text */
}

.test{
  margin-top: 0px;
}

.radio-btn {
  max-width: fit-content !important;
}

// This media query is written for in tablet view the items are not displayed properly with very small so made that by changing the font size

@media (min-width: 1920px) and (max-height: 1000px) {
  .behaviour-checklist-section {
    font-size: 1.2em;
  }

  .input,
  textarea {
    font-size: 1.1em;
    padding: 20px;
  }

  .mat-form-field {
    width: 550px;
  }

  .behav-tree-select {
    width: 550px;
  }

  .mat-select-filter {
    width: 100%;
  }

  .mat-form-field-label {
    font-size: 1.1em;
    margin-bottom: 9px;
  }

  .mat-select-panel {
    font-size: 2em;
  }

  .mat-select-value {
    font-size: 1.1em;
  }

  .search-input-assets {
    width: 550px;
  }

  .icons3d {
    font-size: 2em;
  }

  .category_box,
  .category_box_light {
    font-size: 1.1em;
  }

  .urgent-label-font {
    font-size: 1em;
  }

  .expandHead {
    font-size: 1.1em;
  }

  .uploadPhotoBox {
    height: 200px;
  }

  .proofImage {
    height: 350px;
  }

  .behaviour-list-tree {
    padding: 25px;
  }

  .bahaviour-tabs {
    margin-top: 40px;
  }

  .behaviour-checklist-fotter {
    gap: 40px;
  }

  .popup-icon {
    font-size: 1em;
  }

  .popup-message {
    font-size: 3.5em;
  }
  
  // User Story v2 82968
  // : Allow attach more than one picture on OFWA.

  .uploadPhotoBox {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    max-height: 300px;
  }

  .proofImage {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border: 1px solid #ccc;
    border-radius: 5px;
  }

  .add-photo-container {
    height: 100px;
    width: 100px;
    cursor: pointer;
  }

  .add-photo-container img {
    border: 1px dashed #ccc;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 5px;
  }

  .remove-btn, .view-btn {
    position: absolute;
    top: 5px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }


  .remove-btn .cancel-icon {
    fill: white; 
  }

  .remove-btn {
    right: 5px;
    background: red;
    color: white;
  }

  .view-btn {
    right: 30px;
    background: #037f16;
    color: white;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .view-btn svg {
    width: 14px;
    height: 14px;
  }

  .view-btn svg path {
    fill: #ffffff;
  }

  .remove-btn:hover, .view-btn:hover {
    opacity: 1;
  }

  .image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.9);
    cursor: pointer;
  }

  .image-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-content {
    display: block;
    width: auto;
    height: auto;
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    cursor: default;
  }

  .close-modal {
    position: absolute;
    top: 60px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
  }

  .close-modal:hover {
    color: #bbb;
  }

  .close-modal-inside {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
  }

  .close-modal-inside:hover {
    color: #bbb;
  }
}

//This media query code is for increase the width of the upload photo box when in tablet view (portait mode)
//User Story v2 82968 : Allow attach more than one picture on OFWA.
@media (min-width: 1000px) and (max-width: 1920px) and (orientation: portrait) {
  
  .marginTop .uploadPhotoBox {  
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
  }
  .proofImage {
    width: 150px; 
    height: auto; 
  }

  .uploadPhotoBox {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr)); 
    gap: 15px;
  }

  .add-photo-container { 
    width: 100px; 
    height: 100px; 
  }

  .add-photo-container img {
    width: 100%; 
    height: 100%; 
    object-fit: contain; 
  }


  .remove-btn {
    right: -40px;
    background: red;
    color: white;
  }

  .view-btn {
    right: -20px;
    background: #037f16;
    color: white;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
 
}

.custom-select .mat-select-value-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
