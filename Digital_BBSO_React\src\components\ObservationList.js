// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import * as ExcelJS from 'exceljs';
import { getMessages, translate, DynamicTranslationArea, TranslationContext, TranslationContextProvider } from '@celanese/celanese-sdk'
// import { getLabel, translate } from '../utils/getLabels'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let allListData = []
let initFlag
let colSummary = {}
let dateFormat = "MM/dd/yyyy"
let timeFormat = "hh:mm aa"
let listType = "Observation";
let processId
let operLearning
let category
let subCategory
let safeDisplayName
let notsafeDisplayName
let createdByfilter
let statusfilter
let behalf
let projectName
let workOrderNumber
let location
let subProcess

let displayedColumns = [
  'externalId',
  'date',
  'crafts',
  'site',
  'unit',
  'shortDescription',
  'category',
  'isOperationalLearning',
  'subCategory',
  'observationType',
  'observationStatus',
  'createdTime',
  'location',
  'createdBy',
  'actions',
]

let headersKeyVal = {
  externalId: 'ID',
  date: 'Date',
  week:"Week",
  crafts: 'Craft',
  site: 'Site',
  isOperationalLearning: 'Operational Learning',
  operationalLearningDescription:'Operational Learning Description',
  art: 'Art',
  problem: 'Problem',
  cause: 'Cause',
  solution: 'Solution',
  measure: 'Measure',
  unit:'Unit',
  shortDescription:'Short Description',
  category:'Category',
  subCategory:'Sub Category',
  workOrderNumber:'Work Order Number',
  projectName:'Project Name',
  observationType:'Observtion Type',
  location:'Location Observed',
  observationStatus: 'Status',
  description: 'Description',
  itemNo: 'Item Number',
  compExAssetDescription: 'CompEx Asset Description',
  inspectionNo: 'Inspection Number',
  inspectionGrade: 'Inspection Grade',
  inspectionOutcome: 'Inspection Outcome',
  approvalStatus: 'Approval Status',
  classification: 'Classification',
  assetTag: 'CompEx Asset',
  areaClassification: 'ATEX Category - Area',
  subTagNumber: 'Sub Tag Number',
  manufacturerPartNo: 'Manufacturer Part Number',
  manufacturer: 'Manufacturer',
  serialNo: 'Serial Number',
  atexCategoryDevice: 'ATEX Category - Device',
  eplDevice: 'EPL - Device',
  createdTime: 'Created On',
  createdBy: 'Created By',
  lastUpdatedTime: 'Updated On',
  modifiedBy: 'Updated By',
  actions: 'Actions',
}

var paginationCursor = []
let site
let unit
let search
let tabledata
let startDate
let endDate
let token
let user
let idToken
let userAccessMenu
let isSiteAdmin
let columnOrder;
let prevLanguage = 'EN'
function ObservationList() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('10')
  const { locale, updateLocale } = useContext(TranslationContext)

  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const [messages, setMessages] = useState({})

  const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

  const [t, i18n] = useTranslation('global')

  const handleLanguageChange = (newValue) => {
    if(i18n == undefined){
      window.parent.postMessage(
        {
          type: 'ObservationList',
          action: 'LangError',
          data: true,
        },
        '*'
      )
    }
    setSelectedLanguage(newValue)
    if(i18n){
      i18n.changeLanguage(newValue)
      console.log('Selected Language: ', selectedLanguage)
      console.log('i18n.language: ', i18n)
      
    }
  
  }

  const getIdTokenFromMsal = (token) => {      
    return Promise.resolve(token);
  };
  
  const getAuthToken = () => getIdTokenFromMsal(idToken);

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
        }
      }
    })

    const getIdTokenFromMsal = (token) => {      
      return Promise.resolve(token);
    };
    
    const getAuthToken = () => getIdTokenFromMsal(idToken);

    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        console.log('AuthToken', e.data)
        token = e.data.data
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || shouldTranslateDynamic)
        if(e.data.user){
          user = JSON.parse(e.data.user)
        }
        if(e.data.isSiteAdmin){
          isSiteAdmin = e.data.isSiteAdmin;
        }
        else{
          // createdByfilter = user.userPrincipalName.toLowerCase()
          }
        if(e.data.columnOrder){
          columnOrder = e.data.columnOrder;
        }
        // getMessages(
        //   e.data.LanguageCode,
        //   getAuthToken
        // ).then((m) => {
        //   console.log('Messages from React: ', m)
        //   setMessages(m)
        // })
        // prevLanguage = e.data.LanguageCode
        // localStorage.setItem('LocaleData', e.data.LanguageCode)
        // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, e.data.labels)
      }
      if (e.data.type && e.data.type == 'ObservationList') {
        console.log('e.data', e.data)
        if (e.data.action == 'Column') {
        
          console.log('Column Language: ', e.data)
          displayedColumns = e.data.data
          colFun()
        } else if (e.data.action == 'Filter') {
          console.log('Filter Language: ', e.data)
          site = e.data.sites
          unit = e.data.units
          tabledata= e.data.tabledata
          startDate = e.data.date.start
          endDate = e.data.date.end
          console.log(e.data)
          // language translation
          listType = e.data.listType;
          processId = e.data.processId;
          operLearning = e.data.operLearning;
          category = e.data.category;
          subCategory = e.data.subCategory;
          safeDisplayName= e.data.safeDisplayName;
          notsafeDisplayName= e.data.notsafeDisplayName;
          createdByfilter= e.data.createdBy;
          statusfilter= e.data.status;
          behalf = e.data.behalf;
          projectName= e.data.projectName;
      workOrderNumber= e.data.workOrderNumber;
          location = e.data.location;
          subProcess = e.data.subProcess;
          setting();
          handleLanguageChange(e.data.LanguageCode)
          // remove prev language data
          const prevLang = localStorage.getItem('LocaleData')
          localStorage.removeItem('LocaleData')
          localStorage.removeItem('APP-OFWATranslationData'+prevLang)
          localStorage.setItem('LocaleData', e.data.LanguageCode)
          localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
          idToken = e.data.idToken
              // getAuthToken()
              setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
              window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
              console.log('DynamicTranslation', e.data)
          // headersKeyVal = {
          //   externalId: translate('stLabel.id'),
          //   date: translate('tablecolsdate'),
          //   week: translate('tablecolsweek'),
          //   crafts: translate('observationList.craft'),
          //   site: translate('site'),
          //   unit: translate('tablecolsunit'),
          //   shortDescription:t('shortDescription'),
          //   // category:t('CONFIG_LIST.CATEGORY'),
          //   category:translate('tablecolscategory'),
          //   subCategory:translate('subCategory'),
          //   observationType:translate('observationType'),
          //   observationStatus: translate('tablecolsstatus'),
          //   description: translate('description'),
          //   createdTime: translate('tablecolscreatedon'),
          //   createdBy: translate('tablecolscreatedby'),
          //   lastUpdatedTime: translate('tablecolsupdatedon'),
          //   modifiedBy: translate('tablecolsupdatedby'),
          //   actions: translate('tablecolsactions'),
          // }
          console.log('OBSList headersKeyVal', headersKeyVal)
          colFun()
          getData()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          colFun()
        }else if(e.data.action == 'Excel') {
          exportExcelFile()

        }
        else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu observe', userAccessMenu)
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        idToken = e.data.idToken
        console.log('Language', e.data)
        // remove prev language data
        
        const prevLang = localStorage.getItem('LocaleData')
        localStorage.removeItem('LocaleData')
        localStorage.removeItem('APP-OFWATranslationData'+prevLang)
        localStorage.setItem('LocaleData', e.data.LanguageCode)
        localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        console.log('Language message', e.data)
        
        updateLocale(e.data.LanguageCode.toUpperCase())
        

  //       headersKeyVal = {
  //           externalId: translate('stLabel.id'),
  //           date: translate('stLabel.date'),
  //           week: translate('stLabel.week'),
  //           crafts: translate('stLabel.craft'),
  //           site: translate('stLabel.site'),
  //           unit: translate('stLabel.unit'),
  //           shortDescription:translate('stLabel.shortDescription'),
  //           isOperationalLearning: translate('stLabel.operationalLearning') ,
  //           operationalLearningDescription: translate('stLabel.operationalLearningDescription'),
  //           art: translate('stLabel.art'),
  // workOrderNumber:translate('stLabel.workOrderNumber'),
  // projectName:translate('stLabel.projectName'),
  //           problem: translate('stLabel.problem'),
  //           solution: translate('stLabel.solution'),
  //           cause: translate('stLabel.cause'),
  //           measure: translate('stLabel.measureActivity'),
  //           // category:t('CONFIG_LIST.CATEGORY'),
            
  //           category:translate('stLabel.category'),
  //           subCategory:translate('stLabel.subCategory'),
  //           observationType:translate('stLabel.observationType'),
  //           observationStatus: translate('stLabel.status'),
  //           description: translate('stLabel.description'),
  //           createdTime: translate('stLabel.createdOn'),
  //           createdBy: translate('stLabel.createdBy'),
  //           location: translate('stLabel.location'),
  //           lastUpdatedTime: translate('stLabel.updatedOn'),
  //           modifiedBy: translate('stLabel.updatedBy'),
  //           actions: translate('stLabel.actions'),
  //         }
        console.log('Language', e.data)
   
      handleLanguageChange(e.data.LanguageCode)

        console.log('OBSLIst headersKeyVal', headersKeyVal)
        colFun()
        getData()
      }
            if (e.data.action == 'DynamicTranslation') {
              console.log('App.js DynamicTranslation', e.data)
              idToken = e.data.idToken
              // getAuthToken()
              setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
              window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
              console.log('DynamicTranslation', e.data)
              // localStorage.setItem('LocaleData', e.data.LanguageCode)
              // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, e.data.labels)
            }
          
    }

    setDataCount(1)
    colFun()
    return () => runtime.dispose()
  }, [])

  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = parseInt(e.target.value)

    console.log('limitSet', limitSet)
    filterData()
    setCurrentPage(1)
  }

  const [searchParams, setSearchParams] = useSearchParams()

  {
    /* <div  title="View" style="height:18px;margin-right:18px;cursor: pointer;" >
    <svg  id="${i}"  onClick=${questionList}  xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>               
    </div>
    <div title="Edit" style="height:18px;margin-right:18px;cursor: pointer;" >
      <svg  id="${i}"  onClick=${questionList}  xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"  width="15" height="15" x="0" y="0" viewBox="0 0 348.882 348.882" style="enable-background:new 0 0 512 512" xml:space="preserve"><g><path d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z" fill="#1A2254" data-original="#000000" class=""></path><path d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>
                       
      </div> */
  }
  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i

    var isEdit = (listData[cInd]["createdBy"].toLowerCase() == (user && user.userPrincipalName.toLowerCase())) || isSiteAdmin;
    
    return html`<div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
      ${userAccessMenu &&
      userAccessMenu.obView &&
      userAccessMenu.obView.featureAccessLevelCode
        ? html`${userAccessMenu.obView.featureAccessLevelCode == 'ViewAccess' ||
            userAccessMenu.obView.featureAccessLevelCode == 'EditAccess'
            ? html`<div
                onClick=${() => viewClick(cInd)}
                title=${translate('stLabel.buttonView')}
                style="height:18px;margin-right:12px;cursor: pointer;"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="20"
                  height="20"
                  x="0"
                  y="0"
                  viewBox="0 0 488.85 488.85"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                  class=""
                >
                  <g>
                    <path
                      d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
      ${userAccessMenu &&
      userAccessMenu.obEdit &&
      userAccessMenu.obEdit.featureAccessLevelCode
        ? html` ${userAccessMenu.obEdit.featureAccessLevelCode == 'EditAccess'
            ? html` <div
                onClick=${() => editClick(cInd,isEdit)}
                title=${translate('stLabel.edit')}
                style="height:18px;margin-right:12px;"
                class="${  !isEdit ? 'not-allowed' : 'pointer'}"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
      ${userAccessMenu &&
      userAccessMenu.ob3Dview &&
      userAccessMenu.ob3Dview.featureAccessLevelCode
        ? html`
            ${userAccessMenu.ob3Dview.featureAccessLevelCode == 'ViewAccess' ||
            userAccessMenu.ob3Dview.featureAccessLevelCode == 'EditAccess'
              ? html`<div
                  onClick=${() => assetsClick(cInd)}
                  title=${translate('stLabel.formcontrolsAssets')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="20"
                    height="20"
                    x="0"
                    y="0"
                    viewBox="0 0 24 24"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M6.75 22c0 .41-.34.75-.75.75H5c-2.07 0-3.75-1.68-3.75-3.75v-1c0-.41.34-.75.75-.75s.75.34.75.75v1c0 1.24 1.01 2.25 2.25 2.25h1c.41 0 .75.34.75.75zM19 1.25h-1c-.41 0-.75.34-.75.75s.34.75.75.75h1c1.24 0 2.25 1.01 2.25 2.25v1c0 .41.34.75.75.75s.75-.34.75-.75V5c0-2.07-1.68-3.75-3.75-3.75zM2 6.75c.41 0 .75-.34.75-.75V5c0-1.24 1.01-2.25 2.25-2.25h1c.41 0 .75-.34.75-.75s-.34-.75-.75-.75H5C2.93 1.25 1.25 2.93 1.25 5v1c0 .41.34.75.75.75zm20 10.5c-.41 0-.75.34-.75.75v1c0 1.24-1.01 2.25-2.25 2.25h-1c-.41 0-.75.34-.75.75s.34.75.75.75h1c2.07 0 3.75-1.68 3.75-3.75v-1c0-.41-.34-.75-.75-.75zm-4.12-.85-5 2.89c-.27.16-.57.23-.88.23s-.61-.08-.88-.23l-5-2.89c-.54-.31-.88-.89-.88-1.52V9.11c0-.62.33-1.2.88-1.52l5-2.89c.54-.31 1.21-.31 1.75 0l5 2.89c.54.31.88.89.88 1.52v5.77c0 .62-.33 1.2-.88 1.52zM7.51 8.53l4.37 2.53s.08.03.12.03c.04 0 .09-.01.12-.03l4.37-2.53-4.37-2.52s-.08-.03-.12-.03-.09.01-.12.03zm3.75 9.1v-5.22s-.09-.03-.12-.05L6.76 9.83v5.06c0 .09.05.17.12.22l4.38 2.53zm6-2.74V9.83l-4.38 2.53s-.08.03-.12.05v5.22l4.38-2.53c.08-.04.12-.13.12-.22z"
                        fill="#1A2254"
                        data-original="#000000"
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}
          `
        : ``}
      ${userAccessMenu &&
      userAccessMenu.obCreateAction &&
      userAccessMenu.obCreateAction.featureAccessLevelCode
        ? html`${(userAccessMenu.obCreateAction.featureAccessLevelCode &&
            userAccessMenu.obCreateAction.featureAccessLevelCode ==
              'ViewAccess') ||
          userAccessMenu.obCreateAction.featureAccessLevelCode == 'EditAccess'
            ? html`
                <div
                  onClick=${() => createAction(cInd)}
                  title=${translate('stLabel.createAction')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="18"
                    height="18"
                    x="0"
                    y="0"
                    viewBox="0 0 24 24"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5 12h-4v4a1 1 0 0 1-2 0v-4H7a1 1 0 0 1 0-2h4V7a1 1 0 0 1 2 0v4h4a1 1 0 0 1 0 2z"
                        data-name="Layer 2"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>
              `
            : ``}`
        : ``}
        ${userAccessMenu &&
          userAccessMenu.obdelete &&
          userAccessMenu.obdelete.featureAccessLevelCode
            ? html`
                ${userAccessMenu.obdelete.featureAccessLevelCode == 'DeleteAccess' ||
                userAccessMenu.obdelete.featureAccessLevelCode == 'EditAccess'
                  ? html`<div
                      onClick=${() => deleteClick(cInd)}
                      title=${translate('stLabel.delete')}
                      style="height:18px;margin-right:12px;cursor: pointer;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg"
     version="1.1"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:svgjs="http://svgjs.com/svgjs"
     width="18"
     height="18"
     x="0"
     y="0"
     viewBox="0 -960 960 960"
     fill="#e8eaed">
  
  <path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"
        data-name="Layer 2"
        fill="#1A2254"
        data-original="#000000"
        class=""/>
</svg>

                    </div>`
                  : ``}
              `
            : ``}

            ${userAccessMenu &&
              userAccessMenu.obHistory &&
              userAccessMenu.obHistory.featureAccessLevelCode
                ? html`
                    ${userAccessMenu.obHistory.featureAccessLevelCode == 'ViewAccess' ||
                    userAccessMenu.obHistory.featureAccessLevelCode == 'EditAccess'
                      ? html`<div  onClick=${() => getHistory(cInd)}
                  title=${translate('stLabel.ObsHistory')}
                  style="height:18px;margin-right:12px;cursor: pointer;">
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"    width="18"
                    height="18" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M15 10.25c-3.171 0-5.75 2.58-5.75 5.75s2.579 5.75 5.75 5.75 5.75-2.58 5.75-5.75-2.579-5.75-5.75-5.75zm1.955 6.5H15a.75.75 0 0 1-.75-.75v-2.035a.75.75 0 0 1 1.5 0v1.285h1.205a.75.75 0 0 1 0 1.5zM16 2.25H6C4.48 2.25 3.25 3.48 3.25 5v13c0 1.52 1.23 2.75 2.75 2.75h3.54A7.146 7.146 0 0 1 7.75 16c0-4 3.25-7.25 7.25-7.25 1.38 0 2.66.38 3.75 1.06V5c0-1.52-1.23-2.75-2.75-2.75zm-8 10.5H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h1c.41 0 .75.34.75.75s-.34.75-.75.75zm3-3H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75zm4-3H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75z" fill="#1A2254" opacity="1" data-original="#000000" class=""></path></g></svg>
        </div>`
                      : ``}
                  `
                : ``}
                 ${userAccessMenu &&
              userAccessMenu.obShare &&
              userAccessMenu.obShare.featureAccessLevelCode
                ? html`
                    ${userAccessMenu.obShare.featureAccessLevelCode == 'ViewAccess' ||
                    userAccessMenu.obShare.featureAccessLevelCode == 'EditAccess'
                      ? html`<div onClick=${() => sendMail(cInd)}
                  title=${translate('stLabel.sendMail')}
                  style="height:18px;margin-right:12px;cursor: pointer;">
                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"  width="18"
                    height="18" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path fill="#1A2254" d="m22.63 2.78-3.76 17.47c-.08.38-.33.7-.68.87s-.75.17-1.11 0l-2.8-1.36s-3.58 2.59-4.31 2.93c-.14.07-.2.06-.3.06a.748.748 0 0 1-.75-.74V16.7c-.01-.11 0-.22.06-.33s.01-.02.02-.03c.03-.05.06-.1.1-.15l9.21-10.08-11.45 9.42a.755.755 0 0 1-.96.1l-3.85-1.74c-.43-.21-.7-.63-.72-1.11-.01-.47.24-.91.66-1.14l18.8-10.23c.44-.24.96-.2 1.37.1.4.3.58.79.48 1.27z" opacity="1" data-original="#021e38" class=""></path></g></svg>
              </div>`
                      : ``}
                  `
                : ``}

                ${userAccessMenu &&
                  userAccessMenu.obEdit &&
                  userAccessMenu.obEdit.featureAccessLevelCode
                    ? html`
                        ${(userAccessMenu.obEdit.featureAccessLevelCode == 'ViewAccess' ||
                        userAccessMenu.obEdit.featureAccessLevelCode == 'EditAccess') && listData[cInd]["refSubProcess"]["name"] == 'CompEX' 
                          ? html`<div
                              onClick=${() => compExClick(cInd)}
                              title=${translate('stLabel.formcontrolsAssets')}
                              style="height:18px;margin-right:12px;cursor: pointer;"
                            >
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="stroke: rgb(26, 34, 84);"
    xmlns="http://www.w3.org/2000/svg" stroke="rgb(26, 34, 84);" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <!-- Explosion Symbol -->
    <path d="M12 2 L15 8 L22 9 L17 14 L18 21 L12 18 L6 21 L7 14 L2 9 L9 8 Z"/>

    <!-- Worker Helmet -->
    <path d="M8 16 C8 12 16 12 16 16"/>

    <!-- Body -->
    <line x1="12" y1="16" x2="12" y2="22"/>
</svg>
                            </div>`
                          : ``}
                      `
                    : ``}
              
           
    </div> `
  }
  
  function site1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i

    var isEdit = (listData[cInd]["createdBy"].toLowerCase() == (user && user.userPrincipalName.toLowerCase())) || isSiteAdmin;
    
    return html``
  }
  function sendMail(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'sendMail',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  function getHistory(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'observationHistory',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
 
const exportExcelFile = () => {
  if (!listData || listData.length == 0 || !displayedColumns || displayedColumns.length == 0) {
    console.log('No data or displayed columns to export');
    return;
  }
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("My Sheet");
  worksheet.properties.defaultRowHeight = 20;

   
    const headerMapping = JSON.parse(JSON.stringify(headersKeyVal));
    // Add header row with displayed columns using the header mapping
    const headerRow = displayedColumns.map(column => headerMapping[column] || column);

    const headerRowRef = worksheet.addRow(headerRow);

    // Bold the header row
    headerRowRef.eachCell(cell => {
      cell.font = { bold: true };
    });

    // Add data rows with displayed columns
    listData.forEach(item => {
      const rowData = displayedColumns.map(column => item[column]);
      worksheet.addRow(rowData);
    });

    // Set the column width for A, B, C, D, and E to 12
    const columnsToAdjust = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O'];
    columnsToAdjust.forEach(column => {
      const col = worksheet.getColumn(column);
      col.width = 30;
    });

  // promise.then(() => {
  //   const priceCol = worksheet.getColumn(5);

   
  workbook.xlsx.writeBuffer().then(buffer => {
    saveBufferToFile(buffer);
  });
  //});
 
};
const saveBufferToFile = (buffer) => {
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const link = document.createElement('a');

  // Generate a unique filename using the current timestamp
  const fileName = `Observation_List_${new Date().getMonth() + 1}_${new Date().getDate()}_${new Date().getFullYear()}_${new Date().getHours()}_${new Date().getMinutes()}.xlsx`;

  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  link.click();
 
};
  function viewClick(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'FormView',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  function editClick(index,isEdit) {
    if(isEdit){
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'FormEdit',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  }
  function assetsClick(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'Assets',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  function compExClick(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'CompEx',
        data: listData[parseInt(index)]
      },
      '*'
    )
  }
  
  function deleteClick(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'FormDelete',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }

  function createAction(index) {
    window.parent.postMessage(
      {
        type: 'ObservationList',
        action: 'createAction',
        data: listData[parseInt(index)],
        processType:listType
      },
      '*'
    )
  }

  function colFun() {
    var sortedDisplayedColumns = displayedColumns;
    console.log('sortedDisplayedColumns React', sortedDisplayedColumns, columnOrder)
    if(columnOrder){
      Object.entries(headersKeyVal).forEach(([key, value]) => {
        if ( columnOrder.find(item => item.key === key)) {
          headersKeyVal[key] = columnOrder.find(item => item.key === key).displayName || columnOrder.find(item => item.key === key).field;
        }
      })
      var fieldSequenceMap= columnOrder.reduce((map, item) => {
        map[item.key.toLowerCase()] = item.sequence;
        return map;
      }, {});
      sortedDisplayedColumns = displayedColumns.sort((a, b) => {
        const seqA = fieldSequenceMap[a.toLowerCase()] || Infinity; // Default to Infinity if not found
        const seqB = fieldSequenceMap[b.toLowerCase()] || Infinity;
        return seqA - seqB;
      });
    }

    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
  
    main.redefine('configuration', {
      columns: sortedDisplayedColumns,

      header: JSON.parse(JSON.stringify(headersKeyVal)),
      headerSummary: colSummary,
      format: {
        observationStatus:(x)=>{
          if(safeDisplayName!==undefined && notsafeDisplayName!==undefined){
          if(x=="Safe"){
            return safeDisplayName
          }else{
            return notsafeDisplayName
          }
        }else{
          return x
        }},
        isOperationalLearning: (x) => {
          if(x){
            return "Yes"
          }
          else{
            return "No"
          }
        },
        shortDescription: (x, i, j) => {
          return html`
            <div title=${x} style="cursor: pointer;">
              ${x.length > 20 ? x.substring(0, 20) + '...' : x}
            </div>
          `;
        },
        site: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        unit: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        location: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
          
        date: (x) => format(new Date(new Date(x).getUTCFullYear(),new Date(x).getUTCMonth(),new Date(x).getUTCDate()) , dateFormat),
        createdTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        id: (x) => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i),
      },

      align: {
        externalId: 'left',
        date: 'left',
        crafts: 'left',
        site: 'left',
        unit: 'left',
        shortDescription:'left',
  isOperationalLearning: 'left',
  operationalLearningDescription:'left',
  art: 'left',
  workOrderNumber:'left',
  projectName:'left',
  problem: 'left',
  solution: 'left',
  cause: 'left',
  measure: 'left',
        observationType:'left',
        observationStatus: 'left',
        description: 'left',
        itemNo: 'left',
        compExAssetDescription: 'left',
        inspectionNo: 'left',
        inspectionGrade: 'left',
        inspectionOutcome: 'left',
        approvalStatus: 'left',
        classification: 'left',
        assetTag: 'left',
        areaClassification: 'left',
        subTagNumber: 'left',
        manufacturerPartNo: 'left',
        manufacturer: 'left',
        serialNo: 'left',
        atexCategoryDevice: 'left',
        eplDevice: 'left',
        createdTime: 'left',
        createdBy: 'left',
        location: 'left',
        lastUpdatedTime: 'left',
        modifiedBy: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        externalId: 200,
        date: 200,
        crafts: 200,
        site: 200,
        unit: 200,
        shortDescription:200,
        isOperationalLearning: 200,
  operationalLearningDescription:200,
  art: 200,
  workOrderNumber:200,
  projectName:200,
  problem: 200,
  solution: 200,
  cause: 200,
  measure: 200,
        observationType:200,
        observationStatus: 200,
        description: 200,
        itemNo: 'left',
        compExAssetDescription: 200,
        inspectionNo: 200,
        inspectionGrade: 200,
        inspectionOutcome: 200,
        approvalStatus: 200,
        classification: 200,
        assetTag: 200,
        areaClassification: 200,
        subTagNumber: 200,
        manufacturerPartNo: 200,
        manufacturer: 200,
        serialNo: 200,
        atexCategoryDevice: 200,
        eplDevice: 200,
        createdOn: 200,
        createdBy: 200,
        location: 200,
        updatedOn: 200,
        updatedBy: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  function getData() {
    window.parent.postMessage({
      type: 'ObservationList', action: 'Loading', data: true,
    }, '*' )
    if (!tabledata){
    console.log('getData called')
    console.log(startDate, endDate)
    fetch(
      Constants.NODE_API + '/api/service/list' + Constants.typeObservation,
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site,
          units:unit,
          startDate: startDate,
          endDate: endDate,
          isActive: true,
          listType:listType,
          process:processId,
          category:category,
          subCategory:subCategory,
          operLearning:operLearning,
          createdByfilter: createdByfilter,
          statusfilter:statusfilter,
          behalf:behalf,
          projectName: projectName,
          workOrderNumber: workOrderNumber,
          location:location,
          subProcess:subProcess
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   if(temp){
        //     console.log('temp before', temp[1].childNodes[0].nodeValue)
        //     temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //     console.log('temp after', temp[1].childNodes[0].nodeValue)
        //   }
         
        // }
        var listProcess =
          result['data']['list' + Constants.typeObservation]['items']
        listData = []
        listProcess.forEach((element) => {
          element['actions'] = ''
          element['site'] = element['refSite']
            ? element['refSite']['description']
            : ''
          element['unit'] = element['refUnit']
            ? element['refUnit']['description']
            : '';
            
        element['location'] = element['refReportingLocation']
        ? element['refReportingLocation']['description']
        : '';
        element['workOrderNumber'] = element['refWorkOrderHeader']
        ? element['refWorkOrderHeader']['number']
        : '';
          var subProcessName = element['refSubProcess'] ? element['refSubProcess']["name"] : undefined;
          if(element['refSubProcess']){
            subCategoryName = element['refSubProcess']["name"];
          }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
            try{
              // subProcessName = element['refOFWAProcess']['items'][0]["refOFWAProcess"] && element['refOFWAProcess']['items'][0]["refOFWAProcess"]["refOFWAProcess"]["name"];
              subProcessName = element['refOFWAProcess']['items'][0]["refOFWAProcess"] && element['refOFWAProcess']['items'][0]["refOFWAProcess"]["name"];
            }
            catch{
              subProcessName = "";
            }
          }else{
            subProcessName = "";
          }
          
          var subCategoryName;
          if(element['refSubCategory']['items'].length > 0){
            subCategoryName = element['refSubCategory']['items'][0]["name"];
          }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
            subCategoryName = element['refOFWAProcess']['items'][0]["name"];
          }else{
            subCategoryName = "";
          }
          var categoryName;
          if(element['refCategory']['items'].length > 0){
            categoryName = element['refCategory']['items'][0]["name"];
          }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
            categoryName = element['refOFWAProcess']['items'][0]["refOFWAProcess"] && element['refOFWAProcess']['items'][0]["refOFWAProcess"]["name"];
          }else{
            categoryName = "";
          }
          element['observationType'] = subProcessName;
          element['subCategory'] = subCategoryName;
          element['category'] = categoryName;
  
          element['crafts'] = element['crafts']

          if(element['refCompExAsset']){
            element['itemNo'] = element['refCompExAsset']["itemNo"];
            element['classification'] = element['refCompExAsset']["classification"];
            element['compExAssetDescription'] = element['refCompExAsset']["description"];
            element['inspectionNo'] = element['refCompExAsset']["inspectionNo"];
            element['inspectionGrade'] = element['refCompExAsset']["inspectionGrade"];
            element['inspectionOutcome'] = element['refCompExAsset']["inspectionOutcome"];
            element['approvalStatus'] = element['refCompExAsset']["approvalStatus"];
            element['areaClassification'] = element['refCompExAsset']["areaClassification"];
            
            element['subTagNumber'] = element['refCompExAsset']["subTagNumber"];
            element['manufacturerPartNo'] = element['refCompExAsset']["manufacturerPartNo"];
            element['manufacturer'] = element['refCompExAsset']["manufacturer"];
            element['serialNo'] = element['refCompExAsset']["serialNo"];
            element['atexCategoryDevice'] = element['refCompExAsset']["atexCategoryDevice"];
            element['eplDevice'] = element['refCompExAsset']["eplDevice"];
            element['classification'] = element['refCompExAsset']["classification"];
          }
          listData.push(element)
        })
        //  main.redefine("data", listData);
        window.parent.postMessage(
          {
            type: 'ObservationList',
            action: 'Loading',
            data: false,
          },
          '*'
        )
        setCurrentPage(1)
        initFlag = true
        filterData()
        // colFun();
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }
  else{
    // process(tabledata)
    setting()
    window.parent.postMessage({
        type: 'ObservationList', action: 'Loading', data: false,
      }, '*' )
  }
}
function process(result) {
  console.log('result', result)
  var listProcess = result
    // result['data']['list' + Constants.typeObservation]['items'];
    listData = []
    listProcess.forEach((element) => {
      element['actions'] = ''
      element['site'] = element['refSite']
        ? element['refSite']['description']
        : ''
      element['unit'] = element['refUnit']
        ? element['refUnit']['description']
        : '';
        element['location'] = element['refReportingLocation']
        ? element['refReportingLocation']['description']
        : '';
      var subProcessName = element['refSubProcess'] ? element['refSubProcess']["name"] : undefined;
      if(element['refSubProcess']){
        subCategoryName = element['refSubProcess']["name"];
      }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
        try{
        subProcessName = element['refOFWAProcess']['items'][0]["refOFWAProcess"] && element['refOFWAProcess']['items'][0]["refOFWAProcess"]["refOFWAProcess"]["name"];
      }
      catch{
        subProcessName = "";
      }
      }else{
        subProcessName = "";
      }
      
      var subCategoryName;
      if(element['refSubCategory']['items'].length > 0){
        subCategoryName = element['refSubCategory']['items'][0]["name"];
      }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
        subCategoryName = element['refOFWAProcess']['items'][0]["name"];
      }else{
        subCategoryName = "";
      }
      var categoryName;
      if(element['refCategory']['items'].length > 0){
        categoryName = element['refCategory']['items'][0]["name"];
      }else if(element['refOFWAProcess'] && element['refOFWAProcess']['items'].length > 0){
        categoryName = element['refOFWAProcess']['items'][0]["refOFWAProcess"] && element['refOFWAProcess']['items'][0]["refOFWAProcess"]["name"];
      }else{
        categoryName = "";
      }
      element['observationType'] = subProcessName;
      element['subCategory'] = subCategoryName;
      element['category'] = categoryName;

      element['crafts'] = element['crafts']

      listData.push(element)
    })
    //  main.redefine("data", listData);
    
    setCurrentPage(1)
    initFlag = true
    filterData()
    // colFun();
}

  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
        if (tabledata){
          process(tabledata)
        }
      })
  }
  function filterData() {
    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      console.log(currentList)
      main.redefine('data', currentList)
      colFun()
    }
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
    <DynamicTranslationArea
      getAuthToken={getAuthToken}
      dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
      shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
    >
      <TranslationContextProvider
        getAuthToken={getAuthToken}
      >
        <div ref={viewofSelectionRef} />
      </TranslationContextProvider>
      </DynamicTranslationArea>
      <div className='tableBottom no-translate'>
        <div></div>
        <Pagination
          className='pagination-bar'
          // assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
            {/* {
              localStorage.getItem('APP-OFWATranslationData'+prevLanguage) ? (translate('stLabel.paginationRowsPerPage')):('stLabel.paginationRowsPerPage')
            }: &nbsp; */}
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default ObservationList
