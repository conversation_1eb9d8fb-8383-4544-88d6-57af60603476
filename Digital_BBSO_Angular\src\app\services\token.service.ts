import { Injectable } from '@angular/core';


const ACCESS_TOKEN = "access_token";
const REFRESH_TOKEN = 'refresh_token';

const IDACCESS_TOKEN = "id_access_token";
const IDREFRESH_TOKEN = 'id_refresh_token';

@Injectable({
  providedIn: 'root'
})
export class TokenService {



  constructor() { }
  getToken(): any {
    return localStorage.getItem(ACCESS_TOKEN);
  }

  getRefreshToken(): any {
    return localStorage.getItem(REFRESH_TOKEN);
  }

  saveToken(token:any): void {
    localStorage.setItem(ACCESS_TOKEN, token);
  }

  saveRefreshToken(refreshToken:any): void {
    localStorage.setItem(REFRESH_TOKEN, refreshToken);
  }

  removeToken(): void {
    localStorage.removeItem(ACCESS_TOKEN);
  }

  removeRefreshToken(): void {
    localStorage.removeItem(REFRESH_TOKEN);
  }

  //ID TOKEN
  getIDToken(): any {
    return localStorage.getItem(IDACCESS_TOKEN);
  }

  getIDRefreshToken(): any {
    return localStorage.getItem(IDREFRESH_TOKEN);
  }

  saveIDToken(token:any): void {
    localStorage.setItem(IDACCESS_TOKEN, token);
  }

  async getIdTokenFromMsal() {
    // Simulating an asynchronous operation
    return new Promise((resolve) => {
      const token = localStorage.getItem(IDACCESS_TOKEN);
      this.saveIDToken(token);
      resolve(token);
    });
  }

  saveIDRefreshToken(refreshToken:any): void {
    localStorage.setItem(IDREFRESH_TOKEN, refreshToken);
  }

  removeIDToken(): void {
    localStorage.removeItem(IDACCESS_TOKEN);
  }

  removeIDRefreshToken(): void {
    localStorage.removeItem(IDREFRESH_TOKEN);
  }


  
}
