<ng-container *ngIf="tagName == 'h6'">
    <h6 [class]="cstClassName">
        <i *ngIf="icon == 'Attention'">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"
                viewBox="0 0 22 22">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#071734" />
                        <stop offset="1" stop-color="#596477" />
                    </linearGradient>
                </defs>
                <path id="work_alert_FILL0_wght400_GRAD0_opsz48"
                    d="M81.571,194.333v0Zm6.286-14.143H93.1v-2.619H87.857ZM81.571,195.9A1.611,1.611,0,0,1,80,194.333V181.762a1.611,1.611,0,0,1,1.571-1.571h4.714v-2.619A1.611,1.611,0,0,1,87.857,176H93.1a1.611,1.611,0,0,1,1.571,1.571v2.619h4.714a1.611,1.611,0,0,1,1.571,1.571v6.05a5.548,5.548,0,0,0-.746-.5,8.063,8.063,0,0,0-.825-.393v-5.16H81.571v12.571h9.036a4.645,4.645,0,0,0,.22.81,7.675,7.675,0,0,0,.33.762ZM97.024,198a4.814,4.814,0,0,1-3.51-1.451,4.755,4.755,0,0,1-1.467-3.51,4.976,4.976,0,1,1,9.952.01,4.74,4.74,0,0,1-1.467,3.5A4.82,4.82,0,0,1,97.024,198Zm-.013-1.99a.6.6,0,0,0,.419-.157.6.6,0,0,0,0-.838.581.581,0,0,0-.4-.157.593.593,0,0,0-.419,1A.549.549,0,0,0,97.011,196.01Zm-.458-2.226h.917v-3.745h-.917Z"
                    transform="translate(-80 -176)" fill="url(#linear-gradient)" />
            </svg>
        </i>
        <i *ngIf="icon == 'event'">

            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20"
                height="21.333" viewBox="0 0 20 21.333">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#081734" />
                        <stop offset="1" stop-color="#5a6477" />
                    </linearGradient>
                </defs>
                <path id="Icon_metro-calendar" data-name="Icon metro-calendar"
                    d="M9.237,9.928H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,8H7.9v2.667H5.237Zm4,0H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm-4-4H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,0H7.9v2.667H5.237Zm14.667-12V3.261H17.237V1.928H7.9V3.261H5.237V1.928H2.571V23.261h20V1.928H19.9Zm1.333,20H3.9V7.261H21.237Z"
                    transform="translate(-2.571 -1.928)" fill="url(#linear-gradient)" />
            </svg>

        </i>
        <i *ngIf="icon == 'Reminders'">


            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20"
                height="17.775" viewBox="0 0 20 17.775">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#13223d" />
                        <stop offset="1" stop-color="#616b7d" />
                    </linearGradient>
                </defs>
                <path id="format_list_bulleted_FILL0_wght400_GRAD0_opsz48"
                    d="M128.121,272.745v-1.669H141v1.669Zm0-7.01v-1.669H141v1.669Zm0-7.038v-1.669H141V258.7Zm-5.229,15.076a1.869,1.869,0,0,1-1.336-.529,1.748,1.748,0,0,1-.556-1.321,1.853,1.853,0,0,1,1.878-1.878,1.748,1.748,0,0,1,1.321.556,1.869,1.869,0,0,1,.529,1.336,1.841,1.841,0,0,1-1.836,1.836Zm0-7.01a1.835,1.835,0,0,1-1.336-.547,1.837,1.837,0,0,1,0-2.632,1.835,1.835,0,0,1,1.336-.547,1.755,1.755,0,0,1,1.3.547,1.876,1.876,0,0,1,0,2.632A1.755,1.755,0,0,1,122.892,266.765Zm-.028-7.038a1.869,1.869,0,1,1,1.316-.547A1.795,1.795,0,0,1,122.864,259.727Z"
                    transform="translate(-121 -256)" fill="url(#linear-gradient)" />
            </svg>
        </i>
        <i *ngIf="icon == 'Recognition'">


            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20"
                viewBox="0 0 20 20">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#1e2c46" />
                        <stop offset="1" stop-color="#06090e" />
                    </linearGradient>
                </defs>
                <path id="social_leaderboard_FILL0_wght400_GRAD0_opsz48"
                    d="M89.994,194.5a4.983,4.983,0,1,0-3.537-1.462A4.836,4.836,0,0,0,89.994,194.5ZM86.65,183.925a6.371,6.371,0,0,1,1.5-.662A6.514,6.514,0,0,1,89.825,183l-2.75-5.5h-3.65Zm6.7,0,3.225-6.425h-3.65l-2.075,4.175.75,1.5a6.536,6.536,0,0,1,.912.313A6.145,6.145,0,0,1,93.35,183.925Zm-8.525,9.5a6.676,6.676,0,0,1-.975-1.812,6.55,6.55,0,0,1,0-4.225,6.675,6.675,0,0,1,.975-1.812,3.978,3.978,0,0,0-2.375,1.337,4,4,0,0,0,0,5.175A3.979,3.979,0,0,0,84.825,193.425Zm10.35,0a3.979,3.979,0,0,0,2.375-1.337,4,4,0,0,0,0-5.175,3.978,3.978,0,0,0-2.375-1.337,6.675,6.675,0,0,1,.975,1.813,6.55,6.55,0,0,1,0,4.225A6.675,6.675,0,0,1,95.175,193.425ZM90,196a6.327,6.327,0,0,1-1.912-.287,6.947,6.947,0,0,1-1.687-.788,2.782,2.782,0,0,1-.45.063q-.225.012-.475.012a5.485,5.485,0,0,1-4.025-9.2,5.335,5.335,0,0,1,3.575-1.725L81,176h7l2,4,2-4h7l-4,8.025a5.389,5.389,0,0,1,3.563,1.75A5.5,5.5,0,0,1,94.5,195q-.225,0-.463-.012a2.808,2.808,0,0,1-.463-.062,7.008,7.008,0,0,1-1.675.788A6.207,6.207,0,0,1,90,196ZM90,189.5Zm-3.35-5.575L83.425,177.5Zm6.7,0,3.225-6.425Zm-5.2,8.325.7-2.275L87,188.65h2.275l.725-2.4.725,2.4H93l-1.85,1.325.7,2.275L90,190.85Z"
                    transform="translate(-80 -176)" fill="url(#linear-gradient)" />
            </svg>


        </i>
        <!-- {{ firstlabelText | translate }}  -->
        <!-- {{ labelText | translate }} -->
          {{ firstlabelText }}
          {{ labelText }}
    </h6>
</ng-container>

<ng-container *ngIf="tagName == 'h4'">
    <h4 [class]="cstClassName">
        <i *ngIf="icon == 'Attention'">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"
                viewBox="0 0 22 22">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#071734" />
                        <stop offset="1" stop-color="#596477" />
                    </linearGradient>
                </defs>
                <path id="work_alert_FILL0_wght400_GRAD0_opsz48"
                    d="M81.571,194.333v0Zm6.286-14.143H93.1v-2.619H87.857ZM81.571,195.9A1.611,1.611,0,0,1,80,194.333V181.762a1.611,1.611,0,0,1,1.571-1.571h4.714v-2.619A1.611,1.611,0,0,1,87.857,176H93.1a1.611,1.611,0,0,1,1.571,1.571v2.619h4.714a1.611,1.611,0,0,1,1.571,1.571v6.05a5.548,5.548,0,0,0-.746-.5,8.063,8.063,0,0,0-.825-.393v-5.16H81.571v12.571h9.036a4.645,4.645,0,0,0,.22.81,7.675,7.675,0,0,0,.33.762ZM97.024,198a4.814,4.814,0,0,1-3.51-1.451,4.755,4.755,0,0,1-1.467-3.51,4.976,4.976,0,1,1,9.952.01,4.74,4.74,0,0,1-1.467,3.5A4.82,4.82,0,0,1,97.024,198Zm-.013-1.99a.6.6,0,0,0,.419-.157.6.6,0,0,0,0-.838.581.581,0,0,0-.4-.157.593.593,0,0,0-.419,1A.549.549,0,0,0,97.011,196.01Zm-.458-2.226h.917v-3.745h-.917Z"
                    transform="translate(-80 -176)" fill="url(#linear-gradient)" />
            </svg>
        </i>
        <i *ngIf="icon == 'event'">

            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20"
                height="21.333" viewBox="0 0 20 21.333">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#081734" />
                        <stop offset="1" stop-color="#5a6477" />
                    </linearGradient>
                </defs>
                <path id="Icon_metro-calendar" data-name="Icon metro-calendar"
                    d="M9.237,9.928H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,8H7.9v2.667H5.237Zm4,0H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm-4-4H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,0H7.9v2.667H5.237Zm14.667-12V3.261H17.237V1.928H7.9V3.261H5.237V1.928H2.571V23.261h20V1.928H19.9Zm1.333,20H3.9V7.261H21.237Z"
                    transform="translate(-2.571 -1.928)" fill="url(#linear-gradient)" />
            </svg>

        </i>
        <i *ngIf="icon == 'Reminders'">


            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20"
                height="17.775" viewBox="0 0 20 17.775">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#13223d" />
                        <stop offset="1" stop-color="#616b7d" />
                    </linearGradient>
                </defs>
                <path id="format_list_bulleted_FILL0_wght400_GRAD0_opsz48"
                    d="M128.121,272.745v-1.669H141v1.669Zm0-7.01v-1.669H141v1.669Zm0-7.038v-1.669H141V258.7Zm-5.229,15.076a1.869,1.869,0,0,1-1.336-.529,1.748,1.748,0,0,1-.556-1.321,1.853,1.853,0,0,1,1.878-1.878,1.748,1.748,0,0,1,1.321.556,1.869,1.869,0,0,1,.529,1.336,1.841,1.841,0,0,1-1.836,1.836Zm0-7.01a1.835,1.835,0,0,1-1.336-.547,1.837,1.837,0,0,1,0-2.632,1.835,1.835,0,0,1,1.336-.547,1.755,1.755,0,0,1,1.3.547,1.876,1.876,0,0,1,0,2.632A1.755,1.755,0,0,1,122.892,266.765Zm-.028-7.038a1.869,1.869,0,1,1,1.316-.547A1.795,1.795,0,0,1,122.864,259.727Z"
                    transform="translate(-121 -256)" fill="url(#linear-gradient)" />
            </svg>
        </i>
        <i *ngIf="icon == 'Recognition'">


            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20"
                viewBox="0 0 20 20">
                <defs>
                    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                        <stop offset="0" stop-color="#1e2c46" />
                        <stop offset="1" stop-color="#06090e" />
                    </linearGradient>
                </defs>
                <path id="social_leaderboard_FILL0_wght400_GRAD0_opsz48"
                    d="M89.994,194.5a4.983,4.983,0,1,0-3.537-1.462A4.836,4.836,0,0,0,89.994,194.5ZM86.65,183.925a6.371,6.371,0,0,1,1.5-.662A6.514,6.514,0,0,1,89.825,183l-2.75-5.5h-3.65Zm6.7,0,3.225-6.425h-3.65l-2.075,4.175.75,1.5a6.536,6.536,0,0,1,.912.313A6.145,6.145,0,0,1,93.35,183.925Zm-8.525,9.5a6.676,6.676,0,0,1-.975-1.812,6.55,6.55,0,0,1,0-4.225,6.675,6.675,0,0,1,.975-1.812,3.978,3.978,0,0,0-2.375,1.337,4,4,0,0,0,0,5.175A3.979,3.979,0,0,0,84.825,193.425Zm10.35,0a3.979,3.979,0,0,0,2.375-1.337,4,4,0,0,0,0-5.175,3.978,3.978,0,0,0-2.375-1.337,6.675,6.675,0,0,1,.975,1.813,6.55,6.55,0,0,1,0,4.225A6.675,6.675,0,0,1,95.175,193.425ZM90,196a6.327,6.327,0,0,1-1.912-.287,6.947,6.947,0,0,1-1.687-.788,2.782,2.782,0,0,1-.45.063q-.225.012-.475.012a5.485,5.485,0,0,1-4.025-9.2,5.335,5.335,0,0,1,3.575-1.725L81,176h7l2,4,2-4h7l-4,8.025a5.389,5.389,0,0,1,3.563,1.75A5.5,5.5,0,0,1,94.5,195q-.225,0-.463-.012a2.808,2.808,0,0,1-.463-.062,7.008,7.008,0,0,1-1.675.788A6.207,6.207,0,0,1,90,196ZM90,189.5Zm-3.35-5.575L83.425,177.5Zm6.7,0,3.225-6.425Zm-5.2,8.325.7-2.275L87,188.65h2.275l.725-2.4.725,2.4H93l-1.85,1.325.7,2.275L90,190.85Z"
                    transform="translate(-80 -176)" fill="url(#linear-gradient)" />
            </svg>


        </i>
        <!-- {{ firstlabelText | translate }} {{ labelText | translate }} {{labelTextNoLan}} -->
        {{ firstlabelText }} {{ labelText }} {{labelTextNoLan}}
    </h4>
</ng-container>

<ng-container *ngIf="tagName == 'h2'">
    <h2 [class]="cstClassName">{{ labelText }}</h2>
</ng-container>

<ng-container *ngIf="tagName == 'label'">
    <mat-label [class]="cstClassName">
        <span>          {{ labelText }}</span>
        <span *ngIf="strict" class="strict">*</span>
    </mat-label>
</ng-container>
