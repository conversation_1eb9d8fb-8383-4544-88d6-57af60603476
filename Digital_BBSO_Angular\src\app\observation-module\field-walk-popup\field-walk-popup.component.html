<div>
    <div class="field-walk-row" fxLayout="row" fxLayoutAlign="space-between center">
        <div>
            <span class="subheading-1 palette-color1">
                Suggestions for Field Walk
            </span>
        </div>
        <div >
            <mat-icon style="color: #00B7DA;margin-top: 5px;cursor: pointer;" [mat-dialog-close]="true">cancel</mat-icon>
        </div>
    </div>

    <div class="field-walk-column" >
      
            <div>
                <span class="subheading-1 observe-color">
                    Least observed locations
                </span>
            </div>
            <br>
        
            <div fxLayout="row wrap" fxLayoutGap="25px">
                <div class="field-walk-box1"  *ngFor="let item of locationArray" (click)="locationClick(item)" fxLayoutAlign="center center">
                    <span>
                       {{item.name}}
                    </span>
                </div>
            

            </div>
            <div>
                <span class="subheading-1 observe-color">
                    Categories at risk
                </span>
            </div>
            <br>
        
            <div fxLayout="row wrap" fxLayoutGap="25px">
                <div class="field-walk-box2"  *ngFor="let item of categoriesEidk" fxLayoutAlign="center center" (click)="categoriesRisk(item)">
                    <span>
                        {{item.name}}
                    </span>
                </div>
              

            </div>


       


    </div>
</div>