import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ObservationsComponent } from './observations/observations.component';
import { ObservationListComponent } from './observation-list/observation-list.component';
import { CreateObservationComponent } from './create-observation/create-observation.component';
import { SubObservationComponent } from './sub-observation/sub-observation.component';
import { BehaviourChecklistComponent } from './behaviour-checklist/behaviour-checklist.component';
import { FieldVisitComponent } from './field-visit/field-visit.component';
import { QualityAssessmentComponent } from './quality-assessment/quality-assessment.component';
import { QualityUploadDocComponent } from './quality-upload-doc/quality-upload-doc.component';
import { AuditPlanQualityAssComponent } from './audit-plan-quality-ass/audit-plan-quality-ass.component';
import { AuditPlanQualityFileDocComponent } from './audit-plan-quality-file-doc/audit-plan-quality-file-doc.component';
import { AuditPlanScorecardComponent } from './audit-plan-scorecard/audit-plan-scorecard.component';
import { AuditPlanSummaryComponent } from './audit-plan-summary/audit-plan-summary.component';
import { FieldWalkListComponent } from './fieldwalk-list/fieldwalk-list.component';
import { AuditListComponent } from './audit-list/audit-list.component';
import { CompletedComponent } from './completed/completed.component';
import { ListComponent } from './list/list.component';
import { checklistmetricsComponent } from './Checklist-Metrics/checklist-metrics.component';
import { generalinfoComponent } from './general-info/general-infocomponent';

const routes: Routes = [
  { path: '', redirectTo: 'observation', pathMatch: 'full' },
  {
    path: 'observation',
    component: ObservationsComponent
  },
  {
    path: 'observation-list', 
    component: ObservationListComponent
  },
  {
    path: 'fieldwalk-list', 
    component: FieldWalkListComponent
  },
  {
    path: 'audit-list', 
    component: AuditListComponent
  },
  {
    path: 'create-observation',
    component: CreateObservationComponent
  },
  {
    path: 'sub-observe', 
    component: SubObservationComponent

  },
  {
    path: 'check-list', 
    component: BehaviourChecklistComponent
  },
  {
    path:'field-visit',
    component:FieldVisitComponent
  },
  {
    path:'quality-assessment',
    component:QualityAssessmentComponent
  },
  {
    path:'general-info',
    component:generalinfoComponent
  },
  {
   path:'upload-doc',
   component:QualityUploadDocComponent,
  },
  {
    path:'audit-plan-quality',
    component:AuditPlanQualityAssComponent
  },
  {
    path:'audit-plan-file',
    component:AuditPlanQualityFileDocComponent
  },
  {
    path:'scorecard',
    component:AuditPlanScorecardComponent
  },
  {
    path:'checklist-metrics',
    component:checklistmetricsComponent
  },
  {
    path:'audit-summary',
    component:AuditPlanSummaryComponent
  },
  {
    path:'completed',
    component:CompletedComponent
   },
   {
     path:'list',
     component:ListComponent
    }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ObservationRoutingModule { }