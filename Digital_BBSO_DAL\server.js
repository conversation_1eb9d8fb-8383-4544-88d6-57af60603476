console.info('\nThe server initializing... ');
const express = require('express');
const bodyParser = require('body-parser')
const morgan = require('morgan');
const cors = require('cors');
const app = express();
const BearerStrategy = require('passport-azure-ad').BearerStrategy;
const passport = require('passport');
const constant = require('./api/constant/constant');
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0;
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || 8091;

process.env.AppID = process.env.AppID                             || "OFWA-Dev";
process.env.AzureTenantID = process.env.AzureTenantID             || "7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37";
process.env.AzureClientID = process.env.AzureClientID             || "01c3a55a-e036-4030-854b-8c256966f6aa";
process.env.AzureAudience = process.env.AzureAudience             || "https://az-eastus-1.cognitedata.com";
process.env.AzureAuthority = process.env.AzureAuthority           || "login.microsoftonline.com";
process.env.AzureDiscovery = process.env.AzureDiscovery           || ".well-known/openid-configuration";
process.env.AzureVersion = process.env.AzureVersion               || "v2.0";
process.env.AzureValidateIssuer = process.env.AzureValidateIssuer || true;
process.env.AzurePassReqToCallback = process.env.AzurePassReqToCallback || false;
process.env.AzureLoggingLevel = process.env.AzureLoggingLevel     || "info";

process.env.project = process.env.project                         || "celanese-dev"; 

app.use(cors());
//app.use(express.json());

// parse application/x-www-form-urlencoded
//app.use(bodyParser.urlencoded({ extended: true }))

app.use(bodyParser.urlencoded({ extended: false }))

// parse application/json
// app.use(bodyParser.json())


// parse application/json
//app.use(bodyParser.json())
app.use(bodyParser.json({ limit: "50mb" }))
app.use(bodyParser.urlencoded({ limit: "50mb", extended: false, parameterLimit: 50000000 }))

app.use(morgan('dev'));

// console.log(__dirname);

require('./routes')(app);
app.get('/', (req, res) => {
  res.send('Node CDF working')
});


process
  .on('unhandledRejection', (reason, p) => {
    console.error('\x1b[31m%s\x1b[0m', 'Unhandled Rejection at Promise');
    console.error('\x1b[31m%s\x1b[0m',reason, p);
  })
  .on('uncaughtException', err => {
    console.error('\x1b[31m%s\x1b[0m', 'Uncaught Exception thrown');
    console.error('\x1b[31m%s\x1b[0m', err+'\n');
    // process.exit(1);
    console.log("caughtException" + err.stack);
  });

process.on('beforeExit', code => {
  // Can make asynchronous calls
  setTimeout(() => {
    console.log(`beforeExit Process will exit with code: ${code}`)
    const memory = process.memoryUsage();
    var usedMemory = (memory.heapUsed / 1024 / 1024 / 1024).toFixed(4) + 'GB';
    console.log("usedMemory " + usedMemory);
    process.exit(code)
  }, 100)
})

process.on('exit', code => {
  // Only synchronous calls
  const memory = process.memoryUsage();
  var usedMemory = (memory.heapUsed / 1024 / 1024 / 1024).toFixed(4) + 'GB';
  console.log("usedMemory " + usedMemory);
  console.log(`exit Process exited with code: ${code}`)
})

// const methodOverride = require('method-override')
// app.use(methodOverride())
app.use(clientErrorHandler)
app.use(errorHandler)
function clientErrorHandler(err, req, res, next) {
  console.log("clientErrorHandler")
  console.log(err)
  if (req.xhr) {
    // res.status(500).send({ error: 'Something failed!' })
    res.status(500).json({
      code: 500,
      status:'error',
      data: [],
      message: 'Error'
    });
  } else {
    next(err)
  }
}
function errorHandler (err, req, res, next) {
  console.log("errorHandler")
  if (res.headersSent) {
    return next(err)
  }
  // res.status(500)
  // res.render('error', { error: err })
  res.status(500).json({
    code: 500,
    status:'error',
    data: [],
    message: "Error"
  });
  
}

const authOptions = {
  identityMetadata: `https://${process.env.AzureAuthority}/${process.env.AzureTenantID }/${process.env.AzureVersion}/${process.env.AzureDiscovery}`,
 // issuer: `https://${config.metadata.authority}/${config.credentials.tenantID}/${config.metadata.version}`,
  clientID: process.env.AzureClientID,
  audience: process.env.AzureAudience,//"00000003-0000-0000-c000-000000000000" ,//"https://api.cognitedata.com",// "00000003-0000-0000-c000-000000000000", //config.credentials.audience,
  validateIssuer: process.env.AzureValidateIssuer,
  passReqToCallback: process.env.AzurePassReqToCallback,
  //loggingLevel: config.settings.loggingLevel,
  //loggingNoPII: false,
  issuer: `https://sts.windows.net/${process.env.AzureTenantID }/`,
  scope:['DATA.VIEW']// ['DATA.VIEW IDENTITY user_impersonation'] //scopesCognite //['user.read'],// [ 'openid',]
  //scope: EXPOSED_SCOPES
 // isB2C: false
};

const bearerStrategy = new BearerStrategy(authOptions, (token, done) => {
      // Send user info using the second argument
     // console.log('token',token)
      done(null, {}, token);
  }
);
passport.use(bearerStrategy);
app.get('/api',
    passport.authenticate('oauth-bearer', {session: false}),
    (req, res) => {
       // console.log(' req.authInfo', req.authInfo)
       // console.log('Validated claims: ', req.authInfo);

        // Service relies on the name claim.  
        res.status(200).json(req.authInfo)
    }
);

app.get('/typeConfig',  (req, res) => {
   res.status(200).json(constant)
});

var http = require('http');
var https = require('https');
var fs = require('fs');
var privateKey = fs.readFileSync('./ssl/key.pem', 'utf8');
var certificate = fs.readFileSync('./ssl/cert.pem', 'utf8');
var credentials = { key: privateKey, cert: certificate };
// your express configuration here


// var httpsServer = https.createServer(credentials, app);
// httpsServer.listen(process.env.PORT, '0.0.0.0', () => {
//   console.info('\x1b[36m%s\x1b[0m', '\nThe server is running on port           : ' + process.env.PORT);
//   console.info('\x1b[36m%s\x1b[0m', '\nHTTPS           ');
// });

var httpServer = http.createServer(app);
httpServer.listen(process.env.PORT, '0.0.0.0', () => {
  console.info('\x1b[36m%s\x1b[0m', '\nThe server is running on port           : ' + process.env.PORT);
  console.info('\x1b[36m%s\x1b[0m', '\nHTTP           ');
});


module.exports = app;