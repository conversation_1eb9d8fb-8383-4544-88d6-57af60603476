import { Injectable } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from './language.service';
import { CommonService } from './common.service';
import { environment } from 'src/environments/environment';

@Injectable()
export class CustomPaginatorIntl extends MatPaginatorIntl {
  labels = {}
  constructor(private translate: TranslateService, private languageService: LanguageService, private commonService: CommonService) {
    super();
    this.translate.onLangChange.subscribe(() => this.updateLabels());
    this.languageService.language$.subscribe((language) => this.updateLabels());
    this.updateLabels();
  }

  private updateLabels() {
    this.labels = {
      'paginationRowsPerPage': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'paginationRowsPerPage'] || 'paginationRowsPerPage',
    }
    // this.itemsPerPageLabel = this.translate.instant('REACT_TABLE.ROWS_PER_PAGE');
    this.itemsPerPageLabel = this.labels['paginationRowsPerPage'];
    console.log('Inside Update Labels: ',this.itemsPerPageLabel);
  }
}
