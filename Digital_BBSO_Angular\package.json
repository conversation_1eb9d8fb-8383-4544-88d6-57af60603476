{"name": "bbso", "version": "1.5.71", "scripts": {"ng": "ng", "start": "ng serve --open --port 4900 --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "e2e": "ng e2e", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "private": true, "dependencies": {"@angular/animations": "^14.1.0", "@angular/cdk": "^14.2.3", "@angular/common": "^14.1.0", "@angular/compiler": "^14.1.0", "@angular/core": "^14.1.0", "@angular/flex-layout": "^13.0.0-beta.38", "@angular/forms": "^14.1.0", "@angular/material": "^14.2.3", "@angular/material-moment-adapter": "^14.1.0", "@angular/platform-browser": "^14.1.0", "@angular/platform-browser-dynamic": "^14.1.0", "@angular/router": "^14.1.0", "@azure/msal-angular": "^2.4.3", "@azure/msal-browser": "^2.28.3", "@cognite/reveal": "^4.1.1", "@cognite/sdk": "^8.0.1", "@cognite/sdk-alpha": "^0.7.2", "@ng-bootstrap/ng-bootstrap": "^12.1.2", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.11.6", "@types/jquery": "^3.5.16", "angular-animations": "^0.11.0", "angular-calendar": "^0.30.0", "angular-split": "^5.0.0", "apexcharts": "^3.41.0", "bootstrap": "^5.2.3", "date-fns": "^2.30.0", "html2canvas": "^1.4.1", "jquery": "^3.6.3", "jspdf": "^2.5.1", "leaflet": "^1.9.3", "luxon": "^3.3.0", "mat-select-filter": "^2.4.1", "moment": "^2.29.4", "msal": "^1.4.17", "ng-apexcharts": "^1.7.6", "ng-circle-progress": "^1.7.1", "ng-multiselect-dropdown": "^0.3.9", "ngx-bootstrap": "^10.2.0", "ngx-cookie-service": "^17.1.0", "ngx-material-timepicker": "^13.1.1", "ngx-select-dropdown": "^2.0.0", "ngx-spinner": "^13.0.0", "ngx-toastr": "^13.2.1", "npm": "^10.0.0", "rxjs": "~7.5.0", "start": "^5.1.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.1.2", "@angular/cli": "~14.1.2", "@angular/compiler-cli": "^14.1.0", "@cypress/schematic": "^2.5.0", "@types/jasmine": "~4.0.0", "@types/leaflet": "^1.9.0", "@types/three": "^0.150.1", "cypress": "latest", "jasmine-core": "~4.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2"}}