<div class="overflow-section">
    <div fxFlex="100">
        <div class="workflow-section">
            <div class="action-section-unit-section">
                <commom-label [labelText]="'WORKFLOW.CREATE_WORKFLOW.TITLE'" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
            </div>

            <div class="main-observation-section">
                <div fxLayout="row">
                  <div fxFlex="100" class="d-flex flex-flow-wrap">
                    <mat-card class="grid-stl" (click)="workFlowClick(item)"
                       *ngFor="let item of workflowArray">
                      <div class="grid-stl-ico">
                        <i [innerHTML]="item.icon | safeHtml"></i>
                        <!-- <img [src]="item.icon" width="35" height="35" alt="" srcset=""> -->
                      </div>
                      <div class="grid-stl-heading">
                        <span class="subheading-2">{{ item.name | translate }}</span>
                      </div>
                    </mat-card>
                  </div>
                </div>
            </div>

            <div class="board marginTop">
                <mat-card class="workflow-card" >
                    <div fxLayout="row" fxLayoutGap="30px" >
    
                        <mat-card class="workflow-sub-body-card" fxLayout="column" fxLayoutAlign="space-between center" >
    
                        
                                <div style="padding-left: 40px;">
                                    <img *ngFor="let img of workFlowImage let i = index" [src]="img.image" class="marginLeftMinus" alt="" srcset="">
                                </div>
                                <div  fxLayout="row wrap"  fxLayoutAlign="start center"  fxLayoutGap="30px">
                                    <div>
                                        <span class="subheading-1">
                                           {{ 'WORKFLOW.CREATE_WORKFLOW.WORKFLOW_NAME' | translate }}
                                        </span>
                                    </div>
                                    <div>
                                        <mat-form-field  appearance="outline" >
    
                                            <input type="text" class="input" value="" placeholder="Schedule Overdue" aria-span="Search" matInput >
                                 
                                           </mat-form-field>
                                    </div>
                                    <div class="btn-section">
                                        <common-lib-button [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'"
                                            (buttonAction)="goPage('workflow')"></common-lib-button>
                                        <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.SAVE'" 
                                        (buttonAction)="goPage('workflow')"></common-lib-button>
                                    </div>
    
                                </div>
                           
                         
                        </mat-card>
                  
                    </div>
    
    
                </mat-card>
            </div>
            
        </div>     
    <div>
</div>
  


  <!-- <div class="marginTop-5">
    <mat-card class="workflow-card" >
        <div fxLayout="row wrap" fxLayoutGap="30px" >

            <mat-card class="workflow-sub-card marginTop" *ngFor="let item of workflowArray;"  fxLayout="column" fxLayoutGap="5px" fxLayoutAlign="center center">
                <div>
                    <span class="body-2">
                        {{item.name}}
                    </span>
                </div>
                <div [innerHTML]="item.icon | safeHtml">
                 
                </div>

            </mat-card>
         
        </div>


    </mat-card>
</div> -->