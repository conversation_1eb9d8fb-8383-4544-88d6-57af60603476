// export default App;
import React, {useRef, useEffect} from "react";
import {Runtime, Inspector} from "@observablehq/runtime";
import notebook from "../assets/.innoart-table";
import { useSearchParams } from 'react-router-dom';

function Incident() {
  const viewofSelectionRef = useRef();

  useEffect(() => {
    const runtime = new Runtime();
    const main = runtime.module(notebook, name => {
      if (name === "viewof selection1") return new Inspector(viewofSelectionRef.current);
    });
    var d=[
      {
        "domain": "Surveillance System",
        "id": "Alert-1602-9697",
        "type": "Alert",
        "createdAt": "2021-02-16T04:21:01.723Z",
        "modifiedAt": "2021-02-16T04:21:01.723Z",
        "category": "ITMS Violations",
        "dataProvider": "http://phantom.vehant.in:55580/getViolationsList",
        "source": "Vehant",
        "description": "",
        "location": {
          "type": "Point",
          "coordinates": [
            28.582439,
            77.344013
          ]
        },
        "address": {
          "streetAddress": "Noida Sec25",
          "addressRegion": "",
          "addressLocality": "",
          "postalCode": ""
        },
        "data": {
          "is_acknowledged": false,
          "is_archived": false,
          "TransId": "P03C071-2020121100664",
          "LicenseNum": "DL3CBZ8966",
          "EventType": [
            "Red Light Violation"
          ],
          "ViolationId": [
            "656"
          ],
          "EventInfo": "",
          "SpeedLimit": "",
          "EventTime": "2020-12-11 08:36:35",
          "TimeStamp": **********,
          "Site": "Sec25 UnderBridge Towards CityCentre (L2)",
          "CameraID": "C07",
          "VehicleClass": "2",
          "Location": "Noida Sec25",
          "Latitude": "28.582439",
          "Longitude": "77.344013",
          "UserId": "SYSTEM",
          "UserName": "",
          "TransactionFiles": {
            "FrontalImage": "http://***************:55580/getFile/P03C071-2020121100664_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_1_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_2_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_3_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_4_enc.jpg",
            "ContextImage": "http://***************:55580/getFile/P03C071-2020121100664_2.jpg_2_enc.jpg",
            "LPImage": "http://***************:55580/getFile/P03C071-2020121100664_1_enc.jpg",
            "ANPRVideo": "",
            "ContextVideo": ""
          }
        },
        "severity": "Medium",
        "alertSource": "P03C071-2020121100664",
        "subCategory": "Red Light Violation",
        "dateIssued": "2020-12-11 08:36:35",
        "validFrom": "2020-12-11 08:36:35",
        "validTo": "2020-12-11 08:36:35"
      },
      {
        "domain": "Surveillance System",
        "id": "Alert-1602-9696",
        "type": "Alert",
        "createdAt": "2021-02-16T04:21:01.723Z",
        "modifiedAt": "2021-02-16T04:21:01.723Z",
        "category": "ITMS Violations",
        "dataProvider": "http://phantom.vehant.in:55580/getViolationsList",
        "source": "Vehant",
        "description": "",
        "location": {
          "type": "Point",
          "coordinates": [
            28.582439,
            77.344013
          ]
        },
        "address": {
          "streetAddress": "Noida Sec25",
          "addressRegion": "",
          "addressLocality": "",
          "postalCode": ""
        },
        "data": {
          "is_acknowledged": false,
          "is_archived": false,
          "TransId": "P03C081-2020121101568",
          "LicenseNum": "UP6BX8893",
          "EventType": [
            "No Helmet Violation",
            "Red Light Violation"
          ],
          "ViolationId": [
            "682",
            "656"
          ],
          "EventInfo": "",
          "SpeedLimit": "",
          "EventTime": "2020-12-11 08:36:31",
          "TimeStamp": **********,
          "Site": "Sec25 UnderBridge Towards CityCentre (L2)",
          "CameraID": "C08",
          "VehicleClass": "6",
          "Location": "Noida Sec25",
          "Latitude": "28.582439",
          "Longitude": "77.344013",
          "UserId": "SYSTEM",
          "UserName": "",
          "TransactionFiles": {
            "FrontalImage": "http://***************:55580/getFile/P03C081-2020121101568_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_1_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_2_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_3_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_4_enc.jpg",
            "ContextImage": "http://***************:55580/getFile/P03C081-2020121101568_2.jpg_2_enc.jpg",
            "LPImage": "http://***************:55580/getFile/P03C081-2020121101568_1_enc.jpg",
            "ANPRVideo": "",
            "ContextVideo": ""
          }
        },
        "severity": "Critical",
        "alertSource": "P03C081-2020121101568",
        "subCategory": "No Helmet Violation; Red Light Violation",
        "dateIssued": "2020-12-11 08:36:31",
        "validFrom": "2020-12-11 08:36:31",
        "validTo": "2020-12-11 08:36:31"
      },
      {
        "domain": "Surveillance System",
        "id": "Alert-1502-9695",
        "type": "Alert",
        "createdAt": "2021-02-15T11:50:35.812Z",
        "modifiedAt": "2021-02-15T11:50:35.812Z",
        "category": "ITMS Violations",
        "dataProvider": "http://phantom.vehant.in:55580/getViolationsList",
        "source": "Vehant",
        "description": "",
        "location": {
          "type": "Point",
          "coordinates": [
            28.582439,
            77.344013
          ]
        },
        "address": {
          "streetAddress": "Noida Sec25",
          "addressRegion": "",
          "addressLocality": "",
          "postalCode": ""
        },
        "data": {
          "is_acknowledged": false,
          "is_archived": false,
          "TransId": "P03C071-2020121100664",
          "LicenseNum": "DL3CBZ8966",
          "EventType": [
            "Red Light Violation"
          ],
          "ViolationId": [
            "656"
          ],
          "EventInfo": "",
          "SpeedLimit": "",
          "EventTime": "2020-12-11 08:36:35",
          "TimeStamp": **********,
          "Site": "Sec25 UnderBridge Towards CityCentre (L2)",
          "CameraID": "C07",
          "VehicleClass": "2",
          "Location": "Noida Sec25",
          "Latitude": "28.582439",
          "Longitude": "77.344013",
          "UserId": "SYSTEM",
          "UserName": "",
          "TransactionFiles": {
            "FrontalImage": "http://***************:55580/getFile/P03C071-2020121100664_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_1_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_2_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_3_enc.jpg ; http://***************:55580/getFile/P03C071-2020121100664_event_4_enc.jpg",
            "ContextImage": "http://***************:55580/getFile/P03C071-2020121100664_2.jpg_2_enc.jpg",
            "LPImage": "http://***************:55580/getFile/P03C071-2020121100664_1_enc.jpg",
            "ANPRVideo": "",
            "ContextVideo": ""
          }
        },
        "severity": "Critical",
        "alertSource": "P03C071-2020121100664",
        "subCategory": "Red Light Violation",
        "dateIssued": "2020-12-11 08:36:35",
        "validFrom": "2020-12-11 08:36:35",
        "validTo": "2020-12-11 08:36:35"
      },
      {
        "domain": "Surveillance System",
        "id": "Alert-1502-9694",
        "type": "Alert",
        "createdAt": "2021-02-15T11:50:35.812Z",
        "modifiedAt": "2021-02-15T11:50:35.812Z",
        "category": "ITMS Violations",
        "dataProvider": "http://phantom.vehant.in:55580/getViolationsList",
        "source": "Vehant",
        "description": "",
        "location": {
          "type": "Point",
          "coordinates": [
            28.582439,
            77.344013
          ]
        },
        "address": {
          "streetAddress": "Noida Sec25",
          "addressRegion": "",
          "addressLocality": "",
          "postalCode": ""
        },
        "data": {
          "is_acknowledged": false,
          "is_archived": false,
          "TransId": "P03C081-2020121101568",
          "LicenseNum": "UP6BX8893",
          "EventType": [
            "No Helmet Violation",
            "Red Light Violation"
          ],
          "ViolationId": [
            "682",
            "656"
          ],
          "EventInfo": "",
          "SpeedLimit": "",
          "EventTime": "2020-12-11 08:36:31",
          "TimeStamp": **********,
          "Site": "Sec25 UnderBridge Towards CityCentre (L2)",
          "CameraID": "C08",
          "VehicleClass": "6",
          "Location": "Noida Sec25",
          "Latitude": "28.582439",
          "Longitude": "77.344013",
          "UserId": "SYSTEM",
          "UserName": "",
          "TransactionFiles": {
            "FrontalImage": "http://***************:55580/getFile/P03C081-2020121101568_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_1_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_2_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_3_enc.jpg ; http://***************:55580/getFile/P03C081-2020121101568_event_4_enc.jpg",
            "ContextImage": "http://***************:55580/getFile/P03C081-2020121101568_2.jpg_2_enc.jpg",
            "LPImage": "http://***************:55580/getFile/P03C081-2020121101568_1_enc.jpg",
            "ANPRVideo": "",
            "ContextVideo": ""
          }
        },
        "severity": "High",
        "alertSource": "P03C081-2020121101568",
        "subCategory": "No Helmet Violation; Red Light Violation",
        "dateIssued": "2020-12-11 08:36:31",
        "validFrom": "2020-12-11 08:36:31",
        "validTo": "2020-12-11 08:36:31"
      }
    ];
    main.redefine("data", d);

    return () => runtime.dispose();
  }, []);




  return (
    <>
      <div ref={viewofSelectionRef} />
      {/* <div ref={sparkbarRef} /> */}

    </>
  );
}

export default Incident;