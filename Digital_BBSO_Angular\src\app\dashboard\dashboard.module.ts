import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { DashboardRoutingModule } from "./dashboard-routing.module";
import { DashboardComponent, DialogContentComponent } from "./dashboard.component";


@NgModule({
    declarations: [
        DashboardComponent,
        DialogContentComponent
    ],
    imports: [
        CommonModule,
        DashboardRoutingModule,
        SharedModule
    ],
    providers: [],
  })
  export class DashboardModule { }