// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import format from 'date-fns/format'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import * as ExcelJS from 'exceljs';
import { translate, DynamicTranslationArea, TranslationContext, TranslationContextProvider } from '@celanese/celanese-sdk'

let main
let idToken
let limitSet = 10
let firstPageIndex = 0
let listData = []
let colSummary = {}

let displayedColumns = [
  'externalId',
  'site',
  'functionalLocation',
  'itemNo',
  'subTagNumber',
  // 'description',
  'itemType',
  'certificateNo',
  // 'areaClassifiaction',
  // 'atexCategoryArea',
  // 'equipmentGroupArea',
  // 'manufacturer',
  // 'manufacturerPartNo',
  // 'serialNo',
  // 'rfID',
  // 'drawingNumber',
  // 'barcode',
  // 'isActive',
  // 'associateDevice1',
  // 'associateDevice2',
  // 'deviceType',
  // 'zone',
  // 'exEex',
  // 'exClass',
  // 'gasGroup',
  // 'temperatureClass',
  // 'equipmentGroupDevice',
  // 'eplDevice',
  // 'atexCategoryDevice',
  // 'createdBy',
  // 'modifiedBy',
  'actions',
]
let headerKeyVal = {
  externalId: 'ID',
  site: 'Site',
  functionalLocation: 'Functional Location',
  itemNo: 'Item Number',
  subTagNumber: 'Sub Tag Number',
  inspectionNo: 'Inspection Number',
  inspectionGrade: 'Inspection Grade',
  inspectionOutcome: 'Area Classification',
  approvalStatus: 'Approval Status',
  classification: 'Classification',
  areaClassification: 'Classification',
  description: 'Description',
  itemType: 'Item Type',
  certificateNo: 'Certificate Number',
  areaClassifiaction: 'Area Classification',
  atexCategoryArea: 'ATEX Category Area',
  equipmentGroupArea: 'Equipment Group Area',
  manufacturer: 'Manufacturer',
  manufacturerPartNo: 'Manufacturer Part Number',
  serialNo: 'Serial Number',
  rfID: 'RFID',
  drawingNumber: 'Drawing Number',
  barcode: 'Barcode',
  isActive: 'Is Active',
  associateDevice1: 'Associate Device 1',
  associateDevice2: 'Associate Device 2',
  deviceType: 'Device Type',
  zone: 'Zone',
  exEex: 'Ex Eex',
  exClass: 'Ex Class',
  gasGroup: 'Gas Group',
  temperatureClass: 'Temperature Class',
  equipmentGroupDevice: 'Equipment Group Device',
  eplDevice: 'EPL Device',
  atexCategoryDevice: 'ATEX Category Device',
  createdBy: 'Created By',
  modifiedBy: 'Modified By',
  createdTime: 'Created On',
  lastUpdatedTime: 'Updated On',
  actions: 'Actions',
}

var paginationCursor = []
let site
let unit
let search
let startDate
let endDate
let initFlag
let tabledata
let token
let userAccessMenu
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
let listType = "CompEx Asset";
function CompExAsset() {
  console.log("I am react")
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')

  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState()
  const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
  const cacheNameShouldTranslate = 'shouldTranslateDynamic'
  const { locale, updateLocale } = useContext(TranslationContext)

  const [t, i18n] = useTranslation('global')

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if (i18n) {
      i18n.changeLanguage(newValue)
      console.log('Selected Language: ', selectedLanguage)
      console.log('i18n.language: ', i18n)
    }
  }

  const getIdTokenFromMsal = (token) => {
    return Promise.resolve(token);
  };

  const getAuthToken = () => getIdTokenFromMsal(idToken);

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })
    window.onmessage = function (e) {
      // console.log(e.data.type)
      if (e.data.type && e.data.type == 'AuthToken') {
        token = e.data.data
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || shouldTranslateDynamic)
       
      }
      if (e.data.type && e.data.type == 'CompExAsset') {
        if (e.data.action == 'Column') {

          displayedColumns = e.data.data
          colFun()
        } else if (e.data.action == 'Excel') {
          exportExcelFile()

        } else if (e.data.action == 'Filter') {
          site = e.data.sites

          console.log('Filter Language', e.data)
          // language translation
          setting();
          handleLanguageChange(e.data.LanguageCode)
          colFun()
          getData()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          colFun()
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu CompExAsset', userAccessMenu)
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        console.log('Language', e.data)
        handleLanguageChange(e.data.LanguageCode)

        // remove prev language data
        const prevLang = localStorage.getItem('LocaleData')
        localStorage.removeItem('LocaleData')
        localStorage.removeItem('APP-OFWATranslationData' + prevLang)
        localStorage.setItem('LocaleData', e.data.LanguageCode)
        localStorage.setItem('APP-OFWATranslationData' + e.data.LanguageCode, JSON.stringify(e.data.labels))
        updateLocale(e.data.LanguageCode.toUpperCase())
        colFun()
        getData()
      }
      if (e.data.type == 'DynamicTranslation') {
        console.log('App.js DynamicTranslation', e.data)
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
        window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
        updateLocale(e.data.LanguageCode)
        console.log('DynamicTranslation', e.data)
      }
    }
    setDataCount(1)
    colFun()
    // getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose()
  }, [])

  function rowDroDownChange(e) {
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = parseInt(e.target.value)
    filterData()
    setCurrentPage(1)
  }

  const [searchParams, setSearchParams] = useSearchParams()

  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    return html`<div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
    <div
                onClick=${() => viewClick(cInd)}
                title=${translate('stLabel.buttonView')}
                style="height:18px;margin-right:12px;cursor: pointer;"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="20"
                  height="20"
                  x="0"
                  y="0"
                  viewBox="0 0 488.85 488.85"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                  class=""
                >
                  <g>
                    <path
                      d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>
     <div
                onClick=${() => editClick(cInd)}
                title=${translate('stLabel.edit')}
                style="height:18px;margin-right:12px;cursor: pointer;"
                }
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>
      
    </div> `

  }
  function viewClick(index) {
    window.parent.postMessage(
      {
        type: 'CompExAssetList',
        action: 'FormView',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }

  function editClick(index) {
    window.parent.postMessage(
      {
        type: 'CompExAssetList',
        action: 'FormEdit',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }

  function colFun() {
    var sortedDisplayedColumns = displayedColumns;
   
    const element = document.getElementById("summaryBarChart");
    if (element) {
      element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine('configuration', {
      columns: sortedDisplayedColumns,

      header: JSON.parse(JSON.stringify(headerKeyVal)),
      headerSummary: colSummary,
      format: {
        isOperationalLearning: (x) => {
          if (x) {
            return "Yes"
          }
          else {
            return "No"
          }
        },
        date: (x) => format(new Date(x), dateFormat),
        createdTime: (x) => format(new Date(x), dateFormat + " " + timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat + " " + timeFormat),
        site: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        unit: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        location: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        id: (x) => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i),
      },

      align: {
        externalId: 'left',
        site: 'left',
        functionalLocation: 'left',
        itemNo: 'left',
        subTagNumber: 'left',
        description: 'left',
        itemType: 'left',
        certificateNo: 'left',
        areaClassifiaction: 'left',
        atexCategoryArea: 'left',
        equipmentGroupArea: 'left',
        manufacturer: 'left',
        manufacturerPartNo: 'left',
        serialNo: 'left',
        rfID: 'left',
        drawingNumber: 'left',
        barcode: 'left',
        isActive: 'left',
        associateDevice1: 'left',
        associateDevice2: 'left',
        deviceType: 'left',
        zone: 'left',
        exEex: 'left',
        exClass: 'left',
        gasGroup: 'left',
        temperatureClass: 'left',
        equipmentGroupDevice: 'left',
        eplDevice: 'left',
        atexCategoryDevice: 'left',
        createdBy: 'left',
        modifiedBy: 'left',
        createdTime: 'left',
        lastUpdatedTime: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        externalId: 200,
        site: 200,
        functionalLocation: 200,
        itemNo: 200,
        subTagNumber: 200,
        description: 200,
        itemType: 200,
        certificateNo: 200,
        areaClassifiaction: 200,
        atexCategoryArea: 200,
        equipmentGroupArea: 200,
        manufacturer: 200,
        manufacturerPartNo: 200,
        serialNo: 200,
        rfID: 200,
        drawingNumber: 200,
        barcode: 200,
        isActive: 200,
        associateDevice1: 200,
        associateDevice2: 200,
        deviceType: 200,
        zone: 200,
        exEex: 200,
        exClass: 200,
        gasGroup: 200,
        temperatureClass: 200,
        equipmentGroupDevice: 200,
        eplDevice: 200,
        atexCategoryDevice: 200,
        createdBy: 200,
        modifiedBy: 200,
        createdTime: 200,
        lastUpdatedTime: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  async function getData() {
    console.log('getData', tabledata)
    window.parent.postMessage({
      type: 'CompExAssetList', action: 'Loading', data: true,
    }, '*')
    if (!tabledata) {
      fetch(Constants.NODE_API + '/api/service/listCompExAsset', {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      })
        .then((res) => res.json())
        .then((result) => {
          var listProcess =
            result['data']['listCompExAsset']['items']
          listData = []
          listProcess.forEach((element) => {
            element['actions'] = ''
            element['site'] = element['refSite']
              ? element['refSite']['description']
              : '';
            element['functionalLocation'] = element['refFunctionalLocation']
            ? element['refFunctionalLocation']['description']
            : '';

            listData.push(element)
          })
          window.parent.postMessage(
            {
              type: 'CompExAssetList',
              action: 'Loading',
              data: false,
            },
            '*'
          )
          setCurrentPage(1)
          initFlag = true
          filterData()
        })

    }
    else {
      window.parent.postMessage({
        type: 'CompExAssetList', action: 'Loading', data: false,
      }, '*')
      setting()
    }
  }

  const exportExcelFile = () => {
    if (!listData || listData.length == 0 || !displayedColumns || displayedColumns.length == 0) {
      console.log('No data or displayed columns to export');
      return;
    }
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("My Sheet");
    worksheet.properties.defaultRowHeight = 20;
    console.log(headerKeyVal)


    const headerMapping = JSON.parse(JSON.stringify(headerKeyVal));
    console.log(headerMapping)
    // Add header row with displayed columns using the header mapping
    const headerRow = displayedColumns.map(column => headerMapping[column] || column);

    const headerRowRef = worksheet.addRow(headerRow);

    // Bold the header row
    headerRowRef.eachCell(cell => {
      cell.font = { bold: true };
    });

    // Add data rows with displayed columns
    listData.forEach(item => {
      const rowData = displayedColumns.map(column => item[column]);
      worksheet.addRow(rowData);
    });

    // Set the column width for A, B, C, D, and E to 12
    const columnsToAdjust = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O'];
    columnsToAdjust.forEach(column => {
      const col = worksheet.getColumn(column);
      col.width = 30;
    });

    // promise.then(() => {
    //   const priceCol = worksheet.getColumn(5);


    workbook.xlsx.writeBuffer().then(buffer => {
      saveBufferToFile(buffer);
    });
    //});

  };
  const saveBufferToFile = (buffer) => {
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const link = document.createElement('a');

    // Generate a unique filename using the current timestamp
    const fileName = `CompExAsset_List_${new Date().getMonth() + 1}_${new Date().getDate()}_${new Date().getFullYear()}_${new Date().getHours()}_${new Date().getMinutes()}.xlsx`;

    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();

  };

  function process(result) {
    console.log(result)
    var listProcess = result
  
    listData = []
    listProcess.forEach((element) => {
      element['actions'] = ''
      element['site'] = element['refSite']
        ? element['refSite']['description']
        : ''

      listData.push(element)
    })
    //  main.redefine("data", listData);
    window.parent.postMessage(
      {
        type: 'CompExAssetList',
        action: 'Loading',
        data: true,
      },
      '*'
    )
    setCurrentPage(1)
    initFlag = true
    filterData()
    // colFun();
  }

  function filterData() {
    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    } else {
      setDataCount(0)
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }

  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
        if (tabledata) {
          process(tabledata)
        }
      })
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
      <DynamicTranslationArea
        getAuthToken={getAuthToken}
        dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
        shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
      >
        <TranslationContextProvider
          getAuthToken={getAuthToken}
        >
          <div ref={viewofSelectionRef} />
        </TranslationContextProvider>
      </DynamicTranslationArea>
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
          //  assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default CompExAsset
