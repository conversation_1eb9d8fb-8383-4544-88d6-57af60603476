import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-create-workflow',
  templateUrl: './create-workflow.component.html',
  styleUrls: ['./create-workflow.component.scss']
})
export class CreateWorkflowComponent implements OnInit {

  workflowArray =[]
  workFlowImage = []

  
  constructor(  private router: Router,private dataService:DataService) { 
    this.workflowArray =  [
      {
        name:"WORKFLOW.CREATE_WORKFLOW.CARDS.EMAIL",
        icon:`
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="27.001" viewBox="0 0 40 27.001">
          <path id="Icon_zocial-email" data-name="Icon zocial-email" d="M.072,28.677V6.46q0-.039.116-.733L13.264,16.913.226,29.449a3.269,3.269,0,0,1-.154-.771ZM1.808,4.184a1.662,1.662,0,0,1,.656-.116H37.68a2.184,2.184,0,0,1,.694.116L25.26,15.408,23.524,16.8l-3.433,2.816L16.658,16.8l-1.736-1.389Zm.039,26.77L15,18.34l5.092,4.127,5.092-4.127L38.336,30.953a1.852,1.852,0,0,1-.656.116H2.464a1.747,1.747,0,0,1-.617-.116ZM26.919,16.913,39.956,5.727a2.3,2.3,0,0,1,.116.733V28.677a2.956,2.956,0,0,1-.116.771Z" transform="translate(-0.072 -4.068)" fill="#073148"/>
        </svg>
         `
      },
      {
        name:"WORKFLOW.CREATE_WORKFLOW.CARDS.TEAMS",
        icon:`
        <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
          <path id="call" d="M35.214,29.67A44,44,0,0,0,30.3,25.355c-1.424-1.025-1.882-.952-2.593-.338a19.828,19.828,0,0,1-2.582,2.29c-2.1,1.2-6.41-2.3-8.058-3.556a25.017,25.017,0,0,1-4.848-4.839c-1.253-1.646-4.762-5.954-3.558-8.052a19.864,19.864,0,0,1,2.292-2.579c.615-.711.687-1.169-.338-2.591A43.983,43.983,0,0,0,6.3.785c-1.17-1.1-1.387-.9-2.074-.277C.748,3.679-.713,4.267.331,9.379c.736,3.6,3.654,7.629,5.935,10.425a54.829,54.829,0,0,0,9.914,9.9c3.649,2.971,9.248,6.933,14.27,6.207,1.854-.268,3.8-2.806,5.042-4.17C36.119,31.056,36.312,30.838,35.214,29.67ZM24.365,8.293a1.231,1.231,0,0,1,2.462,0v8.07a1.231,1.231,0,0,1-2.462,0Zm-6.9,0a1.231,1.231,0,0,1,2.462,0v8.07a1.231,1.231,0,0,1-2.462,0Zm3.452-2.343a1.231,1.231,0,0,1,2.462,0V18.707a1.231,1.231,0,0,1-2.462,0Z" fill="#073148" fill-rule="evenodd"/>
        </svg>
        
        `
      },
      {
        name:"WORKFLOW.CREATE_WORKFLOW.CARDS.SMS",
        icon:`
        <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
          <path id="sms" d="M13.014,36.145a16.649,16.649,0,0,0,7.155-6.914h2.26a13.159,13.159,0,0,0,9.6-4.238,15.156,15.156,0,0,0,0-20.464,13.159,13.159,0,0,0-9.6-4.238H13.767A13.09,13.09,0,0,0,4.4,4.081,14.842,14.842,0,0,0,.036,13.69,15.105,15.105,0,0,0,3.047,23.874a13.369,13.369,0,0,0,8.747,5.2,13.847,13.847,0,0,1-.343,5.7,1.233,1.233,0,0,0-.007.682,1.169,1.169,0,0,0,.353.568,1.054,1.054,0,0,0,.591.259A1.033,1.033,0,0,0,13.014,36.145ZM11.59,8.346H24.65a1.278,1.278,0,0,1,.932.412,1.472,1.472,0,0,1,0,1.987,1.278,1.278,0,0,1-.932.412H11.59a1.278,1.278,0,0,1-.932-.412,1.472,1.472,0,0,1,0-1.987A1.278,1.278,0,0,1,11.59,8.346Zm0,5.015H24.65a1.277,1.277,0,0,1,.932.412,1.472,1.472,0,0,1,0,1.987,1.278,1.278,0,0,1-.932.412H11.59a1.278,1.278,0,0,1-.932-.412,1.472,1.472,0,0,1,0-1.987A1.278,1.278,0,0,1,11.59,13.361Zm0,7.813a1.278,1.278,0,0,1-.932-.412,1.472,1.472,0,0,1,0-1.987,1.278,1.278,0,0,1,.932-.412H24.65a1.248,1.248,0,0,1,.5.107,1.314,1.314,0,0,1,.428.3,1.414,1.414,0,0,1,.286.456,1.49,1.49,0,0,1,0,1.076,1.414,1.414,0,0,1-.286.456,1.314,1.314,0,0,1-.428.3,1.246,1.246,0,0,1-.5.107Z" transform="translate(0 -0.29)" fill="#073148"/>
        </svg>
         `
      },
      {
        name:"WORKFLOW.CREATE_WORKFLOW.CARDS.TEAMS",
        icon:`
        <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="27" viewBox="0 0 29.25 27">
          <g id="Icon_ionic-md-chatboxes" data-name="Icon ionic-md-chatboxes" transform="translate(-3.375 -4.5)">
            <path id="Path_464" data-name="Path 464" d="M27.531,4.5H4.05a.677.677,0,0,0-.675.574V20.136a.705.705,0,0,0,.675.606H8.086v6.25l6.337-6.25H27.531a.568.568,0,0,0,.524-.606V5.074A.538.538,0,0,0,27.531,4.5Z" fill="#073148"/>
            <path id="Path_465" data-name="Path 465" d="M32.09,8.93H29.813V20.642c0,1.124-.486,1.858-1.768,1.858H15.335L12.6,25.242h9.119L28.055,31.5V25.242H32.09a.58.58,0,0,0,.535-.61V9.57A.608.608,0,0,0,32.09,8.93Z" fill="#073148"/>
          </g>
        </svg>
        
        `
      }
    ]
  
  }

  ngOnInit(): void {
  }

  goPage(page) {
    this.router.navigate([page]);
  }

  workFlowClick(item){
    if(item.name == 'Email'){
      this.workFlowImage.push({
        image:"../../../assets/workflow_image/card-clip-email.png"
      })
    }else if(item.name == 'SMS'){
      this.workFlowImage.push({
        image:"../../../assets/workflow_image/card-clip-sms.png"
      })
    }else{
      this.workFlowImage.push({
        image:"../../../assets/workflow_image/card-clip-sms.png"
      })
    }

   
  }

}
