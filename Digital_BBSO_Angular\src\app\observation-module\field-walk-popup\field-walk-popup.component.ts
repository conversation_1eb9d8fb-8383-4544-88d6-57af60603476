import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-field-walk-popup',
  templateUrl: './field-walk-popup.component.html',
  styleUrls: ['./field-walk-popup.component.scss']
})
export class FieldWalkPopupComponent implements OnInit {

  
  locationArray = [
    {
      id:1,
      name:"Location 1 - 0"
    },
    {
      id:2,
      name:"Location 8 - 3"
    },
    {
      id:3,
      name:"Location 11 - 1"
    },
    
  ]

  categoriesEidk = [
    {
      id:1,
      name:"PPE - 70%"
    },
    {
      id:2,
      name:"Ergonomics - 80%"
    },
    {
      id:3,
      name:"Tools & Equipment - 65%"
    },
    {
      id:4,
      name:"Tools & Equipment - 65%"
    },
    

  ]

  constructor( private router: Router,@Inject(MAT_DIALOG_DATA) private _data: any, public dialogRef: MatDialogRef<FieldWalkPopupComponent>,
  ) { }

  ngOnInit(): void {
  }

  locationClick(item){
    this.dialogRef.close()
   // this.router.navigate(['field-visit']);
    this.router.navigateByUrl('observations/field-visit', {
      state: {fieldVisit:true}
  });
  }

  categoriesRisk(item){
    this.dialogRef.close()
    this.router.navigateByUrl('observations/field-visit', {
      state: {createObserve:true}
  });
  }
  goPage(page) {
    this.router.navigate([page]);
  }

}
