import { ViewportRuler } from "@angular/cdk/overlay";
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { Subscription } from "rxjs";
import { ParentToChildService } from "src/app/broadcast/parent-to-child.service";

@Component({
    selector: 'app-sub-header',
    templateUrl: './sub-header.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
    // styleUrls: [],
})

export class AppSubHeader implements OnInit {
    subHeaderDataBroadcastSubscription:Subscription;
    headerText:string;
    docReact:any;
    constructor(
        private parentToChildService:ParentToChildService,
        private viewportRuler: ViewportRuler,
        private changeDetector: ChangeDetectorRef,
        private translate: TranslateService,
    ){}
    searchControl: FormControl = new FormControl("");
    ngOnInit(): void {
       this.broadcastSubscription();
       this.docReact = this.viewportRuler.getViewportRect();
    }

    ngOnDestroy():void {
        this.subHeaderDataBroadcastSubscription && this.subHeaderDataBroadcastSubscription.unsubscribe();
    }

    broadcastSubscription():void {
        this.subHeaderDataBroadcastSubscription = this.parentToChildService.subHeaderDataBroadcast$.subscribe(data=>{
            this.headerText = data;
            // this.headerText = this.setFormatedString(data);
            this.changeDetector.markForCheck();
        })
    }

    setFormatedString(data:string):string {
        let headerText;
        let newData = data.split(' ');
        newData.forEach(item =>{
            headerText = headerText ? headerText + ' ' + item[0].toUpperCase() + item.slice(1,item.length).toLowerCase() : item[0].toUpperCase() + item.slice(1,item.length).toLowerCase();
        })
        return headerText;
    };

}