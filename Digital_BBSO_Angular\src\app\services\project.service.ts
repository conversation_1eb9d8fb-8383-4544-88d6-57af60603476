import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import * as _ from "lodash";
@Injectable({ providedIn: 'root' })
export class ProjectService {
  private readonly projectChanges$ = new Subject<string>();
  project(text: string): void {
    this.projectChanges$.next(text);
  }
  projectChanges(): Observable<string> {
    return this.projectChanges$.asObservable();
  }
}