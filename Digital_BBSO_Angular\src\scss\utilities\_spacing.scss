$spacing: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20;
$directions: top, right, bottom, left;

@each $space in $spacing {
  // Creating margin utilities
  .m-#{$space} {
    margin: #{space}rem !important;
  }

  // Creating padding utilities
  .p-#{$space} {
    padding: #{space}rem !important;
  }

  @each $direction in $directions {
    // Creating margin utilities for all direction
    .m#{str-slice($direction, 0, 1)}-#{$space} {
      margin-#{$direction}: #{$space}rem !important;
    }

    // Creating padding utilities for all direction
    .p#{str-slice($direction, 0, 1)}-#{$space} {
      padding-#{$direction}: #{$space}rem !important;
    }
  }
}

// For creating space-x, space-y values
$space-axis-values: 1, 2, 3, 4, 5;
$space-axis-key: "x" "right", "y" "bottom";

@each $space-axis in $space-axis-values {
  @each $key, $value in $space-axis-key {
    .space-#{$key}-#{$space-axis} > * {
      margin-#{$value}: #{$space-axis}rem;
    }
  }
}
