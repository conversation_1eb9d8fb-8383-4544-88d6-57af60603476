import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-question-column-config',
  templateUrl: './question-column-config.component.html',
  styleUrls: ['./question-column-config.component.scss']
})
export class QuestionColumnConfigComponent implements OnInit {

  form: FormGroup;
  observationForm:FormControl = new FormControl('Behaviour');
  observationDropDown = [
    {
      id:1,
      name:"<PERSON>haviour"
    },
    {
      id:2,
      name:"Hazard<PERSON>"
    },
    {
      id:3,
      name:"Incidents"
    },
  
  ]

  constructor(private fb: FormBuilder,private router: Router,private commonService:CommonService, private translate: TranslateService) {

    this.form = this.fb.group({
      rows: this.fb.array([])
    });
    console.log('histroy',history.state)
    var observeHistory = history.state
    if(observeHistory.name){
      this.observationForm.setValue(observeHistory.name)
    }
  }

  ngOnInit() {
    this.initGroup();
    //this.initGroup();
    //this.initGroup();
  }

  onDeleteRow(rowIndex) {
    let rows = this.form.get('rows') as FormArray;
    rows.removeAt(rowIndex)
  }

  initGroup() {
    let rows = this.form.get('rows') as FormArray;
    rows.push(this.fb.group({
      name: [null, Validators.required],
      status: [null, Validators.required]
    }))
  }

  onSubmit() {
    console.log(this.form.value);
  }
  goPage(page) {
    this.router.navigate([page]);
  }

  saveQuestion(){

    if(this.form.valid){
      this.goPage('form-list');
    }else{
      this.commonService.triggerToast({ type: 'error', title: "", msg: this.translate.instant('TOASTER.PLEASE_FILL_DETAILS') });
   
    }
    
  }
}
