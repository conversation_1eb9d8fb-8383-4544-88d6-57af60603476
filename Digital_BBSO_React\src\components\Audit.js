// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import { translate, DynamicTranslationArea, TranslationContextProvider, TranslationContext } from '@celanese/celanese-sdk'

let main
let idToken;
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let allListData = []
let colSummary = {}
let token
let columnOrder=[];
let behalf
let OperLearning
let location
let subProcess
let displayedColumns = [
  'externalId',
  'title',
  'corePrinciple',
  'subProcess',
  'year',
  'quarter',
  'priority',
  'isOperationalLearning',
  'observationStartDate',
  'observationEndDate',
  'status',
  'createdTime',
  'createdBy',
  'actions',
  
]

let headersKeyVal = {
  externalId: 'ID',
  title: 'Title',
  corePrinciple: 'Core Principle',
  subProcess: 'Sub Process',
  year: 'Year',
  quarter: 'Quarter',
  priority: 'Priority',
  isOperationalLearning: 'Operational Learning',
  operationalLearningDescription:'Operational Learning Description',
  observationStartDate: 'Start Date',
  observationEndDate: 'End Date',
  status: 'Status',
  shift: 'Shift',
  refUnit: 'Unit',
  refReportingLocation: 'Location',
  date: 'Date',
  observedOnBehalfOf: 'Observed By',
  createdTime: 'Created On',
  createdBy: 'Created By',
  lastUpdatedTime: 'Updated On',
  modifiedBy: 'Updated By',
  actions: 'Actions',
  month: 'Month',
  workReleaseNumber: 'Work Release Number',
}

var paginationCursor = []
let site
let unit
let search
let startDate
let endDate
let isSiteAdmin
let user
let auditType
let SubProcessType
let SubProcessName
let createdByfilter
let initFlag
let userAccessMenu
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
function Audit() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')
  const { locale, updateLocale } = useContext(TranslationContext)

  const [selectedLanguage, setSelectedLanguage] = useState('en')

  const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

  const [t, i18n] = useTranslation('global')

  const getIdTokenFromMsal = (token) => {      
    return Promise.resolve(token);
  };
  
  const getAuthToken = () => getIdTokenFromMsal(idToken);

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if(i18n){
      i18n.changeLanguage(newValue)
      console.log('Selected Language: ', selectedLanguage)
      console.log('i18n.language: ', i18n)
    }
  
  }

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })
    // window.onmessage = function (e) {
    //   if (e.data.type && e.data.type == 'AuthToken') {
    //     console.log('Token', e.data)
    //     token = e.data.data
    //   }
    //   if (e.data.type && e.data.type == 'Audit') {
    //     console.log('Audit')
    //     if (e.data.action == 'Column') {
    //       window.parent.postMessage(
    //         {
    //           type: 'Audit',
    //           action: 'Loading',
    //           data: true,
    //         },
    //         '*'
    //       )
    //       displayedColumns = e.data.data
    //       colFun()
    //     } else if (e.data.action == 'Filter') {
    //       site = e.data.sites
    //       setting();
    //       startDate = e.data.date.start
    //       endDate = e.data.date.end
    //       auditType = e.data.auditType
    //       SubProcessType = e.data.SubProcessType
    //       console.log('Filter language: ', e.data)
    //       handleLanguageChange(e.data.LanguageCode)

    //       headersKeyVal = {
    //         externalId: t('AUDIT.ID'),
    //         title: t('AUDIT.TITLE'),
    //         year: t('AUDIT.YEAR'),
    //         quarter: t('AUDIT.QUARTER'),
    //         priority: t('AUDIT.PRIORITY'),
    //         observationStartDate: t('AUDIT.START_DATE'),
    //         observationEndDate: t('AUDIT.END_DATE'),
    //         status: t('AUDIT.STATUS'),
    //         createdTime: t('AUDIT.CREATED_ON'),
    //         createdBy: t('AUDIT.CREATED_BY'),
    //         lastUpdatedTime: t('AUDIT.UPDATED_ON'),
    //         modifiedBy: t('AUDIT.UPDATED_BY'),
    //         actions: t('AUDIT.ACTIONS'),
    //       }

    //       console.log('Audit headersKeyVal', headersKeyVal)
    //       colFun()

    //       console.log('Filter Data', e.data)
    //       getData()
    //     } else if (e.data.action == 'AccessMenu') {
    //       userAccessMenu = e.data.data
    //       console.log('userAccessMenu Audit', userAccessMenu)
    //       colFun()
    //     } else if (e.data.action == 'Summary') {
    //       colSummary = e.data.data
    //       colFun()
    //     } else if (e.data.action == 'PageRows') {
    //       setCurrentPage(1)
    //       setLimitCount(parseInt(e.data.data))
    //       limitSet = parseInt(e.data.data)
    //       paginationCursor = []
    //       getData()
    //     }
    //   }
    //   if (e.data.action == 'Language') {
    //     console.log('Language', e.data)
    //     handleLanguageChange(e.data.LanguageCode)

    //     headersKeyVal = {
    //       externalId: t('AUDIT.ID'),
    //       title: t('AUDIT.TITLE'),
    //       year: t('AUDIT.YEAR'),
    //       quarter: t('AUDIT.QUARTER'),
    //       priority: t('AUDIT.PRIORITY'),
    //       observationStartDate: t('AUDIT.START_DATE'),
    //       observationEndDate: t('AUDIT.END_DATE'),
    //       status: t('AUDIT.STATUS'),
    //       createdTime: t('AUDIT.CREATED_ON'),
    //       createdBy: t('AUDIT.CREATED_BY'),
    //       lastUpdatedTime: t('AUDIT.UPDATED_ON'),
    //       modifiedBy: t('AUDIT.UPDATED_BY'),
    //       actions: t('AUDIT.ACTIONS'),
    //     }

    //     console.log('Audit headersKeyVal', headersKeyVal)
    //     getData()
    //     colFun()
    //   }
    // }

    window.onmessage = function (e) {
      // Log the entire event data to see what's being received
      console.log('Received message:', e.data);
    
      // Handle AuthToken messages
      if (e.data.type === 'AuthToken') {
        console.log('Received AuthToken:', e.data);
        token = e.data.data;
        if(e.data.user){
          user = JSON.parse(e.data.user)
        }
        if(e.data.isSiteAdmin){
          isSiteAdmin = e.data.isSiteAdmin;
        }
        else{
          // createdByfilter = user.userPrincipalName.toLowerCase()
          }
        if(e.data.columnOrder){
          columnOrder = e.data.columnOrder;
        }
      }
    
      // Handle Audit messages
      if (e.data.type === 'Audit') {
        console.log('Audit Action:', e.data);
    
        switch (e.data.action) {
          case 'Column':
            console.log('Columns Data:', e.data.data);
        
            displayedColumns = e.data.data;
            colFun();
            break;
    
          case 'Filter':
            site = e.data.sites;
            setting();
            startDate = e.data.date.start;
            endDate = e.data.date.end;
            auditType = e.data.auditType;
            behalf = e.data.behalf;
            unit = e.data.units;
            createdByfilter= e.data.createdBy;
            OperLearning = e.data.OperLearning;
            location = e.data.location;
            SubProcessType = e.data.SubProcessType;
            SubProcessName= e.data.subProcessName;
    
            // Debugging: Log the SubProcessName
            console.log('Selected SubProcessName:', e.data.subProcessName);
            // remove prev language data
            const prevLang = localStorage.getItem('LocaleData')
            localStorage.removeItem('LocaleData')
            localStorage.removeItem('APP-OFWATranslationData'+prevLang)
            localStorage.setItem('LocaleData', e.data.LanguageCode)
            localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
            updateLocale(e.data.LanguageCode)
    
            // Update headersKeyVal based on SubProcessName
            // if (e.data.SubProcessName === 'Layered Process Audit') {
            //   console.log('Layered Process Audit selected, updating headersKeyVal.');
            //   headersKeyVal = {
            //     externalId: t('AUDIT.ID'),
            //     shift: t('AUDIT.Shift'),
            //     year: t('AUDIT.Observered'),
            //     Site: t('AUDIT.Site'),
            //     unit: t('AUDIT.Unit'),
                
            //   };
            // } else {
            //   console.log('Default SubProcess selected, using default headersKeyVal.');
              // headersKeyVal = {
              //   externalId: translate('stLabel.id'),
              //   title: translate('stLabel.title'),
              //   year: translate('stLabel.year'),
              //   quarter: translate('stLabel.quarter'),
              //   priority: translate('stLabel.priority'),
              //   isOperationalLearning: translate('stLabel.operationalLearning') ,
              //   operationalLearningDescription: translate('stLabel.operationalLearningDescription'),
              //   observationStartDate: translate('stLabel.startDate'),
              //   observationEndDate: translate('stLabel.endDate'),
              //   status: translate('stLabel.status'),
              //   shift: translate('stLabel.shift'),
              //   refUnit: translate('stLabel.unit'),
              //   refReportingLocation: translate('stLabel.location'),
              //   observedOnBehalfOf: translate('stLabel.observedBy'),
              //   workReleaseNumber: translate('stLabel.workReleaseNumber'),
              //   date:translate('stLabel.date'),
              //   createdTime: translate('stLabel.createdOn'),
              //   createdBy: translate('stLabel.createdBy'),
              //   lastUpdatedTime: translate('stLabel.updatedOn'),
              //   modifiedBy: translate('stLabel.updatedBy'),
              //   actions: translate('stLabel.actions'),
              //   month: translate('stLabel.month'),
              // };
            // }
    
            console.log('Filter Data:', e.data,);
            handleLanguageChange(e.data.LanguageCode);
            console.log('Updated headersKeyVal:', headersKeyVal);
            colFun();
            getData();
            break;
    
          case 'AccessMenu':
            console.log('AccessMenu Data:', e.data.data);
            userAccessMenu = e.data.data;
            colFun();
            break;
    
          case 'Summary':
            console.log('Summary Data:', e.data.data);
            colSummary = e.data.data;
            colFun();
            break;
    
          case 'PageRows':
            console.log('PageRows Data:', e.data.data);
            setCurrentPage(1);
            setLimitCount(parseInt(e.data.data));
            limitSet = parseInt(e.data.data);
            paginationCursor = [];
            getData();
            break;
    
          default:
            console.warn('Unhandled Audit action:', e.data.action);
        }
      }
    
      // Handle Language change messages
      if (e.data.type === 'Language' || e.data.action === 'Language') {
        console.log('Language Change:', e.data);
        handleLanguageChange(e.data.LanguageCode);
        // remove prev language data
        const prevLang = localStorage.getItem('LocaleData')
        localStorage.removeItem('LocaleData')
        localStorage.removeItem('APP-OFWATranslationData'+prevLang)
        localStorage.setItem('LocaleData', e.data.LanguageCode)
        localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        updateLocale(e.data.LanguageCode)
    
        // headersKeyVal = {
        //   externalId: translate('stLabel.id'),
        //   title: translate('stLabel.title'),
        //   year: translate('stLabel.year'),
        //   quarter: translate('stLabel.quarter'),
        //   priority: translate('stLabel.priority'),
        //   isOperationalLearning: translate('stLabel.operationalLearning') ,
        //   operationalLearningDescription: translate('stLabel.operationalLearningDescription'),
        //   observationStartDate: translate('stLabel.startDate'),
        //   observationEndDate: translate('stLabel.endDate'),
        //   status: translate('stLabel.status'),
        //   shift: translate('stLabel.shift'),
        //   refUnit: translate('stLabel.unit'),
        //   refReportingLocation: translate('stLabel.location'),
        //   observedOnBehalfOf: translate('stLabel.observedBy'),
        //   workReleaseNumber: translate('stLabel.workReleaseNumber'),
        //   date:translate('stLabel.date'),
        //   createdTime: translate('stLabel.createdOn'),
        //   createdBy: translate('stLabel.createdBy'),
        //   lastUpdatedTime: translate('stLabel.updatedOn'),
        //   modifiedBy: translate('stLabel.updatedBy'),
        //   actions: translate('stLabel.actions'),
        //   month: translate('stLabel.month'),
        // };
    
        console.log('Updated headersKeyVal on Language change:', headersKeyVal);
        // getData();
        colFun();
      }
      if (e.data.action == 'DynamicTranslation' || e.data.type == 'DynamicTranslation') {
        console.log('App.js DynamicTranslation', e.data)
        idToken = e.data.idToken
        // getAuthToken()
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
        window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
        updateLocale(e.data.LanguageCode)
        console.log('DynamicTranslation', e.data)
        // localStorage.setItem('LocaleData', e.data.LanguageCode)
        // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, e.data.labels)
      }
    };
    
    
    setDataCount(1)
    headersKeyVal = {
      externalId: translate('stLabel.id'),
      title: translate('stLabel.title'),
      corePrinciple: translate('stLabel.corePrinciple'),
      subProcess: translate('stLabel.subProcess'),
      year: translate('stLabel.year'),
      quarter: translate('stLabel.quarter'),
      priority: translate('stLabel.priority'),
      isOperationalLearning: translate('stLabel.operationalLearning') ,
      operationalLearningDescription: translate('stLabel.operationalLearningDescription'),
      observationStartDate: translate('stLabel.startDate'),
      observationEndDate: translate('stLabel.endDate'),
      status: translate('stLabel.status'),
      shift: translate('stLabel.shift'),
      refUnit: translate('stLabel.unit'),
      refReportingLocation: translate('stLabel.location'),
      observedOnBehalfOf: translate('stLabel.observedBy'),
      workReleaseNumber: translate('stLabel.workReleaseNumber'),
      date:translate('stLabel.date'),
      createdTime: translate('stLabel.createdOn'),
      createdBy: translate('stLabel.createdBy'),
      lastUpdatedTime: translate('stLabel.updatedOn'),
      modifiedBy: translate('stLabel.updatedBy'),
      actions: translate('stLabel.actions'),
      month: translate('stLabel.month'),
    };
    colFun()
    // getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose()
  }, [])

  function rowDroDownChange(e) {
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value
    filterData()
  }

  const [searchParams, setSearchParams] = useSearchParams()

  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    console.log('listData', listData)
    // if (
    //   listData[parseInt(cInd)]['refOFWASchedule'] &&
    //   listData[parseInt(cInd)]['refOFWASchedule']['refOFWACategory'] &&
    //   listData[parseInt(cInd)]['refOFWASchedule']['refOFWACategory'][
    //     'refOFWAProcess'
    //   ]['name'] == 'Internal Audit'
    // ) 
    if (
      listData[parseInt(cInd)].refOFWASchedule && 
      listData[parseInt(cInd)].refOFWASchedule?.refOFWACategory &&
      listData[parseInt(cInd)].refOFWASchedule?.refOFWACategory?.refOFWAProcess?.name == 'Internal Audit'
    )
    {
      return html`<div
        style=" display: flex;
      flex-direction: row;align-item-center;"
      >
        ${userAccessMenu &&
        userAccessMenu.AuditView &&
        userAccessMenu.AuditView.featureAccessLevelCode
          ? html`${userAccessMenu.AuditView.featureAccessLevelCode ==
            'ViewAccess'
              ? html`<div
                  onClick=${() => viewClick(cInd)}
                  title=${translate('stLabel.buttonView')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="20"
                    height="20"
                    x="0"
                    y="0"
                    viewBox="0 0 488.85 488.85"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
        ${userAccessMenu &&
        userAccessMenu.AuditCreateAction &&
        userAccessMenu.AuditCreateAction.featureAccessLevelCode
          ? html`${userAccessMenu.AuditCreateAction.featureAccessLevelCode ==
              'EditAccess' ||
            userAccessMenu.AuditCreateAction.featureAccessLevelCode ==
              'ViewAccess'
              ? html` <div
                  onClick=${() => create(cInd)}
                  title=${translate('stLabel.create')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="18"
                    height="18"
                    x="0"
                    y="0"
                    viewBox="0 0 24 24"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5 12h-4v4a1 1 0 0 1-2 0v-4H7a1 1 0 0 1 0-2h4V7a1 1 0 0 1 2 0v4h4a1 1 0 0 1 0 2z"
                        data-name="Layer 2"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
      </div> `
    } else {
      return html`<div
        style=" display: flex;
      flex-direction: row;align-item-center;"
      >
      
        ${userAccessMenu &&SubProcessName!="Layered Process Audit" && SubProcessName!="Life Critical Audit" &&
        userAccessMenu.AuditSendAudit &&
        userAccessMenu.AuditSendAudit.featureAccessLevelCode
          ? html`${userAccessMenu.AuditSendAudit.featureAccessLevelCode ==
              'EditAccess' ||
            userAccessMenu.AuditSendAudit.featureAccessLevelCode == 'ViewAccess'
              ? html`<div
                  onClick=${() => sendClick(cInd)}
                  title=${translate('stLabel.send')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="20"
                    height="20"
                    x="0"
                    y="0"
                    viewBox="0 0 32 32"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="m28.91 4.417-11 24a1 1 0 0 1-1.907-.334l-.93-11.157-11.156-.93a1 1 0 0 1-.334-1.906l24-11a1 1 0 0 1 1.326 1.326z"
                        fill="#1A2254"
                        data-original="#000000"
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
        ${userAccessMenu &&
        userAccessMenu.AuditView &&
        userAccessMenu.AuditView.featureAccessLevelCode
          ? html`${userAccessMenu &&
            userAccessMenu.AuditView.featureAccessLevelCode == 'ViewAccess'
              ? html`<div
                  onClick=${() => viewClick(cInd)}
                  title=${translate('stLabel.buttonView')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="20"
                    height="20"
                    x="0"
                    y="0"
                    viewBox="0 0 488.85 488.85"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
          ${userAccessMenu &&
            userAccessMenu.AuditEdit &&
            userAccessMenu.AuditEdit.featureAccessLevelCode
              ? html` ${userAccessMenu &&
              userAccessMenu.AuditEdit.featureAccessLevelCode == 'EditAccess'
                  ? html` <div
                      onClick=${() => editClick(cInd)}
                      title=${translate('stLabel.edit')}
                      style="height:18px;margin-right:12px;cursor: pointer;"
                      }
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        version="1.1"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        xmlns:svgjs="http://svgjs.com/svgjs"
                        width="15"
                        height="15"
                        x="0"
                        y="0"
                        viewBox="0 0 348.882 348.882"
                        style="enable-background:new 0 0 512 512"
                        xml:space="preserve"
                      >
                        <g>
                          <path
                            d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                            fill="#1A2254"
                            data-original="#000000"
                            class=""
                          ></path>
                          <path
                            d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                            fill="#1A2254"
                            data-original="#000000"
                            class=""
                          ></path>
                        </g>
                      </svg>
                    </div>`
                  : ``}`
              : ``}
        ${userAccessMenu && SubProcessName!="Layered Process Audit" &&SubProcessName!="Life Critical Audit" &&
        userAccessMenu.AuditScoreCard &&
        userAccessMenu.AuditScoreCard.featureAccessLevelCode
          ? html`${userAccessMenu.AuditScoreCard.featureAccessLevelCode ==
              'EditAccess' ||
            userAccessMenu.AuditScoreCard.featureAccessLevelCode == 'ViewAccess'
              ? html`<div
                  onClick=${() => scorecard(cInd)}
                  title=${translate('stLabel.scorecard')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    onClick=${() => scorecard(cInd)}
                    xmlns="http://www.w3.org/2000/svg"
                    width="15"
                    height="15"
                    viewBox="0 0 12 10.818"
                  >
                    <path
                      id="readiness_score_FILL0_wght400_GRAD0_opsz48"
                      d="M83.225,226.815a2.274,2.274,0,0,1-1.027-.277,3.26,3.26,0,0,1-.923-.773,5.309,5.309,0,0,1-.937-1.717A6.346,6.346,0,0,1,80,222a5.842,5.842,0,0,1,.473-2.34,5.991,5.991,0,0,1,3.188-3.187,6.03,6.03,0,0,1,4.68,0,5.936,5.936,0,0,1,1.905,1.3,6.2,6.2,0,0,1,1.282,1.935A6.025,6.025,0,0,1,92,222.09a6.268,6.268,0,0,1-.36,2.13,4.606,4.606,0,0,1-1.035,1.7,3.171,3.171,0,0,1-.908.675,2.193,2.193,0,0,1-1.508.158,2.638,2.638,0,0,1-.54-.2l-.84-.42a1.542,1.542,0,0,0-.4-.15,1.913,1.913,0,0,0-.825,0,1.542,1.542,0,0,0-.4.15l-.84.42a2.458,2.458,0,0,1-.562.217A1.937,1.937,0,0,1,83.225,226.815Zm.045-.9a1.334,1.334,0,0,0,.338-.045,2.089,2.089,0,0,0,.338-.12l.84-.42a3.147,3.147,0,0,1,.585-.21,2.527,2.527,0,0,1,.615-.075,2.793,2.793,0,0,1,.615.067,2.634,2.634,0,0,1,.6.217l.855.42a2.191,2.191,0,0,0,.33.12,1.249,1.249,0,0,0,.33.045,1.448,1.448,0,0,0,.668-.165,2.221,2.221,0,0,0,.622-.495,3.906,3.906,0,0,0,.81-1.4,5.4,5.4,0,0,0,.285-1.763,5.055,5.055,0,0,0-1.478-3.69,5.106,5.106,0,0,0-7.245.015,5.105,5.105,0,0,0-1.477,3.7,5.318,5.318,0,0,0,.285,1.763,3.925,3.925,0,0,0,.81,1.387,2.142,2.142,0,0,0,.615.488A1.457,1.457,0,0,0,83.27,225.915Zm2.49-3.54a.475.475,0,0,0,.322.06.489.489,0,0,0,.277-.165L87.9,220.3a2.974,2.974,0,0,1,.315.443,2.133,2.133,0,0,1,.21.5h.93a3.4,3.4,0,0,0-1.208-1.943,3.435,3.435,0,0,0-5.512,1.943h.93a2.527,2.527,0,0,1,.923-1.3A2.467,2.467,0,0,1,86,219.45a2.618,2.618,0,0,1,.623.075,2.31,2.31,0,0,1,.577.225l-1.56,1.98a.442.442,0,0,0,.12.645ZM86,221.4Z"
                      transform="translate(-80 -216)"
                      fill="#1A2254"
                    />
                  </svg>
                </div>`
              : ``}`
          : ``}
        ${userAccessMenu && SubProcessName!="Layered Process Audit" &&SubProcessName!="Life Critical Audit" &&
        userAccessMenu.AuditCreateAction &&
        userAccessMenu.AuditCreateAction.featureAccessLevelCode
          ? html` ${(userAccessMenu &&
              userAccessMenu.AuditCreateAction.featureAccessLevelCode ==
                'EditAccess') ||
            userAccessMenu.AuditCreateAction.featureAccessLevelCode ==
              'ViewAccess'
              ? html` <div
                  onClick=${() => create(cInd)}
                  title=${translate('stLabel.create')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="18"
                    height="18"
                    x="0"
                    y="0"
                    viewBox="0 0 24 24"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5 12h-4v4a1 1 0 0 1-2 0v-4H7a1 1 0 0 1 0-2h4V7a1 1 0 0 1 2 0v4h4a1 1 0 0 1 0 2z"
                        data-name="Layer 2"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
        ${userAccessMenu && SubProcessName!="Layered Process Audit" &&SubProcessName!="Life Critical Audit" &&
        userAccessMenu.AuditSummary &&
        userAccessMenu.AuditSummary.featureAccessLevelCode
          ? html`${(userAccessMenu &&
              userAccessMenu.AuditSummary.featureAccessLevelCode ==
                'EditAccess') ||
            userAccessMenu.AuditSummary.featureAccessLevelCode == 'ViewAccess'
              ? html`<div
                  onClick=${() => auditSummary(cInd)}
                  title=${translate('stLabel.auditSummary')}
                  style="height:18px;margin-right:12px;cursor: pointer;"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    version="1.1"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:svgjs="http://svgjs.com/svgjs"
                    width="18"
                    height="18"
                    x="0"
                    y="0"
                    viewBox="0 0 512 512"
                    style="enable-background:new 0 0 512 512"
                    xml:space="preserve"
                    class=""
                  >
                    <g>
                      <path
                        d="M330 150c-19.299 0-35-15.701-35-35V0H116C85.673 0 61 24.673 61 55v402c0 30.327 24.673 55 55 55h280c30.327 0 55-24.673 55-55V150zM143 360h72.72c8.284 0 15 6.716 15 15s-6.716 15-15 15H143c-8.284 0-15-6.716-15-15s6.716-15 15-15zm-15-65c0-8.284 6.716-15 15-15h220c8.284 0 15 6.716 15 15s-6.716 15-15 15H143c-8.284 0-15-6.716-15-15zm235-95c8.284 0 15 6.716 15 15s-6.716 15-15 15H143c-8.284 0-15-6.716-15-15s6.716-15 15-15z"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                      <path
                        d="M325 115c0 2.757 2.243 5 5 5h114.314a54.866 54.866 0 0 0-10.515-13.732l-96.423-91.222a55.137 55.137 0 0 0-12.375-8.825V115z"
                        fill="#1A2254"
                        data-original="#000000"
                        class=""
                      ></path>
                    </g>
                  </svg>
                </div>`
              : ``}`
          : ``}
      </div> `
      
    }
  }

  function sendClick(index) {
    window.parent.postMessage(
      { type: 'Audit', action: 'EMailSend', data: listData[parseInt(index)] },
      '*'
    )
  }
  function viewClick(index) {
    window.parent.postMessage(
      { type: 'Audit', action: 'AuditView', data: listData[parseInt(index)] },
      '*'
    )
  }

  function editClick(index) {
    window.parent.postMessage(
      {
        type: 'Audit',
        action: 'AuditEdit',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  function scorecard(index) {
    window.parent.postMessage(
      { type: 'Audit', action: 'ScoreCard', data: listData[parseInt(index)] },
      '*'
    )
  }
  function create(index) {
    window.parent.postMessage(
      { type: 'Audit', action: 'Create', data: listData[parseInt(index)] },
      '*'
    )
  }
  function auditSummary(index) {
    window.parent.postMessage(
      {
        type: 'Audit',
        action: 'AuditSummary',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }

  function colFun() {
    var sortedDisplayedColumns = displayedColumns;
    if(columnOrder){
      console.log('sortedDisplayedColumns React', sortedDisplayedColumns, columnOrder)
      Object.entries(headersKeyVal).forEach(([key, value]) => {
        if ( columnOrder.find(item => item.key === key)) {
          headersKeyVal[key] = columnOrder.find(item => item.key === key).displayName || columnOrder.find(item => item.key === key).field;
        }
      })
      var fieldSequenceMap= columnOrder.reduce((map, item) => {
        map[item.key.toLowerCase()] = item.sequence;
        return map;
      }, {});
      sortedDisplayedColumns = displayedColumns.sort((a, b) => {
        const seqA = fieldSequenceMap[a.toLowerCase()] || Infinity; // Default to Infinity if not found
        const seqB = fieldSequenceMap[b.toLowerCase()] || Infinity;
        return seqA - seqB;
      });
    }
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine('configuration', {
      columns: sortedDisplayedColumns,
      header: headersKeyVal,
      headerSummary: colSummary,
      format: {
        isOperationalLearning: (x) => {
          if(x){
            return "Yes"
          }
          else{
            return "No"
          }
        },
        createdTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        site: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        unit: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        location: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        observationStartDate: (x) => {
          if (x) {
            return format(new Date(x), dateFormat)
          } else {
            return ''
          }
        },
        observationEndDate: (x) => {
          if (x) {
            return format(new Date(x), dateFormat)
          } else {
            return ''
          }
        },
        id: (x) => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i),
      },
      align: {
        externalId: 'left',
        title: 'left',
        corePrinciple: 'left',
        subProcess: 'left',
        year: 'left',
        quarter: 'left',
        priority: 'left',
        isOperationalLearning: 'left',
        operationalLearningDescription:'left',
        observationStartDate: 'left',
        observationEndDate: 'left',
        status: 'left',
        createdTime: 'left',
        createdBy: 'left',
        lastUpdatedTime: 'left',
        modifiedBy: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        externalId: 200,
        corePrinciple: 200,
        subProcess: 200,
        isOperationalLearning: 200,
  operationalLearningDescription:200,
        createdOn: 200,
        createdBy: 200,
        updatedOn: 200,
        updatedBy: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  async function getData() {
    console.log(startDate, endDate)
    window.parent.postMessage({
      type: 'Audit', action: 'Loading', data: true,
    }, '*' )
    fetch(Constants.NODE_API + '/api/service/listAudit', {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + token,
        Accept: 'application/json',
        'Content-type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify({
        sites: site,
        startDate: startDate,
        endDate: endDate,
        OperLearning:OperLearning,
        auditType: auditType,
        SubProcessType: SubProcessType,
        behalf:behalf,
        createdByfilter: createdByfilter,
        units:unit,
        location:location
      }),
    })
      .then((res) => res.json())
      .then((result) => {
        console.log(result)
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   console.log('temp before', temp[1].childNodes[0].nodeValue)
        //   temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //   console.log('temp after', temp[1].childNodes[0].nodeValue)
        // }
        var listProcess = result['data']['list' + Constants.typeAudit]['items']

        listData = []
        
        listProcess.forEach((element) => {
          element['workReleaseNumber'] = element['workPermitNumber']
          element['actions'] = ''
          element['priority'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['priority']
            : ''
          element['quarter'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['quarter']
            : ''
          element['year'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['year'] + ''
            : ''
          element['title'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['title']
            : ''
          element['observationStartDate'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['observationStartDate']
            : element['startTime']
          element['observationEndDate'] = element['refOFWASchedule']
            ? element['refOFWASchedule']['observationEndDate']
            : element['endTime']
          element['observedOnBehalfOf']=element['observedOnBehalfOf']?element['observedOnBehalfOf']['lastName']:''

          element['refReportingLocation']=element['refReportingLocation']?element['refReportingLocation']['description']:''

           element['refUnit']=element['refUnit']?element['refUnit']['description']:''

           element['corePrinciple'] = element['refCorePrinciple']
           ? element['refCorePrinciple']['name']
           : '';
           element['subProcess'] = element['refSubProcess']
           ? element['refSubProcess']['name']
           : '';

          listData.push(element)
        })
        console.log('listData--->', listData)
        //  main.redefine("data", listData);
        setCurrentPage(1)
        initFlag = true
        filterData()
        // colFun();
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  function filterData() {
    window.parent.postMessage({
      type: 'Audit', action: 'Loading', data: false,
    }, '*' )
    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }

  
  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }


  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
    <DynamicTranslationArea
      getAuthToken={getAuthToken}
      dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
      shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
    >
      <TranslationContextProvider
        getAuthToken={getAuthToken}
      >
      <div ref={viewofSelectionRef} />
      </TranslationContextProvider>
    </DynamicTranslationArea>
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
        //  assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default Audit
