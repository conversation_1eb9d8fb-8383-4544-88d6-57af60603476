import { ComponentFixture, TestBed, async } from '@angular/core/testing';

import { ScheduleListComponent } from './schedule-list.component';
import { AppModule } from 'src/app/app.module';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';



fdescribe('Schedule list Component', () => {
  let component: ScheduleListComponent;
  let fixture: ComponentFixture<ScheduleListComponent>;


  beforeEach(() => {
    TestBed.configureTestingModule({
   
      imports: [HttpClientTestingModule,AppModule],
    //  providers:[CommonService,DataService]
    })
    .compileComponents();
  });

  beforeEach(async(() => {
    fixture = TestBed.createComponent(ScheduleListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(true).toBe(true);
  });
  it('should create two', () => {
    expect(true).toBe(true);
  });
});

