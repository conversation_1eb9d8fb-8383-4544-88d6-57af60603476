import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SubObservationComponent } from './sub-observation.component';

describe('SubObservationComponent', () => {
  let component: SubObservationComponent;
  let fixture: ComponentFixture<SubObservationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SubObservationComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SubObservationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
