import { ComponentFixture, TestBed, async, fakeAsync } from '@angular/core/testing';

import { CreateScheduleComponent } from './create-schedule.component';
import { CommonService } from 'src/app/services/common.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import {HttpClientModule} from '@angular/common/http';
import { Router } from '@angular/router';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { AppModule } from 'src/app/app.module';

fdescribe('CreateScheduleComponent', () => {
  let component: CreateScheduleComponent;
  let fixture: ComponentFixture<CreateScheduleComponent>;


  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule,AppModule],
      providers:[CommonService]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CreateScheduleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  it('should match span text', fakeAsync (() => {
    const span = fixture.debugElement.nativeElement.querySelector('#create_text');
    fixture.detectChanges();
    expect(component.creSTitle).toBe(span.textContent);
  }));

  it('Create Schedule Form Validation', () => {
    //component.createSchedule
    component.submitClick();
    expect(component.createSchedule.valid).toBeFalsy();
    component.createSchedule.controls['title'].setValue("Test title");
    component.createSchedule.controls['observationType'].setValue("Behaviour");
    component.createSchedule.controls['observer'].setValue("Albert");
    component.createSchedule.controls['locationObserver'].setValue("Utility II Chilling plant area");
    component.createSchedule.controls['datetime'].setValue(new Date());
    component.createSchedule.controls['observePoints'].setValue("50");
    expect(component.createSchedule.valid).toBeTruthy();
   // expect(true).toBe(true);
   component.submitClick();
  });
  it('Create Schedule Audit Form Validation', () => {
    //component.createSchedule
    component.auditSubmit();
    expect(component.auditForm.valid).toBeFalsy();
    component.auditForm.controls['title'].setValue("Test title");
    component.auditForm.controls['auditType'].setValue("Supplier Audit");
    component.auditForm.controls['vendorNumber'].setValue("1013380");
    component.auditForm.controls['vendorName'].setValue("GODDING UND DRESSLER GMBH");
    component.auditForm.controls['business'].setValue('EM');
    component.auditForm.controls['region'].setValue("Europe");
    component.auditForm.controls['country'].setValue("Germany");
    component.auditForm.controls['year'].setValue("2023");
    component.auditForm.controls['quarter'].setValue("Q1");
    component.auditForm.controls['priority'].setValue("High");
    component.auditForm.controls['leadAuditor'].setValue('U. Sahoo');
    component.auditForm.controls['procurement'].setValue("Erdem Demir");
    expect(component.auditForm.valid).toBeTruthy();
   // expect(true).toBe(true);
   component.auditSubmit();
  });
 
});
