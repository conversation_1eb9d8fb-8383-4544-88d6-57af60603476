<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
  </div>
<div fxLayout="row" class="overflow-sectionaudit">
    <div fxFlex="100">
        <div class="quality-section">
            <!-- <div class="action-section-unit-section">
              <commom-label [labelText]="'OBSERVATION.QUALITY_UPLOAD.TITLE'" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
            </div> -->

            <div class="quality-upload-section">
                <div fxLayout="row" fxLayoutAlign="space-between center" class="listHeadBox marginTop padding-right_10">
                    <div class="paddingLeft-10">
                        <span class="subheading-1" fxLayoutAlign="start center">
                           {{ labels['treeheaderDocument'] }}
                        </span>
                    </div>
                    <div class="paddingLeft-10" fxLayoutAlign="start center">
                        <span class="subheading-1">
                            {{ labels['treeheaderUploaddocument'] }}
                        </span>
                    </div>
                </div>
                <div fxLayout="row wrap" fxLayoutAlign="space-between center" class="blueLightBox marginTop padding-right_10 upload-section" *ngFor="let item of uploadDocArray" >
                    <div class="paddingLeft-10">
                        <span class="subheading-1"   fxLayoutAlign="start center">
                              {{item.name}}
                        </span>
                    </div>
                    <div *ngIf="!item.evidenceObj" class="paddingLeft-10" fxLayoutAlign="start center">
                        <input hidden type="file" multiple [formControl]="item.upload" #uploader (change)="uploadMultipleFiles(item, $event)" />
                        <span class="subheading-1 uploadLink cursor" (click)="uploader.click()">
                          {{ labels['chooseFile'] }}
                        </span>
                      </div>
                      
                    <div *ngIf="item.evidenceObj" class="paddingLeft-10 " fxLayoutAlign="start center">
                        <span class="subheading-1">
                            {{ labels['treeheaderUploaded'] }}
                         </span>
                    </div>
                </div>
             </div>

             <div class="btn-section marginTop-20">
                <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                (buttonAction)="backButton()"></common-lib-button>
                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSubmit'] }}"
                  (buttonAction)="submitClick()"></common-lib-button>
              </div>
        </div>

    </div>
  </div>

