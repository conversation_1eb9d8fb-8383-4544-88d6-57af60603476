import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-site-unit-search',
  templateUrl: './site-unit-search.component.html',
  styleUrls: ['./site-unit-search.component.scss']
})
export class SiteUnitSearchComponent implements OnInit {

  siteControl: FormControl = new FormControl("All");
  unitControl: FormControl = new FormControl("All");
  siteList = [];
  filteredSiteList = [];
  unitList = [];
  filteredUnitList = [];

  constructor(private sanitizer: DomSanitizer,private dataService: DataService, private commonService: CommonService, private route: ActivatedRoute, private router: Router) {

    var _this = this;
   
   
    
  }
  ngOnInit(): void {
    var _this = this;

  }

  ngOnDestroy(): void {
    
  }
  ngAfterViewInit(): void {
    
    var _this = this;

  }



}
