import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-audit-plan-summary',
  templateUrl: './audit-plan-summary.component.html',
  styleUrls: ['./audit-plan-summary.component.scss']
})
export class AuditPlanSummaryComponent implements OnInit {


  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");
  labels = {}

  // genaralInfo = {
  //   companyName:"GODDING UND DRESSLER GMBH",
  //   hqStreetAddress:"AM HAMBUCH 11",
  //   city:"MECKENHEIM",
  //   region:"EUROPE",
  //   postalCode:"53340",
  //   country:"GERMANY",
  //   telephone:"+49 2225 92040",
  //   mainContactName:" ALINA TANCAU",
  //   mobile:"+49 2225 92040",
  //   jobTitle:"QUALITY MANAGER",
  //   email:"<EMAIL>",
  //   website:"WWW.DRESSLER-GROUP.COM"

  // }
  productInfo = {
    productFamily: "",
    locationManufacuringSite: "",
    postalCode: "",
    country: "",
    product: "",
    mainContactName: "",
    jobTitle: "",
    telephone: ""
  }
  auditInfo = {
    auditDate: "",
    auditor: "",
  }
  auditSummary = {
    consulation: "AUDIT FINDINGS HAVE BEEN REVIEWED WITH THE AUDITED ORGANIZATION IN THE CLOSING MEETING. SUMMARY OF THE DETAILS WILL BE ISSUED BY THE LEAD AUDITOR WITHIN 30 DAYS.",
    pathForward: "AUDITED ORGANIZATION WILL REVIEW FINDINGS/ACTION REQUESTS AND DEFINE APPROPRIATE ACTIONS. AUDITED ORGANIZATION WILL COMMUNICATE OUTCOME TO M&M WITHIN 30 DAYS AFTER RECEPTION OF THE SUMMARY REPORT."
  }

  auditResult = {
    needImprovement: "",
    acceptable: "",
    excellent: ""
  }
  loaderFlag: boolean;
  auditData: any;
  observation: any;
  selectedSite: any;
  vendorInfo: any;
  audits:any;


  summaryForm: FormGroup;
  checklistCount: any;
  unSatisfactoryWeightage: any;
  ofiWeightage: number;
  satisfactoryWeightage: any;
  scorePercent: number;
  auditChecklistCount: any;

  constructor(private fb: FormBuilder, private dataService: DataService, private router: Router, private commonService: CommonService, private translate: TranslateService, private languageService: LanguageService, private ngZone: NgZone, private cd: ChangeDetectorRef) {
    var _this = this;

    _this.labels = {
      'auditSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditSummary'] || 'auditSummary',
      'tableheaderGeneralinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderGeneralinformation'] || 'tableheaderGeneralinformation',
      'tableheadingCompanyname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCompanyname'] || 'tableheadingCompanyname',
      'tableheadingHqstreetaddress': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingHqstreetaddress'] || 'tableheadingHqstreetaddress',
      'tableheadingCity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCity'] || 'tableheadingCity',
      'tableheadingState': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingState'] || 'tableheadingState',
      'tableheadingPostalcode': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPostalcode'] || 'tableheadingPostalcode',
      'tableheadingCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCountry'] || 'tableheadingCountry',
      'tableheadingTelephone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingTelephone'] || 'tableheadingTelephone',
      'tableheadingMaincontactname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMaincontactname'] || 'tableheadingMaincontactname',
      'tableheadingMobile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMobile'] || 'tableheadingMobile',
      'tableheadingJobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingJobtitle'] || 'tableheadingJobtitle',
      'tableheadingEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEmail'] || 'tableheadingEmail',
      'tableheadingCorporatewebsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCorporatewebsite'] || 'tableheadingCorporatewebsite',
      'tableheaderProductrelatedinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderProductrelatedinformation'] || 'tableheaderProductrelatedinformation',
      'tableheadingProductfamily': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingProductfamily'] || 'tableheadingProductfamily',
      'tableheadingLocationmanufacturingsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingLocationmanufacturingsite'] || 'tableheadingLocationmanufacturingsite',
      'tableheadingProduct': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingProduct'] || 'tableheadingProduct',
      'tableheaderAuditrelatedinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderAuditrelatedinformation'] || 'tableheaderAuditrelatedinformation',
      'tableheadingAuditreason': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditreason'] || 'tableheadingAuditreason',
      'tableheadingAuditdate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditdate'] || 'tableheadingAuditdate',
      'tableheadingAuditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditor'] || 'tableheadingAuditor',
      'tableheadingConclusion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingConclusion'] || 'tableheadingConclusion',
      'tableheadingPathforward': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPathforward'] || 'tableheadingPathforward',
      'tableheaderAuditresult': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderAuditresult'] || 'tableheaderAuditresult',
      'tableheadingNeedimprovement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingNeedimprovement'] || 'tableheadingNeedimprovement',
      'tableheadingAcceptable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAcceptable'] || 'tableheadingAcceptable',
      'tableheadingExcellent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingExcellent'] || 'tableheadingExcellent',
      'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
      'formcontrolsLeadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLeadauditor'] || 'formcontrolsLeadauditor',
      'formcontrolsManagerorganization': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsManagerorganization'] || 'formcontrolsManagerorganization',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
      'tableheadingDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingDepartment'] || 'tableheadingDepartment',
    }
    _this.summaryForm = this.fb.group({
      productFamily: [""],
      locationManufacturingSite: [""],
      postalCode: [""],
      country: [""],
      product: [""],
      contactName: [""],
      jobTitle: [""],
      telephone: [""],
      conclusion: [""],
      pathForward: [""],
      auditorSignature: [""],
      managerSignature: [""],
      participant: [""],
      reason: [""]

    });
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'auditSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditSummary'] || 'auditSummary',
          'tableheaderGeneralinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderGeneralinformation'] || 'tableheaderGeneralinformation',
          'tableheadingCompanyname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCompanyname'] || 'tableheadingCompanyname',
          'tableheadingHqstreetaddress': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingHqstreetaddress'] || 'tableheadingHqstreetaddress',
          'tableheadingCity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCity'] || 'tableheadingCity',
          'tableheadingState': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingState'] || 'tableheadingState',
          'tableheadingPostalcode': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPostalcode'] || 'tableheadingPostalcode',
          'tableheadingCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCountry'] || 'tableheadingCountry',
          'tableheadingTelephone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingTelephone'] || 'tableheadingTelephone',
          'tableheadingMaincontactname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMaincontactname'] || 'tableheadingMaincontactname',
          'tableheadingMobile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMobile'] || 'tableheadingMobile',
          'tableheadingJobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingJobtitle'] || 'tableheadingJobtitle',
          'tableheadingEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEmail'] || 'tableheadingEmail',
          'tableheadingCorporatewebsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCorporatewebsite'] || 'tableheadingCorporatewebsite',
          'tableheaderProductrelatedinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderProductrelatedinformation'] || 'tableheaderProductrelatedinformation',
          'tableheadingProductfamily': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingProductfamily'] || 'tableheadingProductfamily',
          'tableheadingLocationmanufacturingsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingLocationmanufacturingsite'] || 'tableheadingLocationmanufacturingsite',
          'tableheadingProduct': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingProduct'] || 'tableheadingProduct',
          'tableheaderAuditrelatedinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderAuditrelatedinformation'] || 'tableheaderAuditrelatedinformation',
          'tableheadingAuditreason': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditreason'] || 'tableheadingAuditreason',
          'tableheadingAuditdate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditdate'] || 'tableheadingAuditdate',
          'tableheadingAuditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAuditor'] || 'tableheadingAuditor',
          'tableheadingConclusion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingConclusion'] || 'tableheadingConclusion',
          'tableheadingPathforward': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPathforward'] || 'tableheadingPathforward',
          'tableheaderAuditresult': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderAuditresult'] || 'tableheaderAuditresult',
          'tableheadingNeedimprovement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingNeedimprovement'] || 'tableheadingNeedimprovement',
          'tableheadingAcceptable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAcceptable'] || 'tableheadingAcceptable',
          'tableheadingExcellent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingExcellent'] || 'tableheadingExcellent',
          'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
          'formcontrolsLeadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLeadauditor'] || 'formcontrolsLeadauditor',
          'formcontrolsManagerorganization': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsManagerorganization'] || 'formcontrolsManagerorganization',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
          'tableheadingDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingDepartment'] || 'tableheadingDepartment',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })
    if (history.state.externalId) {
      _this.loaderFlag = true;
      _this.getScheduleData(history.state.externalId)
    } else {
      _this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }
  }
  ngAfterViewInit(): void {

    var _this = this;
    setTimeout(() => {
      _this.loaderFlag = true;
    }, 5000);


  }

  getScheduleData(id) {
    var _this = this;
    
    _this.dataService.postData({ externalId: id }, _this.dataService.NODE_API + "/api/service/listAudit").subscribe(data => {
      _this.auditData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
    
      if (_this.auditData["refOFWAAuditSummary"]) {
        console.log("summary", _this.auditData["refOFWAAuditSummary"])
        _this.summaryForm.get("productFamily").setValue(_this.auditData["refOFWAAuditSummary"]["productFamily"])
        _this.summaryForm.get("locationManufacturingSite").setValue(_this.auditData["refOFWAAuditSummary"]["locationManufacturingSite"])
        _this.summaryForm.get("postalCode").setValue(_this.auditData["refOFWAAuditSummary"]["postalCode"])
        _this.summaryForm.get("country").setValue(_this.auditData["refOFWAAuditSummary"]["country"])
        _this.summaryForm.get("product").setValue(_this.auditData["refOFWAAuditSummary"]["product"])
        _this.summaryForm.get("contactName").setValue(_this.auditData["refOFWAAuditSummary"]["contactName"])
        _this.summaryForm.get("jobTitle").setValue(_this.auditData["refOFWAAuditSummary"]["jobTitle"])
        _this.summaryForm.get("telephone").setValue(_this.auditData["refOFWAAuditSummary"]["telephone"])
        _this.summaryForm.get("conclusion").setValue(_this.auditData["refOFWAAuditSummary"]["conclusion"])
        _this.summaryForm.get("pathForward").setValue(_this.auditData["refOFWAAuditSummary"]["pathForward"])
        _this.summaryForm.get("auditorSignature").setValue(_this.auditData["refOFWAAuditSummary"]["auditorSignature"])
        _this.summaryForm.get("managerSignature").setValue(_this.auditData["refOFWAAuditSummary"]["managerSignature"])
        _this.summaryForm.get("participant").setValue(_this.auditData["signature"])
        _this.summaryForm.get("reason").setValue(_this.auditData["notes"])
        
      }

      console.log("idddd",_this.auditData)
      _this.audits=_this.auditData;
      console.log("audits---->",_this.audits)
      

      _this.dataService.postData({ externalId: _this.auditData["refOFWASchedule"]["refSupplier"]?_this.auditData["refOFWASchedule"]["refSupplier"]["externalId"] :''}, _this.dataService.NODE_API + "/api/service/listSupplier").subscribe(data => {

        console .log("supplierdata",data)
        _this.vendorInfo = data["data"]["list" + _this.commonService.configuration["typeSupplier"]]["items"][0];
        console.log("vendorInfo",_this.vendorInfo)
        _this.auditInfo["auditDate"] = _this.auditData["createdTime"];
        _this.auditInfo["auditor"] = _this.auditData["refOFWASchedule"]["performerAzureDirectoryUserID"]["firstName"]+ " " +_this.auditData["refOFWASchedule"]["performerAzureDirectoryUserID"]["lastName"];
         
      });
      _this.dataService.postData({ schedules: [_this.auditData["refOFWASchedule"]["externalId"]] }, _this.dataService.NODE_API + "/api/service/listChecklist").subscribe(data1 => {
        _this.observation = data1["data"]["list" + _this.commonService.configuration["typeChecklist"]]["items"];
        _this.commonService.getProcessConfiguration(_this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"], function (data2) {
          var processList = _this.commonService.processList.filter(e => {
            return (e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.auditData["refOFWASchedule"].refOFWACategory["externalId"];
          })
          var site = _this.commonService.siteList.find(e => e.externalId == _this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
          _this.selectedSite = site;
          _this.loaderFlag = false;
          _this.checklistCount = _this.observation.length;
          _this.auditChecklistCount = _this.observation.filter(e => e.comment && e.comment.length > 0).length;
          _this.unSatisfactoryWeightage = _this.observation.filter(e => e.unSatisfactory).length;
          _this.ofiWeightage = _this.observation.filter(e => e.ofi).length / 4;
          _this.satisfactoryWeightage = _this.observation.filter(e => e.satisfactory).length;

          _this.scorePercent = parseFloat(((((_this.auditChecklistCount) - (_this.unSatisfactoryWeightage + _this.ofiWeightage)) / _this.auditChecklistCount) * 100).toFixed(2));

          if (_this.scorePercent <= 40) {
            _this.auditResult["needImprovement"] = _this.scorePercent + "%"
          } else if (_this.scorePercent <= 80) {
            _this.auditResult["acceptable"] = _this.scorePercent + "%"
          } else {
            _this.auditResult["excellent"] = _this.scorePercent + "%"

          }
        });
      })

    })
  }

  goPage(page) {
    this.router.navigate([page]);
  }
 
  submit() {
    var _this = this;
    _this.loaderFlag = true;
    var formData = _this.summaryForm.value;
    // refOFWAVendor: OFWAVendor
    var postSummary = {
      productFamily: formData.productFamily,
      locationManufacturingSite: formData.locationManufacturingSite,
      // postalCode: formData.postalCode,
      // country: formData.country,
      // product: formData.product,
      // contactName: formData.contactName,
      // jobTitle: formData.jobTitle,
      // telephone: formData.telephone,
      conclusion: formData.conclusion,
      pathForward: formData.pathForward,
      auditorSignature: formData.auditorSignature,
      managerSignature: formData.managerSignature,
    }
    if(_this.auditData["refOFWAAuditSummary"]){
      postSummary["externalId"]= _this.auditData["refOFWAAuditSummary"]["externalId"];
    }
    postSummary["refOFWAVendor"] = {
      "space":_this.vendorInfo["space"],
      "externalId":_this.vendorInfo["externalId"]
    }
   
    var postObjSummary = {
      "type": _this.commonService.configuration["typeSummary"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [postSummary]
    }
    _this.dataService.postData(postObjSummary, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {

      if (data["items"].length > 0) {
        _this.updateAudit(data["items"][0]);
      } else {
        _this.loaderFlag = false;
        _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterFailed']});
      }
    })

  }

  updateAudit(refOFWAAuditSummary: any) {
    var _this = this;
    var formData = _this.summaryForm.value;
    // postObjSummary["participant"] = formData.participant;
    // postObjSummary["reason"] = formData.reason;
    var _this = this;
    var postData = {
      "externalId": _this.auditData.externalId,
      "status": "Completed"
    };
    postData["refOFWAAuditSummary"] = {
      "space": refOFWAAuditSummary["space"],
      "externalId": refOFWAAuditSummary["externalId"]
    }
    postData["notes"] = formData.reason;
    postData["signature"] = formData.participant;
    var postObj = {
      "type": _this.commonService.configuration["typeAudit"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [postData]
    }
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.updateSchedule();
    })
  }
  updateSchedule() {
    var _this = this;
    var postData = {
      "externalId": _this.auditData["refOFWASchedule"]["externalId"],
      "status": "Completed"
    };

    var postObj = {
      "type": _this.commonService.configuration["typeSchedule"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [postData]
    }
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
      _this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
      // _this.router.navigate(["observations/audit-plan"]);
    })
  }
  goAudit(){
    this.router.navigate(['observations/list'], { state: {  listType: "Audit" } });
  }
}
