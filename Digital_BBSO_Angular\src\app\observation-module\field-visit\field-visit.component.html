<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<form [formGroup]="fieldForm" *ngIf="configDetail">
    <div fxLayout="row" class="overflow-section">
    
    <div fxFlex="100">
        <div class="audit-plan-unit-section">
           
            <!-- <app-common-filter [siteControl]="siteControl"></app-common-filter> -->
        </div>


        <div class="behaviour-checklist-section">
            <commom-label (click)="testFun()" *ngIf="process && process.name == 'Field Walk'" labelText="{{ labels['buttonCreatefieldwalk'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
            <commom-label  *ngIf="process && (process.name == 'Hazard' || process.name == 'Hazards' ) " labelText="{{ labels['behaviourchecklistCreateanhazards'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>    



<div class="outerbox">
            <div fxLayout="row" fxLayout.md="column" fxLayout.sm="column" fxLayout.xs="column" style="padding: 18px;">
                <div fxFlex="70" fxLayout="column" fxLayoutGap="20px">
                    
                    <div fxLayout="row wrap" fxLayoutGap="20px">
                        <div *ngIf="configDetail && configDetail?.unit?.isEnabled">
                            <div>
                                
                                <!-- <span class="start-color" *ngIf="configDetail && configDetail?.unit?.isMandatory">
                                    *
                                </span> -->
                            </div>
                            <div>

                                <mat-form-field appearance="outline" class="behav-tree-select">
                                    <mat-label> 
                                        <!-- {{ configDetail && configDetail?.unit?.displayName   }} -->
                                          {{ labels['unit'] }}
                                    </mat-label>
                                    <mat-select (click)="filterClick()"
                                                placeholder="{{ labels['commonfilterChooseunit'] }}"
                                                [formControl]="unitControl" disableOptionCentering [disabled]="isView" [ngClass]="{'disabled': isView}">
                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']"
                                                           [displayMember]="'description'" [array]="matchingUnitList"
                                                           (filteredReturn)="filteredUnitList1=$event"></mat-select-filter>
                                        <mat-option *ngFor="let item of filteredUnitList1" [value]="item.externalId">
                                            {{item.name}} - {{item.description}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                        </div>

                        <div *ngIf="configDetail && configDetail.locationObserve.isEnabled">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.locationObserve.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>

                                <mat-form-field appearance="outline" class="behav-tree-select">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.locationObserve.displayName   }} -->
                                          {{ labels['locationObserved']}}
                                    </mat-label>
                                    <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}" formControlName="locationObserve"
                                        disableOptionCentering>
                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                            [array]="reportingLocationList"
                                            (filteredReturn)="filteredReportingLocationList =$event"></mat-select-filter>
                                        <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item">
                                            {{item.description}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>

                        
                    </div>
                    <div fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start end"  >
                        <div *ngIf="configDetail && configDetail.attendee.isEnabled">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.attendee.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>

                                <mat-form-field appearance="outline" class="behav-tree-select">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.attendee.displayName   }} -->
                                          {{ labels['attendees'] }}
    
                                    </mat-label>
                                    <mat-select placeholder="{{ labels['commonfilterChooseattendee'] }}" formControlName="attendee" disableOptionCentering>
                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'fullName'"
                                            [array]="attendeeList"
                                            (filteredReturn)="onAttendeeChange($event)"
                                            ></mat-select-filter>
                                        <mat-option *ngFor="let item of filteredattendeeList" [value]="item">
                                            {{item.fullName}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                        </div>
                        <div *ngFor="let item of selectedAttendee">
                            <div class="chip" >
                                <span class="chip-name">{{item.fullName}}</span>
                                <span class="chip-close" (click)="removeSelectedUser(item)">×</span>
                            </div>

                        </div>
                       


                    </div>
                    <div fxLayout="row wrap" fxLayoutGap="10px" >
                        <div *ngIf="configDetail && configDetail.datetime.isEnabled">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.datetime.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>

                                <mat-form-field appearance="outline" class="set-back-color-action">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.datetime.displayName   }} -->
                                        {{ labels['dateAndTime'] }}
                                    </mat-label>
                                <input autocomplete="off" matInput formControlName="datetime"
                                    [matDatepicker]="releasedAtPicker2" (click)="releasedAtPicker2.open()">
                                <mat-datepicker-toggle matSuffix [for]="releasedAtPicker2">
                                </mat-datepicker-toggle>
                                <mat-datepicker #releasedAtPicker2>
                                </mat-datepicker>
                            </mat-form-field>
                            </div>

                        </div>
                        <div *ngIf="configDetail && configDetail.startTime && configDetail.startTime.isEnabled">
                            <mat-form-field appearance="outline" style="width: 150px;">
                                <mat-label>{{ labels['startTime'] }}</mat-label>
                                <input matInput  [format]=+hourFormat formControlName="startTime"
                                    [ngxTimepicker]="toggleIcon" [disableClick]="true" placeholder="Select start time">
                                <ngx-material-timepicker-toggle matSuffix [for]="toggleIcon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                        <path id="Icon_simple-clockify" data-name="Icon simple-clockify"
                                            d="M11.486,5.892l3.885-3.939L16.483,3.08,12.6,7.019,11.485,5.892ZM9.912,9.917a1.365,1.365,0,0,1-.974-.412,1.419,1.419,0,0,1-.4-.989,1.376,1.376,0,1,1,2.752,0,1.42,1.42,0,0,1-.4.989,1.367,1.367,0,0,1-.974.412ZM16.5,13.986l-1.112,1.128L11.5,11.174l1.113-1.127Zm-6.546.589a5.8,5.8,0,0,0,2.212-.439l1.9,1.925a8.317,8.317,0,0,1-4.11,1.084A8.514,8.514,0,0,1,1.5,8.572,8.514,8.514,0,0,1,9.954,0a8.318,8.318,0,0,1,4.074,1.062L12.162,2.955a5.806,5.806,0,0,0-2.209-.438,5.976,5.976,0,0,0-5.92,6.029A5.975,5.975,0,0,0,9.954,14.575Z"
                                            transform="translate(-1.5)" fill="#1A2254" />
                                    </svg>
                                </ngx-material-timepicker-toggle>
                            </mat-form-field>
                            
                            <!-- Cancel and Confirm Buttons for Timepicker -->
                            <ngx-material-timepicker #toggleIcon [cancelBtnTmpl]="cancelBtn"
                                [confirmBtnTmpl]="confirmBtn"></ngx-material-timepicker>
                            <div class="behaviour-checklist-footer">
                                <ng-template #cancelBtn>
                                    <button mat-raised-button class="cancel cst-btn" color="primary">
                                        <span>{{ labels['buttonCancel'] }}</span>
                                    </button>
                                </ng-template>
                                <ng-template #confirmBtn>
                                    <button mat-raised-button color="primary" class="ok-btn cst-btn">
                                        <span style="color: #fff !important">{{ labels['buttonOk'] }}</span>
                                    </button>
                                </ng-template>
                            </div>
                        </div>

                        <div *ngIf="configDetail && configDetail.endTime && configDetail.endTime.isEnabled">
                            <mat-form-field appearance="outline" style="width: 150px;">
                                <mat-label>{{ labels['endTime'] }}</mat-label>
                                <input matInput [format]=+hourFormat formControlName="endTime"
                                    [ngxTimepicker]="toggleIcon2" [disableClick]="true" placeholder="Select end time">
                                <ngx-material-timepicker-toggle matSuffix [for]="toggleIcon2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                        <path id="Icon_simple-clockify" data-name="Icon simple-clockify"
                                            d="M11.486,5.892l3.885-3.939L16.483,3.08,12.6,7.019,11.485,5.892ZM9.912,9.917a1.365,1.365,0,0,1-.974-.412,1.419,1.419,0,0,1-.4-.989,1.376,1.376,0,1,1,2.752,0,1.42,1.42,0,0,1-.4.989,1.367,1.367,0,0,1-.974.412ZM16.5,13.986l-1.112,1.128L11.5,11.174l1.113-1.127Zm-6.546.589a5.8,5.8,0,0,0,2.212-.439l1.9,1.925a8.317,8.317,0,0,1-4.11,1.084A8.514,8.514,0,0,1,1.5,8.572,8.514,8.514,0,0,1,9.954,0a8.318,8.318,0,0,1,4.074,1.062L12.162,2.955a5.806,5.806,0,0,0-2.209-.438,5.976,5.976,0,0,0-5.92,6.029A5.975,5.975,0,0,0,9.954,14.575Z"
                                            transform="translate(-1.5)" fill="#1A2254" />
                                    </svg>
                                </ngx-material-timepicker-toggle>
                            </mat-form-field>
                            
                            <!-- Cancel and Confirm Buttons for Timepicker -->
                            <ngx-material-timepicker #toggleIcon2 [cancelBtnTmpl]="cancelBtn"
                                [confirmBtnTmpl]="confirmBtn"></ngx-material-timepicker>
                            <div class="behaviour-checklist-fotter">
                                <ng-template #cancelBtn>
                                    <button mat-raised-button class="cancel cst-btn" color="primary">
                                        <span>{{ labels['buttonCancel'] }}</span>
                                    </button>
                                </ng-template>
                                <ng-template #confirmBtn>
                                    <button mat-raised-button color="primary" class="ok-btn cst-btn">
                                        <span style="color: #fff !important">{{ labels['buttonOk'] }}</span>
                                    </button>
                                </ng-template>
                            </div>
                        </div>
                        


                    </div>
                    <div *ngIf="configDetail && configDetail.assets.isEnabled">
                        <div>
                            <span class="body-2 regular">
                                <!-- {{ configDetail.assets.displayName.toLowerCase() === 'assets' && configDetail && configDetail.assets.displayName ? (configDetail.assets.displayName) : configDetail.assets.displayName }} -->
                                  {{ labels['assets'] }}
                            </span>
                            <span class="start-color"  *ngIf="configDetail && configDetail.assets.isMandatory">
                                &nbsp; *
                            </span>
                        </div>

                        <div fxLayout="row">
                            <div fxLayout="row">

                                <ng-multiselect-dropdown class="search-input-assets" formControlName="assets"
                                [placeholder]="labels['commonfilterChooseassets']"
                                [settings]="dropdownSettings" (onFilterChange)="onFilterChange($event)"
                                [data]="assetsList" (onSelect)="onItemSelect($event)"
                                (onSelectAll)="onSelectAll($event)" [(ngModel)]="assetsListDeSelect">
                            </ng-multiselect-dropdown>
                            <button type="button" class="assets-close-btn" (click)="resetSelection()">
                                <mat-icon>close</mat-icon>
                            </button>
                            </div>
                            <div>
                                <mat-icon class="icons3d">
                                    view_in_ar
                                </mat-icon>
                            </div>
                        </div>



                    </div>

                    
                    <div fxLayout="row wrap" fxLayoutGap="10px" >

                    <!-- <div
                                    *ngIf="configDetail && configDetail.workOrderNumber.isEnabled">
                                    <div> -->
                                        
                                        <!-- <span class="start-color"
                                            *ngIf="configDetail && configDetail.contractorDetail.isMandatory">
                                            *
                                        </span> -->
                                    <!-- </div>
                                    <div>
                                        <mat-form-field appearance="outline" class="location-input"
                                            class="behav-tree-select">
                                            <mat-label > -->
                                                <!-- {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CONTRACTOR_DETAIL' |
                                                translate }} -->
                                                <!-- {{ labels['workOrderNumber'] }}
                                            </mat-label>
                                            <input type="text" formControlName="workOrderNumber" class="input"
                                                value="" placeholder="" aria-span="Search" matInput>
                                        </mat-form-field>
                                    </div>
                                </div> -->

                                <div
                                    *ngIf="configDetail && configDetail.projectName.isEnabled">
                                    <div>
                                        
                                        <!-- <span class="start-color"
                                            *ngIf="configDetail && configDetail.contractorDetail.isMandatory">
                                            *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" class="location-input"
                                            class="behav-tree-select">
                                            <mat-label >
                                                <!-- {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CONTRACTOR_DETAIL' |
                                                translate }} -->
                                                {{ labels['projectName'] }}
                                            </mat-label>
                                            <input type="text" formControlName="projectName" class="input"
                                                value="" placeholder="" aria-span="Search" matInput>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>

                            <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="configDetail && configDetail.workOrderNumber.isEnabled">
                                <div>
                                    <div fxLayout="row">
                                        <span class="body-2 regular">
                                            {{ labels['workOrderNumber'] }}
                                        </span>
                                        <span class="start-color" *ngIf="configDetail && configDetail.workOrderNumber.isMandatory">
                                            *
                                        </span>
                                    </div>
                                    <div fxLayout="row">
                            
                                        <ng-multiselect-dropdown class="search-input-assets" formControlName="workOrderNumber"
                                            [placeholder]="labels['chooseWorkOrderNumber']" [settings]="dropdownSettingsWorkOrderNumber"
                                            (onFilterChange)="onFilterChangeWorkOrderNumber($event)" [data]="filteredWorkOrderNumberList">
                                        </ng-multiselect-dropdown>
                                        <button type="button" class="assets-close-btn" (click)="resetSelectionWorkOrderNumber()">
                                            <mat-icon>close</mat-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="configDetail && configDetail.title.isEnabled">
                                <div style="width: 50%">
                                    <div>
                                        
                                        <!-- <span class="start-color"  *ngIf="configDetail && configDetail.whatWentWell.isMandatory">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                                            <mat-label>
                                                <!-- {{ configDetail && configDetail.title.displayName   }} -->
                                                {{ labels['title'] }}
                                                  <!-- {{ labels['whatWentWell'] }} -->
            
                                            </mat-label>
        
                                            <textarea  formControlName="title" matInput cdkTextareaAutosize cdkAutosizeMinRows="4"
                                                cdkAutosizeMaxRows="5"></textarea>
                                        </mat-form-field>
        
                                    </div>
        
                                </div>
        
                            </div>
                    <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="configDetail && configDetail.whatWentWell.isEnabled">
                        <div style="width: 100%">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.whatWentWell.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.whatWentWell.displayName   }} -->
                                          {{ labels['whatWentWell'] }}
    
                                    </mat-label>

                                    <textarea  formControlName="whatWentWell" matInput cdkTextareaAutosize cdkAutosizeMinRows="4"
                                        cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>

                            </div>

                        </div>

                    </div>


                    <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="configDetail && configDetail.whatNeedsAttention.isEnabled">
                        <div style="width: 100%">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.whatNeedsAttention.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.whatNeedsAttention.displayName   }} -->
                                          {{ labels['whatNeedsAttention'] }}
                                    </mat-label>

                                    <textarea formControlName="whatNeedsAttention" matInput cdkTextareaAutosize cdkAutosizeMinRows="4"
                                        cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>

                            </div>

                        </div>

                    </div>
                    <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="configDetail && configDetail.whatDidYouDoAboutIt.isEnabled">
                        <div style="width: 100%">
                            <div>
                                
                                <!-- <span class="start-color"  *ngIf="configDetail && configDetail.whatDidYouDoAboutIt.isMandatory">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                                    <mat-label >
                                        <!-- {{ configDetail && configDetail.whatDidYouDoAboutIt.displayName   }} -->
                                          {{ labels['whatDidYouDoAboutIt'] }}
                                    </mat-label>

                                    <textarea formControlName="whatDidYouDoAboutIt" matInput cdkTextareaAutosize cdkAutosizeMinRows="4"
                                        cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>

                            </div>

                        </div>

                    </div>

                    <div *ngIf="configDetail && configDetail?.operationalLearning?.isEnabled">
                        <div>
                            <span class="body-2 regular">

                                {{  labels[configDetail.operationalLearning.translateKey] }}
                                  <!-- {{ labels }} -->
                                <!-- {{ configDetail && configDetail.operationalLearning.displayName | translate }} -->                                        
                            </span>
                            <span class="start-color"
                            *ngIf="configDetail && configDetail.operationalLearning.isMandatory">
                                *
                            </span>
                            <span class="info-button">
                                <mat-icon
                                            matTooltip="{{ labels['operationalLearningTooltip'] }}"
                                            [matTooltipPosition]="'right'"
                                            class="custom-tooltip-style"
                                            [matTooltipClass]="'custom-tooltip'"
                                        >
                                            info
                                        </mat-icon>
                                <!-- <div class="tooltip">
                                  {{ labels['operationalLearningTooltip'] }}
                                </div> -->
                              </span>
                        </div>
                        <div>
                            <mat-radio-group formControlName="operationalLearning">
                                <mat-radio-button *ngFor="let item of filteredContractorList" [value]="item.value" style="margin-right: 16px;margin-top: 3px;">
                                    {{ labels['formcontrols'+item.value]  }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                    

                    
                        <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="fieldForm.get('operationalLearning').value == 'Yes' && configDetail && configDetail?.operationalLearningDescription?.isEnabled">
                            <div style="width: 100%"
                                *ngIf="configDetail && configDetail.operationalLearningDescription.isEnabled">
                                <div>
                                    
                                    <!-- <span class="start-color"
                                        *ngIf="configDetail && configDetail.operationalLearningDescription.isMandatory">
                                        *
                                    </span> -->
                                </div>
                                <div>
                                    <mat-form-field appearance="outline" style="width: 100%;height: 90px;"
                                        class="behav-tree-select">
                                        <mat-label>
                                            {{ labels[configDetail.operationalLearningDescription.translateKey] }}
                                                <!-- {{ configDetail && configDetail.operationalLearningDescription.displayName ? 'TABLE_COLS.'+configDetail.operationalLearningDescription.displayName : configDetail.operationalLearningDescription.displayName  |
                                                translate }} -->
                                            </mat-label>

                                        <textarea matInput id="ob_textarea" formControlName="operationalLearningDescription"
                                            cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                            cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>

                                </div>

                            </div>

                        </div>

                    <div fxLayout="row wrap" fxLayoutGap="10px" class="marginTop" *ngIf="configDetail && configDetail.overallSummary.isEnabled">
                        <div style="width: 100%">
                            <div>
                                
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.overallSummary.isMandatory">
                                        &nbsp; *
                                    </span> -->
                            </div>
                            <div>
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                                    <mat-label>
                                        <!-- {{ configDetail && configDetail.overallSummary.displayName   }} -->
                                        {{ labels['overallSummary'] }}
                                    </mat-label>

                                    <textarea formControlName="overallSummary" matInput cdkTextareaAutosize cdkAutosizeMinRows="4"
                                        cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>

                            </div>

                        </div>

                    </div>

                    <div *ngIf="configDetail && configDetail.signature.isEnabled">
                        <mat-form-field appearance="outline" style="width: 273px;height: 90px;">
                            <mat-label>
                                <!-- {{ configDetail && configDetail.signature.displayName   }} -->
                                  {{ labels['signature'] }}
                            </mat-label>
        
                            <textarea formControlName="signature" matInput cdkTextareaAutosize cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                        </mat-form-field>
        
                    </div>

                </div>
                <!-- <div fxFlex="30">

                    <div>
                        <br><br><br><br>

                        <div class="uploadPhotoBoxField marginTop">

                            <div fxLayout="column" fxLayoutGap="20px" style="height: 100%;" fxLayoutAlign="end center">

                                <div>
                                    <img src="../../../assets/field-work.PNG" height="404" width="346" alt="" srcset="">
                                </div>
                                <div class="mb-10">
                                    <common-lib-button [className]="'cst-btn'"
                                        [text]="'BUTTON.UPLOAD_PHOTO_VIDEO'"></common-lib-button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div> -->
                <div class="marginTop">
                    <div>
                      <div>
                        <span class="body-2 regular">
                          {{ labels['formcontrolsUploadphotos']}}
                        </span>
                        
                      </div>
                      <span class="upload-info">({{ labels['formcontrolsMaxphotos'] }})</span>
                      <div class="uploadPhotoBox">
                        <!-- Image container -->
                        <div class="image-container" *ngFor="let image of images; let i = index">
                          <img class="proofImage" [src]="image" alt="Uploaded Image" />
                          <button class="remove-btn" *ngIf="!isView" (click)="removeImage(i)" title="Remove"><mat-icon id="close">close</mat-icon></button>
                          <button class="view-btn" (click)="viewImage(i)" title="View">
                            <!-- View icon -->
                            <svg class="view-icon" xmlns="http://www.w3.org/2000/svg" version="1.1"
                              xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                              viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve">
                              <g>
                                <path
                                  d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                                  fill="#00ff00"></path>
                              </g>
                            </svg>
                          </button>
                        </div>
                  
                        <!-- Video container -->
                        <div class="video-container" *ngFor="let video of videos; let i = index">
                          <video class="proofVideo" [src]="video" controls></video>
                          <button class="remove-btn" *ngIf="!isView" (click)="removeVideo(i)" title="Remove"><mat-icon id="close">close</mat-icon></button>
                          <button class="view-btn" (click)="viewVideo(i)" title="View">
                            <!-- View icon -->
                            <svg class="view-icon" xmlns="http://www.w3.org/2000/svg" version="1.1"
                              xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                              viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve">
                              <g>
                                <path
                                  d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                                  fill="#00ff00"></path>
                              </g>
                            </svg>
                          </button>
                        </div>
                  
                        <!-- Add photo container -->
                        <div class="add-photo-container" *ngIf="images.length + videos.length < maxMedia" (click)="uploader.click()">
                          <img src="../../../assets/icons/add-photo.svg" height="100" width="100" alt="Add Photo or Video" />
                        </div>
                        <!-- File uploader input -->
                        <input hidden type="file" #uploader (change)="uploadFile($event)" multiple />
                      </div>
                  
                      <!-- Fullscreen modal for viewing images and videos -->
                      <div class="fullscreen-modal" *ngIf="viewedImageIndex !== null">
                        <img class="modal-content" [src]="images[viewedImageIndex]" alt="Viewed Image" />
                        <button class="close-modal" (click)="closeViewedImage()"><mat-icon>close</mat-icon></button>
                      </div>
                      <div class="fullscreen-modal" *ngIf="viewedVideoIndex !== null">
                        <video class="modal-content" [src]="videos[viewedVideoIndex]" controls></video>
                        <button class="close-modal" (click)="closeViewedVideo()"><mat-icon>close</mat-icon></button>
                      </div>
                    </div>
                  </div>

            </div>
            <div class="marginTop" *ngIf="configDetail && configDetail.signature.isEnabled">
                
                    <!-- <span class="start-color"  *ngIf="configDetail && configDetail.signature.isMandatory">
                        &nbsp; *
                    </span> -->

            </div>
            
            <div class="behaviour-checklist-fotter">
                <!-- <common-lib-button *ngIf="!isView" [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                    (buttonAction)="cancelClick()"></common-lib-button> -->
                <common-lib-button [className]="'cancel cst-btn'" text="{{labels['buttonCancel']}}"
                    (buttonAction)="backClick()"></common-lib-button>
                <common-lib-button *ngIf="!isView" [className]="'cst-btn'" text="{{labels['buttonSubmit']}}"
                    (buttonAction)="submitClick()"></common-lib-button>
            </div>
            <div fxLayout="row" class="clear"> </div>
        </div>
        </div>
    </div>
    <div>

    </div>
</div>
</form>
