@use "../abstract/variable" as var;
@use "../abstract/functions" as func;
.btn {
  padding: 0.8rem 1.6rem;
  border-radius: 6px;
  line-height: 1;
  font-size: 0.9rem;
  font-weight: 600;
  border: none;
  outline: none;
  display: inline-block;
  cursor: pointer;

  &-primary {
    @extend .btn;
    background-color: func.theme-colors();
    color: #fff;

    &:hover {
      background-color: func.theme-colors("primary-hover");
    }
  }

  &-primary-outlined {
    @extend .btn;
    background-color: #fff;
    border: 2px solid func.theme-colors();
    color: func.theme-colors();

    &:hover {
      background-color: func.theme-colors();
      color: #fff;
    }
  }
}
