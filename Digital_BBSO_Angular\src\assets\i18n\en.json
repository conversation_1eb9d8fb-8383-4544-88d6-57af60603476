{"MENU": {"HOME": "Home", "DASHBOARD": "Dashboards", "ObSERVATION": "Observation", "FIELDWALK": "Field Walk", "AUDIT": "Audit", "SCHEDULE": "Schedule", "CONFIGURATION": "Configuration", "ACTION": "Actions", "ALERT": "<PERSON><PERSON><PERSON>", "RULE": "Rules", "WORKFLOW": "Workflow", "SETTINGS": "Settings", "FETCH_USER_SETTINGS": "Please wait, loading your User Access settings.", "CHOOSE_LANG": "Choose Language", "SIGN_OUT": "Sign Out"}, "Who is being Audited": "Who is being Audited", "Name of Company": "Name of Company", "Which Activity is being Audited": "Which Activity is being Audited", "Work permits": "Work permits", "Work Release Number": "Work Release Number", "Discussion Point": "Discussion Point", "Correct issue on spot": "Correct issue on spot", "Minor corrections to Procedures": "Minor corrections to Procedures", "Significant process or Training upgrades required": "Significant process or Training upgrades required", "Unsafe Act/Behavior": "Unsafe Act/Behavior", "Unsafe Condition": "Unsafe Condition", "Expand": "Expand", "Collapse": "Collapse", "Set Recurrence": "Set Recurrence", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "On day": "On day", "on": "on", "and": "and", "month": "month", "starting": "starting", "until": "until", "Week Days": "Week Days", "One Time": "One Time", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Remove": "Remove", "Occurs every": "Occurs every", "day": "day", "LOG_INFO": "Log Information", "Type": "Type", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Week 1": "Week 1", "Week 2": "Week 2", "Week 3": "Week 3", "Week 4": "Week 4", "Week 5": "Week 5", "Contractor": "Contractor", "Contractor Detail": "Contractor <PERSON><PERSON>", "Crafts": "Crafts", "Department": "Department", "Floor": "Floor", "Injury Potential": "Injury <PERSON>", "Not Observed": "Not Observed", "Notes": "Notes", "Observed on behalf of": "Observed on behalf of", "TimeCapture": "Time Capture", "Suggested Solution": "Suggested Solution", "Urgency": "Urgency", "Unit": "Unit", "1W": "1W", "1M": "1M", "3M": "3M", "1Y": "1Y", "YTD": "YTD", "Shift": "Shift", "Short Description": "Short Description", "Safe": "Safe", "Unsafe": "Un safe", "IsSafe": "Is Safe", "Observed Craft": "Craft Observed", "Observered Craft": "Craft Observed", "Month": "Month", "Week": "Week", "Not Safe": "Not Safe", "Feedback": "<PERSON><PERSON><PERSON>", "Activity": "Activity", "Risky Action": "Risky Action", "Risk": "Risk", "Risk Agreement": "Risk Agreement", "Reason for Action": "Reason for Action", "Safe Behaviour": "Safe Behaviour", "Describe the Observations": "Describe the Observations", "Additional comments on Observation": "Additional comments on Observation", "Do not Proceed": "Do not Proceed", "Signature": "Signature", "Location Observed": "Location Observed", "Attendees": "Attendees", "Date and Time": "Date and Time", "Date": "Date", "Start Time": "Start Time", "End Time": "End Time", "Assets": "Assets", "What went well": "What went well", "What needs attention": "What needs attention", "What did you do about it": "What did you do about it", "Overall Summary": "Overall Summary", "Did corrective actions occur specific to the stop work event ?": "Did corrective actions occur specific to the stop work event ?", "Describe corrective action taken": "Describe corrective action taken", "Event Description": "Event Description", "Event Type": "Event Type", "Stop Work Authority": "Stop Work Authority", "SETTINGS": {"TITLE": "SETTINGS", "DATE_TIME_CONFIG": "Date and Time Configuration", "DATE_FORMAT": "Date Format", "TIME_FORMAT": "Time Format"}, "Observation type": "Observation Type", "Add Observation Type": "Add Observation Type", "Add Category": "Add Category", "Add Sub-Category": "Add Sub-Category", "Choose the Observation type": "Choose the Observation type", "All Sites": "All Sites", "TITLE": "Observations, Field Walks and Audits", "MOB_TITLE": "OFA", "CREATE_ACTION": "Create Action", "SITE_UNIT": {"SITE": "Site", "UNIT": "Unit"}, "CONFIRMATION_MESSAGE": {"TITLE": "Do you want to assign this RCA?", "USERINTERGRATION_TITLE": "Are you sure do you want to switch to a different site."}, "COMMONFILTER": {"CHOOSE_SITE": "Choose Site", "CHOOSE_UNIT": "Choose Unit", "CHOOSE_PROCESS": "Choose Process", "CHOOSE_LOCATION": "Choose Location", "CHOOSE_ATTENDEE": "<PERSON><PERSON>", "CHOOSE_AUDIT_TYPE": "<PERSON><PERSON>", "CHOOSE_SUB_PROCESS": "Choose Sub Process", "CHOOSE_FLOOR": "<PERSON>ose <PERSON>", "CHOOSE_CATEGORY": "Choose Category", "CHOOSE_SUB_CATEGORY": "Choose Sub Category", "CHOOSE_CRAFT": "Choose Craft", "CHOOSE_ASSETS": "<PERSON><PERSON>", "CHOOSE_CONTRACTOR": "<PERSON>ose Contractor", "CHOOSE_BEHALF": "<PERSON><PERSON>", "CHOOSE_ASSIGNED": "<PERSON><PERSON> Assigned To", "CHOOSE_PRIORITY": "Choose Priority", "CHOOSE_CORE_PRINCIPLE": "Choose Core Principle", "CHOOSE_QUARTER": "Choose Quarter", "SEARCH": "Search", "START_DATE": "Start Date", "END_DATE": "End Date", "NO_RESULTS": "No Results", "NO_DATA": "No Data Available", "USERS": "Users", "CHOOSE_PPR": "Choose PPR", "CHOOSE_CO_AUDITOR": "<PERSON><PERSON> Co-Auditor", "CHOOSE_Supplier": "<PERSON><PERSON> Supplier", "CHOOSE": "<PERSON><PERSON>", "CHOOSE_YEAR": "Choose Year", "CHOOSE_CATEGORIES": "Choose Categories"}, "OBSERVATION": {"COMMON": {"CREATE_AN": "Create an"}, "SUB_HEADER": {"TITLE": "Welcome To OBSERVATIONS, FIELD WALKS AND AUDITS"}, "MAIN": {"TITLE": "What would you like to do today?", "CARDS": {"OBSERVATION": "Observation", "Observation": "Observation", "Field Walk": "Field Walk", "Audit": "Audit", "Stewardship": "Stewardship", "Quality": "Quality", "Reliability": "Reliability", "Behaviour": "Behaviour", "Final Product": "Final Product", "HAZARD": "Hazard", "Hazards": "Hazards", "OFFICE_SAFETY": "Office Safety", "INCIDENT": "Incident", "SUPPLIER_AUDIT": "Supplier Audit", "CUSTOMER_AUDIT": "Customer <PERSON><PERSON>", "All": "All", "Engagement": "Engagement"}}, "CREATE": {"CARD": {"TITLE": "Process", "DESCRIPTION": "Choose the process under which you want to report", "CHOOSE_THE": "Choose the", "TYPE": "type"}}, "SUB_OBSERVATION": {"CARD": {"TITLE": "Core Principles", "DESCRIPTION": "Choose the core principle under which you want to report", "CHOOSE_THE": "Choose the", "TYPE": "type"}}, "BEHAVIOUR_CHECKLIST": {"CREATE_AN_OBERVATION": "Create Observation", "CREATE_AN_HAZARDS": "Create Hazards", "CREATE_AN_AUDIT": "Create <PERSON><PERSON>", "FORM_CONTROLS": {"PROCESS": "Process", "CORE_PRINCIPLE": "Core Principles", "OBSERVATION_TYPE": "Observation Type", "CHOOSE_CONTRACTOR": "<PERSON>ose Contractor", "DEPARTMENT": "Department", "CHOOSEDEPARTMENT": "Choose Department", "CHOOSE_FLOOR": "<PERSON>ose <PERSON>", "CATEGORY": "Category", "SUB_CATEGORY": "Sub-Category", "LOCATION_OBSERVED": "Location Observed", "OBSERVED_BEHALF": "Observed on behalf of", "PLANT": "Plant", "CRAFT": "Crafts", "MONTH": "Month", "WEEK": "Week", "CHOOSE_MONTH": "Choose Month", "CHOOSE_WEEK": "Choose Week", "DATE_TIME": "Date and Time", "START_TIME": "Start Time", "END_TIME": "End Time", "ASSETS": "Assets", "VIEW_IN": "view_in_ar", "CONTRACTOR": "Contractor", "CONTRACTOR_DETAIL": "Contractor <PERSON><PERSON>", "DESCRIBE_OBSERVATION": "Describe the Observations", "SHIFT": "Shift", "ACTION": "Actions", "SHORT_DESCRIPTION": "Short Description", "FLOOR": "Floor", "URGENCY": "Urgency", "ADDITIONAL_COMMENTS": "Additional comments on Observation", "UPLOAD_PHOTOS": "Upload Photos or Videos (If applicable)", "BEHAVIOUR_CHECKLIST": "Checklist", "ADD_CIRCLE": "add_circle", "CHECKLIST": "Checklist", "SAFE": "Safe", "UNSAFE": "Unsafe", "ISSAFE": "Is Safe", "IS_NOT_OBSERVED": "Is Not Observed", "NOT_OBSERVED": "Not Observed", "INJURY_POTENTIAL": "Injury <PERSON>", "NOTE": "Notes", "FEEDBACK": "<PERSON><PERSON><PERSON>", "ACTIVITY": "Activity", "RISKY_ACTION": "Risky Action", "RISK": "Risk", "RISK_AGREEMENT": "Risk Agreement", "REASON_ACTION": "Reason for Action", "SAFE_BEHAVIOUR": "Safe Behaviour", "POSSIBLE": "Possible", "DIFFICULT": "<PERSON><PERSON><PERSON><PERSON>", "IMPOSIBLE": "Imposible", "SUGGESTED_SOLUTION": "Suggested Solution", "SIGNATURE": "Signature", "High": "High", "Medium": "Medium", "Low": "Low", "YES": "Yes", "NO": "No", "PLEASE_CHOOSE_CAT": "Please choose the applicable Category", "PLEASSE_CHOOSE_SUB_CAT": "Please choose the applicable Sub-Category", "Week": "Week", "CHOOSE_SHIFT": "<PERSON><PERSON>", "CHOOSE_ACTION": "Choose Action", "MAX_PHOTOS": "Please note: You can upload a maximum of 50 photos.", "DAY": "Day", "NIGHT": "Night", "CONTRACTOR_CRAFT": "Observed Craft", "SELECT_ALL": "Select All", "SHIFT A": "Shift A", "SHIFT B": "Shift B", "SHIFT C": "Shift C", "SHIFT D": "Shift D", "GUIDELINE_DOC": "Guideline Document", "ACTION_LVL": "Action Level", "BASIC_DETAILS": "Basic Details", "COMP_CHECKLIST": "Complete Checklist", "PARTNER_COMPANY": "Partner Company", "CELANESE": "Celanese", "CHOOSE_TYPE": "Choose Auditer Type", "WP": "Choose Work Permit", "Fall hazards/elevated workplace (AE)": "Fall hazards/elevated workplace (AE)", "Special dangers (AE)": "Special dangers (AE)", "Entering containers/confined spaces (AE)": "Entering containers/confined spaces (AE)", "Earthworks (CL)": "Earthworks (CL)", "High pressure cleaning (AE)": "High pressure cleaning (AE)", "Crane work (CL)": "Crane work (CL)", "Ignition hazards (AE)": "Ignition hazards (AE)", "EARLY SHIFT": "Early Shift"}, "CHIPS": {"STOP_WORK": "Stop Work and Report", "USE_CAUTION": "Use Caution and Report", "CONTINUE_REPORT": "Continue and Report", "TEST_CATEGORY": "Test Category", "SCAFFOLDS": "ScaffoldS", "PPE": "PPE", "NEW_CATEGORY": "New Category", "LINE_FIRE": "Line of Fire"}}, "FIELD_VISIT": {"TITLE": "Field Visit", "HAZARDS": "Hazards", "FORM_CONTROLS": {"LOCATION": "Location", "OPTION_1": "Option 1", "ATTENDEES": "Attendees", "DATE_TIME": "Date and Time", "START_TIME": "Start Time", "END_TIME": "End Time", "ASSETS": "Assets", "VIEW_IN": "view_in_ar", "WHAT_WENT_WELL": "What went well?", "WHAT_NEED_ATTENTION": "What needs attention?", "WHAT_YOU_ABOUT_IT": "What did you do about it?", "OVERALL_SUMMARY": "Overall Summary", "SIGNATURE": "Signature"}}, "OBSERVATION_LIST": {"TITLE_OBSERVATION": "Observations", "TITLE_FIELDWALK": "Field Walk", "TITLE_AUDIT": "Audit", "LIST": "List"}, "QUALITY_ASSESSMENT": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"CHECKLIST": "Checklist", "SUPPLIER_SELF_COMMENTS": "Supplier self assessment Comments"}}, "QUALITY_UPLOAD": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"DOCUMENT": "Evidence/Document", "UPLOAD_DOCUMENT": "Upload Documents", "CHOSSE_FILE": "Choose file", "UPLOADED": "Uploaded", "VIEW": "View"}}, "AUDIT_PLAN": {"TITLE": "Quality Assessment Questionnaire", "TREE_HEADER": {"DOCUMENT": "Evidence/Document", "UPLOAD_DOCUMENT": "Documents Uploaded", "VERIFICATION_COMMENTS": "Verification Comments"}}, "SCORECARD": {"TITLE": "Scorecard", "TABLE_HEADER": {"GENERAL_INFORMATION": " GENERAL INFORMATION", "PRODUCT_RELATED_INFORMATION": "PRODUCT RELATED INFORMATION", "AUDIT_RELATED_INFORMATION": "AUDIT RELATED INFORMATION", "AUDIT_SUMMARY": "AUDIT SUMMARY", "AUDIT_RESULT": "AUDIT RESULT", "SATISFACTORY": "Satisfactory", "OFI": "OFI", "UNSATISFACTORY": "Unsatisfactory", "SUMMARY": "Summary", "NO_FINDINGS": "No of Findings.", "NO_AUDIT_Q": "No. Audit Questions(total)", "NO_Q_AUDIT": "No. Questions Audited", "UNSAT_WEIGHT_SCORE": "Unsatisfactory Weightage Score", "OFI_WEIGHT_SCORE": "OFI Weightage Score", "SAT_WEIGHT_SCORE": "Satisfactory Weightage Score", "SUB_CATS": "Sub Categories", "SUPPLY_SELF_ASSES": "Supplier self assessment Comments", "ON_SITE_VISUAL": "On- Site Visit / Virtual audit notes"}, "FORM_CONTROLS": {"SIGNATURE": "Signature", "LEAD_AUDITOR": "LEAD AUDITOR", "MANAGER_ORGANIZATION": "MANAGER OF AUDITED ORGANIZATION"}, "TABLE_HEADING": {"COMPANY_NAME": "COMPANY NAME", "TELEPHONE": "TELEPHONE", "HQ_STREET_ADDRESS": "HQ STREET ADDRESS", "MAIN_CONTACT_NAME": "MAIN CONTACT NAME", "CITY": "CITY", "MOBILE": "MOBILE", "STATE": "REGION / STATE", "JOB_TITLE": "JOB TITLE", "POSTAL_CODE": "POSTAL (ZIP) CODE", "E_MAIL": "E-MAIL", "COUNTRY": "COUNTRY", "CORPORATE_WEB_SITE": "CORPORATE WEB SITE", "PROD_INFO": "PRODUCT RELATED INFORMATION", "PRODUCT_FAMILY": "PRODUCT FAMILY", "LOCATION_MANUFACTURING_SITE": "LOCATION MANUFACTURING SITE", "PRODUCT": "PRODUCT", "AUDIT_REASON": "AUDIT REASON", "AUDIT_DATE": "AUDIT DATE", "AUDITOR": "AUDITOR", "DEPARTMENT": "PARTICIPANTS/DEPARTMENT", "CONCLUSION": "CONCLUSION", "PATH_FORWARD": "PATH FORWARD", "NEED_IMPROVEMENT": "NEED IMPROVEMENT", "ACCEPTABLE": "ACCEPTABLE", "EXCELLENT": "EXCELLENT"}}, "CHECKLIST": {"TITLE": "Checklist Metrics"}, "AUDITSUMMARY": {"TITLE": "<PERSON>t <PERSON>"}, "SEND_MAIL": "Send Mail"}, "DASHBOARD": {"TITLE": "Dashboard", "SUB_HEADING": {"chartTitle1": "Trends", "chartTitle2": "Observers Points", "chartTitle3": "By Location", "chartTitle4": "By Categories", "chartTitle5": "By Department", "chartTitle6": "Risk Behaviours at last 7 days", "chartTitle7": "At Risk by Section", "chartTitle8": "By Department", "chartTitle9": "Sitewide participation last 7 days", "chartTitle10": "By Categories", "chartTitle11": "Percentage Participation", "chartTitle12": "By Unit"}, "CHARTS": {"PARTICIPATION": "Participation", "NON_PARTICIPATION": "Non Participation", "COUNT": "Count", "PERIOD": "Period"}, "CARDS": {"TITLE_OBSERVATION": "Observations", "BEHAVIOUR": "Behaviour", "HAZARD": "Hazards", "INCIDENT": "Incidents", "AUDIT": "Audits", "ACTION": "Actions", "FIELD_WALKS": "Field Walks"}}, "SCHEDULE": {"SCHEDULE_LIST": {"TITLE": "Schedule", "CANCEL_MSG": "Are you sure, you want to cancel this schedule?"}, "CREATE_SCHEDULE": {"TITLE": "Create Schedule", "FORM_CONTROLS": {"PROCESS": "Process", "OBSERVATION": "Observation", "FIELD_WALK": "Field Walk", "AUDIT": "Audit", "TITLE": "Title", "OBSERVATION_TYPE": "Observation Type", "BEHAVIOUR": "Behaviour", "HAZARDS": "Hazards", "INCIDENTS": "Incidents", "OBSERVER": "Observer", "LOCATION_OBSERVED": "Location to be Observed", "UTILITY_CHILLING_PLANT": "Utility II Chilling plant area", "UTILITY_CHILLING_SECOND": "Utility I Chilling plant area", "DATE_TIME": "Date and Time", "OBSERVATION_POINTS": "Observation Points", "AUDIT_TYPE": "Audit Type", "CATEGORY": "Category", "CORE_PRINCIPLE": "Core Principles", "SUPPLIER_AUDIT": "Supplier Audit", "Supplier_NUMBER": "Supplier Number", "Supplier_NAME": "Supplier Name", "Supplier_EMAIL": "Supplier Email", "BUSINESS": "Business", "REGION": "Region", "EUROPE": "Europe", "COUNTRY": "Country", "GERMANY": "Germany", "YEAR": "Year", "QUARTER": "Quarter", "PRIORITY": "Priority", "HIGH": "High", "LOW": "Low", "LEAD_AUDITOR": "Lead Auditor", "PROCUREMENT_PPR": "Procurement PPR", "SITE_MANAGER": "Site Manager", "START_DATE": "Start Date", "END_DATE": "End Date", "FIRST_TIME_AUDIT": "First Time Audit", "LEGAL_ENTITY": "Legal Entity", "SITE_CERTIFICATE_NUMBER": "Site Certificate Number", "CO_AUDITOR": "Co-Auditor", "EMAIL": "E-Mail", "CHOOSE_LEAD_AUDITOR": "<PERSON><PERSON> Lead Auditor"}}, "TRACKER": {"TITLE": "Tracker", "COMPLETED": "Completed", "NOT_COMPLETED": "Not Completed", "SCHEDULED": "Scheduled"}}, "CONFIGURATION": {"TITLE": "Configuration", "UNIT_SITE": {"PROCESS": "Process", "SITE": "Site"}, "FORM_CONTROLS": {"CORE_PRINCIPLE": "Core Principles", "OBSERVATION_TYPE": "Observation Types", "ADD_SUB_PROCESS": "Add Sub Process", "AUDIT_TYPE": "Audit Type", "ADD_AUDIT_TYPE": "Add Audit Type", "Process": "Process", "Sub Process": "Sub Process", "CATEGORY": "Categories", "Category": "Categories", "Add Category": "Add Category ABC", "ADD_CATEGORY": "Add Category", "SUB_CATEGORY": "Sub-Categories", "Sub-Category": "Sub-Categories", "ADD_SUB_CATEGORY": "Add Sub-Category", "FEEDBACK": "Feedback to be enabled when selected", "TIME_CAPTURE": "Time Capture Required", "UNSAFE": "Unsafe", "SIGNATURE": "Signature Required", "PROCESS_CONFIG": "Process Configuration", "CHECKLIST_QUES": "Checklist Questions", "FIELD_CONFIG": "Field Configuration", "NORIF_FIELD_ALERT_SETTINGS": "Notification Alert <PERSON>s", "Observation Type Name": "Observation Type Name", "Observation Type": "Observation Type", "Sub Process Name": "Sub Process Name", "SEQUENCE": "Sequence", "Category Name": "Category Name", "Sub-Category Name": "Sub Category Name", "FIELD": "Field", "DISP_NAME": "Display Name", "ENABLED": "Enabled", "ATLEAST_ONE": "(Atleast One)", "MANDATORY": "Mandatory", "DATE_TIME_CONFIG": "Date and Time Configuration", "DATE_FORMAT": "Date Format", "TIME_FORMAT": "Time Format", "ADD_CRAFT": "Add Craft", "CRAFT_OBS": "Craft Observed", "LIST_FIELDS": "List of Fields", "DEF_VALUE": "Default value(Any One)", "CHECKLIST_FIELDS": "Checklist Fields", "Audit Name": "Audit Name", "Audit": "Audit", "DASHBOARD_CONFIG": "Dashboard Configuration", "DASHBOARD_GRAPHS": "Dashboard Graphs", "GRAPH_NAME": "Graph Name", "GRAPH_DISP_NAME": "Graph Display Name", "LIST_CONFIG": "List Configuration", "CONFIG_LIST_COLUMNS": "Configure List Columns", "COLUMN_NAME": "Column Name"}, "FORM_CONFIG": {"TITLE": "Form Configuration"}, "QUESTION_CONFIG": {"TITLE": "Form Configuration", "FORM_CONTROLS": {"OBSERVATION_TYPE": "Observation Type", "COLUMN_NAME": "Column Name", "CHECK_BOX": "Check Box", "RADIO": "Radio", "DROP_DOWN": "Dropdown", "INPUT_BOX": "Input Box", "SUB_CATS": "Sub Categories"}}, "QUESTION_LIST": {"TITLE": "Form for "}, "ADD_QUESTION": {"TITLE": "Questions for ", "FORM_CONTROLS": {"QUESTION": "Question #", "QUESTION_DESCRIPTION": "Question Description", "FIELD_REQUIRED": "Fields Required", "SAFE": "Safe", "UNSAFE": "Unsafe", "N_A": "N/A", "INJURY_POTENTIAL": "Injury <PERSON>", "SEQUENCE_NO": "Sequence"}}}, "ACTION": {"ACTION_LIST": {"TITLE": "Actions"}, "CREATE_ACTION": {"TITLE": "Create Action for Observation", "ARE_YOU_SURE": "Are you sure you want to delete this observation?", "DESC_MESSAGE": "Minimum length of 10 and maximum of 200 characters", "FORM_CONTROLS": {"OCCURRENCE": "Occurrence", "OBSERVATION_DESCRIPTION": "Action description", "SUGGESTION_FEEDBACK": "Suggestions / Feedback", "UPLOAD_FILES": "Upload files", "DRAG_DROP": "Drag and drop file here or", "CHOOSE_FILE": "Choose file", "OBSERVED_BY": "Observed by", "ASSIGNED_TO": "Assigned to", "STATUS": "Status", "ASSIGNES": "Assigned", "START_DATE": "Start date", "DUE_DATE": "Due date", "PRIORITY": "Priority", "S_NO": "S.No", "OBS_COUNT": "Observation Count", "HAZ_COUNT": "Hazards Count"}}}, "ALERT": {"TITLE": "<PERSON><PERSON><PERSON>"}, "RULE": {"RULE_LIST": {"TITLE": "Rule List"}, "CREATE_RULE": {"TITLE": "Create Rule", "FORM_CONTROLS": {"RULE_NAME": "Rule Name", "ADD": "Add", "WHEN": "When", "SCHEDULE": "Schedule", "OPERATOR": "Operators", "GREATER_THAN": "Greater than", "VALUE": "Value", "DUE_DATE": "Due date", "THEN": "Then", "ALERT": "<PERSON><PERSON>", "WORKFLOW": "Workflow", "CHOOSE_WORKFLOW": "Choose Workflow", "EMAIL": "Email", "TEXT": "Text", "TEAMS": "Teams", "EMAIL_TEXT": "Email & Text"}}}, "WORKFLOW": {"MAIN_WORKFLOW": {"TITLE": "Workflows", "CARDS": {"CREATED_AT": "Created at", "CREATED_BY": "Created by", "SCHEDULE_OVERDUE": "Schedule Overdue", "OBSERVATION_POINTS": "Observation Points", "AUDIT_OVERDUE": "Audit Overdue"}}, "CREATE_WORKFLOW": {"TITLE": "Create Workflow", "WORKFLOW_NAME": "Workflow name", "CARDS": {"EMAIL": "Email", "TEAMS": "Teams", "SMS": "SMS"}}}, "BUTTON": {"SUBSCRIBE": "Subscribe", "UNSUBSCRIBE": "Unsubscribe", "DOWNLOAD_PDF": "Download Dashboard as PDF", "KNOW_MORE": "Know more", "READ_MORE": "Read More", "ADD_LINKS": "Add Links", "ADD_KPI": "Add KPI's", "CANCEL": "Cancel", "NEXT": "Next", "SAVE": "Save", "SAVE_ACTION": "Save Action", "VIEW": "View", "YES": "Yes", "NO": "No", "ASSIGN": "Assign", "SUBMIT": "Submit", "BACK": "Back", "NIL": "NIL", "UPLOAD_PHOTO_VIDEO": "Upload Photo or Video", "CREATE_OBSERVATION": "Create Observation", "CREATE_FIELDWALK": "Create Field Walk", "CREATE_AUDIT": "Create <PERSON><PERSON>", "CREATE_SCHEDULE": "Create Schedule", "TRACKER": "Tracker", "ADD_FORM_CONFIGURATION": "Add Form Configuration", "REMOVE": "Remove", "ADD_MORE": "Add More", "ADD_RULE": "Add Rule", "CREATE_WORKFLOW": "Create Workflow", "ADD_QUESTION": "Add Question", "ADD_ANOTHER_QUESTION": "Add another Question", "APPLY": "Apply", "CLEAR": "Clear", "OK": "OK", "CLOSE": "Close", "ADD_CRAFTS": "Add Crafts", "CREATE_HAZARDS": "Create Hazards"}, "AUDIT_LIST": {"CORE_PRINCIPLE": "Core Principle", "AUDIT_TYPE": "Audit Type", "START_DATE": "Start Date", "END_DATE": "End Date", "OPERLEARNING": "Operational Learning"}, "TOASTER": {"PLEASE_FILL_REQ_FIELDS": "Please fill the required fields.", "SAVE_SUCCESS": "Saved Successfully!", "ACTION_ALREADY_CREATED": "Action already created", "ACTION_ITEM_CREATED_SUCCESSFULLY": "Action item created successfully", "USER_NOT_FOUND": "User not found", "PLEASE_FILL_DETAILS": "Please fill the details", "PLEASE_CHOOSE_CAT": "Please Choose Categories", "SOMETHING_WENT_WRONG": "Something went wrong", "FAILED": "Failed", "UPLOAD_SUCCESS": "Uploaded Successfully", "FAILED_TO_SAVE_OBS": "Failed to save observation", "PLEASE_CHOOSE_CORE_PRINCIPLE": "Please choose the core principle", "PLEASE_CHOOSE_PROCESS_TYPE": "Please choose the process type", "PLEASE_CHOOSE_OBERVATION": "Please choose the observation type", "NOT_ASSOCIATED_ROLE": "You are not associated with any role for this site", "RESP_SUCCESS": "Your response has been submitted successfully.", "RESP_FAILED": "Something Went Wrong while submitting your response.", "SUCCESS": "Success", "FAILURE": "Failure", "NO_CONFIG": "No configuration available.", "UPLOAD_REACHED": "You have reached the upload limit", "FILE_FAILED": "File upload failed", "IMG_UPLOAD_SUCCESS": "Images uploaded successfully", "IMG_UPLOAD_FAILED": "Images upload failed", "OP_SUCCESS": "Operation Successful", "ALREADY_EXISTS": "Already Exists", "NO_PROCESS": "There is no process associated with this Core Principle", "COMP_THANK_YOU": "Completed. Thank you.", "SOMETHING_WENT_WRONG_MAIL": "Something went wrong, Please come again from mail.", "DONT_ENTER_DUPLICATE_MAIL": "Don't Enter duplicate mail id.", "FILL_VALID_EMAIL": "Please fill valid email address", "EMAIL_SENT": "<PERSON><PERSON> sent successfully.", "DISAB_SUCCESS": "Disabled Successfully", "ACTION_ITEM_CREATE_FAILED": "Action item was notcreated due to invalid title length", "OBS_DELETE": "This observation has been deleted", "QUALITY_ASSESSMENT": "Quality Assessment Questionnaire has been send to the supplier", "INFO": "Info", "TURN_LANDSCAPE": "Please turn your device to landscape mode and then export as PDF.", "NO_DASHBOARD_CONFIG": "No dashboard configuration available", "ENABLE_CHECKLIST": "Enable atleast one field for checklist", "EMAIL_ALREADY_ADDED": "Don't Enter duplicate mail id.", "VALID_EMAIL": "Please fill valid email address"}, "REACT_TABLE": {"COL_SUMMARY": "Column Summary", "COL_SELECTION": "Column Selection", "LIST_COLS": "List of Columns", "EXPORT": "Export", "METRICS_BY_PERSON": "Metrics by Person", "CHECKLIST_METRICS": "Checklist Metrics", "ROWS_PER_PAGE": "Rows per page"}, "TABLE_COLS": {"Week": "Week", "ID": "ID", "Title": "Title", "Year": "Year", "Quarter": "Quarter", "Priority": "Priority", "Start Date": "Start Date", "End Date": "End Date", "Observation Type": "Observation Type", "Observation Status": "Observation Status", "Occurrence": "Occurrence", "Observer": "Observer", "Status": "Status", "Created Time": "Created Time", "Updated Time": "Updated Time", "Modified By": "Modified By", "Created On": "Created On", "Created By": "Created By", "Updated On": "Updated On", "Updated By": "Updated By", "Actions": "Actions", "Description": "Description", "Short Description": "Short Description", "Application Name": "Application Name", "Assigned To": "Assigned To", "Site": "Site", "Unit": "Unit", "Reporting Location": "Reporting Location", "Assign Date": "Assign Date", "Due Date": "Due Date", "Object Type": "Object Type", "Object Id": "Object Id", "Observation Date": "Observation Date", "Craft": "Craft", "Process": "Process", "Core Principle": "Core Principle", "Sub Process": "Sub Process", "Category": "Category", "Sub Category": "Sub Category", "Observation type": "Observation Type", "Question #": "Question #", "Created At": "Created At", "Date": "Date", "Name": "Name", "Role": "Role", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Nov": "Nov", "Dec": "Dec", "Total": "Total", "Complete": "Complete", "Point": "Point", "Assigned Date": "Assigned Date", "Shift": "Shift", "Observed By": "Observed By", "Month": "Month", "Location": "Location", "Audit Number": "Audit Number", "Operational Learning": "Did you identify any opportunities for Operational Learning?", "Describe the Operational Learning opportunities you found": "Describe the Operational Learning opportunities you found", "Art": "Art", "Problem": "Problem", "Cause": "Cause", "Solution": "Solution", "Measure/Activity": "Measure/Activity", "ALL": "All", "TRUE": "True", "FALSE": "False"}}