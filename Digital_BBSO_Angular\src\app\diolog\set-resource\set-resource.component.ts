import { Component, OnInit, Inject, <PERSON><PERSON><PERSON>, ChangeDetectorRef } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import { TranslateService } from "@ngx-translate/core";
import format from "date-fns/format";
import { ParentToChildService } from "src/app/broadcast/parent-to-child.service";
import { DAYCOUNT, FULLWEEKDAYS, LINKOPTIONS, OccurrenceData, Options, WEEKDAYS } from "src/app/modals/home.modal";
import { CommonService } from "src/app/services/common.service";
import { LanguageService } from "src/app/services/language.service";
import { environment } from "src/environments/environment";


@Component({
    selector: 'set-resource',
    templateUrl: './set-resource.component.html',
    styles: [`
    .example-form {
        min-width: 150px;
        max-width: 500px;
        width: 100%;
      }
      
      .example-full-width {
        width: 100%;
      }
    `]
})

export class SetResourceComponent implements OnInit {
   quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
   selectedQuarter: string | null = null;
  selectedDate: Date | null = null;
  radioOptionList: string[] = [
   'On day',
   //'On the'
];
  dayCount:Options[] = DAYCOUNT;
  fullWeekDays:Options[] = FULLWEEKDAYS;
  weeksDays:any[] = WEEKDAYS;
  selectedDays:any[]= [];
  eventData:any = {};
  currentDate:Date = new Date();
  startDate:string =  format(new Date(), "dd/LL/yyyy");
  endDate:string;
  selectedItem:string = 'On day';
  inputValue:number = 9;
  selectedDayCount:any = DAYCOUNT[0];
  selectedWeekDay:any = FULLWEEKDAYS[0];
  endDateData:Date;
  occurrenceData:OccurrenceData = new OccurrenceData({});
  labels ={}
  onQuarterSelect() {
   console.log(`Selected Quarter: ${this.selectedQuarter}`);
   this.selectedDate = null; // Clear previously selected date
 }
    constructor(
         public dialogRef: MatDialogRef<SetResourceComponent>,
         private parentToChildService:ParentToChildService,
         private commonService: CommonService,
         private translate: TranslateService,
         @Inject(MAT_DIALOG_DATA) public data,
         private languageService: LanguageService,
         private ngZone: NgZone,
         private changeDetectorRef: ChangeDetectorRef
        ){
            this.eventData = data;
            this.labels = {
               'setrecurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'setrecurrence'] || 'setrecurrence',
               'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
               'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
               'remove': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'remove'] || 'remove',
               'weekdays': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'weekdays'] || 'weekdays',
               'occursevery': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occursevery'] || 'occursevery',
               'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
               'monday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monday'] || 'monday',
               'tuesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tuesday'] || 'tuesday',
               'wednesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'wednesday'] || 'wednesday',
               'thursday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'thursday'] || 'thursday',
               'friday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'friday'] || 'friday',
               'saturday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'saturday'] || 'saturday',
               'sunday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sunday'] || 'sunday',
               'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
               'onday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onday'] || 'onday',
               'on': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'on'] || 'on',
               'starting': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'starting'] || 'starting',
               'until': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'until'] || 'until',
               'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
               'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
               'and': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'and'] || 'and',
               'noQuartersFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'noQuartersFound'] || 'noQuartersFound',
               'searchQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'searchQuarter'] || 'searchQuarter',
               'selectQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'selectQuarter'] || 'selectQuarter',
               'selectDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'selectDate'] || 'selectDate',
               'chooseQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseQuarter'] || 'chooseQuarter',
            }
            this.radioOptionList = [
               this.labels['onday'],
            ];
        }

    ngOnInit(): void {
        //this.selectedDays.push(this.weeksDays[0].fullName);
        var _this = this
        _this.ngZone.run(() => {
         console.log('Out subscribe', _this.labels)
         _this.languageService.language$.subscribe((language) => {
           console.log('obs labels language', _this.commonService.selectedLanguage)
           console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
           this.labels = {
            'setrecurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'setrecurrence'] || 'setrecurrence',
            'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
            'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
            'remove': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'remove'] || 'remove',
            'weekdays': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'weekdays'] || 'weekdays',
            'occursevery': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occursevery'] || 'occursevery',
            'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
            'monday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monday'] || 'monday',
            'tuesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tuesday'] || 'tuesday',
            'wednesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'wednesday'] || 'wednesday',
            'thursday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'thursday'] || 'thursday',
            'friday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'friday'] || 'friday',
            'saturday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'saturday'] || 'saturday',
            'sunday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sunday'] || 'sunday',
            'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
            'onday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onday'] || 'onday',
            'on': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'on'] || 'on',
            'starting': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'starting'] || 'starting',
            'until': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'until'] || 'until',
            'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
            'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
            'and': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'and'] || 'and',
            'noQuartersFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'noQuartersFound'] || 'noQuartersFound',
               'searchQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'searchQuarter'] || 'searchQuarter',
               'selectQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'selectQuarter'] || 'selectQuarter',
               'selectDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'selectDate'] || 'selectDate',
               'chooseQuarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseQuarter'] || 'chooseQuarter',
         }
            this.radioOptionList = [
               this.labels['onday'],
            ];
           console.log('commonService label', _this.labels)
           _this.changeDetectorRef.detectChanges();
         })
         _this.changeDetectorRef.detectChanges();
       })
    }

    cancel():void{
        this.dialogRef.close(this.occurrenceData);
    }

    selectedUnSelectedDay(day):void{
       if(!this.selectedDays.includes(day.fullName)){
          this.selectedDays.push(day.fullName);
          return;
       }
       this.selectedDays = this.selectedDays.filter((item =>{
          return item != day.fullName
       }))
    }

    getSelectedDate(date:Date, isStartDate?:boolean):void{
       this.startDate = isStartDate ? format(date, "dd/LL/yyyy") :  this.startDate;
       this.endDate = !isStartDate ? format(date, "dd/LL/yyyy") :  this.endDate;
    }

    getSelectedRadio(event:any):void {
       this.selectedItem = event;
    }

    getInputValue(value:number):void {
      this.inputValue = value;
    }

    selectedDayCountOption(event):void {
       this.selectedDayCount = event;
    }

    selectedWeekDayOption(event):void {
      this.selectedWeekDay = event;
   }

   removeEndDate():void{
      this.endDateData = null;
      this.endDate = '';
   }
   isDayBetweenDates(day, startDateString, endDateString) {
      // Convert the input strings to Date objects
    
      const [startDay, startMonth, startYear] = startDateString.split('/');
      const [endDay, endMonth, endYear] = endDateString.split('/');
      const startDate = new Date(`${startYear}-${startMonth}-${startDay}`);
      const endDate = new Date(`${endYear}-${endMonth}-${endDay}`);
    
  
      // Check if the dates are in the correct order
      if (startDate > endDate) {
          return 'Invalid date range';
      }
  
      // Check if the day exists within the start and end dates
      let currentDate = new Date(startDate);
      while (currentDate <= endDate) {
          if (currentDate.getDate() === day) {
              return true;
          }
          currentDate.setDate(currentDate.getDate() + 1); // Increment the date by 1 day
      }
  
      return false;
  }
  
   save():void {
      this.occurrenceData.startDate = this.startDate;
      this.occurrenceData.endDate = this.endDate;
      this.occurrenceData.quarter = this.selectedQuarter
      this.occurrenceData.days = this.selectedDays;
      this.occurrenceData.dayCount = this.inputValue;
      this.occurrenceData.day = this.selectedWeekDay.name;
      this.occurrenceData.datType = this.selectedDayCount.value;
      this.occurrenceData.view = this.eventData.event.name;
      this.occurrenceData.selectedItem = this.selectedItem;
      this.parentToChildService.broadcastOccurrenceData(this.occurrenceData);
      if(this.eventData.event.name == 'Weekly'){
       if(this.selectedDays.length > 0 && this.endDate){
         this.cancel();
       }else{
         this.commonService.triggerToast({
            type: 'error',
            title: '',
            msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'],
          });
       }
      }
      if(this.eventData.event.name == "Daily"){
         if(this.startDate && this.endDate){
            this.cancel();
         }else{
            this.commonService.triggerToast({
               type: 'error',
               title: '',
               msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'],
             });
         }
        
      }
      if(this.eventData.event.name == "Monthly"){
         console.log(this.startDate,this.endDate)
         const result = this.isDayBetweenDates(this.inputValue, this.startDate, this.endDate);
         console.log(result);
         console.log('this.inputValue',this.inputValue)
         if(this.startDate && this.endDate && result){
            this.cancel();
         }else{
            this.commonService.triggerToast({
               type: 'error',
               title: '',
               msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'],
             });
         }
      }
      if(this.eventData.event.name == 'Quarterly'){
         if(this.selectedQuarter && this.startDate){
           this.cancel();
         }else{
           this.commonService.triggerToast({
              type: 'error',
              title: '',
              msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'],
            });
         }
        }

     
   }
}
