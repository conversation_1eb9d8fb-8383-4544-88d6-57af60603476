import { ChangeDetectorRef, Component, Inject, NgZone, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, FormControl } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonService } from 'src/app/services/common.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { environment } from 'src/environments/environment';
import { EventEmitter, Output } from '@angular/core';


/**
 * @title Dialog with header, scrollable content and actions
 */

@Component({
  selector: 'column-dialog',
  templateUrl: 'column-dialog.html',
})
export class ColumnDialog implements OnInit {

  checkColumns:any = [];
  
  form: FormGroup;
  @Output() dataEmitter = new EventEmitter<any>();
  labels = {}

  
  constructor(private fb: FormBuilder, public dialogRef: MatDialogRef<ColumnDialog>,@Inject(MAT_DIALOG_DATA) data, public translationService: TranslationService, private commonService: CommonService, private languageService: LanguageService, private changeDetector: ChangeDetectorRef, private ngZone: NgZone) {

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'reacttableListcols': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableListcols'] || 'reacttableListcols',
        'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        'id': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'id'] || 'id',
        'tablecolsObservationstatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObservationstatus'] || 'tablecolsObservationstatus',
        'tablecolsCreatedtime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsCreatedtime'] || 'tablecolsCreatedtime',
        'tablecolsUpdatedtime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsUpdatedtime'] || 'tablecolsUpdatedtime',
        'tablecolsModifiedby': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsModifiedby'] || 'tablecolsModifiedby',
        'createdOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdOn'] || 'createdOn',
        'createdBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdBy'] || 'createdBy',
        'updatedOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedOn'] || 'updatedOn',
        'updatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedBy'] || 'updatedBy',
        'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
        'tablecolsApplicationname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsApplicationname'] || 'tablecolsApplicationname',
        'tablecolsAssignedto': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssignedto'] || 'tablecolsAssignedto',
        'tablecolsReportinglocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsReportinglocation'] || 'tablecolsReportinglocation',
        'tablecolsAssigndate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssigndate'] || 'tablecolsAssigndate',
        'tablecolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsDuedate'] || 'tablecolsDuedate',
        'tablecolsObjecttype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObjecttype'] || 'tablecolsObjecttype',
        'tablecolsObjectid': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObjectid'] || 'tablecolsObjectid',
        'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
        'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
        'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
        'tablecolsCreatedat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsCreatedat'] || 'tablecolsCreatedat',
        'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'] || 'tablecolsName',
        'tablecolsRole': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsRole'] || 'tablecolsRole',
        'tablecolsJan': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJan'] || 'tablecolsJan',
        'tablecolsFeb': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsFeb'] || 'tablecolsFeb',
        'tablecolsMar': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsMar'] || 'tablecolsMar',
        'tablecolsApr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsApr'] || 'tablecolsApr',
        'tablecolsMay': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsMay'] || 'tablecolsMay',
        'tablecolsJun': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJun'] || 'tablecolsJun',
        'tablecolsJul': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJul'] || 'tablecolsJul',
        'tablecolsAug': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAug'] || 'tablecolsAug',
        'tablecolsSep': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsSep'] || 'tablecolsSep',
        'tablecolsOct': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsOct'] || 'tablecolsOct',
        'tablecolsNov': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsNov'] || 'tablecolsNov',
        'tablecolsDec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsDec'] || 'tablecolsDec',
        'tablecolsTotal': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsTotal'] || 'tablecolsTotal',
        'tablecolsComplete': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsComplete'] || 'tablecolsComplete',
        'tablecolsPoint': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsPoint'] || 'tablecolsPoint',
        'tablecolsAssigneddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssigneddate'] || 'tablecolsAssigneddate',
        'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
        'tablecolsAuditnumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAuditnumber'] || 'tablecolsAuditnumber',
        'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
        'status': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'status'] || 'status',
        'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
        'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
        'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
        'location': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'location'] || 'location',
        'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
        'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
        'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
        'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
        'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
        'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
        'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
        'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'occurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occurrence'] || 'occurrence',
        'observer': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observer'] || 'observer',
        'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
        'tablecolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsQuestion'] || 'tablecolsQuestion',
        'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'art': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'art'] || 'art',
        'cause': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cause'] || 'cause',
        'problem': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'problem'] || 'problem',
        'solution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'solution'] || 'solution',
        'measureActivity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measureActivity'] || 'measureActivity',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'workReleaseNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workReleaseNumber'] || 'workReleaseNumber',
      }
    }

    this.form = this.fb.group({
      columns: new FormArray([])
    });
    data['allColumns'].forEach((col:any) => {
      this.addCheckboxes(col.activeFlag)
      this.checkColumns.push(col);
    });
    // data['availableColumns'].forEach((aCol) => {
    //   this.addCheckboxes(false)
    //   this.checkColumns.push(aCol);
    // });
    // this.addCheckboxes();

  }
  
  ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'reacttableListcols': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableListcols'] || 'reacttableListcols',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
          'id': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'id'] || 'id',
          'tablecolsObservationstatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObservationstatus'] || 'tablecolsObservationstatus',
          'tablecolsCreatedtime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsCreatedtime'] || 'tablecolsCreatedtime',
          'tablecolsUpdatedtime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsUpdatedtime'] || 'tablecolsUpdatedtime',
          'tablecolsModifiedby': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsModifiedby'] || 'tablecolsModifiedby',
          'createdOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdOn'] || 'createdOn',
          'createdBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdBy'] || 'createdBy',
          'updatedOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedOn'] || 'updatedOn',
          'updatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedBy'] || 'updatedBy',
          'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
          'tablecolsApplicationname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsApplicationname'] || 'tablecolsApplicationname',
          'tablecolsAssignedto': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssignedto'] || 'tablecolsAssignedto',
          'tablecolsReportinglocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsReportinglocation'] || 'tablecolsReportinglocation',
          'tablecolsAssigndate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssigndate'] || 'tablecolsAssigndate',
          'tablecolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsDuedate'] || 'tablecolsDuedate',
          'tablecolsObjecttype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObjecttype'] || 'tablecolsObjecttype',
          'tablecolsObjectid': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsObjectid'] || 'tablecolsObjectid',
          'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
          'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
          'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
          'tablecolsCreatedat': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsCreatedat'] || 'tablecolsCreatedat',
          'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'] || 'tablecolsName',
          'tablecolsRole': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsRole'] || 'tablecolsRole',
          'tablecolsJan': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJan'] || 'tablecolsJan',
          'tablecolsFeb': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsFeb'] || 'tablecolsFeb',
          'tablecolsMar': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsMar'] || 'tablecolsMar',
          'tablecolsApr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsApr'] || 'tablecolsApr',
          'tablecolsMay': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsMay'] || 'tablecolsMay',
          'tablecolsJun': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJun'] || 'tablecolsJun',
          'tablecolsJul': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsJul'] || 'tablecolsJul',
          'tablecolsAug': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAug'] || 'tablecolsAug',
          'tablecolsSep': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsSep'] || 'tablecolsSep',
          'tablecolsOct': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsOct'] || 'tablecolsOct',
          'tablecolsNov': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsNov'] || 'tablecolsNov',
          'tablecolsDec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsDec'] || 'tablecolsDec',
          'tablecolsTotal': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsTotal'] || 'tablecolsTotal',
          'tablecolsComplete': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsComplete'] || 'tablecolsComplete',
          'tablecolsPoint': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsPoint'] || 'tablecolsPoint',
          'tablecolsAssigneddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAssigneddate'] || 'tablecolsAssigneddate',
          'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
          'tablecolsAuditnumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAuditnumber'] || 'tablecolsAuditnumber',
          'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
          'status': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'status'] || 'status',
          'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
          'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
          'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
          'location': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'location'] || 'location',
          'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
          'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
          'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
          'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
          'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
          'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
          'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
          'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
          'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
          'occurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occurrence'] || 'occurrence',
          'observer': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observer'] || 'observer',
          'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
          'tablecolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsQuestion'] || 'tablecolsQuestion',
          'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
          'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
  }

  private addCheckboxes(val) {
    this.columnFormArray.push(new FormControl(val));
  }
  get columnFormArray() {
    return this.form.get('columns') as FormArray;
  }

  updateValue(event, index) {
    this.form.value.columns[index] = event;
    this.submit();
  }
  submit() {
    var _this = this;
    this.checkColumns.forEach(function (value, i) {
      _this.checkColumns[i].activeFlag=_this.form.value.columns[i];
      console.log(_this.form.value.columns[i])
    });
    // const selectedColumnsIds = this.form.value.columns
    //   .map((checked, i) => checked ? this.checkColumns[i] : null)
    //   .filter(v => v !== null);
    // this.dialogRef.close(_this.checkColumns);
    this.dataEmitter.emit(this.checkColumns);
  }

  cancel():void {
    this.dialogRef.close();
  }


}
