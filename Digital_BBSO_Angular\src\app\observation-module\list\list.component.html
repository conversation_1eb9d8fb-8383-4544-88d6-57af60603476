<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-sectionscreen">
    <div fxFlex="100">

        <div class="ovservation-list-section configuration-section">
            <div class="audit-plan-unit-section">
                <commom-label labelText="{{ labels['observationlistList'] }}" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>

            </div>
            <!-- <div class="marginTop">
                <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                    <div fxLayout="row auto" fxLayoutAlign="start center">
                        <div class="treat-md">
                            <span class="semi-bold">{{ 'CONFIGURATION.UNIT_SITE.SITE' | translate }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()"
                                    placeholder="{{ 'COMMONFILTER.CHOOSE_SITE' | translate }}"
                                    [formControl]="siteControl" disableOptionCentering>
                                    <mat-select-filter [placeholder]="'COMMONFILTER.SEARCH' | translate"
                                        [displayMember]="'description'" [array]="siteList"
                                        (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>

            </div> -->

            <br>
            <mat-tab-group class="outerbox" mat-stretch-tabs *ngIf="ifSite"
                [(selectedIndex)]="selectedIndexBinding" #tabGroup (selectedTabChange)="selectTab($event)">

                <mat-tab *ngIf="configurationList && configurationList.observation" label="{{ labels['observation'] }}">
                    <div class="tab-height" *ngIf="selectedTab == 'Observation'">
                        <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                            <app-observation-list fxFlex="100" listType="Observation"></app-observation-list>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.fieldWalk" label="{{ labels['fieldWalk'] }}">
                    <div class="tab-height" *ngIf="selectedTab == 'FieldWalk'">
                        <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                            <app-fieldwalk-list fxFlex="100" listType="Field Walk"></app-fieldwalk-list>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.auditMenu" label="{{ labels['audit'] }}">
                    <div class="tab-height" *ngIf="selectedTab == 'Audit'">
                        <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                            <app-audit-list fxFlex="100"></app-audit-list>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.hazards" label="{{ labels['cardsHazards']}}">
                    <div class="tab-height" *ngIf="selectedTab == 'Hazards'">
                        <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                            <app-observation-list fxFlex="100" listType="Hazards"></app-observation-list>
                        </div>
                    </div>
                </mat-tab>
            </mat-tab-group>

        </div>
    </div>
</div>
