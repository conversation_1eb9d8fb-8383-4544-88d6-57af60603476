import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, Subject, Subscription, asyncScheduler, debounceTime, map, startWith, switchMap } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';

import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { OccurrenceData } from 'src/app/modals/home.modal';
import { SetResourceComponent } from 'src/app/diolog/set-resource/set-resource.component';
import { MatDialog } from '@angular/material/dialog';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';

import { MatDateFormats } from '@angular/material/core';
import { name } from '@azure/msal-angular/packageMetadata';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};





@Component({
  selector: 'app-create-schedule',
  templateUrl: './create-schedule.component.html',
  styleUrls: ['./create-schedule.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},]
})
export class CreateScheduleComponent implements OnInit {
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  siteControl: FormControl = new FormControl("");
  selectionControl = new FormControl('user'); // Default selection type
  groupList: Array<any> = []; // List of groups
  filteredGroupList: Array<any> = [];
  
  siteList = [];
  filteredSiteList = [];


  DateControl: FormControl = new FormControl(new Date())


  processRadio: FormControl = new FormControl("");
  radioForm: boolean = false

  createSchedule: FormGroup;
  auditForm: FormGroup;
  initialDataFlag = 0;
  process: any;
  corePrincipleList: any[];
  filteredCorePrincipleList: any[];
  corePrincipleControl = new FormControl();

  processList: any[];
  filteredProcessList: any[];
  processControl = new FormControl();
  observationType = new FormControl();
  labels = {}

  subProcessList: any[];
  filteredSubProcessList: any[];
  categoryList: any[];
  filteredCategoryList: any[];

  SupplierList: any[];
  filteredSupplierList: any[];

  regionList: any[];
  filteredRegionList: any[];
  filteredCountryList: any[];
  countryList: any[];
  businessLineList: any[];
  filteredBusinessLineList: any[];
  filteredQuarterList: any;
  quarterList: any;
  filteredYearList: any;
  yearList: any;
  filteredPriorityList: any;
  priorityList: any;
  editData: any;
  selectedSite: any;
  selectedCountry: any;
  selectedRegion: any;
  selectedSupplier: any;
  actionFlag: any = "";
  loaderFlag: boolean;

  
  attendeeList = [];
  filteredattendeeList = this.attendeeList.slice();

  procurementList = [];
  filteredprocurementList = this.procurementList.slice();

  coAuditorList = [];
  filteredcoAuditorList = this.coAuditorList.slice();

  selectedAuditType: any;

  // Internal Audit
  // Supplier Audit
  yesOrNoList = [{name:"Yes"},{name:"No"}]

  reportingLocationControl: FormControl = new FormControl("");
  reportingLocationList = [];
  filteredReportingLocationList = this.reportingLocationList.slice();

  
  behalfList = [];
  filteredBehalfList = this.behalfList.slice();
  userAccessMenu: any;
  ScheduleCreateAudit: any;
  ScheduleCreateFieldWalk: any;
  ScheduleCreateObservation: any;
  ScheduleTracker: any;
  fl_searchVal: any;
  fl_searchVal3: any;
  firstElement: any;
  firstElement1: any;

  checkList= [];
  currentsideId: any;
  scheduleProcessList = [];
  filteredScheduleProcessList = [];
  configDetail: any;
  OccurrenceOptions = [
    {
      name: "One Time",
      value: "1",
      key:"onetime"
    },
    {
      name: "Daily",
      value: "2",
      key:"daily"
    },
    {
      name: "Weekly",
      value: "3",
      key:"weekly"
    },
    {
      name: "Monthly",
      value: "4",
      key:"monthly"
    },
    {
      name: "Quarterly",
      value: "5",
      key:"quarterly"
    }
  ];
  dueDate:Date = new Date();
  eventData:any = this.OccurrenceOptions[0];
  occurrenceData:OccurrenceData = new OccurrenceData({});
  occurrenceDataSubscription:Subscription;
  isView: boolean;
  loadForm: boolean =false;
  
  searchSubject = new Subject<string>();
  searchSubject1 = new Subject<string>();
  searchSubject2 = new Subject<string>();
  searchSubject3 = new Subject<string>();
  selectedProcess: any;
  
  constructor(private changeDetector: ChangeDetectorRef,  private cdRef: ChangeDetectorRef, public dialog: MatDialog,  private dataService: DataService, private router: Router, private commonService: CommonService, private fb: FormBuilder,private translate: TranslateService,private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string,private cd: ChangeDetectorRef, public translationService: TranslationService,
    private languageService: LanguageService, private ngZone: NgZone
  ) {

      this.labels = {
        'createscheduleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createscheduleTitle'] || 'createscheduleTitle',
        'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
        'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'createactionDescmessage': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionDescmessage'] || 'createactionDescmessage',
        'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'observer': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observer'] || 'observer',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'occurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occurrence'] || 'occurrence',
        'occursevery': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occursevery'] || 'occursevery',
        'sunday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sunday'] || 'sunday',
        'monday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monday'] || 'monday',
        'tuesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tuesday'] || 'tuesday',
        'wednesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'wednesday'] || 'wednesday',
        'thursday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'thursday'] || 'thursday',
        'friday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'friday'] || 'friday',
        'saturday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'saturday'] || 'saturday',
        'and': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'and'] || 'and',
        'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
        'onday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onday'] || 'onday',
        'on': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'on'] || 'on',
        'starting': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'starting'] || 'starting',
        'until': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'until'] || 'until',
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
        'formcontrolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDuedate'] || 'formcontrolsDuedate',
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
        'commonfilterChooseaudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseaudittype'] || 'commonfilterChooseaudittype',
        'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
        'tablecolsAuditnumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAuditnumber'] || 'tablecolsAuditnumber',
        'formcontrolsFirsttimeaudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFirsttimeaudit'] || 'formcontrolsFirsttimeaudit',
        'commonfilterChoose': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoose'] || 'commonfilterChoose',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
        'formcontrolsLegalentity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLegalentity'] || 'formcontrolsLegalentity',
        'formcontrolsSitecertificatenumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSitecertificatenumber'] || 'formcontrolsSitecertificatenumber',
        'formcontrolsSuppliernumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSuppliernumber'] || 'formcontrolsSuppliernumber',
        'commonfilterChoosesupplier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesupplier'] || 'commonfilterChoosesupplier',
        'formcontrolsSuppliername': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSuppliername'] || 'formcontrolsSuppliername',
        'formcontrolsEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEmail'] || 'formcontrolsEmail',
        'formcontrolsRegion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsRegion'] || 'formcontrolsRegion',
        'formcontrolsCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCountry'] || 'formcontrolsCountry',
        'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
        'commonfilterChooseyear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseyear'] || 'commonfilterChooseyear',
        'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
        'commonfilterChoosequarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosequarter'] || 'commonfilterChoosequarter',
        'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
        'commonfilterChoosepriority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosepriority'] || 'commonfilterChoosepriority',
        'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
        'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
        'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
        'formcontrolsLeadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLeadauditor'] || 'formcontrolsLeadauditor',
        'formcontrolsChooseleadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChooseleadauditor'] || 'formcontrolsChooseleadauditor',
        'formcontrolsProcurementppr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsProcurementppr'] || 'formcontrolsProcurementppr',
        'formcontrolsSitemanager': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSitemanager'] || 'formcontrolsSitemanager',
        'formcontrolsCoauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCoauditor'] || 'formcontrolsCoauditor',
        'commonfilterChoosecoauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoauditor'] || 'commonfilterChoosecoauditor',
        'commonfilterChooseppr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseppr'] || 'commonfilterChooseppr',
        'formcontrolsSupplieremail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSupplieremail'] || 'formcontrolsSupplieremail',
        'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
      'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',  
          'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
          'quarterly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarterly'] || 'quarterly',
      }

   var _this = this;
   _this.setDateFormat();
   _this.commonService.auditNumberHistory()
    _this.searchSubject.pipe(
      debounceTime(1000), // Wait for 300ms pause in events
      switchMap(value => {
        _this.loaderFlag = true;
        _this.dataService.postData({ SupplierNumber: _this.fl_searchVal }, _this.dataService.NODE_API + "/api/service/listSupplier").subscribe((resData: any) => {
          _this.loaderFlag = false;
          _this.SupplierList = resData["data"]["list" + _this.commonService.configuration["typeSupplier"]]["items"]
          _this.filteredSupplierList = _this.SupplierList.slice();
          _this.cd.detectChanges();_this.loaderFlag = false;
        })
        return [];
      })
    ).subscribe(results => {
      _this.cd.detectChanges();
    });
    _this.searchSubject3.pipe(
      debounceTime(1000), // Wait for 300ms pause in events
      switchMap(value => {
        _this.loaderFlag = true;
        _this.dataService.postData({searchUser:_this.fl_searchVal3}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
  console.log('resData',resData)
      _this.procurementList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.procurementList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredprocurementList = _this.procurementList.slice();
      _this.cd.detectChanges()
      _this.cdRef.detectChanges();
            if (_this.procurementList.length > 0) {
              _this.firstElement = _this.procurementList[0];
            }
            _this.loaderFlag = false;
   
        })
        return [];
      })
    ).subscribe(results => {
      _this.cd.detectChanges();
    });

    _this.searchSubject2.pipe(
      debounceTime(1000), // Wait for 300ms pause in events
      switchMap(value => {
        _this.loaderFlag = true;
        _this.dataService.postData({ searchUser: _this.fl_searchVal }, _this.dataService.NODE_API + "/api/service/listAllUser")
          .subscribe((resData: any) => {
            console.log('resData', resData);
            _this.behalfList = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
            _this.behalfList.forEach((element) => {
              element.name = element.firstName + ' ' + element.lastName;
            });
            _this.filteredBehalfList = _this.behalfList.slice();
            _this.cdRef.detectChanges();
            if (_this.behalfList.length > 0) {
              _this.firstElement = _this.behalfList[0];
            }
            _this.loaderFlag = false;
          });
        return [];
      })
    ).subscribe(results => {
      _this.cd.detectChanges();
    });
  
    _this.searchSubject1.pipe(
      debounceTime(1000), // Wait for 300ms pause in events
      switchMap(value => {
        _this.loaderFlag = true;
        _this.dataService.postData({ searchUser: _this.fl_searchVal }, _this.dataService.NODE_API + "/api/service/listAllUser").
          subscribe((resData: any) => {
            console.log('resData', resData)
            // _this.loaderFlag = false;
            if(_this.editData){
        _this.corePrincipleControl.setValue(_this.editData.refOFWACorePrinciple.externalId);
        _this.processControl.setValue(_this.editData.refOFWAProcess.externalId)
        _this.observationType.setValue(_this.editData.refOFWAObservationType.externalId)
            _this.initEdit();
            }
           
            _this.attendeeList = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
            _this.attendeeList.forEach((element) => {
              element.name = element.firstName + ' ' + element.lastName
            })
            _this.filteredattendeeList = _this.attendeeList.slice();
            _this.cd.detectChanges();
            if (_this.attendeeList.length > 0) {
              _this.firstElement1 = _this.attendeeList[0];
            }
            _this.loaderFlag = false;
          })
        return [];
      })
    ).subscribe(results => {
      _this.cd.detectChanges();
    });
    // if (_this.commonService.siteList.length > 0) {
    //   _this.siteList = _this.commonService.siteList;
    //   _this.filteredSiteList = _this.siteList.slice();
    // }
    // _this.commonService.filterListSubject.subscribe((fiterType: any) => {
    //   if (fiterType) {
    //     if (fiterType == "Site") {
    //       _this.siteList = _this.commonService.siteList;
    //       _this.filteredSiteList = _this.siteList.slice();
    //     }
    //   }
    // })
  }
  selectedOption(event):void {
    this.dueDate = null;
    if(event.name == 'Does not repeat'){
        this.dueDate = this.dueDate ? this.dueDate : new Date();
    }
    this.occurrenceData = new OccurrenceData({});
    this.eventData = event;
    if(event.value != '1'){
        this.openActionDilog(event)
    }
}

  ngOnDestroy():void {
    this.occurrenceDataSubscription && this.occurrenceDataSubscription.unsubscribe();
}
  openActionDilog(event):void{
    console.log(event)
  const dialogRef =    this.dialog.open(SetResourceComponent, {width:'600px', minWidth: '600px !important', panelClass: 'set-resource', data: {event}});
   dialogRef.afterClosed().subscribe(result => {
    console.log(result)
    this.occurrenceData  = result
    console.log(this.occurrenceData)

    this.createSchedule.get("datetime").setValue(this.occurrenceData.startDate)
    this.createSchedule.get("duetime").setValue(this.occurrenceData.endDate)
    this.cdRef.detectChanges();
  })
  }


  ngOnInit(): void {

  
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'createscheduleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createscheduleTitle'] || 'createscheduleTitle',
          'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
          'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'createactionDescmessage': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionDescmessage'] || 'createactionDescmessage',
          'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
          'observer': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observer'] || 'observer',
          'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
          'occurrence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occurrence'] || 'occurrence',
          'occursevery': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'occursevery'] || 'occursevery',
          'sunday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sunday'] || 'sunday',
          'monday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monday'] || 'monday',
          'tuesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tuesday'] || 'tuesday',
          'wednesday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'wednesday'] || 'wednesday',
          'thursday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'thursday'] || 'thursday',
          'friday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'friday'] || 'friday',
          'saturday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'saturday'] || 'saturday',
          'and': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'and'] || 'and',
          'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
          'onday': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onday'] || 'onday',
          'on': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'on'] || 'on',
          'starting': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'starting'] || 'starting',
          'until': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'until'] || 'until',
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
          'formcontrolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDuedate'] || 'formcontrolsDuedate',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
          'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
          'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
          'commonfilterChooseaudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseaudittype'] || 'commonfilterChooseaudittype',
          'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
          'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
          'tablecolsAuditnumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsAuditnumber'] || 'tablecolsAuditnumber',
          'formcontrolsFirsttimeaudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFirsttimeaudit'] || 'formcontrolsFirsttimeaudit',
          'commonfilterChoose': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoose'] || 'commonfilterChoose',
          'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
          'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
          'formcontrolsLegalentity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLegalentity'] || 'formcontrolsLegalentity',
          'formcontrolsSitecertificatenumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSitecertificatenumber'] || 'formcontrolsSitecertificatenumber',
          'formcontrolsSuppliernumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSuppliernumber'] || 'formcontrolsSuppliernumber',
          'commonfilterChoosesupplier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesupplier'] || 'commonfilterChoosesupplier',
          'formcontrolsSuppliername': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSuppliername'] || 'formcontrolsSuppliername',
          'formcontrolsEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEmail'] || 'formcontrolsEmail',
          'formcontrolsRegion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsRegion'] || 'formcontrolsRegion',
          'formcontrolsCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCountry'] || 'formcontrolsCountry',
          'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
          'commonfilterChooseyear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseyear'] || 'commonfilterChooseyear',
          'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
          'commonfilterChoosequarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosequarter'] || 'commonfilterChoosequarter',
          'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
          'commonfilterChoosepriority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosepriority'] || 'commonfilterChoosepriority',
          'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
        'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
        'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
          'formcontrolsLeadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsLeadauditor'] || 'formcontrolsLeadauditor',
          'formcontrolsChooseleadauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChooseleadauditor'] || 'formcontrolsChooseleadauditor',
          'formcontrolsProcurementppr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsProcurementppr'] || 'formcontrolsProcurementppr',
          'formcontrolsSitemanager': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSitemanager'] || 'formcontrolsSitemanager',
          'formcontrolsCoauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCoauditor'] || 'formcontrolsCoauditor',
          'commonfilterChoosecoauditor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoauditor'] || 'commonfilterChoosecoauditor',
          'commonfilterChooseppr': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseppr'] || 'commonfilterChooseppr',
          'formcontrolsSupplieremail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSupplieremail'] || 'formcontrolsSupplieremail',
        'day': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'day'] || 'day',
          'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',  
          'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
          'quarterly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarterly'] || 'quarterly',
          
        }
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })
    _this.dataService.postData({ "name": "EvidenceChecklist" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      var checkList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      
      // if(_this.editData &&_this.editData.evidenceChecklist ){
        _.each(checkList,function(eData){
          var myFlag = false;
          if(_this.editData && _this.editData.evidenceChecklist && _this.editData.evidenceChecklist.find(e=> e == eData.value)){
            myFlag = true
          }
          _this.checkList.push({
            name:eData.value,
            isSelected:_this.fb.control(myFlag)
          })
        })
      // }
      _this.cd.detectChanges()
    
    });
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.attendeeList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();
   
      _this.attendeeList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredattendeeList = _this.attendeeList.slice();
      _this.procurementList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.procurementList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredprocurementList = _this.procurementList.slice();
      _this.coAuditorList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.coAuditorList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredcoAuditorList = _this.coAuditorList.slice();
      if(history.state.data){
        _this.editData = history.state.data;
        console.log('_this.editData',_this.editData);
        console.log(_this.editData)
        if(history.state.data && _this.editData.procurementPPRAzureDirectoryUserID){
          
          _this.fl_searchVal3=_this.editData.procurementPPRAzureDirectoryUserID["externalId"]
          _this.searchSubject3.next(_this.editData.procurementPPRAzureDirectoryUserID["externalId"]);
        _this.auditForm.get("procurement").setValue(_this.editData.procurementPPRAzureDirectoryUserID["externalId"]);

        }

        if(history.state.data && _this.editData.performerAzureDirectoryUserID){
          
          _this.fl_searchVal=_this.editData.performerAzureDirectoryUserID["externalId"]
          _this.searchSubject1.next(_this.editData.performerAzureDirectoryUserID["externalId"]);
          _this.auditForm.get("leadAuditor").setValue(_this.editData.performerAzureDirectoryUserID["externalId"]);
        }

        if(history.state.data && _this.editData.coAuditor){
          _this.auditForm.get("coAuditor").setValue(_this.editData.coAuditor["externalId"]);
        }
        // _this.initEdit();
      }

    })
  

    if (history.state.data) {
      _this.editData = history.state.data;
      _this.actionFlag = history.state.action;
      if (_this.actionFlag == "View") {
        
        this.corePrincipleControl.disable();
        this.processControl.disable();
      }

      console.log('_this.editData',_this.editData);
      console.log('_this.actionFlag ',_this.actionFlag);
    }
 

    // this.processRadio.valueChanges.subscribe(data => {
    //   console.log('data',data);
    //   // this.processSelect(this.siteControl.value);
    //   if (data == "Audit") {
    //     this.radioForm = true;
    //   //  this.processSelect(this.siteControl.value);
    //     this.filterInit("Region");
    //     this.filterInit("Country");
    //     this.filterInit("BusinessLine");

    //     _this.dataService.postData({ "name": "Priority" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
    //       _this.priorityList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
    //       _this.filteredPriorityList = _this.priorityList.slice();
    //     })
    //     _this.dataService.postData({ "name": "Year" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
    //       _this.yearList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
    //       _this.yearList = _this.yearList.sort((a, b) => parseInt(b) - parseInt(a));
    //       _this.filteredYearList = _this.yearList.slice();
    //     })
    //     _this.dataService.postData({ "name": "Quarter" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
    //       _this.quarterList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
    //       _this.filteredQuarterList = _this.quarterList.slice();
    //     })

    //     _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listSupplier").subscribe((resData: any) => {

    //       _this.SupplierList = resData["data"]["list" + _this.commonService.configuration["typeSupplier"]]["items"]
    //       _this.filteredSupplierList = _this.SupplierList.slice();
    //       if (_this.editData) {
    //         console.log(_this.editData)
    //         _this.selectedSupplier = _this.SupplierList.find(e => {
    //           return (e.externalId == _this.editData.refSupplier?.externalId);
    //         })
    //         console.log( _this.selectedSupplier)
    //       }

    //     })

    //   }
    //   else {
    //     this.radioForm = false;
    //   }
    // })

    
    this.auditForm = this.fb.group({
      title: ['', Validators.required],
      email: [''],
      auditNumber: [''],
      category: ['', Validators.required],
      Supplier: [''],
      // business: ['EM', Validators.required],
      // region: ['Europe', Validators.required],
      // country: ['Germany', Validators.required],
      year: [new Date().getFullYear()+"", Validators.required],
      quarter: ['Q1', Validators.required],
      priority: [''],
      leadAuditor: ['U. Sahoo', Validators.required],
      procurement: ['Erdem Demir', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      isFirstTimeAudit: [''],
      legalEntity: [''],
      siteCertificateNumber: [''],
      coAuditor: [''],
      checkList: this.fb.array([])

    });
    // _this.auditForm.get("region").valueChanges.subscribe(value => {
    //   _this.regionChanged();
    // });
  }
loopCount:any=0;
  ngAfterViewInit(): void {

    var _this = this;
    if (history.state.action == "View") {
      _this.isView = true;

    }
   
    _this.siteControl.valueChanges.subscribe(siteId => {
      _this.dataService.siteId = siteId;
      console.log('siteId',siteId)

      _this.currentsideId=siteId
    _this.siteSelFun();
    _this.getDateFormat(siteId);
    _this.commonService.getProcessConfiguration(
      _this.siteControl.value,
      function (data) {
        _this.corePrincipleFind(_this.siteControl.value);
      }
    );
    });
    if (_this.commonService.siteList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if(_this.userAccessMenu){
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }else{
      _this.goPage("schedule-list");
    }
    if (_this.initialDataFlag > 1) {
      if (_this.editData) {
        var proc = _this.editData["refOFWAProcess"];
        _this.siteControl.setValue(_this.editData["refSite"]["externalId"]);
        _this.dataService.siteId = _this.commonService.siteList;
        console.log('siteId', _this.commonService.siteList)
  
        _this.currentsideId= _this.commonService.siteList
        _this.siteSelFun();
        _this.commonService.getProcessConfiguration(
          _this.siteControl.value,
          function (data) {
            _this.corePrincipleFind(_this.siteControl.value);
          }
        );
        _this.processRadio.setValue("Audit");
        
      } else {
        _this.siteControl.setValue(_this.dataService.siteId);
        _this.commonService.getProcessConfiguration(
          _this.siteControl.value,
          function (data) {
            _this.corePrincipleFind(_this.siteControl.value);
          }
        );
      }
    }


    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (fiterType == "Site") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (_this.initialDataFlag > 1) {
          if (_this.editData) {
            _this.siteControl.setValue(_this.editData["refSite"]["externalId"]);
            _this.processRadio.setValue("Audit");
            _this.selectedAuditType = _this.commonService.processList.find(e => {
              return (e.externalId == _this.editData["refOFWAObservationType"]["externalId"]);
            })
          
            _this.commonService.getProcessConfiguration(
              _this.siteControl.value,
              function (data) {
                _this.corePrincipleFind(_this.siteControl.value);
              }
            );
          
          } else {
            _this.siteControl.setValue(_this.dataService.siteId);
            _this.commonService.getProcessConfiguration(
              _this.siteControl.value,
              function (data) {
                _this.corePrincipleFind(_this.siteControl.value);
              }
            );
          }
        }
      }
    })


    _this.corePrincipleControl.valueChanges.subscribe(value => {
      _this.processList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value && (e.name != "Field Walk"));
      })
      _this.filteredProcessList = _this.processList.slice();
      
    });
    

    _this.processControl.valueChanges.subscribe(processId => {
      
      _this.selectedProcess = _this.commonService.processList.find(e => {
        return (e.externalId == processId);
      })
      _this.subProcessList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == processId);
      })
      _this.subProcessList = _this.subProcessList.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      
      _this.filteredSubProcessList = _this.subProcessList.slice();
      if(this.loopCount<1){
        _this.loopCount++
      console.log('processId',processId)
     
      _this.radioForm = false;
      var value = _this.commonService.processList.find(e => {
        return (e.externalId == processId);
      })
      
      this.createSchedule = this.fb.group({
        title: ['', Validators.required],
        observer: ['',Validators.required],
        locationObserver: [{ value: '', disabled: _this.isView }],
        datetime: [''],
        duetime: [''],
        //observePoints: [''],
  
      });

      if (value.name == "Audit") {
        _this.radioForm = true;
        this.filterInit("Region");
        this.filterInit("Country");
        this.filterInit("BusinessLine");

        _this.dataService.postData({ "name": "Priority" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
          _this.priorityList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
          _this.filteredPriorityList = _this.priorityList.slice();
          _this.cd.detectChanges()
        })
        _this.dataService.postData({ "name": "Year" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
          _this.yearList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
          _this.yearList = _this.yearList.sort((a, b) => parseInt(b) - parseInt(a));
          _this.filteredYearList = _this.yearList.slice();
          _this.cd.detectChanges()
        })
        _this.dataService.postData({ "name": "Quarter" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
          _this.quarterList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
          _this.quarterList.sort((a, b) => {
            // Extract the number from the value (Q1, Q2, etc.)
            const quarterA = parseInt(a.value.substring(1), 10);
            const quarterB = parseInt(b.value.substring(1), 10);
            return quarterA - quarterB;
        });
          console.log("quarterList", _this.quarterList)
          _this.filteredQuarterList = _this.quarterList.slice();
          _this.cd.detectChanges()
        })

        _this.loaderFlag =true;
        var reqJSON = {
          "SupplierNumber": (_this.editData && _this.editData.refSupplier) ?_this.editData.refSupplier?.number : undefined
        }
        _this.dataService.postData(reqJSON, _this.dataService.NODE_API + "/api/service/listSupplier").subscribe((resData: any) => {
          console.log("511supler",resData)
          _this.loaderFlag =false;
          _this.cd.detectChanges();
          _this.SupplierList = resData["data"]["list" + _this.commonService.configuration["typeSupplier"]]["items"]
          _this.filteredSupplierList = _this.SupplierList.slice();
          if (_this.editData) {
            console.log(_this.editData)
            _this.selectedSupplier = _this.SupplierList.find(e => {
              return (e.externalId == _this.editData.refSupplier?.externalId);
            })
            console.log("selectedsupplier", _this.selectedSupplier)
            _this.auditForm.get("Supplier").setValue(_this.editData.refSupplier?.externalId)
          }

        })
      }else{
        _this.radioForm = false;
      }
  }});

  _this.observationType.valueChanges.subscribe(processId => {
    _this.selectedAuditType = _this.commonService.processList.find(e => {
      return (e.externalId == processId);
    })
    _this.categoryList = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == processId);
    })
    _this.filteredCategoryList = _this.categoryList.slice();
    
    _this.dataService.postData({ "externalId": processId }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
      
      // if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {

        var processConfig;
        if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
          processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
        }else{
          processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.selectedProcess.name)
        }
        var configDetail = processConfig["configDetail"];
        console.log('processConfig',processConfig)
        
      
        _this.configDetail = processConfig["configDetail"];
        _this.auditForm.get("priority").setValidators(configDetail && configDetail.priority?.isMandatory ? Validators.required : undefined)
        _this.createSchedule.get("locationObserver").setValidators(configDetail && configDetail.priority?.isMandatory ? Validators.required : undefined)
        _this.loadForm = true;
         _this.cdRef.detectChanges();
        // _this.initEdit();
      // }
    })

  })
    // _this.createSchedule.get("processType").valueChanges.subscribe(value => {
    //   _this.subProcessList = _this.commonService.processList.filter(e => {
    //     return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value);
    //   })
    //   _this.filteredSubProcessList = _this.subProcessList.slice();
    //   if (value.name == "Audit") {
    //     _this.radioForm = true;
    //   }else{
    //     _this.radioForm = false;
    //   }
    // });

    // _this.auditForm.get("auditType").valueChanges.subscribe(value => {

    //   _this.selectedAuditType = _this.commonService.processList.find(e => {
    //     return (e.externalId == value);
    //   })
    //   _this.categoryList = _this.commonService.processList.filter(e => {
    //     return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value);
    //   })
    //   _this.filteredCategoryList = _this.categoryList.slice();
    // });
    _this.auditForm.get("Supplier").valueChanges.subscribe(value => {
      _this.selectedSupplier = _this.SupplierList.find(e => {
        console.log("554", _this.selectedSupplier)
        return (e.externalId == value);
      })
    });


    this.changeDetector.detectChanges();
  }

  getDateFormat(site){
    var _this = this;
    _this.dataService.postData({ "sites": site }, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
      var listSetting = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
      if (listSetting.length > 0) {
        var settingData = listSetting[0];
          var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
          _this._locale = dateFormat.local;
          _this.commonService.dateFormat = dateFormat;
          _this._adapter.setLocale(_this._locale);
          _this.setDateFormat();
        }else{
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
        _this._locale = dateFormat.local;
        _this.commonService.dateFormat = dateFormat;
        _this._adapter.setLocale(_this._locale);
        _this.setDateFormat();
        _this.cd.detectChanges();
      }
    });
  }
  async onSupplierNumChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.SupplierList = []
    _this.filteredSupplierList = [];
    _this.loaderFlag = true;
    
    _this.searchSubject.next(_this.fl_searchVal);
      _this.cdRef.detectChanges()
      
  }
}
  async onAttendeesChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
     _this.attendeeList = []
    _this.filteredattendeeList = [];
    
    // _this.loaderFlag = true;
    _this.searchSubject1.next(_this.fl_searchVal);
       

     
      _this.cdRef.detectChanges()
      
  }
}
async onBehalfChange(item: any) {
  console.log('item  -->>>>>>>', item);
  var _this = this;
  var filter: any = document.getElementsByClassName('mat-filter-input');
console.log(this.selectionControl)
  if (this.selectionControl.value === 'user') {
      if (filter.length > 0) {
          _this.fl_searchVal = filter[0]["value"];
          _this.behalfList = [];
          _this.filteredBehalfList = [];
          _this.searchSubject2.next(_this.fl_searchVal);
       
          
        
      }
  }
  else{
  }
}
onSelectionChange(event: any) {
  var _this=this
  console.log(event.value)
  
  
  if (this.selectionControl.value === 'group') {
    // this.dataService.postData({ }, this.dataService.NODE_API + "/api/service/listAllUser")
    this.dataService.postData({role: "RoleSite",site: this.currentsideId, application: this.commonService.applicationInfo.externalId },
      this.dataService.NODE_API + '/api/service/getRolesInfo')
      .subscribe((apigroupList:any) => {
        _this.groupList=apigroupList

        // _this.groupList= [
        //   {
        //     "value": "7b7d5578-12f3-4b58-b3d5-63304bb47bcd",
        //     "label": "CLK Business User",
        //     "description": "CLK Business User"
        //   },
        //   {
        //     "value": "ROLESITE_DATASCIENTIST_STS-CLK",
        //     "label": "CLK Data Scientist",
        //     "description": "Data Scientist Role for Clear Lake, TX, USA"
        //   },
        //   {
        //     "value": "ROLESITE_CLKDATASCIENTIST_STS-CLK",
        //     "label": "CLK Data Scientist",
        //     "description": "teste"
        //   }]
          _this.groupList.forEach((element) => {
            element.externalId = element.value;
            element.name = element.label;
        });
        console.log("groupList",_this.groupList)
        this.filteredGroupList = this.groupList.slice();
        
      })
      
  } else {
      this.onBehalfChange(null);
  }
}
async onProcurementChangee(item: any) {
  console.log('item  -->>>>>>>',item)
  var _this = this;
  
 var filter:any = document.getElementsByClassName('mat-filter-input');
 
 if(filter.length > 0){
  _this.fl_searchVal3 = filter[0]["value"]
  
  _this.procurementList = [];
  _this.filteredprocurementList = [];
  //   _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
  //   subscribe((resData: any) => {
  // console.log('resData',resData)
  //     _this.procurementList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
  //     _this.procurementList.forEach((element)=>{
  //       element.name =  element.firstName+' '+element.lastName
  //     })
  //     _this.filteredprocurementList = _this.procurementList.slice();
  //     _this.cd.detectChanges()
   
  //   })
  
  _this.searchSubject3.next(_this.fl_searchVal3);
    _this.cdRef.detectChanges()
    
}
}
  siteSelFun() {
    var _this = this;
    console.log("_this.commonService.siteList")
    console.log(_this.commonService.siteList)

    var site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value)
    _this.selectedSite = site;
    console.log(_this.commonService.siteList)
    console.log(_this.siteControl.value)
    console.log(site)
    if(site){
      if (site["country"] && site["country"]["parent"]) {
        _this.selectedRegion = site["country"]["parent"];
      }
     
      if (site && site["reportingLocations"]["items"].length > 0) {
        _this.reportingLocationList = _.orderBy(site["reportingLocations"]["items"], ['description'], ['asc']);
        _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
        _this.filteredReportingLocationList = _this.reportingLocationList.slice();
       // _this.tempLocation =  _this.reportingLocationList.slice()
        console.log('reportingLocationList',_this.reportingLocationList)
      }
    }
    _this.selectedCountry = site["country"];
   
    _this.commonService.getProcessConfiguration(_this.siteControl.value, function (data) {
      // if (_this.editData) {
      //   _this.initEdit();
      // }
    });
  }



  // regionChanged() {
  //   var _this = this;
  //   var selectedRegion = _this.commonService.getSelectedValue([_this.auditForm.get("region").value]);
  //   console.log(selectedRegion)
  //   var finalCountryList = [];
  //   _this.countryList = [];
  //   _this.filteredCountryList = _this.countryList.slice();
  //   if (selectedRegion.length > 0) {
  //     selectedRegion.filter(e => {
  //       var countryList = _this.commonService.countryList.filter(f => {
  //         var parentId = f.parent ? f.parent.externalId : " ";
  //         return _.startsWith(parentId, e);
  //       })
  //       finalCountryList = finalCountryList.concat(_.orderBy(countryList, ['name'], ['asc']));
  //       finalCountryList = _.uniqBy(finalCountryList, 'externalId');
  //       return finalCountryList;
  //     })
  //     _this.countryList = finalCountryList;
  //     _this.filteredCountryList = _this.countryList.slice();
  //   } else {
  //     _this.countryList = _this.commonService.countryList;
  //     _this.filteredCountryList = _this.countryList.slice();
  //   }

  // }
  getUserMenuConfig(){
    var _this = this
     
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
  
      if(_this.commonService.menuFeatureUserIn.length>0){
     _this.ScheduleTracker = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleTracker);
     _this.ScheduleCreateAudit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleAuditSchedule);
     _this.ScheduleCreateFieldWalk = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateFieldWalk);
     _this.ScheduleCreateObservation = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateObservation);
   
      }else{
        _this.ScheduleTracker = {};
        _this.ScheduleCreateAudit = {};
        _this.ScheduleCreateFieldWalk = {};
        _this.ScheduleCreateObservation = {}
      
      }
    
  }
  getDaysBetweenDates(startDate, endDate) {
    const days = [];
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    let currentDate = new Date(startDate);

    while (currentDate <= new Date(endDate)) {
        days.push(dayNames[currentDate.getDay()]);
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return [...new Set(days)]; // Removes duplicates
}
  initEdit() {
    var _this = this;
    console.log('_this.editData',_this.editData);
    if(_this.editData && _this.editData.refOFWAProcess && _this.editData.refOFWAProcess.name == "Observation"){
        // _this.corePrincipleControl.setValue(_this.editData.refOFWACorePrinciple.externalId);
        // _this.processControl.setValue(_this.editData.refOFWAProcess.externalId)
        _this.createSchedule.get("title").setValue(_this.editData.title)
        _this.observationType.setValue(_this.editData.refOFWAObservationType.externalId)
        if(_this.editData.refReportingLocation){
          _this.createSchedule.get("locationObserver").setValue(_this.editData.refReportingLocation.externalId)
        }
        if(_this.editData.performerAzureDirectoryUserID){
          _this.fl_searchVal=_this.editData.performerAzureDirectoryUserID.externalId
          _this.searchSubject2.next(_this.editData.performerAzureDirectoryUserID.externalId);
          _this.createSchedule.get("observer").setValue(_this.editData.performerAzureDirectoryUserID.externalId)
        }
        _this.createSchedule.get("datetime").setValue(_this.editData.observationStartDate)
        _this.createSchedule.get("duetime").setValue(_this.editData.observationEndDate)
        
        _this.eventData =_this.OccurrenceOptions.find(item => item.name ==_this.editData.occurrence)

        console.log("_this.eventData")
        console.log(_this.editData)
        _this.occurrenceData.view=_this.editData.occurrence
        _this.occurrenceData.startDate=_this.formatDate(_this.editData.observationStartDate)
        _this.occurrenceData.endDate=_this.formatDate(_this.editData.observationEndDate)
        const startDate = _this.editData.observationStartDate;
        const endDate = _this.editData.observationEndDate;

        _this.occurrenceData.days = _this.getDaysBetweenDates(startDate, endDate);
        _this.cd.detectChanges()
        if (_this.actionFlag == "View") {
          this.auditForm.get("title").disable();
          this.auditForm.get("email").disable();
          // this.auditForm.get("auditType").disable();
          this.auditForm.get("auditNumber").disable();
          this.createSchedule.get("locationObserver").disable();
          this.createSchedule.get("observer").disable();
          this.createSchedule.get("datetime").disable();
          this.createSchedule.get("duetime").disable();
          this.createSchedule.get("title").disable();
          this.observationType.disable();
        
        }
    }
  
    if(_this.editData && _this.editData.refOFWAProcess && _this.editData.refOFWAProcess.name == "Audit"){
      _this.auditForm.get("title").setValue(_this.editData.title)
      _this.auditForm.get("email").setValue(_this.editData.email)
      _this.subProcessList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.editData.refOFWACorePrinciple.externalId);
      })
      _this.subProcessList = _this.subProcessList.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      // _this.filteredSubProcessList = _this.subProcessList.slice();
      _this.categoryList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.editData.refOFWAObservationType.externalId);
      })
      _this.filteredCategoryList = _this.categoryList.slice();
  
      this.corePrincipleControl.setValue(_this.editData.refOFWACorePrinciple.externalId)
      this.processControl.setValue(_this.editData.refOFWAProcess.externalId)
      this.observationType.setValue(_this.editData.refOFWAObservationType.externalId)
      this.auditForm.get("auditNumber").setValue(_this.editData.auditNumber)
      this.auditForm.get("category").setValue(_this.editData.refOFWACategory.externalId)
      this.auditForm.get("Supplier").setValue(_this.editData.refSupplier?.externalId)
      if (this.editData.refSupplierBusinessLine) {
        this.auditForm.get("business").setValue(_this.editData.refSupplierBusinessLine?.externalId)
      }
  
      // if (this.editData.refGeoRegion) {
      //   this.auditForm.get("region").setValue(_this.editData.refGeoRegion["externalId"])
      // }
  
      // if (_this.editData.refCountry) {
      //   this.auditForm.get("country").setValue(_this.editData.refCountry["externalId"])
      // }
  
      this.auditForm.get("year").setValue(_this.editData.year + "")
      this.auditForm.get("quarter").setValue(_this.editData.quarter)
      this.auditForm.get("priority").setValue(_this.editData.priority)
      this.auditForm.get("startDate").setValue(_this.editData.observationStartDate)
      this.auditForm.get("endDate").setValue(_this.editData.observationEndDate)
      this.auditForm.get("isFirstTimeAudit").setValue(_this.editData.isFirstTimeAudit ? "Yes" : "No")
      this.auditForm.get("legalEntity").setValue(_this.editData.legalEntity)
      this.auditForm.get("siteCertificateNumber").setValue(_this.editData.siteCertificateNumber)
      // this.auditForm.get("leadAuditor").setValue(_this.editData.performerAzureDirectoryUserID)
      // this.auditForm.get("procurement").setValue(_this.editData.procurementPPRAzureDirectoryUserId)
  
  
      if (_this.actionFlag == "View") {
        this.auditForm.get("title").disable();
        this.auditForm.get("email").disable();
        // this.auditForm.get("auditType").disable();
        this.auditForm.get("auditNumber").disable();
        // this.auditForm.get("region").disable();
        this.auditForm.get("category").disable();
        this.auditForm.get("Supplier").disable();
        // this.auditForm.get("business").disable();
        // this.auditForm.get("country").disable();
        this.auditForm.get("year").disable();
        this.auditForm.get("quarter").disable();
        this.auditForm.get("priority").disable();
        this.auditForm.get("leadAuditor").disable();
        this.auditForm.get("procurement").disable();
        this.auditForm.get("startDate").disable();
        this.auditForm.get("endDate").disable();
        this.auditForm.get("isFirstTimeAudit").disable();
        this.auditForm.get("legalEntity").disable();
        this.auditForm.get("siteCertificateNumber").disable();

        this.createSchedule.get("title").disable();
        this.observationType.disable();
        this.createSchedule.get("observer").disable();
        this.createSchedule.get("datetime").disable();
      }
    }
    
  }
  corePrincipleFind(siteId) {
    var _this = this;
    
    console.log(siteId)
    console.log(_this.commonService.processList)
    _this.corePrincipleList = _this.commonService.processList.filter(e => {
      return ((e.refSite && e.refSite.externalId) == siteId && e.processType == "Core Principles") ;
    })
    _this.filteredCorePrincipleList = _this.corePrincipleList.slice();
    // if(_this.editData && _this.editData.refOFWAProcess && _this.editData.refOFWAProcess.name == "Observation"){
    //   _this.corePrincipleControl.setValue(_this.editData.refOFWACorePrinciple.externalId);
    //  // _this.processControl.setValue(_this.editData.refOFWAProcess.externalId)
    //  _this.processList = _this.commonService.processList.filter(e => {
    //   return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.editData.refOFWACorePrinciple.externalId);
    // })
    // _this.filteredProcessList = _this.processList.slice();
    // if(_this.editData && _this.editData.refOFWAProcess && _this.editData.refOFWAProcess.name == "Observation"){
    //   //  _this.corePrincipleControl.setValue(_this.editData.refOFWACorePrinciple.externalId);
    //     _this.processControl.setValue(_this.editData.refOFWAProcess.externalId)
    //     _this.subProcessList = _this.commonService.processList.filter(e => {
    //       return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.editData.refOFWAProcess.externalId);
    //     })
    //     _this.filteredSubProcessList = _this.subProcessList.slice();
    //     _this.dataService.postData({ "externalId": _this.editData.refOFWAProcess.externalId }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
    
    //       if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
    //         var processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
    //         var configDetail = processConfig["configDetail"];
    //         console.log('processConfig',processConfig)
    //         this.createSchedule = this.fb.group({
    //           title: ['', Validators.required],
    //           observationType: ['',Validators.required],
    //           observer: ['',Validators.required],
    //          locationObserver: [{ value: '', disabled: _this.isView },configDetail && configDetail.locationObserved && configDetail.locationObserved.isMandatory ? Validators.required : undefined],
    //           datetime: [''],
    //           duetime: [''],
    //           //observePoints: [''],
        
    //         });
          
    //         _this.configDetail = processConfig["configDetail"];
    //         _this.loadForm = true;
    //          _this.cdRef.detectChanges();
    //         _this.initEdit();
    //       }
    //     })
    //   }
    // }

  }

  processFind(core) {
    var _this = this;
      _this.processList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == core.externalId);
      })
      _this.filteredProcessList = _this.processList.slice();

     
  }

  goPage(page) {
    this.router.navigate([page]);
  }


  filterInit(fiterType) {
    var _this = this;
    if (fiterType == "Region") {
      _this.regionList = _this.commonService.regionList;
      _this.filteredRegionList = _this.commonService.regionList.slice();
    }
    if (fiterType == "Country") {
      _this.countryList = _this.commonService.countryList;
      _this.filteredCountryList = _this.commonService.countryList.slice();
    }
    // if (fiterType == "Site") {
    //   _this.siteList = _this.commonService.siteList;
    //   _this.filteredSiteList = _this.commonService.siteList.slice();
    // }
    // if (fiterType == "Unit") {
    //   _this.unitList = _this.commonService.unitList;
    //   _this.filteredUnitList = _this.commonService.unitList.slice();
    // }
    if (fiterType == "BusinessLine") {
      _this.businessLineList = _this.commonService.businessLineList;
      _this.filteredBusinessLineList = _this.commonService.businessLineList.slice();
    }
    // if (fiterType == "ReportingLocation") {
    //   _this.reportingLocationList = _this.commonService.reportingLocationList;
    //   _this.filteredReportingLocationList = _this.commonService.reportingLocationList.slice();
    // }
  }
  trackerClick() {
    this.newItemEvent.emit({ "tracker": true, });
  }
  saveClick() {
    this.newItemEvent.emit({ "schedule": true, });

  }
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredRegionList = _this.regionList.slice();
      _this.filteredCountryList = _this.countryList.slice();
      _this.filteredBusinessLineList = _this.businessLineList.slice();
    }
  }

  getDatesInRange(startDate, endDate) {
    const start = new Date(startDate.split('/').reverse().join('/'));
    const end = new Date(endDate.split('/').reverse().join('/'));
    const dates = [];

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        dates.push(new Date(date));
    }

    return dates;
}

 formatDate(date) {
  var dateN = new Date(date)
    const day = String(dateN.getDate()).padStart(2, '0');
    const month = String(dateN.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = dateN.getFullYear();
    return `${day}-${month}-${year}`;
}

getDayName(dateString) {
  const date = new Date(dateString);
  const options:any = { weekday: 'long' };
  return date.toLocaleDateString('en-US', options);
}
 convertDateFormat(dateString) {
  const [day, month, year] = dateString.split('/');
  var nDateVal = new Date();
  console.log('month',month);
  nDateVal.setDate(parseInt(day));
  nDateVal.setMonth(parseInt(month)-1);
  nDateVal.setFullYear(year)
  return nDateVal;
}
  submitClick() {
  console.log('this.occurrenceData ',this.occurrenceData);  
  
 var _this =this;

 var scheduleVal = this.createSchedule.value
 console.log('scheduleVal.datetime',scheduleVal.datetime);
 console.log('scheduleVal.datetime',scheduleVal.duetime);
 var mStart;
 var mEnd;
 console.log('_this.corePrincipleControl.value',_this.corePrincipleControl.value);
 console.log('_this.processControl.value',_this.processControl.value);
 console.log('_this.createSchedule',_this.createSchedule.valid);
 console.log('_this.createSchedule',_this.createSchedule.value);

    if (_this.createSchedule.valid &&  _this.corePrincipleControl.value && _this.processControl.value) {    
      if(10 < scheduleVal.title.length && scheduleVal.title.length < 200){
        _this.loaderFlag = true;
        if(_this.eventData && _this.eventData.value != 1 ){
          mStart = _this.convertDateFormat(_this.occurrenceData.startDate);
          mEnd = _this.convertDateFormat(_this.occurrenceData.endDate);
          const datesInRange = _this.getDatesInRange(_this.occurrenceData.startDate, _this.occurrenceData.endDate);
          // Format the dates to a readable string format
          var formattedDates:any = datesInRange.map(date => date.toLocaleDateString());
          var weeklyDates = [];
           if(_this.occurrenceData.view == "Daily"){
             console.log('formattedDates',formattedDates);
            }
            if(_this.occurrenceData.view == "Weekly"){
              console.log('formattedDates',formattedDates);
              formattedDates.forEach((ele)=>{
                var dayName = _this.getDayName(ele)
           
               var findDay =  _this.occurrenceData.days.find(item => item == dayName)
               if(findDay){
                weeklyDates.push(ele)
               }
              })
              console.log('weeklyDates',weeklyDates);
              formattedDates = weeklyDates
            }
            if(_this.occurrenceData.view == "Quarterly"){
              console.log('formattedDates',formattedDates);
             }
            if(_this.occurrenceData.view == "Monthly"){
              if(_this.occurrenceData.selectedItem == "On day"){
                
                formattedDates.forEach((ele)=>{
                  var mday = new Date(ele);
                
                  if(mday.getDate() == _this.occurrenceData.dayCount ){
                    weeklyDates.push(ele)
                  }
                })
                formattedDates = weeklyDates
              }
              // if(_this.occurrenceData.selectedItem == "On the"){
              //   if(_this.occurrenceData.datType == "first"){
        
              //   }
              //   formattedDates.forEach((ele)=>{
              //     var mday = new Date(ele);
              //     console.log('mday.getDay()',mday.getDate(),'==',_this.occurrenceData.dayCount)
              //     if(mday.getDate() == _this.occurrenceData.dayCount ){
              //       weeklyDates.push(ele)
              //     }
              //   })
        
              // }
              console.log('weeklyDates',weeklyDates);
        
            }
          }
          var postObj = {
            "title": scheduleVal.title,
            "occurrence":_this.eventData.name,
            "isEnable":true,
            // "year": parseInt(myValue.year),
            // "quarter": myValue.quarter,
            // "priority": myValue.priority,
              "status": "Pending",
            // "observationStartDate": scheduleVal.datetime,
            // "observationEndDate": scheduleVal.duetime,
            // "isFirstTimeAudit": myValue.isFirstTimeAudit && myValue.isFirstTimeAudit=="Yes" ? true : false,
            // "legalEntity": myValue.legalEntity,
            // "siteCertificateNumber": myValue.siteCertificateNumber
      
          }
          if(_this.editData && _this.editData.externalId){
            postObj["externalId"] = _this.editData.externalId;
          }
          if(_this.eventData && _this.eventData.value != 1 ){
            var mStart;
            var mEnd;
            console.log('mStart',mStart);
            console.log('mEnd',mEnd);
           
            postObj["observationStartDate"] = mStart
            postObj["observationEndDate"] = mEnd
          }else{
            postObj["observationStartDate"] = scheduleVal.datetime;
            postObj["observationEndDate"] = scheduleVal.duetime;
          }
        
          var attendee = _this.behalfList.find(e => e.externalId == scheduleVal["observer"])
          if (attendee) {
            postObj["performerAzureDirectoryUserID"] = {
              "space": attendee["space"],
              "externalId": attendee["externalId"]
            }
          }
          if (_this.observationType.value) {
            var observationType = _this.subProcessList.find(e => { return (e.externalId == _this.observationType.value) });
            postObj["refOFWAObservationType"] = {
              "space": observationType.space,
              "externalId": observationType.externalId
            }
          }
          if (scheduleVal.locationObserver) {
            var reportingLocation = _this.reportingLocationList.find(e => { return (e.externalId == scheduleVal.locationObserver) });
            postObj["refReportingLocation"] = {
              "space": reportingLocation.space,
              "externalId": reportingLocation.externalId
            }
          }
         
          if (_this.corePrincipleControl.value) {

      var corePrinciple = _this.commonService.processList.find(e => { return (e.externalId == _this.corePrincipleControl.value) });

            postObj["refOFWACorePrinciple"] = {
              "space": corePrinciple.space,
              "externalId": corePrinciple.externalId
            }
          }
          if (_this.processControl.value) {

      var process = _this.commonService.processList.find(e => { return (e.externalId == _this.processControl.value) });

            postObj["refOFWAProcess"] = {
              "space": process.space,
              "externalId": process.externalId
            }
          }
        
          if (_this["selectedRegion"]) {
            postObj["refGeoRegion"] = {
              "space": _this["selectedRegion"].space,
              "externalId": _this["selectedRegion"].externalId
            }
          }
          if (_this["selectedCountry"]) {
            postObj["refCountry"] = {
              "space": _this["selectedCountry"].space,
              "externalId": _this["selectedCountry"].externalId
            }
          }
        
          if (_this["selectedSite"]) {
            postObj["refSite"] = {
              "space": _this["selectedSite"].space,
              "externalId": _this["selectedSite"].externalId
            }
          }  
          console.log('postObj',postObj)
          var instanceObj = {
            "type": _this.commonService.configuration["typeSchedule"],
            "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
            "unitCode": _this.commonService.configuration["allUnitCode"],
            "items": [
              postObj
            ]
          }
      
      
          _this.dataService.postData(instanceObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
            if (data["items"].length > 0) {
              var detailArray = [];
              var mySchedule = data["items"][0];
              console.log('formattedDates',formattedDates)
              if(this.eventData && this.eventData.value != 1 ){
                formattedDates.forEach((ele)=>{
                  var postObjDetail = {
                    refOFWASchedule:{
                      "space": mySchedule.space,
                      "externalId": mySchedule.externalId
                    },
                    startDate:new Date(ele),
                    endDate:new Date(ele),
                    status: "Pending",
                    isEnable:true
                  }
                  detailArray.push(postObjDetail)
                })
              
              }else{
                var postObjDetail = {
                  refOFWASchedule:{
                    "space": mySchedule.space,
                    "externalId": mySchedule.externalId
                  },
                  startDate:scheduleVal.datetime,
                  endDate:scheduleVal.duetime,
                  status: "Pending",
                  isEnable:true
                }
                detailArray.push(postObjDetail)
              }
              var instanceAuditObj = {
                "type": _this.commonService.configuration["typeOFWAScheduleDetail"],
                "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
                "unitCode": _this.commonService.configuration["allUnitCode"],
                "items": detailArray
                }
                console.log('instanceAuditObj',instanceAuditObj);
                _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
                  _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
                  _this.loaderFlag = false;
                  _this.goPage('schedule-list')
               
                });
              
            }
            })
      }else{
        this.commonService.triggerToast({
          type: 'error',
          title: '',
          msg: _this.commonService.toasterLabelObject['toasterPleasefilldetails'],
        });
      } 
  
    } else {
      this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterPleasefilldetails']});
      _this.loaderFlag = false;
    }
   
 

  }

  auditSubmit() {
    var selectedChecklist = [];
    _.each(this.checkList,function(eData){
      if(eData.isSelected.value){
        selectedChecklist.push(eData.name)
      }
    })
    if (!this.auditForm.valid) {
      this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'] });
      return;
    }
    this.loaderFlag = true;
    var myValue = this.auditForm.value;
    var _this = this;
    if(myValue.auditNumber){
      console.log(_this.commonService.auditNumbers)
      if(_this.commonService.auditNumbers.includes(myValue.auditNumber)){
        if ( myValue.auditNumber != _this.editData?.auditNumber) {
        this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterDuplicateAuditNo'] });
        this.loaderFlag = false;
        return;}
      }
    }
    // "performerAzureDirectoryUserId": myValue.leadAuditor,
    //   "procurementPPRAzureDirectoryUserId": myValue.procurement,
    var postObj = {
      "title": myValue.title,
      "auditNumber": myValue.auditNumber,
      "email": myValue.email,
      "year": parseInt(myValue.year),
      "quarter": myValue.quarter,
      "priority": myValue.priority,
      "status": "Pending",
      "observationStartDate": myValue.startDate,
      "observationEndDate": myValue.endDate,
      "isFirstTimeAudit": myValue.isFirstTimeAudit && myValue.isFirstTimeAudit=="Yes" ? true : false,
      "legalEntity": myValue.legalEntity,
      "siteCertificateNumber": myValue.siteCertificateNumber
    }
    if(this.selectedAuditType.name=='Supplier Audit'){
      if(selectedChecklist.length ==0){
        this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'] });
        this.loaderFlag = false;
        return;
      }else{
        postObj["evidenceChecklist"] = selectedChecklist;
      }
    }
    
    var attendee = _this.attendeeList.find(e => e.externalId == myValue["leadAuditor"])
    if (attendee) {
      postObj["performerAzureDirectoryUserID"] = {
        "space": attendee["space"],
        "externalId": attendee["externalId"]
      }
    }
    var procurement = _this.procurementList.find(e => e.externalId == myValue["procurement"])
    if (procurement) {
      postObj["procurementPPRAzureDirectoryUserID"] = {
        "space": procurement["space"],
        "externalId": procurement["externalId"]
      }
    }
    var coAuditor = _this.coAuditorList.find(e => e.externalId == myValue["coAuditor"])
    if (coAuditor) {
      postObj["coAuditor"] = {
        "space": coAuditor["space"],
        "externalId": coAuditor["externalId"]
      }
    }
    if (_this.editData) {
      postObj["externalId"] = _this.editData["externalId"];
    }
    if (myValue.Supplier) {
      var Supplier = _this.SupplierList.find(e => { return (e.externalId == myValue.Supplier) });
      postObj["refSupplier"] = {
        "space": Supplier.space,
        "externalId": Supplier.externalId
      }
    }

    if (_this.corePrincipleControl.value) {
      var corePrinciple = _this.commonService.processList.find(e => { return (e.externalId == _this.corePrincipleControl.value) });
      postObj["refOFWACorePrinciple"] = {
        "space": corePrinciple.space,
        "externalId": corePrinciple.externalId
      }
    }
    if (_this.processControl.value) {
      var process = _this.commonService.processList.find(e => { return (e.externalId == _this.processControl.value) });

      postObj["refOFWAProcess"] = {
        "space": process.space,
        "externalId": process.externalId
      }
    }

    if (_this.observationType.value) {
      var auditType = _this.commonService.processList.find(e => { return (e.externalId == _this.observationType.value) });
      postObj["refOFWAObservationType"] = {
        "space": auditType.space,
        "externalId": auditType.externalId
      }
    }

    if (myValue.category) {
      var category = _this.categoryList.find(e => { return (e.externalId == myValue.category) });
      postObj["refOFWACategory"] = {
        "space": category.space,
        "externalId": category.externalId
      }
    }

    // if (myValue["business"]) {
    //   var business = _this.businessLineList.find(e => { return (e.externalId == myValue["business"]) });
    //   postObj["refSupplierBusinessLine"] = {
    //     "space": business.space,
    //     "externalId": business.externalId
    //   }
    // }
    // if (myValue["region"]) {
    //   var SupplierGeoRegion = _this.regionList.find(e => { return (e.externalId == myValue["region"]) });
    //   postObj["refSupplierGeoRegion"] = {
    //     "space": SupplierGeoRegion.space,
    //     "externalId": SupplierGeoRegion.externalId
    //   }
    // }
    // if (myValue["country"]) {
    //   var country = _this.countryList.find(e => { return (e.externalId == myValue["country"]) });
    //   postObj["refSupplierCountry"] = {
    //     "space": country.space,
    //     "externalId": country.externalId
    //   }
    // }

    if (_this["selectedRegion"]) {
      postObj["refGeoRegion"] = {
        "space": _this["selectedRegion"].space,
        "externalId": _this["selectedRegion"].externalId
      }
    }
    if (_this["selectedCountry"]) {
      postObj["refCountry"] = {
        "space": _this["selectedCountry"].space,
        "externalId": _this["selectedCountry"].externalId
      }
    }

    if (_this["selectedSite"]) {
      postObj["refSite"] = {
        "space": _this["selectedSite"].space,
        "externalId": _this["selectedSite"].externalId
      }
    }

    
    var instanceObj = {
      "type": _this.commonService.configuration["typeSchedule"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        postObj
      ]
    }


    _this.dataService.postData(instanceObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      
      
      if (data["items"].length > 0) {
        let usersOrRoles;
        if (this.selectionControl.value === 'user') {
            usersOrRoles = {
                users: [
                    this.attendeeList.find((e) => e.externalId == myValue['leadAuditor']).email
                ]
            };
        } else {
            usersOrRoles = {
                roles: [
                    // this.roleList.find((e) => e.externalId == myValue['leadRole']).name
                ]
            };
        }
        console.log(this.subProcessList, this.subProcessList);
                var instanceNotification = [
                  {
                    application: this.commonService.applicationInfo.name, 
                    description: 'Schedule Notification',
                    ...usersOrRoles,
                    severity: myValue.priority,
                    properties: [
                      {
                        name: 'Title',
                        value: myValue.title,
                        type: 'text',
                      },
                      {
                        name: 'Audit Number',
                        value: myValue.auditNumber,
                        type: 'text',
                      },
                      {
                        name: 'Audit Type',
                        value: _this.commonService.processList.find(e => { return (e.externalId == _this.observationType.value) }).name,
                        type: 'text',
                      },
                      {
                        name: 'Start',
                        value: new Date(myValue.startDate).toDateString(),
                        type: 'text',
                      },
                      {
                        name: 'End',
                        value: new Date(myValue.endDate).toDateString(),
                        type: 'text',
                      },
                      { name: 'Year', value: parseInt(myValue.year), type: 'number' },
                      {
                        name: 'Quarter',
                        value: myValue.quarter,
                        type: 'text',
                      },
                    ],
                  },
                ];
        if (!_this.editData) {
          let notificationType= 'Schedule Creation'
        var mySchedule = data["items"][0];
        var postAuditObj = {
          "status": "Pending"
        }
        if (_this["selectedRegion"]) {
          postAuditObj["refGeoRegion"] = {
            "space": _this["selectedRegion"].space,
            "externalId": _this["selectedRegion"].externalId
          }
        }
        if (_this["selectedCountry"]) {
          postAuditObj["refCountry"] = {
            "space": _this["selectedCountry"].space,
            "externalId": _this["selectedCountry"].externalId
          }
        }
    
        if (_this["selectedSite"]) {
          postAuditObj["refSite"] = {
            "space": _this["selectedSite"].space,
            "externalId": _this["selectedSite"].externalId
          }
        }
        postAuditObj["refOFWASchedule"] = {
          "space": mySchedule.space,
          "externalId": mySchedule.externalId
        }
        if (_this.corePrincipleControl.value) {
          var corePrinciple = _this.commonService.processList.find(e => { return (e.externalId == _this.corePrincipleControl.value) });
          postAuditObj["refCorePrinciple"] = {
            "space": corePrinciple.space,
            "externalId": corePrinciple.externalId
          }
        }
        if (_this.observationType.value) {
          var auditType = _this.commonService.processList.find(e => { return (e.externalId == _this.observationType.value) });
          postAuditObj["refSubProcess"] = {
            "space": auditType.space,
            "externalId": auditType.externalId
          }
        }
        if (_this.processControl.value) {
          var process = _this.commonService.processList.find(e => { return (e.externalId == _this.processControl.value) });
    
          postAuditObj["refProcess"] = {
            "space": process.space,
            "externalId": process.externalId
          }
        }
        var instanceAuditObj = {
          "type": _this.commonService.configuration["typeAudit"],
          "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
          "unitCode": _this.commonService.configuration["allUnitCode"],
          "items": [
            postAuditObj
          ]
        }
//                 let usersOrRoles;
// if (this.selectionControl.value === 'user') {
//     usersOrRoles = {
//         users: [
//             this.attendeeList.find((e) => e.externalId == myValue['leadAuditor']).email
//         ]
//     };
// } else {
//     usersOrRoles = {
//         roles: [
//             // this.roleList.find((e) => e.externalId == myValue['leadRole']).name
//         ]
//     };
// }

//         var instanceNotification = [
//           {
//             application: this.commonService.applicationInfo.name, 
//             description: 'Schedule Notification',
//             ...usersOrRoles,
//             severity: myValue.priority,
//             properties: [
//               {
//                 name: 'Title',
//                 value: myValue.title,
//                 type: 'text',
//               },
//               {
//                 name: 'Audit Number',
//                 value: myValue.auditNumber,
//                 type: 'text',
//               },
//               {
//                 name: 'Audit Type',
//                 value: this.subProcessList.find((e) => {
//                   return e.externalId == myValue.auditType;
//                 }).name,
//                 type: 'text',
//               },
//               {
//                 name: 'Start',
//                 value: new Date(myValue.startDate).toDateString(),
//                 type: 'text',
//               },
//               {
//                 name: 'End',
//                 value: new Date(myValue.endDate).toDateString(),
//                 type: 'text',
//               },
//               { name: 'Year', value: parseInt(myValue.year), type: 'number' },
//               {
//                 name: 'Quarter',
//                 value: myValue.quarter,
//                 type: 'text',
//               },
//             ],
//           },
//         ];
        console.log(instanceNotification)
        _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
          _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
          this.commonService.notification(instanceNotification,notificationType);
          _this.loaderFlag = false;
          this.goPage('schedule-list');
        });
      }else{
        let notificationType= 'Schedule Update'
        _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
        _this.commonService.notification(instanceNotification,notificationType);
        _this.loaderFlag = false;
        this.goPage('schedule-list');
      }
      } else {
        _this.loaderFlag = false;
        _this.commonService.triggerToast({ type: 'error', title: "", msg: this.translate.instant("TOASTER.FAILED") });
        this.goPage('schedule-list');
      }


    })


  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
}
