import React from 'react'
import classnames from 'classnames'
import { usePagination, DOTS } from './usePagination'
import './pagination.css'
import { useTranslation } from 'react-i18next'
import { translate } from '@celanese/celanese-sdk'

const Pagination = (props) => {
  const {
    assetsType,
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    className,
  } = props
  //console.log('assetsType----------------============>',assetsType);  assets_cognite
  var paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  })

  const [t] = useTranslation('global')

  if (!paginationRange || paginationRange.length == 0) {
    paginationRange = [1]
  }
  // if (currentPage === 0 || !paginationRange || paginationRange.length < 2) {
  //   return null;
  // }
  //selectedTwo
  const onNext = () => {
    onPageChange(currentPage + 1)
  }

  const onPrevious = () => {
    onPageChange(currentPage - 1)
  }

  let lastPage = paginationRange[paginationRange.length - 1]
  return (
    <ul
      className={classnames('pagination-container', { [className]: className })}
    >
      <div
        className={classnames('pagination-item', {
          disabled: currentPage === 1,
        })}
        onClick={onPrevious}
      >
        <div>{translate('stLabel.prev')}</div>
        {/* <div className="arrow left" /> */}
      </div>
      {paginationRange.map((pageNumber) => {
        if (pageNumber === DOTS) {
          return <li className='pagination-item dots'>&#8230;</li>
        }
        if (assetsType == 'assets_cognite') {
          return (
            <li
              className={classnames('pagination-item', {
                selectedTwo: pageNumber === currentPage,
              })}
              onClick={() => onPageChange(pageNumber)}
            >
              {pageNumber}
            </li>
          )
        } else {
          return (
            <li
              className={classnames('pagination-item', {
                selected: pageNumber === currentPage,
              })}
              onClick={() => onPageChange(pageNumber)}
            >
              {pageNumber}
            </li>
          )
        }
      })}
      <div
        className={classnames('pagination-item', {
          disabled: currentPage === lastPage,
        })}
        onClick={onNext}
      >
        <div>{translate('stLabel.next')}</div>
        {/* <div className="arrow right" /> */}
      </div>
    </ul>
  )
}

export default Pagination
