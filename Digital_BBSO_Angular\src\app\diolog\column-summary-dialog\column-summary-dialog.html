<div class="c-dialog palette4">
  <form [formGroup]="form">
    <h3 mat-dialog-title class="my-title">{{ labels['reacttableListcols'] }}</h3>
    <div class="c-dialog-content">
      <div formArrayName="columns" *ngFor="let column of columnFormArray.controls; let i = index">
        <!-- <mat-checkbox [formControlName]="i"> -->
          <!-- {{ 'TABLE_COLS.'+checkColumns[i].displayName | translate}} -->
          <!-- {{ labels['tablecols'+checkColumns[i].key] }}
        </mat-checkbox> -->
        <mat-slide-toggle [formControlName]="i" (ngModelChange)="updateValue($event, i)">
          {{ labels[checkColumns[i].key] }}
        </mat-slide-toggle>
      </div>
    </div>
    <mat-dialog-actions align="end">
      <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
      (buttonAction)="cancel()"></common-lib-button>
      <!-- <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSubmit'] }}"
      (buttonAction)="submit()"></common-lib-button> -->
    </mat-dialog-actions>
  </form>
</div>
