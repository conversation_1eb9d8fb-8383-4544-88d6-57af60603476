import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { AddSubCategoryPopupComponent } from '../add-sub-category-popup/add-sub-category-popup.component';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ParentToChildService } from 'src/app/broadcast/parent-to-child.service';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { AddCraftComponent } from '../add-craft/add-craft.component';
import _ from "lodash";
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { AddQuestionsComponent } from '../add-questions/add-questions.component';
import { TranslateService } from '@ngx-translate/core';
import { CogniteClient } from '@cognite/sdk';
import { v1 as uuidv1 } from 'uuid';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';
import { MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import { HttpClient } from '@angular/common/http';
import { sequence } from '@angular/animations';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-configuration',
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.scss'],
  providers:[ { provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS },]
})
export class ConfigurationComponent implements OnInit {

  displayedColumns: any = [];
  labels = {};

  allColumns = [
    {
      name: 'externalId',
      displayName: 'Id',
      key: 'id',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'subProcess',
      displayName: 'Sub Process',
      key: 'subProcess',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'category',
      displayName: 'Category',
      key: 'category',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'subCategory',
      displayName: 'Sub Category',
      key: 'subCategory',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'name',
      displayName: 'Question',
      key: 'tablecolsQuestion',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'description',
      displayName: 'Description',
      key: 'description',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'createdBy',
      displayName: 'Created By',
      key: 'createdBy',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'createdTime',
      displayName: 'Created On',
      key: 'createdOn',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'date',
      displayName: 'Date',
      key: 'date',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'actions',
      displayName: 'Actions',
      key: 'actions',
      activeFlag: true,
      summary: false,
    },
  ];

  url: any = '';
  configListRow: any;

  isDisabled = true;

  configList:any = [];
  notificationList:any = [];
  yesNoOption:any = [{"name":"Yes","value":true},{"name":"No","value":false}]
  yesNoNullOption:any = [{"name":"Yes","value":true},{"name":"No","value":false},{"name":"Nil","value":"null"}]
  filteredYesNoList:any = this.yesNoOption.slice();
  filteredYesNoNullList:any = this.yesNoNullOption.slice();
  // defaultOptions:any = [{"name":"Safe","value":"Safe"},{"name":"Unsafe","value":"Unsafe"},{"name":"NotObserved","value":"NotObserved"}]
  // filteredDefaultOptions:any = this.defaultOptions.slice();

  client: CogniteClient;
  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  selectedIndexBinding = 0;
  searchControl: FormControl = new FormControl("");

  siteControl: FormControl = new FormControl("");
  siteList = [];
  filteredSiteList = [];

  processControl: FormControl = new FormControl();
  processList = [];
  filteredProcessList = [];

  corePrinciplesControl: FormControl = new FormControl();
  corePrinciplesList = [];
  filteredCorePrinciplesList = [];

  subProcessControl: FormControl = new FormControl();
  subProcessList = [];
  subProcessListAll = [];
  filteredSubProcessList = [];

  categoryControl: FormControl = new FormControl();
  categoryList = [];
  categoryListAll = [];
  filteredCategoryList = [];

  subCategoryControl: FormControl = new FormControl();
  subCategoryList = [];
  subCategoryListAll = [];
  filteredSubCategoryList = [];

  dateControl: FormControl = new FormControl();
  dateList = [
    {name:"MM/DD/YYYY",value:"MM/DD/YYYY"},
    {name:"DD/MM/YYYY",value:"DD/MM/YYYY"},
    {name:"YYYY-MM-DD",value:"YYYY-MM-DD"},
    {name:"YYYY/MM/DD",value:"YYYY/MM/DD"},
    {name:"DD.MM.YYYY",value:"DD.MM.YYYY"},
    {name:"DD-MM-YYYY",value:"DD-MM-YYYY"}
  ];
  filteredDateList = this.dateList.slice();

  timeControl: FormControl = new FormControl();
  timeList = [
    {name:"HH:mm:ss",value:"HH:mm:ss"},
    {name:"hh:mm:ss a",value:"hh:mm:ss a"},
    {name:"HH:mm",value:"HH:mm"},
    {name:"hh:mm a",value:"hh:mm a"}
  ];
  filteredTimeList = this.timeList.slice();

  feedbackControl: FormControl = new FormControl("true");
  signatureControl: FormControl = new FormControl("true");
  timeCaptureControl: FormControl = new FormControl("true");
  // processList = [];
  

  selectedCorePrinciple: any;
  selectedProcess: any;
  selectedSubProcess: any;
  selectedCategory: any
  selectedSubCategory: any

  auditBool: boolean = false;


  ifSite = false;
  ifProcess = false;
  ifCorePrinciple = false;
  ifSubProcess = false;
  ifCategory = false;
  ifSubCategory = false;
  processConfig: any;
  loaderFlag: boolean;
  userAccessMenu: any;
  configurationList: any;

  craftList: any = [];
  craftExternalId: any;
  CheckList:any = [];
  subMenuList: any[];
  dataSetImageId: any;
  dashboardConfigList: any[];
  columnConfigList: any[];
  checklistEnableValue: boolean; 
  notificationEnableValue: boolean;
  CheckListContoller: any[];
  NotificationContoller: any[];
  notificationGroupData: any;
  selectednotificationGroupData: any;
  constructor(
    private dataService: DataService,
    public dialog: MatDialog,
    private commonService: CommonService,
    private parentchildService: ParentToChildService,
    private router: Router,
    private tokenService: TokenService,
    private translate: TranslateService,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone,
    private changeDetector: ChangeDetectorRef
  ) {

    // var iframe = document.getElementById('iFrameFormConfig');
    // var iWindow = (<HTMLIFrameElement>iframe).contentWindow;


    window?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'menuConfiguration': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuConfiguration'] || 'menuConfiguration',
        'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
        'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
        'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
        'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
        'formcontrolsAddsubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddsubprocess'] || 'formcontrolsAddsubprocess',
        'formcontrolsProcessconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsProcessconfig'] || 'formcontrolsProcessconfig',
        'addcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addcategory'] || 'addcategory',
        'addSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addSubCategory'] || 'addSubCategory',
        'formcontrolsChecklistques': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChecklistques'] || 'formcontrolsChecklistques',
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
        'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
        'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
        'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
        'chooseSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseSubCategory'] || 'chooseSubCategory',
        'buttonAddquestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonAddquestion'] || 'buttonAddquestion',
        'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
        'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
        'formcontrolsFieldconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFieldconfig'] || 'formcontrolsFieldconfig',
        'formcontrolsChecklistfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChecklistfields'] || 'formcontrolsChecklistfields',
        'formcontrolsField': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsField'] || 'formcontrolsField',
        'formcontrolsEnabled': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnabled'] || 'formcontrolsEnabled',
        'formcontrolsAtleastone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAtleastone'] || 'formcontrolsAtleastone',
        'formcontrolsDispname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDispname'] || 'formcontrolsDispname',
        'formcontrolsDefvalue': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDefvalue'] || 'formcontrolsDefvalue',
        'injuryPotential': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'injuryPotential'] || 'injuryPotential',
        'notes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'] || 'notes',
        'notesActive': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notesActive'] || 'notesActive',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
        'buttonNil': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonNil'] || 'buttonNil',
        'formcontrolsListfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsListfields'] || 'formcontrolsListfields',
        'formcontrolsMandatory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMandatory'] || 'formcontrolsMandatory',
        'notesMandatory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notesMandatory'] || 'notesMandatory',
        'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        'formcontrolsDashboardconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDashboardconfig'] || 'formcontrolsDashboardconfig',
        'formcontrolsDashboardgraphs': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDashboardgraphs'] || 'formcontrolsDashboardgraphs',
        'formcontrolsGraphname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGraphname'] || 'formcontrolsGraphname',
        'formcontrolsGraphdispname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGraphdispname'] || 'formcontrolsGraphdispname',
        'edit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'edit'] || 'edit',
        'disable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'disable'] || 'disable',
        'enable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'enable'] || 'enable',
        'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',
        'formcontrolsSubcategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategories'] || 'formcontrolsSubcategories',
        'formcontrolsCategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategories'] || 'formcontrolsCategories',
        'formcontrolsColumnname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsColumnname'] || 'formcontrolsColumnname',
        'formcontrolsConfiglistcolumns': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsConfiglistcolumns'] || 'formcontrolsConfiglistcolumns',
        'formcontrolsListConfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsListConfig'] || 'formcontrolsListConfig',
        'activity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'] || 'activity',
        'additionalCommentsOnObservation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'additionalCommentsOnObservation'] || 'additionalCommentsOnObservation',
        'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
        'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
        'contractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractorDetail'] || 'contractorDetail',
        'didCorrectiveActionsOccurSpecificToTheStopWorkEvent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'didCorrectiveActionsOccurSpecificToTheStopWorkEvent'] || 'didCorrectiveActionsOccurSpecificToTheStopWorkEvent',
        'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
        'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
        'department': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'department'] || 'department',
        'describeTheObservations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheObservations'] || 'describeTheObservations',
        'describeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeCorrectiveActionTaken'] || 'describeCorrectiveActionTaken',
        'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
        'eventDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventDescription'] || 'eventDescription',
        'eventType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventType'] || 'eventType',
        'feedback': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'] || 'feedback',
        'floor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'floor'] || 'floor',
        'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
        'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
        'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
        'observeredCraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observeredCraft'] || 'observeredCraft',
        'observedOnBehalfOf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'] || 'observedOnBehalfOf',
        'reasonForAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'] || 'reasonForAction',
        'risk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'] || 'risk',
        'riskAgreement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'] || 'riskAgreement',
        'riskyAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'] || 'riskyAction',
        'safeBehaviour': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'] || 'safeBehaviour',
        'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
        'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
        'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
        'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
        'stopWorkAuthority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'stopWorkAuthority'] || 'stopWorkAuthority',
        'suggestedSolution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'] || 'suggestedSolution',
        'timeCapture': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'timeCapture'] || 'timeCapture',
        'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
        'urgency': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'urgency'] || 'urgency',
        'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
        'art': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'art'] || 'art',
        'cause': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cause'] || 'cause',
        'problem': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'problem'] || 'problem',
        'solution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'solution'] || 'solution',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'measureActivity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measureActivity'] || 'measureActivity',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'safe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'] || 'safe',
          'notSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'] || 'notSafe',
          'notObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'] || 'notObserved',
          'whichActivityIsBeingAudited': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whichActivityIsBeingAudited'] || 'whichActivityIsBeingAudited',
          'whoIsBeingAudited': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whoIsBeingAudited'] || 'whoIsBeingAudited',
          'auditedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditedBy'] || 'auditedBy',
          'discussionPoint': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'discussionPoint'] || 'discussionPoint',
          'nameOfCompany': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'nameOfCompany'] || 'nameOfCompany',
          'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
          'workPermits': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workPermits'] || 'workPermits',
          'workReleaseNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workReleaseNumber'] || 'workReleaseNumber',
          'attendees': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'attendees'] || 'attendees',
          'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
          'overallSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'overallSummary'] || 'overallSummary',
          'whatDidYouDoAboutIt': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatDidYouDoAboutIt'] || 'whatDidYouDoAboutIt',
          'whatNeedsAttention': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatNeedsAttention'] || 'whatNeedsAttention',
          'whatWentWell': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatWentWell'] || 'whatWentWell',
          'atRiskBySection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'atRiskBySection'] || 'atRiskBySection',
          'byCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byCategory'] || 'byCategory',
          'byDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byDepartment'] || 'byDepartment',
          'byUnit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byUnit'] || 'byUnit',
          'byLocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byLocation'] || 'byLocation',
          'percentageParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'percentageParticipation'] || 'percentageParticipation',
          'riskBehavioursAtLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskBehavioursAtLast7Days'] || 'riskBehavioursAtLast7Days',
          'sitewideParticipationLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sitewideParticipationLast7Days'] || 'sitewideParticipationLast7Days',
          'trends': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'trends'] || 'trends',
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
          'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
          'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
          'location': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'location'] || 'location',
          'status': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'status'] || 'status',
          'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
          'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
          'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
          'createdBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdBy'] || 'createdBy',
          'createdOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdOn'] || 'createdOn',
          'updatedOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedOn'] || 'updatedOn',
          'updatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedBy'] || 'updatedBy',
          'id': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'id'] || 'id',
          'descripeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'descripeCorrectiveActionTaken'] || 'descripeCorrectiveActionTaken',
          'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
          'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
          'measure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measure'] || 'measure',
          'observationDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationDate'] || 'observationDate',
          'yourDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yourDepartment'] || 'yourDepartment',
          'companyObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'companyObserved'] || 'companyObserved',
          'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
          'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
          'metricsByChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'metricsByChecklist'] || 'metricsByChecklist',
          'shiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftIdentifier'] || 'shiftIdentifier',
          'observedOnBehalfOfSec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOfSec'] || 'observedOnBehalfOfSec',
          'new': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'new'] || 'new',
          'close': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'close'] || 'close',
          'evidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'evidenceChecklist'] || 'evidenceChecklist',
          'newEvidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'newEvidenceChecklist'] || 'newEvidenceChecklist',
          'editEvidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'editEvidenceChecklist'] || 'editEvidenceChecklist',
          'formcontrolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestion'] || 'formcontrolsQuestion',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'formcontrolsNotificationfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNotificationfields'] || 'formcontrolsNotificationfields',
          'formcontrolsEnabledGroup': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnabledGroup'] || 'formcontrolsEnabledGroup',
          'notificationGroup': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notificationGroup'] || 'notificationGroup',
      }
      
    }

    var _this = this;
    _this.loaderFlag = true;
    _this.commonService.notificationGroup();

  }

  initialDataFlag = 0;
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'menuConfiguration': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuConfiguration'] || 'menuConfiguration',
          'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'corePrincipleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'] || 'corePrincipleTitle',
          'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
          'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
          'formcontrolsAddsubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddsubprocess'] || 'formcontrolsAddsubprocess',
          'formcontrolsProcessconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsProcessconfig'] || 'formcontrolsProcessconfig',
          'addcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addcategory'] || 'addcategory',
          'addSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addSubCategory'] || 'addSubCategory',
          'formcontrolsChecklistques': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChecklistques'] || 'formcontrolsChecklistques',
          'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'] || 'formcontrolsAudittype',
          'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
          'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
          'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
          'subCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'] || 'subCategory',
          'chooseSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseSubCategory'] || 'chooseSubCategory',
          'buttonAddquestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonAddquestion'] || 'buttonAddquestion',
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'formcontrolsFieldconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFieldconfig'] || 'formcontrolsFieldconfig',
          'formcontrolsChecklistfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChecklistfields'] || 'formcontrolsChecklistfields',
          'formcontrolsField': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsField'] || 'formcontrolsField',
          'formcontrolsEnabled': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnabled'] || 'formcontrolsEnabled',
          'formcontrolsAtleastone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAtleastone'] || 'formcontrolsAtleastone',
          'formcontrolsDispname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDispname'] || 'formcontrolsDispname',
          'formcontrolsDefvalue': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDefvalue'] || 'formcontrolsDefvalue',
          'injuryPotential': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'injuryPotential'] || 'injuryPotential',
          'notes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'] || 'notes',
          'notesActive': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notesActive'] || 'notesActive',
          'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
          'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
          'buttonNil': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonNil'] || 'buttonNil',
          'formcontrolsListfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsListfields'] || 'formcontrolsListfields',
          'formcontrolsMandatory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMandatory'] || 'formcontrolsMandatory',
          'notesMandatory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notesMandatory'] || 'notesMandatory',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
          'formcontrolsDashboardconfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDashboardconfig'] || 'formcontrolsDashboardconfig',
          'formcontrolsDashboardgraphs': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDashboardgraphs'] || 'formcontrolsDashboardgraphs',
          'formcontrolsGraphname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGraphname'] || 'formcontrolsGraphname',
          'formcontrolsGraphdispname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGraphdispname'] || 'formcontrolsGraphdispname',
          'edit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'edit'] || 'edit',
          'disable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'disable'] || 'disable',
          'enable': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'enable'] || 'enable',
          'observationTypes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationTypes'] || 'observationTypes',
          'formcontrolsSubcategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategories'] || 'formcontrolsSubcategories',
          'formcontrolsCategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategories'] || 'formcontrolsCategories',
          'formcontrolsColumnname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsColumnname'] || 'formcontrolsColumnname',
          'formcontrolsConfiglistcolumns': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsConfiglistcolumns'] || 'formcontrolsConfiglistcolumns',
          'formcontrolsListConfig': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsListConfig'] || 'formcontrolsListConfig',
          'activity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'] || 'activity',
          'additionalCommentsOnObservation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'additionalCommentsOnObservation'] || 'additionalCommentsOnObservation',
          'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
          'contractor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractor'] || 'contractor',
          'contractorDetail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'contractorDetail'] || 'contractorDetail',
          'didCorrectiveActionsOccurSpecificToTheStopWorkEvent': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'didCorrectiveActionsOccurSpecificToTheStopWorkEvent'] || 'didCorrectiveActionsOccurSpecificToTheStopWorkEvent',
          'crafts': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'crafts'] || 'crafts',
          'date': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'] || 'date',
          'department': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'department'] || 'department',
          'describeTheObservations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheObservations'] || 'describeTheObservations',
          'describeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeCorrectiveActionTaken'] || 'describeCorrectiveActionTaken',
          'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
          'eventDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventDescription'] || 'eventDescription',
          'eventType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'eventType'] || 'eventType',
          'feedback': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'] || 'feedback',
          'floor': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'floor'] || 'floor',
          'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
          'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
          'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'month': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'month'] || 'month',
          'observeredCraft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observeredCraft'] || 'observeredCraft',
          'observedOnBehalfOf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'] || 'observedOnBehalfOf',
          'reasonForAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'] || 'reasonForAction',
          'risk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'] || 'risk',
          'riskAgreement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'] || 'riskAgreement',
          'riskyAction': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'] || 'riskyAction',
          'safeBehaviour': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'] || 'safeBehaviour',
          'shift': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shift'] || 'shift',
          'shortDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shortDescription'] || 'shortDescription',
          'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
          'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
          'stopWorkAuthority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'stopWorkAuthority'] || 'stopWorkAuthority',
          'suggestedSolution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'] || 'suggestedSolution',
          'timeCapture': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'timeCapture'] || 'timeCapture',
          'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
          'urgency': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'urgency'] || 'urgency',
          'week': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'week'] || 'week',
          'art': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'art'] || 'art',
          'cause': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cause'] || 'cause',
          'problem': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'problem'] || 'problem',
          'solution': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'solution'] || 'solution',
          'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
          'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
          'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
          'measureActivity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measureActivity'] || 'measureActivity',
          'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
          'safe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'] || 'safe',
            'notSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'] || 'notSafe',
            'notObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'] || 'notObserved',
            'whichActivityIsBeingAudited': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whichActivityIsBeingAudited'] || 'whichActivityIsBeingAudited',
            'whoIsBeingAudited': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whoIsBeingAudited'] || 'whoIsBeingAudited',
            'auditedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditedBy'] || 'auditedBy',
            'discussionPoint': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'discussionPoint'] || 'discussionPoint',
            'nameOfCompany': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'nameOfCompany'] || 'nameOfCompany',
            'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
            'workPermits': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workPermits'] || 'workPermits',
            'workReleaseNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workReleaseNumber'] || 'workReleaseNumber',
            'attendees': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'attendees'] || 'attendees',
            'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
            'overallSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'overallSummary'] || 'overallSummary',
            'whatDidYouDoAboutIt': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatDidYouDoAboutIt'] || 'whatDidYouDoAboutIt',
            'whatNeedsAttention': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatNeedsAttention'] || 'whatNeedsAttention',
            'whatWentWell': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatWentWell'] || 'whatWentWell',
            'atRiskBySection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'atRiskBySection'] || 'atRiskBySection',
            'byCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byCategory'] || 'byCategory',
            'byDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byDepartment'] || 'byDepartment',
            'byUnit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byUnit'] || 'byUnit',
            'byLocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'byLocation'] || 'byLocation',
            'percentageParticipation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'percentageParticipation'] || 'percentageParticipation',
            'riskBehavioursAtLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskBehavioursAtLast7Days'] || 'riskBehavioursAtLast7Days',
            'sitewideParticipationLast7Days': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'sitewideParticipationLast7Days'] || 'sitewideParticipationLast7Days',
            'trends': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'trends'] || 'trends',
            'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
            'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
            'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
            'quarter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarter'] || 'quarter',
            'location': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'location'] || 'location',
            'status': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'status'] || 'status',
            'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
            'year': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'year'] || 'year',
            'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
            'createdBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdBy'] || 'createdBy',
            'createdOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createdOn'] || 'createdOn',
            'updatedOn': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedOn'] || 'updatedOn',
            'updatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'updatedBy'] || 'updatedBy',
            'id': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'id'] || 'id',
            'descripeCorrectiveActionTaken': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'descripeCorrectiveActionTaken'] || 'descripeCorrectiveActionTaken',
            'craft': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'craft'] || 'craft',
            'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
            'measure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'measure'] || 'measure',
            'observationDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationDate'] || 'observationDate',
            'yourDepartment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'yourDepartment'] || 'yourDepartment',
            'companyObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'companyObserved'] || 'companyObserved',
            'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
          'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
          'metricsByChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'metricsByChecklist'] || 'metricsByChecklist',
          'shiftIdentifier': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'shiftIdentifier'] || 'shiftIdentifier',
          'observedOnBehalfOfSec': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOfSec'] || 'observedOnBehalfOfSec',
          'new': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'new'] || 'new',
          'close': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'close'] || 'close',
          'evidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'evidenceChecklist'] || 'evidenceChecklist',
          'newEvidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'newEvidenceChecklist'] || 'newEvidenceChecklist',
          'editEvidenceChecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'editEvidenceChecklist'] || 'editEvidenceChecklist',
          'formcontrolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestion'] || 'formcontrolsQuestion',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'formcontrolsNotificationfields': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNotificationfields'] || 'formcontrolsNotificationfields',
          'formcontrolsEnabledGroup': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnabledGroup'] || 'formcontrolsEnabledGroup',
          'notificationGroup': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notificationGroup'] || 'notificationGroup',
          }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    console.log(_this.tabGroup)
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.loaderFlag = false;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId)
      _this.ifSite = true;
      _this.loaderFlag = false;
    }
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Core Principles") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
        }
        if (fiterType == "userAccess") {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.loaderFlag = false;
        }
        console.log(_this.initialDataFlag)
        if (_this.initialDataFlag > 0) {
          _this.siteControl.setValue(_this.dataService.siteId);
          _this.loadCorePrinciples();
        }
      }
    })

    _this.corePrinciplesControl.valueChanges.subscribe(process => {
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == process);
    })
    _this.processList = childrenProcess;
    _this.filteredProcessList = _this.processList.slice();
      
    var myProcess = _this.commonService.processList.find(e => {
      return ( e.externalId == process);
    })
    _this.selectCorePrinciple(myProcess)
    })
    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.loadCorePrinciples();
      

    });
    _this.getInialTokel(this.tokenService.getToken());
    _this.processControl.valueChanges.subscribe((value: any) => {
      console.log('====>',value)
      _this.processConfig = undefined;
      // _this.loaderFlag = true;
      var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value);
      })
      _this.subProcessListAll = childrenProcess;
      _this.subProcessList = childrenProcess.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      _this.filteredSubProcessList = _this.subProcessList.slice();

      _this.categoryList = [];
      _this.categoryListAll = [];
      _this.filteredCategoryList = _this.categoryList.slice();

      _this.subCategoryList = [];
      _this.subCategoryListAll = [];
      _this.filteredCategoryList = _this.subCategoryList.slice();

     
    //   var myProcess = _this.commonService.processList.find(e => {
    //     return ( e.externalId == value);
    //   })
    //   console.log(myProcess)
    // _this.selectProcess(myProcess)
    });

    _this.subProcessControl.valueChanges.subscribe(process => {
      console.log(process, this.subProcessList)
      const subprocessName = this.subProcessList.find((sub) => sub.externalId === process);
      console.log(subprocessName)
      if(subprocessName.name=='Supplier Audit'){
        _this.supplieraduitFlag=true
      }
      else{ 
        _this.supplieraduitFlag = false;}
      if(process){
        var childrenProcess = _this.commonService.processList.filter(e => {
          return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == process);
        })
        _this.categoryListAll = childrenProcess;
        _this.categoryList = childrenProcess.filter(e => e.isActive != false);
        _this.filteredCategoryList = _this.categoryList.slice();
  
        _this.categoryControl.setValue(null);
        _this.applyFilter();
        var myProcess = _this.commonService.processList.find(e => {
          return ( e.externalId == process);
        })
        _this.selectSubProcess(myProcess)
      }else{
        _this.categoryControl.setValue(null);
      }
      console.log(myProcess)
      _this.loaderFlag = true;
      _this.dataService.postData({"externalId":process}, _this.dataService.NODE_API+"/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
        _this.loaderFlag = false;
        if(resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"].length>0){
          var processConfig = resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
          const hardCodedConfig = _this.commonService.defaultConfigList.find(e => e.refProcess === myProcess.refOFWAProcess.name);
          if (hardCodedConfig) {
            const mergedConfig = _this.mergeConfigs(processConfig, hardCodedConfig);
            console.log(mergedConfig);
            _this.processConfig = mergedConfig;
          }
          else _this.processConfig = processConfig;
          console.log(_this.processConfig);
          console.log(_this.commonService.defaultConfigList.find(e => e.refProcess == myProcess.refOFWAProcess.name))
        }else{
          _this.processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == myProcess.refOFWAProcess.name)
        }
        console.log("processConfig");
        console.log( _this.processConfig);
        _this.configList = [];
        _this.dashboardConfigList = [];
        _this.columnConfigList = [];
        _this.CheckList = [];
        _this.CheckListContoller =[];
        _this.NotificationContoller =[];
        _this.notificationList = [];
        if(_this.commonService.notificationGroupData){
        this.notificationGroupData = _this.commonService.notificationGroupData.message.map((item) => ({
          name: item.name,
          externalId: item.externalId,
        }));}
        Object.entries(_this.processConfig.configDetail).forEach(([key, value]) => {
          if (key == "dateFormat") {
            _this.dateControl.setValue(value)
          }
          if (key == "timeFormat") {
            _this.timeControl.setValue(value)
          }
          console.log(_this.CheckListContoller)
          if (typeof value === "object" && value.hasOwnProperty('isEnabled') && !value.hasOwnProperty('displayName') && key=='checklistEnableValue') {
            _this.checklistEnableValue=value["isEnabled"]
            _this.CheckListContoller.push({
              key: key,
              isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null)
            })
          }
          if (typeof value === "object" && value.hasOwnProperty('isEnabled') && !value.hasOwnProperty('displayName') && key=='notificationEnableValue') {
            _this.notificationEnableValue=value["isEnabled"]
            _this.NotificationContoller.push({
              key: key,
              isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null)
            })
          }          
          if (typeof value === "object" && value.hasOwnProperty('displayName') && value.hasOwnProperty('isEnabled') && value.hasOwnProperty('isMandatory') && value.hasOwnProperty('field') && !value.hasOwnProperty('groupName')) {
            _this.configList.push({
                key: key,
                field: value["field"],
                displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
                isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null),
                isMandatoryControl: new FormControl(value.hasOwnProperty('isMandatory') ? value["isMandatory"] : null),
                translateKey: value["translateKey"]
              });
        }
        if (typeof value === "object" && value.hasOwnProperty('displayName') && value.hasOwnProperty('field') && value.hasOwnProperty('groupName')) {
          console.log(value["groupName"])
          _this.notificationList.push({
              key: key,
              field: value["field"],
              displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
              groupName: new FormControl(value.hasOwnProperty('groupName') ? value["groupName"] : null),
              translateKey: value["translateKey"]
            });
      }
        if (typeof value === "object" && value.hasOwnProperty('displayName') && value.hasOwnProperty('field') && value.hasOwnProperty('isDefault') && value.hasOwnProperty('isInjuryPotential') && !value.hasOwnProperty('groupName')) {
          _this.CheckList.push({
              key: key,
              field: value["field"],
              isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null),
              displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
              isDefaultControl: new FormControl(value.hasOwnProperty('isDefault') ? value["isDefault"] : null),
              isInjuryPotential: new FormControl(value.hasOwnProperty('isInjuryPotential') ? value["isInjuryPotential"] : null),
              isNotes: new FormControl(value.hasOwnProperty('isNotes') ? value["isNotes"] : null),
              isNotesMandatory: new FormControl(value.hasOwnProperty('isNotesMandatory') ? value["isNotesMandatory"] : null),
              translateKey: value["translateKey"]
            });
        }
        if (typeof value === "object" && value.hasOwnProperty('displayName') && value.hasOwnProperty('isEnabled') && value.hasOwnProperty('isMandatory') && value.hasOwnProperty('field') && !value.hasOwnProperty('groupName')) {
          if(key=="injuryPotential")
          {_this.CheckList.push({
              key: key,
              field: value["field"],
              displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
              isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null),
              isMandatoryControl: new FormControl(value.hasOwnProperty('isMandatory') ? value["isMandatory"] : null),
              isDefaultControl: new FormControl(value.hasOwnProperty('isDefault') ? value["isDefault"] : null),
                isInjuryPotential: new FormControl(value.hasOwnProperty('isInjuryPotential') ? value["isInjuryPotential"] : null),
                isNotes: new FormControl(value.hasOwnProperty('isNotes') ? value["isNotes"] : null),
                isNotesMandatory: new FormControl(value.hasOwnProperty('isNotesMandatory') ? value["isNotesMandatory"] : null),
                translateKey: value["translateKey"]
          });}
      }
        });
        if(_this.processConfig && _this.processConfig.dashboardConfig){
        Object.entries(_this.processConfig.dashboardConfig).forEach(([key, value]) => {
          if (key == "dateFormat") {
            _this.dateControl.setValue(value)
          }
          if (key == "timeFormat") {
            _this.timeControl.setValue(value)
          }
          if (typeof value === "object" && value.hasOwnProperty('displayName') && value.hasOwnProperty('isEnabled') && value.hasOwnProperty('field')) {
            _this.dashboardConfigList.push({
                key: key,
                field: value["field"],
                displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
                isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null),
                  translateKey: value["translateKey"]
              });
        }
        });}

        if(_this.processConfig && _this.processConfig.columnConfig){
        Object.entries(_this.processConfig.columnConfig).forEach(([key, value]) => {
          if (typeof value === "object" && value.hasOwnProperty('isEnabled') && value.hasOwnProperty('field')) {
            _this.columnConfigList.push({
                key: key,
                field: value["field"],
                sequence: value["sequence"],
                displayNameControl: new FormControl(value.hasOwnProperty('displayName') ? value["displayName"] : null),
                isEnabledControl: new FormControl(value.hasOwnProperty('isEnabled') ? value["isEnabled"] : null),
                sequenceControl: new FormControl(value.hasOwnProperty('sequence') ? value["sequence"] : null),
                  translateKey: value["translateKey"]
              });
        }
        });
      }

        _this.columnConfigList = _this.columnConfigList.sort(function (a, b) {
          if (a.sequence < b.sequence) { return -1; }
          if (a.sequence > b.sequence) { return 1; }
          return 0;
        })
        // _.each(_this.columnConfigList, function (eData) {
        //   eData.sequenceControl.valueChanges.subscribe((sequence) => {
        //     let isDuplicate = false;
        
        //     // Check if the sequence is duplicated in the columnConfigList
        //     _.each(_this.columnConfigList, function (eachData) {
        //       if (eachData !== eData && eachData.sequenceControl.value === sequence) {
        //         isDuplicate = true;
        //       }
        //     });
        
        //     // Set or clear the error based on the duplicate check
        //     if (isDuplicate) {
        //       eData.sequenceControl.setErrors({ isDuplicate: true });
        //       _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterAlreadyexists'] });
        //     } else {
        //       eData.sequenceControl.setErrors(null); // Clear the error
        //     }
        //   });
        // });
        // _.each(_this.columnConfigList, function (eData) {
        //   eData.sequenceControl.valueChanges.subscribe((sequence) => {
        //     // Check for duplicates and set errors for all controls accordingly
        //     const sequenceCounts = _.countBy(
        //       _this.columnConfigList.map((eachData) => eachData.sequenceControl.value)
        //     );
        
        //     _.each(_this.columnConfigList, function (eachData) {
        //       const currentSequence = eachData.sequenceControl.value;
        //       if (sequenceCounts[currentSequence] > 1) {
        //         eachData.sequenceControl.setErrors({ isDuplicate: true });
        //       } else {
        //         eachData.sequenceControl.setErrors(null);
        //       }
        //     });
        
        //     // Trigger a toast if there's a duplicate in the current control
        //     if (sequenceCounts[sequence] > 1) {
        //       _this.commonService.triggerToast({
        //         type: 'error',
        //         title: "",
        //         msg: _this.commonService.toasterLabelObject['toasterAlreadyexists']
        //       });
        //     }
        //   });
        // });
        // _.each(_this.columnConfigList, function (eData) {
        //   eData.sequenceControl.valueChanges.subscribe((sequence) => {
        //     // Check for duplicates considering only enabled controls
        //     const enabledData = _this.columnConfigList.filter(
        //       (eachData) => eachData.isEnabledControl.value === true
        //     );
        
        //     const sequenceCounts = _.countBy(
        //       enabledData.map((eachData) => eachData.sequenceControl.value)
        //     );
        
        //     _.each(_this.columnConfigList, function (eachData) {
        //       const currentSequence = eachData.sequenceControl.value;
        //       const isEnabled = eachData.isEnabledControl.value;
        
        //       if (isEnabled && sequenceCounts[currentSequence] > 1) {
        //         eachData.sequenceControl.setErrors({ isDuplicate: true });
        //       } else {
        //         eachData.sequenceControl.setErrors(null);
        //       }
        //     });
        
        //     // Trigger a toast if there's a duplicate and the current control is enabled
        //     const isCurrentEnabled = eData.isEnabledControl.value;
        //     if (isCurrentEnabled && sequenceCounts[sequence] > 1) {
        //       _this.commonService.triggerToast({
        //         type: 'error',
        //         title: "",
        //         msg: _this.commonService.toasterLabelObject['toasterAlreadyexists']
        //       });
        //     }
        //   });
        // });
        _.each(_this.columnConfigList, function (eData) {
          // Watch for changes in isEnabledControl
          eData.isEnabledControl.valueChanges.subscribe((isEnabled) => {
            if (!isEnabled) {
              // Set sequence value to null when isEnabled is false
              eData.sequenceControl.setValue(null, { emitEvent: false });
              eData.sequenceControl.setErrors(null); // Clear errors
            }
          });
        
          // Watch for changes in sequenceControl
          eData.sequenceControl.valueChanges.subscribe((sequence) => {
            // Filter enabled controls
            const enabledData = _this.columnConfigList.filter(
              (eachData) => eachData.isEnabledControl.value === true
            );
        
            // Count occurrences of each sequence value for enabled controls
            const sequenceCounts = _.countBy(
              enabledData.map((eachData) => eachData.sequenceControl.value)
            );
        
            _.each(_this.columnConfigList, function (eachData) {
              const currentSequence = eachData.sequenceControl.value;
              const isEnabled = eachData.isEnabledControl.value;
        
              if (isEnabled) {
                if (!currentSequence) {
                  // Set error if the sequence is empty for enabled controls
                  eachData.sequenceControl.setErrors({ isEmpty: true });
                } else if (sequenceCounts[currentSequence] > 1) {
                  // Set error if the sequence is a duplicate for enabled controls
                  eachData.sequenceControl.setErrors({ isDuplicate: true });
                } else {
                  // Clear errors if there are no issues
                  eachData.sequenceControl.setErrors(null);
                }
              } else {
                // Clear errors if the control is not enabled
                eachData.sequenceControl.setErrors(null);
              }
            });
        
            // Trigger a toast if there's a duplicate or empty value for the current enabled control
            const isCurrentEnabled = eData.isEnabledControl.value;
            if (isCurrentEnabled) {
               if (sequenceCounts[sequence] > 1 || !sequence) {
                _this.commonService.triggerToast({
                  type: 'error',
                  title: "",
                  msg: _this.commonService.toasterLabelObject['toasterAlreadyexists']
                });
              }
            }
          });
        });
        
        
        
        

        _this.dashboardConfigList = _this.dashboardConfigList.sort(function (a, b) {
          if (a.key < b.key) { return -1; }
          if (a.key > b.key) { return 1; }
          return 0;
        })

        _this.configList = _this.configList.sort(function (a, b) {
          if (a.key < b.key) { return -1; }
          if (a.key > b.key) { return 1; }
          return 0;
        })

        _this.fieldListInit();
        _this.fieldDefaultInit();
        _this.fieldNotesInit();
      // }else{
        
      // }
    })

      })
    _this.categoryControl.valueChanges.subscribe(process => {
      if (process) {
        var childrenProcess = _this.commonService.processList.filter(e => {
          return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == process);
        })
        _this.subCategoryListAll = childrenProcess
        _this.subCategoryList = childrenProcess.filter(e => e.isActive != false);
        _this.filteredSubCategoryList = _this.subCategoryList.slice();
        _this.subCategoryControl.setValue(null);
        var myProcess = _this.commonService.processList.find(e => {
          return (e.externalId == process);
        })
        _this.selectCategory(myProcess)
        _this.applyFilter();
      } else {
        _this.subCategoryListAll = [];
        _this.subCategoryList = [];
        _this.filteredSubCategoryList = _this.subCategoryList.slice();
        _this.subCategoryControl.setValue(null);
      }
    })
    _this.subCategoryControl.valueChanges.subscribe(process => {
      if (process) {
        var myProcess = _this.commonService.processList.find(e => {
          return (e.externalId == process);
        })
        _this.selectSubCategory(myProcess)
        _this.applyFilter();
      }
    })

    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == 'QuestionList') {
        console.log('e.data', e);
        console.log('e.data', e.data.data);
        if (e.data.action == 'FormView') {
          console.log('FormView-->');
          _this.viewQuestion("View",e.data.data)
          
        } else if (e.data.action == 'FormEdit') {
          console.log('FormEdit-->');
          _this.viewQuestion("Edit",e.data.data)
         
        }
      }
    };

   
    
  }
  
  onSelectionChange(selectedName: string) {
    const selectedItem = this.notificationGroupData.find(item => item.name === selectedName);
    if (selectedItem) {
      this.selectednotificationGroupData = selectedItem.externalId;
      console.log('Selected External ID:', this.selectednotificationGroupData);
    }
  }
 
  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }

  getSiteProcess(){
    var _this = this;
    _this.loaderFlag = true;
    var mySite = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
      _this.commonService.getProcessConfiguration(_this.siteControl.value, function (data) {
        _this.loaderFlag = false;
        if (mySite) {
          _this.loadCorePrinciples();
        }
      });
  }
  ngAfterViewInit(): void {
    var _this = this;
    _this.url = _this.dataService.React_API + '/questionList';
    this.setDateFormat();
    // _this.loaderFlag = false;
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FormConfig", "action": "Column", "data": _this.displayedColumns }, '*');
    iWindow?.postMessage({
      type: 'Language iWindow',
      action: 'Language',
      LanguageCode: `${this.commonService.selectedLanguage}`,
    });
    _this.getUserMenuConfig();
 
    var frame = document.getElementsByTagName('iframe');
    var frameWindow = frame[0].contentWindow;
    console.log('frameWindow line 438: ', frameWindow);
 
    frameWindow?.postMessage({
      type: 'Language',
      action: 'Language',
      LanguageCode: `${this.commonService.selectedLanguage}`,
    });
 
    console.log('after postmessage', frameWindow);
 
    _this.getUserMenuConfig();
  }

  fieldListInit(){
    var _this = this;
    _.each(_this.configList,function(eData){
      // console.log(eData)
      if(!eData.isEnabledControl.value){
        // eData.isMandatoryControl.setValue(false);
        eData.isMandatoryControl.disable();
      }
      eData.isEnabledControl.valueChanges.subscribe(value => {
        if(!value){
          eData.isMandatoryControl.setValue(false);
          eData.isMandatoryControl.disable();
        }else{
          eData.isMandatoryControl.enable();
        }
      })
      eData.isMandatoryControl.valueChanges.subscribe(value => {

      })

    })
  }
  fieldNotesInit(){
    var _this = this;
    _.each(_this.CheckList,function(eData){
      // console.log(eData)
      if(!eData.isInjuryPotential.value){
        eData.isNotes.setValue(false);
        // eData.isCheckedControl.disable();
      }
      eData.isInjuryPotential.valueChanges.subscribe(value => {
        if(!value){
          eData.isNotes.setValue(false);
        }
      })
      eData.isNotes.valueChanges.subscribe(value => {
        if(value){
          eData.isNotesMandatory.setValue(true);
        }else{
          eData.isNotesMandatory.setValue(false);
        }
      })

      if(!eData.isNotesMandatory || !eData.isNotesMandatory.value){
        eData.isNotesMandatory.setValue(false);
      }
    })
  }

  fieldDefaultInit(){
    var _this = this;
    _.each(_this.CheckList, function(eData){
        if(!eData.isDefaultControl.value){
            // eData.isInjuryPotential.setValue(false);
            // eData.isInjuryPotential.disable();
        }
        eData.isEnabledControl.valueChanges.subscribe(value => {
          if (!value) {
                eData.isDefaultControl.setValue(false, { emitEvent: false });
          }
        });
        eData.isDefaultControl.valueChanges.subscribe(value => {
            console.log(value)
            if(value){
                _.each(_this.CheckList, function(otherEData){
                    if(otherEData !== eData){
                        otherEData.isDefaultControl.setValue(false, { emitEvent: false });
                    }
                });
                // eData.isInjuryPotential.setValue(true);
                // eData.isInjuryPotential.enable();
            } else {
                // eData.isInjuryPotential.setValue(false);
                // eData.isInjuryPotential.disable();
            }
        });
    });
}

  selectTab(event:MatTabChangeEvent){
    var _this = this;
    this.tabGroup.selectedIndex = event.index;
   
    if(_this.subMenuList[event.index] == "processConfiguration"){
      console.log("processConfiguration")
    }else if(_this.subMenuList[event.index] == "checklistQuestions"){
      _this.applyFilter();
    }else if(_this.subMenuList[event.index] == "fieldConfiguration"){
      console.log(_this.subProcessControl.value)
      if(_this.subProcessControl.value && _this.subProcessControl.value.length>0){
        _this.subProcessControl.setValue(_this.subProcessControl.value)
      }
      
      // if(this.configList.length == 0){
      //   this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterNoconfig'] });
      // }
    }
    else if(_this.subMenuList[event.index] == "dashboardConfiguration"){
      console.log(_this.subProcessControl.value)
      if(_this.subProcessControl.value && _this.subProcessControl.value.length>0){
        _this.subProcessControl.setValue(_this.subProcessControl.value)
      }
      // if(this.dashboardConfigList.length == 0){
      //   this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterNodashboardconfig'] });
      // }
    }
    else if(_this.subMenuList[event.index] == "columnConfiguration"){
      if(_this.subProcessControl.value && _this.subProcessControl.value.length>0){
        _this.subProcessControl.setValue(_this.subProcessControl.value)
      }
      // if(this.columnConfigList.length == 0){
      //   this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['noColumnConfig'] });
      // }
    }
  }
 
  mergeConfigs(apiConfig: any, defaultConfig: any): any {
    // Recursively merge objects
    const mergeObjects = (target: any, source: any) => {
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null) {
            if (!target[key] || typeof target[key] !== 'object') {
              target[key] = Array.isArray(source[key]) ? [] : {};
            }
            mergeObjects(target[key], source[key]);
          } else if (target[key] === undefined) {
            target[key] = source[key];
          }
        }
      }
    };
  
    const result = { ...apiConfig };
    mergeObjects(result, defaultConfig);
    if (!apiConfig.dashboardConfig || Object.keys(apiConfig.dashboardConfig).length === 0) {
      result.dashboardConfig = { ...defaultConfig.dashboardConfig };
    }
  
    if (!apiConfig.columnConfig || Object.keys(apiConfig.columnConfig).length === 0) {
      result.columnConfig = { ...defaultConfig.columnConfig };
    }
  
    return result;
  }

  getUserMenuConfig() {
    var _this = this

    if (_this.siteControl.value != _this.dataService.siteId) {
      _this.siteControl.setValue(_this.dataService.siteId)
    }
    if (_this.commonService.menuFeatureUserIn.length > 0) {
      _this.configurationList = {}
      _this.configurationList["processConfiguration"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.processConfiguration);
      _this.configurationList["checklistQuestions"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.checklistQuestions);
      _this.configurationList["fieldConfiguration"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldConfiguration);
      _this.configurationList["dashboardConfiguration"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.dashboardConfiguration);
      _this.configurationList["columnConfiguration"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.columnConfiguration);
      var myArray = [];
      if(_this.configurationList["processConfiguration"]){
        myArray.push("processConfiguration")
      }
      if(_this.configurationList["checklistQuestions"]){
        myArray.push("checklistQuestions")
      }
      if(_this.configurationList["fieldConfiguration"]){
        myArray.push("fieldConfiguration")
      }
      if(_this.configurationList["dashboardConfiguration"]){
        myArray.push("dashboardConfiguration")
      }
      if(_this.configurationList["columnConfiguration"]){
        myArray.push("columnConfiguration")
      }
      _this.subMenuList = myArray;
      
    } else {
      _this.configurationList = {}
    }

  }


  loadCorePrinciples() {
    var _this = this;
   
    var corePrinciplesList = _this.commonService.processList.filter(e => {
      return e.processType == "Core Principles" && ((e.refSite && e.refSite.externalId) == _this.siteControl.value);
    })
    var myList = [];
    _.each(corePrinciplesList,function(eData){
      if(eData.name == "Stewardship"){
        eData["sequence"] = eData.sequence ? eData.sequence : 1;
      }else
      if(eData.name == "Quality"){
        eData["sequence"] = eData.sequence ? eData.sequence : 2;
      }else
      if(eData.name == "Reliability"){
        eData["sequence"] = eData.sequence ? eData.sequence : 2;
      }else{
        eData["sNo"] = 4;
      }
      myList.push(eData);
    })
     var processList = _.sortBy(myList, ['sequence']);
    _this.corePrinciplesList = processList;
    _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
    if(_this.corePrinciplesList.length == 0 ){
      _this.getSiteProcess();
    }
    _this.processList = [];
    _this.filteredProcessList = _this.processList.slice();
    _this.configList = [];
    _this.dashboardConfigList = [];
    _this.columnConfigList = [];
    _this.ifSite = true;
    _this.ifCorePrinciple = false;
    _this.ifProcess = false;
    _this.ifSubProcess = false;
    _this.ifCategory = false;
    _this.ifSubCategory = false;
    _this.selectedCorePrinciple = null;
    _this.selectedProcess = null;
    _this.selectedSubProcess = null;
    _this.selectedCategory = null;
    _this.selectedSubCategory = null;
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  selectCorePrinciple(item) {
    var _this = this;
    this.selectedCorePrinciple = item;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId) && ((e.refSite && e.refSite.externalId) == _this.siteControl.value);
    })
    _this.processList = childrenProcess;
    _this.ifCorePrinciple = true;
    _this.ifProcess = false;
    _this.ifSubProcess = false;
    _this.ifCategory = false;
    _this.ifSubCategory = false;
    _this.selectedProcess = null;
    _this.selectedSubProcess = null;
    _this.selectedCategory = null;
    _this.selectedSubCategory = null;
  }

  selectProcess(item) {
    var _this = this;
    this.selectedProcess = item;
  
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId);
    })
    console.log(item.externalId)
    console.log(childrenProcess)
    _this.subProcessListAll = childrenProcess;
    _this.subProcessList = childrenProcess.filter(e => e.isActive != false);;
    _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
    console.log(_this.subProcessList)
    _this.ifProcess = true;
    _this.ifSubProcess = false;
    _this.ifCategory = false;
    _this.ifSubCategory = false;
    _this.selectedSubProcess = null;
    _this.selectedCategory = null;
    _this.selectedSubCategory = null;
    _this.processControl.setValue(item.externalId)
    _this.getDataSetId();
  }

  getDataSetId() {
    var _this = this;
    var site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value)
    var objPost = { "items": [
        { "externalId": `${_this.commonService.configuration["AppCode"]}-${site.siteCode}-${_this.commonService.configuration["allUnitCode"]}-${_this.commonService.configuration["dataSpaceCode"]}`}
      ], "ignoreUnknownIds": false
    }
    _this.dataService.postData(objPost, _this.dataService.NODE_API + "/api/service/getDataSetId").subscribe((resData: any) => {
      if (resData && resData.items && resData.items.length > 0) {
        _this.dataSetImageId = resData.items[0].id
      }
    })
  }
  selectSubProcess(item) {
    var _this = this;
    console.log(item)
    if(item.isActive == false){
      return true;
    }
    _this.selectedSubProcess = item;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId) && ((e.refSite && e.refSite.externalId) == _this.siteControl.value);
    })
    _this.categoryListAll = childrenProcess;
    _this.categoryList = childrenProcess.filter(e => e.isActive != false);
    _this.categoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
    _this.ifSubProcess = true;
    _this.ifCategory = false;
    _this.ifSubCategory = false;
    _this.selectedCategory = null;
    _this.selectedSubCategory = null;
  };
  selectCategory(item) {
    var _this = this;
    if(item.isActive == false){
      return true;
    }
    _this.selectedCategory = item;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId) && ((e.refSite && e.refSite.externalId) == _this.siteControl.value);
    })
    _this.subCategoryListAll = childrenProcess;
    _this.subCategoryList = childrenProcess.filter(e => e.isActive != false);;
    _this.subCategoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
    console.log(_this.subCategoryList)
    _this.ifCategory = true;
    _this.ifSubCategory = false;
    _this.selectedSubCategory = null;
  }
  selectSubCategory(item) {
    var _this = this;
    if(item.isActive == false){
      return true;
    }
    _this.selectedSubCategory = item;
    _this.ifSubCategory = true;
  }

  goPage(page) {
    this.router.navigate([page]);
  }

  addSubProcess(obj) {
    var _this = this;
    console.log("obj")
    console.log(obj)
    console.log(_this.selectedProcess)
    console.log(_this.processList)
    var dataPass = {
      type: "subProcess",
      headername: "formcontrolsAddsubprocess",
      firstKey: "process",
      firstValue: _this.processList,
      selectedValue: _this.selectedProcess,
      list:_this.subProcessList,
      secondKey: "formcontrolsSubprocessname",
      secondPlaceholder: "subProcess"
    }
    if(_this.selectedProcess.name == "Observation"){
      dataPass["headername"] = "addobservationtype"
      dataPass["secondKey"] = "formcontrolsObservationtypename"
      dataPass["secondPlaceholder"] = "observationType"
    }else if(_this.selectedProcess.name == "Audit"){
      dataPass["headername"] = "formcontrolsAddaudittype"
      dataPass["secondKey"] = "formcontrolsAuditname"
      dataPass["secondPlaceholder"] = "audit"
    }
    
    dataPass = { ...dataPass, ...obj };
    var refOFWAProcess = {
      "externalId": _this.selectedProcess["externalId"],
      "space": _this.selectedProcess["space"]
    }
    _this.openPopupForm("Sub Process", dataPass, refOFWAProcess);
  }

  openPopupForm(processType, myObj, refOFWAProcess) {
    var _this = this;
    var dataPass = myObj
    const dialogRef = this.dialog.open(AddSubCategoryPopupComponent, {
      data: dataPass,
      height: '470px',
      width: '430px',
      panelClass: 'add-sub-category-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result) {
          _this.uploadImage(result,function(fileRes){
            if (myObj.externalId) {
              result["externalId"] = myObj.externalId;
            }
            if(fileRes){
              result["guidelineDocument"] = fileRes;
              result["guidelineDocumentId"] = fileRes.externalId;
            }
            _this.addProcessApi(processType, result, refOFWAProcess);
          })
      }
    });
  }

  async uploadImage(res, cb) {
    if(res.file){
      this.loaderFlag = true;
      const fileContent = res.file;
      const file = res.file;
      const buffer = await fileContent.arrayBuffer();
      const fileNameArray = file.name.split(".");
      var imgObj = { name: fileNameArray[0], mimeType: file.type,externalId: uuidv1()}
      if (this.dataSetImageId) {
        imgObj["dataSetId"] = this.dataSetImageId
      }
      const fileupload: any = await this.client.files.upload(imgObj, buffer);
      cb(fileupload)
    }else{
      cb(null)
    }
  }

  addCategory(obj) {
    var _this = this;
    var dataPass = {
      type: "category",
      headername: "addcategory",
      firstKey: "observationType",
      firstValue: this.subProcessList,
      list:_this.categoryList,
      selectedValue: this.selectedSubProcess,
      secondKey: "formcontrolsCategoryname",
      secondPlaceholder: "formcontrolsCategories"
    }
    dataPass = { ...dataPass, ...obj };
    var refOFWAProcess = {
      "externalId": _this.selectedSubProcess["externalId"],
      "space": _this.selectedSubProcess["space"]
    }
    _this.openPopupForm("Category", dataPass, refOFWAProcess);
  }


  addSubCategory(obj) {
    var _this = this;
    var dataPass = {
      type: "subCategory",
      headername: "addSubCategory",
      firstKey: "formcontrolsCategories",
      firstValue: this.categoryList,
      list:_this.subCategoryList,
      selectedValue: this.selectedCategory,
      secondKey: "formcontrolsSubCategoryName",
      secondPlaceholder: "formcontrolsSubcategories"
    }
    dataPass = { ...dataPass, ...obj };
    var refOFWAProcess = {
      "externalId": _this.selectedCategory["externalId"],
      "space": _this.selectedCategory["space"]
    }
    _this.openPopupForm("Sub Category", dataPass, refOFWAProcess);
  }
  onEdit(item) {
    var _this = this;

    if (item.processType == "Sub Process") {
      _this.addSubProcess(item)
    } else if (item.processType == "Category") {
      _this.addCategory(item)
    } else if (item.processType == "Sub Category") {
      _this.addSubCategory(item)
    }
  }

  onDisable(item, myFlag) {
    var _this = this;
    console.log(item);
    var myObj = {
      externalId: item.externalId,
      isActive: myFlag
    }
    var postObj = {
      "type": _this.commonService.configuration["typeProcess"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        myObj
      ]
    }
    _this.loaderFlag = true;
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        var finIndex = _this.commonService.processList.findIndex(e => e.externalId == myObj["externalId"])
        _this.commonService.processList[finIndex]["isActive"] = myObj["isActive"];
      }
      _this.loaderFlag = false;
    });
  }
  addProcessApi(processType, obj, refOFWAProcess) {

    var _this = this;
    _this.loaderFlag = true;
    var mySite = _this.commonService.siteList.find(e => this.siteControl.value == e.externalId);

    var myObj = {
      "name": obj.name,
      "sequence": obj.sequence ? parseInt(obj.sequence) : 0,
      "processType": processType,
      "description": obj.description,
      "refSite": {
        "externalId": mySite["externalId"],
        "space": mySite["space"]
      },
      "refOFWAProcess": {
        "externalId": refOFWAProcess["externalId"],
        "space": refOFWAProcess["space"]
      }
    };
    if (obj.externalId) {
      myObj["externalId"] = obj.externalId;
    }
    if (obj.guidelineDocumentId) {
      myObj["guidelineDocument"] = [obj.guidelineDocumentId];
    }
    var postObj = {
      "type": _this.commonService.configuration["typeProcess"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        myObj
      ]
    }
    var parent = _this.commonService.processList.find(e => e.externalId == refOFWAProcess["externalId"])
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        _this.loaderFlag = false;
        myObj["externalId"] = data["items"][0]["externalId"];
        myObj["space"] = data["items"][0]["space"];
        if(obj.guidelineDocumentId){
        myObj["guidelineDocument"] = [obj.guidelineDocument];
        }
        myObj["refOFWAProcess"] = parent;
        var finIndex = _this.commonService.processList.findIndex(e => e.externalId == myObj["externalId"])
        if (finIndex > -1) {
          _this.commonService.processList[finIndex]["name"] = myObj["name"];
          _this.commonService.processList[finIndex]["guidelineDocument"] = [obj["guidelineDocument"]]
          if (processType == "Sub Process") {
            var kIndex = _this.subProcessList.findIndex(e => e.externalId == myObj["externalId"])
            _this.subProcessList[kIndex]["name"] = myObj["name"];
            _this.subProcessList[kIndex]["description"] = myObj["description"];
            _this.subProcessList[kIndex]["sequence"] = myObj["sequence"];
            _this.subProcessList[kIndex]["guidelineDocument"] = [obj["guidelineDocument"]]
            _this.subProcessList[kIndex]["refOFWAProcess"] = parent
            _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));

          } else if (processType == "Category") {
            var kIndex = _this.categoryList.findIndex(e => e.externalId == myObj["externalId"])
            _this.categoryList[kIndex]["name"] = myObj["name"];
            _this.categoryList[kIndex]["sequence"] = myObj["sequence"];
            _this.categoryList[kIndex]["refOFWAProcess"] = parent
            _this.categoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
          } else if (processType == "Sub Category") {
            var kIndex = _this.subCategoryList.findIndex(e => e.externalId == myObj["externalId"])
            _this.subCategoryList[kIndex]["name"] = myObj["name"];
            _this.subCategoryList[kIndex]["sequence"] = myObj["sequence"];
            _this.subCategoryList[kIndex]["guidelineDocument"] = [obj["guidelineDocument"]]
            _this.subCategoryList[kIndex]["refOFWAProcess"] = parent
            _this.subCategoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
          }
        } else {
          _this.commonService.processList.push(myObj);
          if (processType == "Sub Process") {
            _this.subProcessList.push(myObj);
            _this.subProcessListAll.push(myObj);
            _this.subProcessListAll = _this.subProcessListAll.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
            _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
            _this.filteredSubProcessList = _this.subProcessList.slice();
          } else if (processType == "Category") {
            _this.categoryList.push(myObj);
            _this.categoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
          } else if (processType == "Sub Category") {
            _this.subCategoryList.push(myObj);
            _this.subCategoryList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
          }
        }
        setTimeout(function () {
          _this.changeDetector.detectChanges();
        }, 1000);
      }
    })
  }

  saveDashboard(){
    var _this = this;
    _this.loaderFlag = true;

    var myObj = {
      dashboardConfig:{}
    }

    if(_this.processConfig["externalId"]){
      myObj["externalId"] = _this.processConfig["externalId"]
    }
    var mySite = _this.commonService.siteList.find(e => this.siteControl.value == e.externalId);
    var subProcess = _this.commonService.processList.find(e => {
      return (e.externalId == _this.subProcessControl.value);
    })
    myObj["refSite"] = {
      "externalId": mySite["externalId"],
      "space": mySite["space"]
    }
    myObj["refOFWAProcess"] = {
      "externalId": subProcess["externalId"],
      "space": subProcess["space"]
    }
    var i=0;
    var a=0;
    var shouldPostData = true;
    Object.entries(_this.processConfig.dashboardConfig).forEach(([key, value]) => {

    if (typeof value == "object") {
      myObj["dashboardConfig"][key] = value;
      var findVal = _this.dashboardConfigList.find(e => e.key == key);
      if(findVal){
        myObj["dashboardConfig"][key]["displayName"] = findVal["displayNameControl"].value;
        myObj["dashboardConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
      }
     
    }
  });
  if (!_this.processConfig["externalId"]) {
    myObj["columnConfig"] = {};
    myObj["configDetail"] = {};
    Object.entries(_this.processConfig.columnConfig).forEach(([key, value]) => {

      if (typeof value == "object") {
        myObj["columnConfig"][key] = value;
        var findVal = _this.columnConfigList.find(e => e.key == key);
        if(findVal){
          myObj["columnConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
        }
       
      }
    });
    Object.entries(_this.processConfig.configDetail).forEach(([key, value]) => {
      if (key == "dateFormat") {
        if (_this.dateControl.value) {
          myObj["configDetail"][key] = _this.dateControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key == "timeFormat") {
        if (_this.timeControl.value) {
          myObj["configDetail"][key] = _this.timeControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key != "dateFormat" && key != "timeFormat") {
        if (typeof value == "string") {
          myObj["configDetail"][key] = value;
        }
      }

      if (typeof value == "object") {
        myObj["configDetail"][key] = value;
        var findVal = _this.configList.find(e => e.key == key);
        if(findVal){
          myObj["configDetail"][key]["displayName"] = findVal["displayNameControl"].value;
          myObj["configDetail"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          myObj["configDetail"][key]["isMandatory"] = findVal["isMandatoryControl"].value;
          // myObj["configDetail"][key]["translateKey"] = findVal["isMandatoryControl"].value;
        }
        var findVal1 = _this.CheckList.find(e => e.key == key);
        
        if(findVal1){
          if (findVal1["isEnabledControl"].value){
            a++
          }
          i++
          myObj["configDetail"][key]["isEnabled"] = findVal1["isEnabledControl"].value;
          myObj["configDetail"][key]["displayName"] = findVal1["displayNameControl"].value;
          myObj["configDetail"][key]["isDefault"] = findVal1["isDefaultControl"].value;
          myObj["configDetail"][key]["isInjuryPotential"] = findVal1["isInjuryPotential"].value;
          myObj["configDetail"][key]["isNotes"] = findVal1["isNotes"].value;
          myObj["configDetail"][key]["isNotesMandatory"] = findVal1["isNotesMandatory"].value;

          if(i==3 && a<1 && this.selectedProcess.name !== 'Field Walk'){
            console.log("hello")
            shouldPostData = false;
            _this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: _this.commonService.toasterLabelObject['toasterEnablechecklist'] });
            i=0;
            a=0
          }
        }
       
      }
    });
  }
  if (!shouldPostData) {
    _this.loaderFlag = false;  // Reset the loader flag if the condition is met
    return;
}
  var postObj = {
    "type": _this.commonService.configuration["typeProcessConfiguration"],
    "siteCode": _this.commonService.configuration["allSiteCode"],
    "unitCode": _this.commonService.configuration["allUnitCode"],
    "items": [
      myObj
    ]
  }
  console.log("postObj")
  console.log(postObj)
  _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
    _this.loaderFlag = true;
      _this.dataService.postData({"externalId":process}, _this.dataService.NODE_API+"/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
        if(resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"].length>0){
          var processConfig = resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
          _this.processConfig = processConfig;
        }
      _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })
  })
  }

  saveColumnconfig(){
    var _this = this;
    let isDuplicate = false;
    // Check if the sequence is duplicated in the columnConfigList
    // _.each(_this.columnConfigList, function (eachData) {
    //   if (!eachData.sequenceControl.valid) {
    //     isDuplicate = true;
    //   }
    // });
    // _.each(_this.columnConfigList, function (eachData) {
    //   const isEnabled = eachData.isEnabledControl.value;
    //   const sequence = eachData.sequenceControl.value;
    
    //   if (isEnabled) {
    //     if (!sequence || sequence === "") {
    //       // If enabled and sequence is null or empty, mark as duplicate
    //       eachData.sequenceControl.setErrors({ isEmpty: true });
    //       isDuplicate = true;
    //     } else if (!eachData.sequenceControl.valid) {
    //       // Check if there are any other validation errors
    //       isDuplicate = true;
    //     }
    //   }
    // });
    _.each(_this.columnConfigList, function (eachData) {
      const isEnabled = eachData.isEnabledControl.value;
      const sequence = eachData.sequenceControl.value;
    
      if (isEnabled) {
        if (!sequence || sequence === "") {
          // Set error for empty values when enabled
          eachData.sequenceControl.setErrors({ isDuplicate: true });
          isDuplicate = true;
        } else if (!eachData.sequenceControl.valid) {
          // Check for other validation errors
          isDuplicate = true;
        }
      } else {
        // Clear errors when not enabled
        eachData.sequenceControl.setErrors(null);
      }
    });
    
    // Ensure the UI reflects the errors
    _this.columnConfigList.forEach((eachData) => eachData.sequenceControl.markAsTouched());
    

    if(isDuplicate){
      return false;
    }

    _this.loaderFlag = true;

    var myObj = {
      columnConfig:{}
    }
    if(_this.processConfig["externalId"]){
      myObj["externalId"] = _this.processConfig["externalId"]
    }
    var mySite = _this.commonService.siteList.find(e => this.siteControl.value == e.externalId);
    var subProcess = _this.commonService.processList.find(e => {
      return (e.externalId == _this.subProcessControl.value);
    })
    myObj["refSite"] = {
      "externalId": mySite["externalId"],
      "space": mySite["space"]
    }
    myObj["refOFWAProcess"] = {
      "externalId": subProcess["externalId"],
      "space": subProcess["space"]
    }

    var i=0;
    var a=0;
    var shouldPostData = true;
    Object.entries(_this.processConfig.columnConfig).forEach(([key, value]) => {

    if (typeof value == "object") {
      myObj["columnConfig"][key] = value;
      var findVal = _this.columnConfigList.find(e => e.key == key);
      if(findVal){
        myObj["columnConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
        myObj["columnConfig"][key]["sequence"] = findVal["isEnabledControl"].value?(findVal["sequenceControl"].value ? parseInt(findVal["sequenceControl"].value) : parseInt(value["sequence"])):"";
      }
     
    }
  });
  if (!_this.processConfig["externalId"]) {
    myObj["dashboardConfig"] = {};
    myObj["configDetail"] = {};
    Object.entries(_this.processConfig.dashboardConfig).forEach(([key, value]) => {
      if (typeof value == "object") {
        myObj["dashboardConfig"][key] = value;
        var findVal = _this.dashboardConfigList.find(e => e.key == key);
        if (findVal) {
          myObj["dashboardConfig"][key]["displayName"] = findVal["displayNameControl"].value;
          myObj["dashboardConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
        }
      }
    });
    Object.entries(_this.processConfig.configDetail).forEach(([key, value]) => {
      if (key == "dateFormat") {
        if (_this.dateControl.value) {
          myObj["configDetail"][key] = _this.dateControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key == "timeFormat") {
        if (_this.timeControl.value) {
          myObj["configDetail"][key] = _this.timeControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key != "dateFormat" && key != "timeFormat") {
        if (typeof value == "string") {
          myObj["configDetail"][key] = value;
        }
      }

      if (typeof value == "object") {
        myObj["configDetail"][key] = value;
        var findVal = _this.configList.find(e => e.key == key);
        if(findVal){
          myObj["configDetail"][key]["displayName"] = findVal["displayNameControl"].value;
          myObj["configDetail"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          myObj["configDetail"][key]["isMandatory"] = findVal["isMandatoryControl"].value;
          // myObj["configDetail"][key]["translateKey"] = findVal["isMandatoryControl"].value;
        }
        var findVal1 = _this.CheckList.find(e => e.key == key);
        
        if(findVal1){
          if (findVal1["isEnabledControl"].value){
            a++
          }
          i++
          myObj["configDetail"][key]["isEnabled"] = findVal1["isEnabledControl"].value;
          myObj["configDetail"][key]["displayName"] = findVal1["displayNameControl"].value;
          myObj["configDetail"][key]["isDefault"] = findVal1["isDefaultControl"].value;
          myObj["configDetail"][key]["isInjuryPotential"] = findVal1["isInjuryPotential"].value;
          myObj["configDetail"][key]["isNotes"] = findVal1["isNotes"].value;
          myObj["configDetail"][key]["isNotesMandatory"] = findVal1["isNotesMandatory"].value;

          if(i==3 && a<1 && this.selectedProcess.name !== 'Field Walk'){
            console.log("hello")
            shouldPostData = false;
            _this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: _this.commonService.toasterLabelObject['toasterEnablechecklist'] });
            i=0;
            a=0
          }
        }
       
      }
    });
  }
  if (!shouldPostData) {
    _this.loaderFlag = false;  // Reset the loader flag if the condition is met
    return;
}
  var postObj = {
    "type": _this.commonService.configuration["typeProcessConfiguration"],
    "siteCode": _this.commonService.configuration["allSiteCode"],
    "unitCode": _this.commonService.configuration["allUnitCode"],
    "items": [
      myObj
    ]
  }
  console.log("postObj")
  console.log(postObj)
  _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
    _this.loaderFlag = true;
      _this.dataService.postData({"externalId":process}, _this.dataService.NODE_API+"/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
        _this.loaderFlag = false;
        if(resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"].length>0){
          var processConfig = resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
          _this.processConfig = processConfig;
        }
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })
  })
  }

  saveConfig() {
    var _this = this;
    _this.loaderFlag = true;
    console.log(_this.dateControl.value)
    console.log(_this.CheckList)
    console.log(_this.configList)
    
    var myObj = {
      configDetail:{}
    }
    if(_this.processConfig["externalId"]){
      myObj["externalId"] = _this.processConfig["externalId"]
    }
    var mySite = _this.commonService.siteList.find(e => this.siteControl.value == e.externalId);
    var subProcess = _this.commonService.processList.find(e => {
      return (e.externalId == _this.subProcessControl.value);
    })
    myObj["refSite"] = {
      "externalId": mySite["externalId"],
      "space": mySite["space"]
    }
    myObj["refOFWAProcess"] = {
      "externalId": subProcess["externalId"],
      "space": subProcess["space"]
    }
    var i=0;
    var a=0;
    var shouldPostData = true;
    Object.entries(_this.processConfig.configDetail).forEach(([key, value]) => {
      if (key == "dateFormat") {
        if (_this.dateControl.value) {
          myObj["configDetail"][key] = _this.dateControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key == "timeFormat") {
        if (_this.timeControl.value) {
          myObj["configDetail"][key] = _this.timeControl.value;
        } else {
          myObj["configDetail"][key] = value;
        }
      }
      if (key != "dateFormat" && key != "timeFormat") {
        if (typeof value == "string") {
          myObj["configDetail"][key] = value;
        }
      }

      if (typeof value == "object") {
        myObj["configDetail"][key] = value;
        var findVal = _this.configList.find(e => e.key == key);
        if(findVal){
          myObj["configDetail"][key]["displayName"] = findVal["displayNameControl"].value;
          myObj["configDetail"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          myObj["configDetail"][key]["isMandatory"] = findVal["isMandatoryControl"].value;
          // myObj["configDetail"][key]["translateKey"] = findVal["isMandatoryControl"].value;
        }
        var findVal1 = _this.CheckList.find(e => e.key == key);
        
        if(findVal1){
          if (findVal1["isEnabledControl"].value){
            a++
          }
          i++
          myObj["configDetail"][key]["isEnabled"] = findVal1["isEnabledControl"].value;
          myObj["configDetail"][key]["displayName"] = findVal1["displayNameControl"].value;
          myObj["configDetail"][key]["isDefault"] = findVal1["isDefaultControl"].value;
          myObj["configDetail"][key]["isInjuryPotential"] = findVal1["isInjuryPotential"].value;
          myObj["configDetail"][key]["isNotes"] = findVal1["isNotes"].value;
          myObj["configDetail"][key]["isNotesMandatory"] = findVal1["isNotesMandatory"].value;

          if(i==3 && a<1 && this.selectedProcess.name !== 'Field Walk'){
            console.log("hello")
            shouldPostData = false;
            _this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: _this.commonService.toasterLabelObject['toasterEnablechecklist'] });
            i=0;
            a=0
          }
        }
        console.log(_this.CheckListContoller)
        var findVal2 = _this.CheckListContoller.find(e => e.key == key);
        if(findVal2){
          if (findVal2["isEnabledControl"].value){
            a++
          }
          i++
          myObj["configDetail"][key]["isEnabled"] = _this.checklistEnableValue;
          console.log("checklistEnableValue",_this.checklistEnableValue)

          if(i==3 && a<1 && this.selectedProcess.name !== 'Field Walk'){
            console.log("hello")
            shouldPostData = false;
            _this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: _this.commonService.toasterLabelObject['toasterEnablechecklist'] });
            i=0;
            a=0
          }
        }
        var findVal3 = _this.notificationList.find(e => e.key == key);
        if(findVal3){
          myObj["configDetail"][key]["groupName"] = this.selectednotificationGroupData;
        }

        var findVal4 = _this.NotificationContoller.find(e => e.key == key);
        if(findVal4){
          if (findVal4["isEnabledControl"].value){
            a++
          }
          i++
          myObj["configDetail"][key]["isEnabled"] = _this.notificationEnableValue;
          console.log("notificationEnableValue",_this.notificationEnableValue)

          if(i==3 && a<1 && this.selectedProcess.name !== 'Field Walk'){
            console.log("hello")
            shouldPostData = false;
            _this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: _this.commonService.toasterLabelObject['toasterEnablechecklist'] });
            i=0;
            a=0
          }
        }

       
      }
    });
    if (!_this.processConfig["externalId"]) {
      myObj["dashboardConfig"] = {};
      myObj["columnConfig"] = {};
      Object.entries(_this.processConfig.dashboardConfig).forEach(([key, value]) => {
        if (typeof value == "object") {
          myObj["dashboardConfig"][key] = value;
          var findVal = _this.dashboardConfigList.find(e => e.key == key);
          if (findVal) {
            myObj["dashboardConfig"][key]["displayName"] = findVal["displayNameControl"].value;
            myObj["dashboardConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          }
        }
      });
      Object.entries(_this.processConfig.columnConfig).forEach(([key, value]) => {
        if (typeof value == "object") {
          myObj["columnConfig"][key] = value;
          var findVal = _this.columnConfigList.find(e => e.key == key);
          if (findVal) {
            myObj["columnConfig"][key]["isEnabled"] = findVal["isEnabledControl"].value;
          }
        }
      });
    }
    if (!shouldPostData) {
      _this.loaderFlag = false;  // Reset the loader flag if the condition is met
      return;
  }
    var postObj = {
      "type": _this.commonService.configuration["typeProcessConfiguration"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        myObj
      ]
    }
    console.log("postObj")
    console.log(postObj)
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = true;
      _this.dataService.postData({"externalId":process}, _this.dataService.NODE_API+"/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
        if(resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"].length>0){
          var processConfig = resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
          _this.processConfig = processConfig;
        }
      _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })
  })
  }
  formConfig() {
    var _this = this;

    this.router.navigateByUrl('configuration/form-list', {
      state: {}
    });

  }

  configListPage() {
    this.router.navigateByUrl('configuration/config-list', {
      state: {
        category: this.selectedCategory,
        subCategory: this.selectedSubCategory
      }
    });
  }

  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result == 'object') {
        this.setColumn(result);
      }
    });
  }


  setColumn(selColumn) {
    var _this = this;
    _this.allColumns = [];
    _this.displayedColumns = [];
    var i = 0;
    selColumn.forEach((element) => {
      i++;
      _this.allColumns.push(element);
      if (element.activeFlag) {
        _this.displayedColumns.push(element.name);
      }
    });

    console.log('_this.displayedColumns,', _this.displayedColumns);
    setTimeout(function () {
      _this.ngAfterViewInit();
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);
  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) {
        myColumns.push(eData);
      }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };
    dialogConfig.height = 'auto';
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe((result) => {
    //   if (typeof result == 'object') {
    //     this.setSummary();
    //   }
    // });
  }
  setSummary() {
    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          enable: 'Y',
          colorRange: eData['summaryColor'] ? eData['summaryColor'] : [],
        };
      }
    });
    console.log('summaryCol', summaryCol);
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { 
        type: 'AuthToken', 
        data: _this.tokenService.getToken(),
        LanguageCode:_this.commonService.selectedLanguage.toUpperCase(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
    iWindow?.postMessage(
      { type: 'FormConfig', action: 'Summary', data: summaryCol },
      '*'
    );
    _this.getUserMenuConfig();
  }

  viewQuestion(type,data){
    var _this = this;
    var process = _this.commonService.processList.find(e => e.externalId == data.subProcessExternalId )
    var subCategory = _this.commonService.processList.find(e => e.externalId == data.subCategoryExternalId )
    var category = _this.commonService.processList.find(e => e.externalId == data.categoryExternalId )
    var dataPass = {
      formType:type,
      externalId:data.externalId,
      name:data.name,
      description:data.description,
      sequence:data.sequence,
      title: subCategory ? subCategory.name : (category ? category.name : process.name),
      category : category,
      subCategory : subCategory,
      process : process
    }
    const dialogRef = this.dialog.open(AddQuestionsComponent, {
      data: dataPass,
      height: '450px',
      width: '450px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result ) {
        _this.applyFilter();
      }
    });
  }
  addQuestion() {
    var _this = this;
    console.log(_this.selectedSubProcess.name)

    var dataPass = {
      formType:"Add",
      // title: _this.selectedSubCategory ? _this.selectedSubCategory.name : _this.selectedCategory.name,
      title: _this.selectedSubCategory ? _this.selectedSubCategory.name : (_this.selectedCategory ? _this.selectedCategory.name : _this.selectedSubProcess.name),
      process : _this.selectedSubProcess,
      category : _this.selectedCategory,
      subCategory : _this.selectedSubCategory
    }
    const dialogRef = this.dialog.open(AddQuestionsComponent, {
      data: dataPass,
      height: '450px',
      width: '450px',
      panelClass: 'add-craft-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed', result);
      if (result ) {
        _this.applyFilter();
      }
    });

    // this.router.navigateByUrl('configuration/add-question', {
    //   state: { config: this.configListRow, formType: 'Add' },
    // });
  }

  applyFilter() {
    var _this = this;
    setTimeout(function () {
    var iframe = document.getElementById('iFrameFormConfig');
    console.log('iframe', iframe);
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { 
        type: 'AuthToken', 
        data: _this.tokenService.getToken(),
        LanguageCode:_this.commonService.selectedLanguage.toUpperCase(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${_this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: _this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'FormConfig',
        action: 'Filter',
        site: _this.siteControl.value,
        process: _this.selectedSubProcess? _this.selectedSubProcess.externalId : null,
        category: _this.selectedCategory ? _this.selectedCategory.externalId : null,
        subCategory: _this.selectedSubCategory ? _this.selectedSubCategory.externalId :null,
        LanguageCode: `${_this.commonService.selectedLanguage.toUpperCase()}`,
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
    console.log('applyFilter');
    _this.getUserMenuConfig();
    
      _this.loaderFlag = false;
    }, 2000);
  }
  isPopupVisible: boolean = false;
  checkList: any[] = []; // Store checklist items
  isEditPopupVisible: boolean = false; // Edit/New popup visibility
  currentItem: any = { value: '' }; // Current item being edited/added
  isEditing: boolean = false; // Whether editing or adding new
  supplieraduitFlag: boolean = false;
  evidenceQuestion(data: any) {

    const subprocessName = this.subProcessList.find((sub) => sub.externalId === data.value);
    if (subprocessName.name === 'Supplier Audit') {
      this.loaderFlag = true;
      this.dataService
        .postData({ name: 'EvidenceChecklist' }, this.dataService.NODE_API + '/api/service/listCommonRefEnum')
        .subscribe((resData: any) => {
          this.checkList =
            resData?.data?.['list' + this.commonService.configuration['typeCommonRefEnum']]?.items || [];
          this.openPopup();
          
      this.loaderFlag = false;
        });
    } else {
      console.log('Evidence Checklist only works for Supplier Audit');
    }
  }

  openPopup() {
    this.isPopupVisible = true;
  }

  closePopup() {
    this.isPopupVisible = false;
  }

  openEditPopup(item?: any) {
    this.isEditing = !!item; // Determine if editing or adding new
    this.currentItem = item ? { ...item } : { value: '' }; // Set current item for editing or initialize for new
    this.isEditPopupVisible = true;
  }
  saveItem() {
    var newdata={}
    if (this.isEditing) {
      // Update the item in the checklist
      const index = this.checkList.findIndex((i) => i.externalId === this.currentItem.externalId);
      if (index !== -1) {
        this.checkList[index] = { ...this.currentItem };
        newdata = {
          externalId: this.currentItem.externalId,
          value: this.currentItem.value,
          name: 'EvidenceChecklist',
        };
        console.log("hii1")
      }
    } else if (this.currentItem.value.length>0) {
      newdata = {
        value: this.currentItem.value,
        name: 'EvidenceChecklist',
      };
      this.checkList.push(newdata);
    }
    const postObj = {
      "type": this.commonService.configuration["typeCommonRefEnum"],
      "siteCode":this.commonService.configuration["allSiteCode"],
      "unitCode": this.commonService.configuration["allUnitCode"],
      "items": [newdata]
    };
    // Add a new item to the checklist
    if (this.currentItem.value.length>0) {

    this.loaderFlag = true;
    this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      
      this.loaderFlag = false;
      this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
    })}
    else{
      this.commonService.triggerToast({ type: 'error', title: "Saving Failed", msg: this.commonService.toasterLabelObject['toasterNoDataToSave'] });
    }
      this.closeEditPopup();

    
    
  }
  closeEditPopup() {
    this.isEditPopupVisible = false;
  }


  setDateFormat() {
    console.log('this.commonService.dateFormat observaation list')
    console.log(this.commonService.dateFormat)
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }a
}
