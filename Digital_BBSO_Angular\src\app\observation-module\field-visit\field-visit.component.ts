import { ChangeDetectorRef, Component, OnInit,Input, Sanitizer, Inject, NgZone, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Observable } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { TokenService } from 'src/app/services/token.service';
import { CogniteAuthentication, CogniteClient } from '@cognite/sdk';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatDateFormats } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';


export interface ImageItem {
  image: SafeUrl; 
}

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-field-visit',
  templateUrl: './field-visit.component.html',
  styleUrls: ['./field-visit.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},
  ],
})
export class FieldVisitComponent implements OnInit {
  fieldForm: FormGroup;
  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");
  startDateControl: FormControl = new FormControl(new Date());

  labels = {}
  reportingLocationControl: FormControl = new FormControl("");
  reportingLocationList = [];
  filteredReportingLocationList = this.reportingLocationList.slice();


  maxImages: number = 50;
  maxMedia = 50;
  showOverlayIndex: number | null = null;
  viewedImageIndex: number | null = null;
  showModal = false;
  modalImage = '';
  evidenceObj: any[] = [];
  images: ImageItem[] = [];
  videos: any[] = []; 
  viewedVideoIndex: number | null = null;
  langChangeSubscription: Subscription;

  dropdownSettings: IDropdownSettings = {
    "singleSelection": false,
    "defaultOpen": false,
    "idField": "externalId",
    "textField": "nameDesc",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };

  dropdownSettingsWorkOrderNumber: IDropdownSettings = {
    "singleSelection": true,
    "defaultOpen": false,
    "idField": "externalId",
    "textField": "number",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };

  assetsList: any;
  assetsListDeSelect: any

  selectedRegion: any;
  selectedCountry: any;
  selectedSite: any;
  process: any;
  imageSrc: SafeUrl;
  fileUP: any;
  client: CogniteClient;
  obRes: any;
  editData: any;
  loaderFlag: boolean;
  isView = false;

  attendeeList = [];
  filteredattendeeList = this.attendeeList.slice();
  fl_searchVal: any;
  configDetail: any;
  selectedAttendee: any = [];
  editSelectedAttendee: any =[];
  editEquipment: any =[];
  dataSetImageId: any;
  matchingUnits: any;
  matchingUnitList: any[];
  filteredUnitList1: any[];
  filteredUnitList: any[];
  contractorList = [];
  filteredContractorList = this.contractorList.slice();
  workOrderNumberList: any;
  filteredWorkOrderNumberList: any;
  processConfig: any;

  constructor(private sanitizer: DomSanitizer,  private cdRef: ChangeDetectorRef,private tokenService: TokenService, private router: Router, public dataService: DataService, private fb: FormBuilder, private commonService: CommonService, private translate: TranslateService,private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string, private languageService: LanguageService, private ngZone: NgZone) {

    var _this = this;
    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'formcontrolsUploadphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsUploadphotos'] || 'formcontrolsUploadphotos',
        'formcontrolsMaxphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMaxphotos'] || 'formcontrolsMaxphotos',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
        'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
        'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
        'commonfilterChooseassets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassets'] || 'commonfilterChooseassets',
        'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
        'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'] || 'buttonCreatefieldwalk',
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
        'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'commonfilterNodata': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'] || 'commonfilterNodata',
        'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
        'attendees': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'attendees'] || 'attendees',
        'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
        'whatWentWell': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatWentWell'] || 'whatWentWell',
        'whatNeedsAttention': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatNeedsAttention'] || 'whatNeedsAttention',
        'whatDidYouDoAboutIt': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatDidYouDoAboutIt'] || 'whatDidYouDoAboutIt',
        'overallSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'overallSummary'] || 'overallSummary',
        'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
        'operationalLearningTooltip': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'] || 'operationalLearningTooltip',
        'chooseWorkOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
      'commonfilterChooseworkorder': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseworkorder'] || 'Choose Work Order',
      'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
        }
      _this.dropdownSettings.searchPlaceholderText = _this.labels['commonfilterSearch']
      _this.dropdownSettings.noDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
      _this.dropdownSettings.noFilteredDataAvailablePlaceholderText = _this.labels['commonfilterNodata']
    }
    


    var currentRoute: any = this.router.getCurrentNavigation().extras.state;
    console.log('currentRoute', currentRoute)
    //  currentRoute.fieldVisit
    _this.loaderFlag = true;
    this.setDateFormat();
    _this.getInialTokel(tokenService.getToken());

    if (history.state.action == "View") {
      _this.isView = true;
    }

    
    _this.hourFormat = _this.commonService.configuration["hourFormat"];
  }
  unitList = [];

  hourFormat = "";
  afterSettingInit(){
    var _this=this
    console.log('_this.commonService.userUnitList',_this.commonService.userUnitList)
    let details=_this.commonService.userUnitList
    _this.matchingUnits = this.unitList.filter(unit =>
 details.some(detailsUnit => detailsUnit.unitCode === unit.externalId)
);

    _this.resetSelectionWorkOrderNumber();
    if(_this.matchingUnits.length==0){
      _this.matchingUnits = this.unitList;
    }
    console.log('_this.matchingUnits',_this.matchingUnits)
    if (_this.matchingUnits.length > 0) {
      if(this.isView){
        _this.unitControl.disable();  
        _this.filteredUnitList1=_this.unitList
      _this.matchingUnitList=_this.unitList
      }
      // else if(_this.observation && _this.observation.refUnit && _this.observation.refUnit.externalId){
      //   _this.unitControl.setValue(_this.observation.refUnit.externalId)
      //   _this.filteredUnitList1=_this.unitList
      // _this.matchingUnitList=_this.unitList
      // }
      else {
        if(!history.state.fieldwalk)
        _this.unitControl.setValue(_this.matchingUnits[0].externalId);
      _this.filteredUnitList1=_this.unitList
      _this.matchingUnitList=_this.unitList
    }
    } 
    else {
    //   _this.unitControl.setValue(_this.unitList[0].externalId);
    _this.filteredUnitList1=_this.unitList
      _this.matchingUnitList=_this.unitList
    }
  }

  testFun(){
    console.log(document.querySelectorAll("section"))
   }
   
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredUnitList1 = _this.matchingUnitList.slice();
    }
    // if(_this.observation){
    //   if(_this.matchingUnits.length==0){
    //     _this.matchingUnits = this.unitList;
    //   }
    //   _this.filteredUnitList1=this.unitList
    //   _this.matchingUnitList=this.unitList
    // }
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'formcontrolsUploadphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsUploadphotos'] || 'formcontrolsUploadphotos',
          'formcontrolsMaxphotos': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsMaxphotos'] || 'formcontrolsMaxphotos',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
          'startTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'] || 'startTime',
          'endTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'] || 'endTime',
          'assets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'assets'] || 'assets',
          'commonfilterChooseassets': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassets'] || 'commonfilterChooseassets',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
          'buttonOk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'] || 'buttonOk',
          'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'] || 'buttonCreatefieldwalk',
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
          'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'commonfilterNodata': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'] || 'commonfilterNodata',
          'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
          'attendees': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'attendees'] || 'attendees',
          'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
          'whatWentWell': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatWentWell'] || 'whatWentWell',
          'whatNeedsAttention': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatNeedsAttention'] || 'whatNeedsAttention',
          'whatDidYouDoAboutIt': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whatDidYouDoAboutIt'] || 'whatDidYouDoAboutIt',
          'overallSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'overallSummary'] || 'overallSummary',
          'signature': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'] || 'signature',
          'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
          'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
          'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'] || 'operationalLearningDescription',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
        'operationalLearningTooltip': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'] || 'operationalLearningTooltip',
        'chooseWorkOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'] || 'doYouHaveAnyOpportunityForOperationalLearning',
          'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'] || 'describeTheOperationalLearningOpportunitiesYouFound',
        'commonfilterChooseworkorder': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseworkorder'] || 'Choose Work Order',
        'title': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'title'] || 'title',
        }
        _this.dropdownSettings= {
          "singleSelection": false,
          "defaultOpen": false,
          "idField": "externalId",
          "textField": "nameDesc",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
        };
        _this.dropdownSettingsWorkOrderNumber= {
          "singleSelection": true,
          "defaultOpen": false,
          "idField": "externalId",
          "textField": "number",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
        };
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })

    
    setTimeout(() => {
      const temp = document.getElementsByClassName('timepicker-button')
      // console.log('incident temp', temp.getElementsByTagName('td'),temp.getElementsByTagName('td')[0].innerHTML)
      // temp.getElementsByTagName('td')[1].innerHTML = 'New cancel'
      // console.log('incident temp after innerhtml', temp.getElementsByTagName('td'),temp.getElementsByTagName('td')[0].innerHTML)
      console.log('incident temp', temp)
    }, 5000)

    _this.process = history.state.process;
    if(!_this.process){
      _this.backClick();
    }
    _this.dataService.postData({ "externalId": history.state.subProcess.externalId }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
      
      if(resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"].length>0){
        var processConfig = resData["data"]["list"+_this.commonService.configuration["typeProcessConfiguration"]]["items"][0];
        
        const hardCodedConfig = _this.commonService.defaultConfigList.find(e => e.refProcess === _this.process.name);
          if (hardCodedConfig) {
            const mergedConfig = _this.mergeConfigs(processConfig, hardCodedConfig);
            console.log(mergedConfig);
            _this.processConfig = mergedConfig;
          }
          _this.processConfig = processConfig;
          _this.configDetail = processConfig["configDetail"];
      }else{
        _this.configDetail = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.process.name)["configDetail"]
      }
console.log(_this.configDetail)
      // if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
      //   var processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
      const currentTime = new Date();
      const formattedTime = this.formatTime(currentTime);
        
        var configDetail = _this.configDetail;
        _this.fieldForm = this.fb.group({
          locationObserve: [{ value: '', disabled: _this.isView },configDetail && configDetail.locationObserve.isMandatory ? Validators.required : undefined],
          unit: [{ value: '', disabled: _this.isView }, configDetail && configDetail?.unit?.isMandatory ? Validators.required : undefined],
          title: [{ value: '', disabled: _this.isView }, configDetail && configDetail.title.isMandatory ? Validators.required : undefined],
          attendee: [{ value: '', disabled: _this.isView }, configDetail && configDetail.attendee.isMandatory ? Validators.required : undefined],
          datetime: [{ value: new Date(), disabled: _this.isView }, configDetail && configDetail.datetime.isMandatory ? Validators.required : undefined],
          startTime: [{ value: formattedTime, disabled: _this.isView }, configDetail && configDetail.startTime.isMandatory ? Validators.required : undefined],
          endTime: [{ value: '', disabled: _this.isView }, configDetail && configDetail.endTime.isMandatory ? Validators.required : undefined],
          assets: [{ value: '', disabled: _this.isView }, configDetail && configDetail.assets.isMandatory ? Validators.required : undefined],
          whatWentWell: [{ value: '', disabled: _this.isView }, configDetail && configDetail.whatWentWell.isMandatory ? Validators.required : undefined],
          operationalLearning:  [{ value: 'No', disabled: _this.isView },configDetail && configDetail?.operationalLearning?.isMandatory ? Validators.required : undefined],
          operationalLearningDescription :  [{ value: '', disabled: _this.isView },configDetail && configDetail?.operationalLearningDescription?.isMandatory ? Validators.required : undefined],
          whatNeedsAttention: [{ value: '', disabled: _this.isView },configDetail && configDetail.whatNeedsAttention.isMandatory ? Validators.required : undefined],
          whatDidYouDoAboutIt: [{ value: '', disabled: _this.isView },configDetail && configDetail.whatDidYouDoAboutIt.isMandatory ? Validators.required : undefined],
          overallSummary: [{ value: '', disabled: _this.isView },configDetail && configDetail.overallSummary.isMandatory ? Validators.required : undefined],
          signature: [{ value: '', disabled: _this.isView },configDetail && configDetail.signature.isMandatory ? Validators.required : undefined],
          workOrderNumber: [{ value: '', disabled: _this.isView },configDetail && configDetail.workOrderNumber?.isMandatory ? Validators.required : undefined],
          projectName: [{ value: '', disabled: _this.isView },configDetail && configDetail.projectName?.isMandatory ? Validators.required : undefined],
        });
        
        console.log(configDetail)
        _this.loaderFlag = false;


        _this.myInit();
      // }
    })
    

    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      var tempAttend = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"]
      tempAttend.forEach(element => {
        element.fullName = element.firstName+' '+element.lastName
      });
      _this.attendeeList = tempAttend;
      _this.filteredattendeeList = _this.attendeeList.slice();
      if(history.state.fieldwalk){
        _this.editData = history.state.fieldwalk;
        console.log(_this.editData)
        _this.editData.refAttendee.items.forEach(element => {
          element.fullName = element.firstName+' '+element.lastName
        });
        _this.selectedAttendee = _this.editData.refAttendee.items
        _this.editSelectedAttendee =  _.cloneDeep(_this.selectedAttendee); 
        if(_this.editData.refWorkOrderHeader){
          _this.fieldForm.get("workOrderNumber").setValue([
            {name:_this.editData.refWorkOrderHeader.name,value:_this.editData.refWorkOrderHeader.externalId,number:_this.editData.refWorkOrderHeader.number}
          ]);
        }
        // _this.fieldForm.get("attendee").setValue(_this.editData.attendee["externalId"]);
        if(_this.editData && _this.editData.refReportingLocation){
          var findLoc = _this.reportingLocationList.find(item => item.externalId == _this.editData.refReportingLocation["externalId"]) 
          _this.fieldForm.get("locationObserve").setValue(findLoc);
        }
        if(_this.editData && _this.editData.refUnit){
          var findLoc = _this.unitList.find(item => item.externalId == _this.editData.refUnit["externalId"]) 
          _this.unitControl.setValue(findLoc.externalId);
          // _this.fieldForm.get("unit").setValue(findLoc);
        }
        
        
 
        console.log(_this.fieldForm.get("attendee").value)
      }
    

    })


    
    var myProcess = _this.commonService.processList.find(e => e.externalId == _this.process.externalId)
    console.log(_this.commonService.processList)
    console.log(history.state)
    console.log(_this.process,myProcess)
    var site = _this.commonService.siteList.find(e => e.externalId == _this.process.refSite.externalId)
    if (site) {
      _this.siteControl.setValue(site.externalId)
      _this.selectedSite = site;
      _this.getDataSetId();
      _this.selectedCountry = site["country"];
      if (site["country"] && site["country"]["parent"]) {
        _this.selectedRegion = site["country"]["parent"];
      }
      if (site && site["reportingLocations"]["items"].length > 0) {
        _this.reportingLocationList = _this.reportingLocationList.concat(_.orderBy(site["reportingLocations"]["items"], ['description'], ['asc']));
        _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
        _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      }
      _this.sitesetting(site)
    }

    _this.dataService.postData({ "sites": site.externalId }, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
      var listCraft = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
      console.log(listCraft)
      if (listCraft.length > 0) {
        var settingData = listCraft[0];

          var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
          _this._locale = dateFormat.local;
          _this.commonService.dateFormat = dateFormat;
          _this._adapter.setLocale(_this._locale);
          _this.setDateFormat();
          _this.cdRef.detectChanges();

      }else{
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
        _this._locale = dateFormat.local;
        _this.commonService.dateFormat = dateFormat;
        _this._adapter.setLocale(_this._locale);
        _this.setDateFormat();
        _this.cdRef.detectChanges();
      }
      
    })



    _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "","siteCode":_this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {

        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
        var mapAss = _this.editData && _this.editData.refEquipment? _this.editData.refEquipment["items"].map(item => item.externalId):[]
        _this.dataService.postData({ externalId:mapAss?mapAss:[] ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
        subscribe((resData: any) => {
          var assetMap = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
          assetMap.forEach(element => {
            element.nameDesc = element.name+' / '+element.description
          });
          _this.assetsList = _.unionBy(assetMap, _this.assetsList, 'externalId');

          if (history.state.fieldwalk) {
            _this.editData = history.state.fieldwalk;
            var asL = [];
            _.each(_this.editData.refEquipment["items"], function (eAsset) {

              eAsset.nameDesc = eAsset.name+' / '+eAsset.description;
              asL.push(eAsset)

            })
            _this.editEquipment =  _.cloneDeep(asL);  
            _this.fieldForm.get("assets").setValue(asL);
          }
        })
        
     
      })
      _this.siteControl.valueChanges.subscribe(value => {
        _this.dataService.siteId = value;
        // _this.processLoad(() => { });
        _this.sitesetting(_this.commonService.siteList.find(e => e.externalId == value),true);
      });
      _this.dataService.postData({ "name": "Contractor" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
        _this.contractorList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
        _this.filteredContractorList = _this.contractorList.slice();
      })
      _this.afterSettingInit();
      _this.cdRef.detectChanges();
  }

  
  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
  
  mergeConfigs(apiConfig: any, defaultConfig: any): any {
    // Recursively merge objects
    const mergeObjects = (target: any, source: any) => {
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null) {
            if (!target[key] || typeof target[key] !== 'object') {
              target[key] = Array.isArray(source[key]) ? [] : {};
            }
            mergeObjects(target[key], source[key]);
          } else if (target[key] === undefined) {
            target[key] = source[key];
          }
        }
      }
    };
  
    const result = { ...apiConfig };
    mergeObjects(result, defaultConfig);
    if (!apiConfig.dashboardConfig || Object.keys(apiConfig.dashboardConfig).length === 0) {
      result.dashboardConfig = { ...defaultConfig.dashboardConfig };
    }
  
    if (!apiConfig.columnConfig || Object.keys(apiConfig.columnConfig).length === 0) {
      result.columnConfig = { ...defaultConfig.columnConfig };
    }
  
    return result;
  }

  sitesetting(site: any, newsite?: boolean) {
    var _this = this
    if(_this.selectedSite && !newsite){
      _this.commonService.siteList.filter(function (e) {
        if (_this.selectedSite.externalId.indexOf(e.externalId) > -1 ) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.unitList = _this.unitList.filter(item => item.isActive === true);
            _this.filteredUnitList = _this.unitList.slice();
          }
  
       
        }
        //return selectedSite.indexOf(e.externalId) > -1;
      });
    }
    else if(newsite){
      _this.unitList=[]
      _this.commonService.siteList.filter(function (e) {
        if (site.externalId.indexOf(e.externalId) > -1 ) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }
  
       
        }
        //return selectedSite.indexOf(e.externalId) > -1;
      });
    }
    _this.afterSettingInit()
    _this.cdRef.detectChanges();
  }
  getDataSetId(){
    var _this =this
    var objPost = {
      "items": [
        {
          "externalId": `${_this.commonService.configuration["AppCode"]}-${_this["selectedSite"].siteCode}-${_this.commonService.configuration["allUnitCode"]}-${_this.commonService.configuration["dataSpaceCode"]}`
        }
      ],
      "ignoreUnknownIds": false
    }
  _this.dataService.postData(objPost, _this.dataService.NODE_API + "/api/service/getDataSetId").subscribe((resData: any) => {
    console.log('resData',resData)
    
    if(resData && resData.items && resData.items.length > 0){
      _this.dataSetImageId = resData.items[0].id

    }

    })
  }

  resetSelection() {
    //  this.selectedItemsRoot = []; 
    var _this =this;
    _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "" ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
        subscribe((resData: any) => {
          var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
        })       
      }
      resetSelectionWorkOrderNumber() {
        var _this =this;
        _this.dataService.postData({ "workOrderNumber": "", "siteCode": _this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listWorkOrderHeader").
          subscribe((resData: any) => {
            var orderList = resData["data"]["list" + _this.commonService.configuration["typeWorkOrderHeader"]]["items"]
            _this.filteredWorkOrderNumberList = orderList;
          })
      }
      removeSelectedUser(item){
        const index = this.selectedAttendee.findIndex(prop => prop.externalId === item.externalId)
        this.selectedAttendee.splice(index,1)
      }

  myInit(){
    var _this = this;
    _this.setDateFormat();
    if (history.state.process) {
      _this.process = history.state.process;
      if (history.state.fieldwalk) {
        _this.editData = history.state.fieldwalk;
        console.log(_this.editData)
        _this.fieldForm.get("datetime").setValue(_this.editData.date);
        if(_this.editData.startTime){
          _this.fieldForm.get("startTime").setValue(_this.editData.startTime?.split("T")[1].split(":")[0]+":"+_this.editData.startTime?.split("T")[1].split(":")[1]);
          _this.fieldForm.get("endTime").setValue(_this.editData.endTime?.split("T")[1].split(":")[0]+":"+_this.editData.endTime?.split("T")[1].split(":")[1]);
        }
       
        _this.fieldForm.get("whatWentWell").setValue(_this.editData.whatWentWell);
        _this.fieldForm.get("title").setValue(_this.editData.title);
        _this.fieldForm.get("whatNeedsAttention").setValue(_this.editData.whatNeedsAttention);
        // _this.fieldForm.get("operationalLearning").setValue(_this.editData.operationalLearning);
        _this.fieldForm.get("operationalLearning").setValue(_this.editData.isOperationalLearning ?"Yes":"No");
        _this.fieldForm.get("operationalLearningDescription").setValue(_this.editData.operationalLearningDescription);
        _this.fieldForm.get("whatDidYouDoAboutIt").setValue(_this.editData.whatDidYouDoAboutIt);
        // _this.fieldForm.get("workOrderNumber").setValue(_this.editData.workOrderNumber);
        _this.fieldForm.get("projectName").setValue(_this.editData.projectName);
        _this.fieldForm.get("overallSummary").setValue(_this.editData.overallSummary);
        _this.fieldForm.get("signature").setValue(_this.editData.signature);


        async function processMediaList(mediaList) {
          let delayTime = 500; // Initial delay time in milliseconds
          const maxDelayTime = 16000; // Maximum delay time in milliseconds
          const maxRetries = 5; // Maximum number of retries for each request
      
          for (let i = 0; i < mediaList.length; i++) {
              const item = mediaList[i];
              const mediaType = item.mimetype.split('/')[0]; // Get the type (image or video)
              let mediaUrl;
              let retries = 0; // Retry counter
      
              console.log(`Processing item ${i + 1} of ${mediaList.length}`);
      
              if (mediaType === 'image') {
                  mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
                  console.log("media url for image", mediaUrl);
      
                  while (retries < maxRetries) {
                      try {
                          const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                          console.log("image data", resData);
                          const objectURL = URL.createObjectURL(resData);
                          item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                          console.log('Image loaded successfully:', item.image);
                          this.images.push(item.image);
                          console.log('Images:', this.images);
                          this.cd.detectChanges(); // Trigger change detection after image is loaded
      
                          // Reset delay time after a successful request
                          delayTime = 500;
                          break; // Exit retry loop on success
                      } catch (error) {
                          console.error('Error loading image:', error);
                          if (error.status === 429) {
                              console.error('Rate limit exceeded. Retrying after delay...');
                              await delay(delayTime);
                              delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                              retries++; // Increment retry counter
                          } else {
                              // Handle other errors
                              break; // Exit retry loop on other errors
                          }
                      }
                  }
              } else if (mediaType === 'video') {
                  console.log("hi");
                  mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
                  console.log("mediaUrl for video", mediaUrl);
      
                  while (retries < maxRetries) {
                      try {
                          const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                          console.log("video data", resData);
                          const objectURL = URL.createObjectURL(resData);
                          item.video = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                          console.log('Video loaded successfully:', item.video);
                          this.videos.push(item.video);
                          console.log('Videos:', this.videos);
                          this.cd.detectChanges(); // Trigger change detection after video is loaded
      
                          // Reset delay time after a successful request
                          delayTime = 500;
                          break; // Exit retry loop on success
                      } catch (error) {
                          console.error('Error loading video:', error);
                          if (error.status === 429) {
                              console.error('Rate limit exceeded. Retrying after delay...');
                              await delay(delayTime);
                              delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                              retries++; // Increment retry counter
                          } else {
                              // Handle other errors
                              break; // Exit retry loop on other errors
                          }
                      }
                  }
              }
      
              // Fixed delay between requests
              await delay(delayTime);
          }
      
          console.log('All items processed');
          console.log('Total images:', this.images.length);
          console.log('Total videos:', this.videos.length);
      }
      
      function delay(ms: number) {
          return new Promise(resolve => setTimeout(resolve, ms));
      }
      
      if (this.editData.evidenceDocument && this.editData.evidenceDocument.items.length > 0) {
          const mediaList = this.editData.evidenceDocument.items;
          processMediaList.call(this, mediaList);
      }
      

        // _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/priviewImage").
        //   subscribe((resData: any) => {
        //     console.log(resData)
        //   })
      }
    } else {
      _this.goPage('observations/observation')
    }

    _this.fieldForm.get("attendee").valueChanges.subscribe(value => {
      console.log('value',value)
      function push(array, item) {
        if (!array.find((ele) => ele.externalId === item.externalId)) {
          array.push(item);
        }
      }
      push(_this.selectedAttendee, value); 
      
    })
  }
  async onAttendeeChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.attendeeList = [];
    _this.filteredattendeeList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.attendeeList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.attendeeList.forEach((element)=>{
          element.fullName =  element.firstName+' '+element.lastName
        })
        _this.filteredattendeeList = _this.attendeeList.slice();
      
  
      })
      _this.cdRef.detectChanges()
      
  }
  }
  goPage(page) {
    this.router.navigate([page]);
  }

  siteSelected(region) {
  }
  unitSelected(region) {
  }

  async onFilterChange(item: any) {
    var _this = this;
    _this.dataService.postData({ "functionalLocationName": "", "equipmentName": item,"siteCode":_this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {
        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
      })

  }
  async onFilterChangeWorkOrderNumber(item: any) {
    var _this = this;
    _this.dataService.postData({ "workOrderNumber": item, "siteCode": _this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listWorkOrderHeader").
      subscribe((resData: any) => {
        var orderList = resData["data"]["list" + _this.commonService.configuration["typeWorkOrderHeader"]]["items"]
        _this.filteredWorkOrderNumberList = orderList;
      })
  }
  onItemSelect(item: any) {
  }
  onSelectAll(items: any) {
  }
  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }


  async uploadFile($event: any) {
    this.loaderFlag = true;
    this.cdRef.detectChanges();  // Trigger change detection
    
    const fileInput = $event.target;
    const files = fileInput.files;
  
    // Check if files are selected
    if (!files || files.length === 0) {
        this.loaderFlag = false;
        this.cdRef.detectChanges();  // Trigger change detection
        console.log('No files selected.');
        return;
    }
  
    const totalFiles = files.length + this.images.length + this.videos.length;
    if (totalFiles > this.maxMedia) {
        this.loaderFlag = false;
        this.cdRef.detectChanges();  // Trigger change detection
        this.commonService.triggerToast({ type: 'error', title: '', msg: this.commonService.toasterLabelObject['toasterUploadreached'] });
        return;
    }
  
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        const isImage = file.type.startsWith('image/');
        const isVideo = file.type.startsWith('video/');
  
        // Using a promise to handle file reading
        await new Promise<void>((resolve, reject) => {
            reader.onload = (e: any) => {
                if (isImage) {
                    this.images.push(e.target.result);
                    console.log(`Image ${i + 1}/${files.length} loaded:`, file.name);
                } else if (isVideo) {
                    this.videos.push(e.target.result);
                    console.log(`Video ${i + 1}/${files.length} loaded:`, file.name);
                }
                this.cdRef.detectChanges();  // Trigger change detection after each file is loaded
                resolve();
            };
            reader.onerror = (e: any) => {
                console.error(`Error loading file ${file.name}:`, e);
                reject(e);
            };
  
            reader.readAsDataURL(file);
        });
  
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
  
        const myObj = {
            name: fileupload.name,
            mimetype: fileupload.mimeType,
            pathURL: fileupload.uploadUrl,
            pathStore: "CDF",
            evidenceDocumentId: fileupload.id + "",
            description: ""
        };
  
        console.log('External ID of newly uploaded file:', myObj["externalId"]);
        this.evidenceObj.push(myObj);
        console.log("this.evidenceObj", this.evidenceObj);
        console.log("myobjjjj", myObj);
    }
  
    this.loaderFlag = false;
    this.cdRef.detectChanges();  // Trigger change detection after all files are processed
    
    // Reset file input value to allow re-selection of the same file
    fileInput.value = '';
}

  




  async saveImages() {
    console.log("saveImages called");
    if (this.evidenceObj.length === 0) {
      return; 
    }
  
    this.loaderFlag = true;
  
    const postObj = {
      type: this.commonService.configuration["typeEvidenceDocument"],
      siteCode: this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      unitCode: this.commonService.configuration["allUnitCode"],
      items: this.evidenceObj
    };

    console.log("postObj", postObj);
    console.log("this.evidenceObj")
    console.log(this.evidenceObj)
  
    try {
      const response = await this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").toPromise();
      console.log('All files data posted:', response);
      this.evidenceObj = response["items"];
      this.evidenceEdgeCreation(this.obRes);
  
      this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterImguploadsuccess'] });
    } catch (error) {
      console.error('Error saving images:', error);
      this.commonService.triggerToast({ type: 'error', title: 'Error', msg: this.commonService.toasterLabelObject['toasterImguploadfailed'] });
    } finally {
      this.loaderFlag = false; 
    }
   
  }
  



 
  removeImage(index: number) {
    if (index >= 0 && index < this.images.length) {
      // Remove the image from the UI
      this.images.splice(index, 1);
      console.log('Image removed at index', index);

      this.evidenceObj.splice(index, 1);

      this.removeObservationImage(index);
    }
  }

  removeVideo(index: number) {
    this.videos.splice(index, 1);
  }


  viewVideo(index: number) {
    this.viewedVideoIndex = index;
  }

  closeViewedVideo() {
    this.viewedVideoIndex = null;
  }
  removeObservationImage(index: number) {
    var _this = this;
    console.log("Calling removeObservationImage with index", index);
    
    if (index < 0 || index >= _this.editData.evidenceDocument.items.length) {
      console.error('Invalid index for image removal:', index);
      return;
    }
    
    const removedImage = _this.editData.evidenceDocument.items[index];
    _this.editData.evidenceDocument.items.splice(index, 1);
    console.log('Updated editData:', _this.editData);
  
    const evidenceIndex = _this.evidenceObj.findIndex(item => item.externalId === removedImage.externalId);
    if (evidenceIndex !== -1) {
      _this.evidenceObj.splice(evidenceIndex, 1);
    }
  
    const disEdge = {
      items: [{
        instanceType: "edge",
        externalId: `${_this.editData.externalId}-${removedImage.externalId}`,
        space: _this.editData.space
      }]
    };
  
    console.log('disEdge object:', disEdge);
  
    if (!this.dataService.postData) {
      console.error('postData method not found in dataService');
      return;
    }
  
    console.log('Calling deleteInstance API...');
  
    this.dataService.postData(disEdge, this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(
      (response) => {
        console.log('Image deleted successfully from the server:', response);
      },
      (error) => {
        console.error('Error deleting image from the server:', error);
      }
    );
  }
  

  
  updateServerData() {
    const postObj = {
      "type": this.commonService.configuration["typeEvidenceDocument"],
      "siteCode": this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      "unitCode": this.commonService.configuration["allUnitCode"],
      "items": this.evidenceObj
    };
  
    this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        this.evidenceObj = data["items"];
      }
      this.loaderFlag = false;
      this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterOpsuccess'] });
    });
  }
  

  viewImage(index: number): void {
    this.viewedImageIndex = index;
  }

 
  closeViewedImage(): void {
    this.viewedImageIndex = null;
  }

  closeModal() {
    this.showModal = false;
    this.modalImage = '';
  }

 showOverlay(index: number): void {
  this.showOverlayIndex = index;
}

closeOverlay(): void {
  this.showOverlayIndex = null;
}



submitClick() {
  var _this = this;
  var unitFind = _this.unitList.find(item => item.externalId == _this.unitControl.value)
    console.log(history.state)
    if (unitFind)
    _this.fieldForm.get('unit').setValue(unitFind.externalId);
    _this.loaderFlag = true;
    if(this.fieldForm.get('operationalLearning').value == 'No'){
      this.fieldForm.get('operationalLearningDescription').clearValidators();
    }
    this.fieldForm.get('operationalLearningDescription').updateValueAndValidity();
  console.log(this.fieldForm.value)
  if (this.selectedAttendee.length > 0) {
    this.fieldForm.get('attendee').clearValidators();
  } 
  this.fieldForm.get('attendee').updateValueAndValidity(); 
  if(this.fieldForm.valid){
    var fieldObj = this.fieldForm.value;
    console.log(fieldObj)
    var obDate = new Date(fieldObj["datetime"]);
    const currentYear = obDate.getFullYear().toString()
    const currentMonth = ("0" + (obDate.getMonth() + 1)).slice(-2);
    const currentDay = ("0" + obDate.getDate()).slice(-2);
    var strDate = currentYear + "-" + currentMonth + "-" + currentDay;
    //06:15 AM
    const convertTime12to24 = (time12h) => {
      var tempDate = new Date(fieldObj["datetime"]);
      if(time12h){
        var time12hArray =  time12h.split(':');
        var hours = time12hArray[0];
      // let [hours, minutes] = time12h.split(':');
      var timeMinute = time12hArray[1].split(" ");
      var minutes = timeMinute[0];
      if(timeMinute && timeMinute.length>1){
        hours = parseInt(time12hArray[0]) + 12;
      }
      console.log(hours)
      tempDate.setHours(hours);
      tempDate.setMinutes(minutes);
    }
      return tempDate;
    }

    // 2023-09-11T21:45:00.000Z
    var startTimeGet = convertTime12to24(fieldObj["startTime"]);
    var endTimeGet = convertTime12to24(fieldObj["endTime"]);

    const currentstartMonth = ("0" + (startTimeGet?.getMonth() + 1)).slice(-2);
    const currentstartDay = ("0" + startTimeGet?.getDate()).slice(-2);
    const currentstartHour = ("0" + (startTimeGet?.getHours())).slice(-2);
    const currentstartMinute = ("0" + startTimeGet?.getMinutes()).slice(-2);

    const currentendMonth = ("0" + (endTimeGet?.getMonth() + 1)).slice(-2);
    const currentendDay = ("0" + endTimeGet?.getDate()).slice(-2);
    const currentendHour = ("0" + (endTimeGet?.getHours())).slice(-2);
    const currentendMinute = ("0" + endTimeGet?.getMinutes()).slice(-2);
    // 2023-09-12T3-15:00.000Z
    // 2023-09-30T03:30:00+00:00

    var startTime = startTimeGet?.getFullYear() + "-" + currentstartMonth + "-" + currentstartDay + "T" + currentstartHour + ":" + currentstartMinute + ":00.000Z";
    var endTime = endTimeGet?.getFullYear() + "-" + currentendMonth + "-" + currentendDay + "T" + currentendHour + ":" + currentendMinute + ":00.000Z";

    var postObj = {
      
      "whatWentWell": fieldObj["whatWentWell"],
      "title": fieldObj["title"],
      "whatNeedsAttention": fieldObj["whatNeedsAttention"],
      // "workOrderNumber": fieldObj["workOrderNumber"],
      "projectName":fieldObj["projectName"],
      "isOperationalLearning": fieldObj["operationalLearning"] == 'Yes' ? true : false,
      "operationalLearningDescription": fieldObj["operationalLearningDescription"],
      "whatDidYouDoAboutIt": fieldObj["whatDidYouDoAboutIt"],
      "overallSummary": fieldObj["overallSummary"],
      "signature": fieldObj["signature"],
      "isActive" :true

    }
    if(fieldObj["workOrderNumber"] && fieldObj["workOrderNumber"].length>0){
      var workOrderNumber = _this.filteredWorkOrderNumberList.find(e=> e.externalId == fieldObj["workOrderNumber"][0]["externalId"])
      if(workOrderNumber){
        postObj["refWorkOrderHeader"] = {
          "space":workOrderNumber["space"],
          "externalId":workOrderNumber["externalId"]
        }
       }
     }

    if(strDate && strDate.length>0){
      postObj["date"] = strDate.toString()
    }
    if(fieldObj["startTime"] && fieldObj["startTime"].length>0){
      postObj["startTime"] = startTime
    }
    if(fieldObj["endTime"]  && fieldObj["endTime"].length>0){
      postObj["endTime"] = endTime
    }
    if (_this.editData) {
      postObj["externalId"] = _this.editData.externalId
    }
    if(_this.commonService["userInfo"] && _this.commonService["userInfo"]["externalId"]){
      postObj["performerAzureDirectoryUserID"] = {
        "space":_this.commonService["userInfo"]["space"],
        "externalId":_this.commonService["userInfo"]["externalId"]
      }
     }
    // var attendee = _this.attendeeList.find(e => e.externalId == fieldObj["attendee"])
    // if (attendee) {
    //   postObj["attendee"] = {
    //     "space": attendee["space"],
    //     "externalId": attendee["externalId"]
    //   }
    // }
    // if (history.state.process) {
    //   postObj["refOFWAProcess"] = {
    //     "space": history.state.process.space,
    //     "externalId": history.state.process.externalId
    //   }
    // }
    if (_this["selectedRegion"]) {
      postObj["refGeoRegion"] = {
        "space": _this["selectedRegion"].space,
        "externalId": _this["selectedRegion"].externalId
      }
    }
    if (_this["selectedCountry"]) {
      postObj["refCountry"] = {
        "space": _this["selectedCountry"].space,
        "externalId": _this["selectedCountry"].externalId
      }
    }

    if (_this["selectedSite"]) {
      postObj["refSite"] = {
        "space": _this["selectedSite"].space,
        "externalId": _this["selectedSite"].externalId
      }
    }
    if (_this["selectedUnit"]) {
      postObj["refUnit"] = {
        "space": _this["selectedUnit"].space,
        "externalId": _this["selectedUnit"].externalId
      }
    }
    if (fieldObj["locationObserve"]) {
      postObj["refReportingLocation"] = {
        "space": fieldObj["locationObserve"].space,
        "externalId": fieldObj["locationObserve"].externalId
      }
    }
    if (unitFind) {
      postObj["refUnit"] = {
        "space": unitFind.space,
        "externalId": unitFind.externalId
      }
    }
    if (_this["selectedReportingLine"]) {
      postObj["refReportingLine"] = {
        "space": _this["selectedReportingLine"].space,
        "externalId": _this["selectedReportingLine"].externalId
      }
    }

    if (_this["selectedFunctionalLocation"]) {
      postObj["refFunctionalLocation"] = {
        "space": _this["selectedFunctionalLocation"].space,
        "externalId": _this["selectedFunctionalLocation"].externalId
      }
    }
    if (_this["selectedEquipment"]) {
      postObj["refEquipment"] = {
        "space": _this["selectedEquipment"].space,
        "externalId": _this["selectedEquipment"].externalId
      }
    }
    if (_this["selectedBusinessLine"]) {
      postObj["refBusinessSegment"] = {
        "space": _this["selectedBusinessLine"].space,
        "externalId": _this["selectedBusinessLine"].externalId
      }
    }

      if (history.state.process) {
        postObj["refProcess"] = {
          "space": history.state.process.space,
          "externalId": history.state.process.externalId
        }
      }
      if (history.state.corePrinciple) {
        postObj["refCorePrinciple"] = {
          "space": history.state.corePrinciple.space,
          "externalId": history.state.corePrinciple.externalId
        }
      }
      if (history.state.subProcess) {
        postObj["refSubProcess"] = {
          "space": history.state.subProcess.space,
          "externalId": history.state.subProcess.externalId
        }
      }
      
  
    var fieldWalkObj = {
      "type": _this.commonService.configuration["typeFieldWalk"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        postObj
      ]
    }

    

    _this.dataService.postData(fieldWalkObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {

      var logObj = {
        objectExternalId:data["items"][0].externalId,
        objectType:_this.commonService.configuration["typeFieldWalk"],
        refUser:{
          "space": _this.dataService.userInfo.user.space,
          "externalId": _this.dataService.userInfo.user.externalId
        },
        logType:"Created",
        dateTime:new Date(),
        beforeJSON:postObj,
        afterJSON:postObj
      }
      if(_this.editData){
        logObj["beforeJSON"] = _this.editData;
        logObj["logType"] = "Edited";
      }
      var mainLog = {
        "type": _this.commonService.configuration["typeOFWALog"],
        "siteCode": _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          logObj
        ]
      }
      _this.dataService.postData(mainLog, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(logdata => {
      });

      if (data["items"].length > 0) {
        _this.processEdgeCreation(data["items"][0]);
        _this.obRes = data["items"][0]
        _this.saveImages(); 
        _this.attendeesEdgeCreation(_this.selectedAttendee, data["items"][0])
        // _this.evidenceEdgeCreation(data["items"][0]);
        if (fieldObj.assets && fieldObj.assets.length > 0) {
          _this.assetEdgeCreation(fieldObj, data["items"][0]);
        } else {
          _this.loaderFlag = false;
            _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
           this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
        }

      } else {
        _this.loaderFlag = false;
          _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterFailed'] });
      }
    });

  }else{
    console.log('toasterLabelObject',_this.commonService.toasterLabelObject)
    _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
  }
 
}

  processEdgeCreation(fdmObj) {
    var _this = this;
    
    var mySubCategoryArray = [];
        var edgeObj = {
          "instanceType": "edge",
          "space": _this.commonService.configuration["DataModelSpace"],
          "externalId": fdmObj["externalId"] + "-" + history.state.process["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeFieldWalk"] + ".refOFWAProcess"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": history.state.process["space"],
            "externalId": history.state.process["externalId"]
          }
        }
        mySubCategoryArray.push(edgeObj);

        console.log(mySubCategoryArray)
      _this.dataService.postData(mySubCategoryArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
  }
  attendeesEdgeCreation(attendee, fdmObj) {
    var _this = this;
    if(history.state.fieldwalk && _this.editData){
      
      var disconnectNode=   {
        "items": [
        
         ]
      }
  
      _this.editSelectedAttendee.forEach(element => {
        var ob =   {
          "instanceType":"edge",
          "externalId": fdmObj["externalId"]+"-"+element.externalId, 
          "space": fdmObj["space"]
        }
        disconnectNode.items.push(ob)
      })
      console.log('disconnectNode',disconnectNode)
      _this.dataService.postData(disconnectNode, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(disNode => {
      
        var myAssetArray = [];
        _.each(attendee, function (eUser) {
          var edgeObj = {
            "instanceType": "edge",
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"] + "-" + eUser["externalId"],
            "type": {
              "space": _this.commonService.configuration["DataModelSpace"],
              "externalId": _this.commonService.configuration["typeFieldWalk"] + ".refAttendee"
            },
            "startNode": {
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"]
            },
            "endNode": {
              "space": eUser.space,
              "externalId": eUser.externalId
            }
          }
          myAssetArray.push(edgeObj);
        })
    
        console.log(myAssetArray);
        _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
          // _this.loaderFlag = false;
          // _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
          // this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
        });
      })

    }else{
      var myAssetArray = [];
      _.each(attendee, function (eUser) {
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eUser["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeFieldWalk"] + ".refAttendee"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eUser.space,
            "externalId": eUser.externalId
          }
        }
        myAssetArray.push(edgeObj);
      })
  
      console.log(myAssetArray);
      _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
        // _this.loaderFlag = false;
        // _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
        // this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
      });
    }
  
  }

  assetEdgeCreation(observedObj, fdmObj) {

    var _this = this;
    if(_this.editEquipment.length > 0){

       
      var disconnectNode=   {
        "items": [
        
         ]
      }
  
      _this.editEquipment.forEach(element => {
        var ob =   {
          "instanceType":"edge",
          "externalId": fdmObj["externalId"]+"-"+element.externalId, 
          "space": fdmObj["space"]
        }
        disconnectNode.items.push(ob)
      })
      console.log('disconnectNode',disconnectNode)
      _this.dataService.postData(disconnectNode, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(disNode => {
        var myAssetArray = [];
        _.each(observedObj.assets, function (eAsset) {
          var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
          var edgeObj = {
            "instanceType": "edge",
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
            "type": {
              "space": _this.commonService.configuration["DataModelSpace"],
              "externalId": _this.commonService.configuration["typeFieldWalk"] + ".refEquipment"
            },
            "startNode": {
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"]
            },
            "endNode": {
              "space": myAsset.space,
              "externalId": myAsset.externalId
            }
          }
          myAssetArray.push(edgeObj);
        })
    
        console.log(myAssetArray);
        _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
          _this.loaderFlag = false;
          _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
          this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
        });
      })
    }else{
      var myAssetArray = [];
      _.each(observedObj.assets, function (eAsset) {
        var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeFieldWalk"] + ".refEquipment"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": myAsset.space,
            "externalId": myAsset.externalId
          }
        }
        myAssetArray.push(edgeObj);
      })
  
      console.log(myAssetArray);
      _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
        _this.loaderFlag = false;
        _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
        this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
      });
    }
  
  }

  evidenceEdgeCreation(fdmObj) {
    var _this = this;
  var myEvidenceArray = [];
    if (_this.evidenceObj) {
      _.each(_this.evidenceObj, function (eEvidence) {
      var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eEvidence["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeFieldWalk"] + ".evidenceDocument"
          },
          "startNode": {
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eEvidence["space"],
            "externalId": eEvidence["externalId"]
          }
      }
      myEvidenceArray.push(edgeObj);
  });

      console.log(myEvidenceArray)
      _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
  }



  loadExistingImages() {
    if (this.editData .evidenceDocument && this.editData .evidenceDocument.items.length > 0) {
      this.images = []; // Clear existing images array
      this.editData .evidenceDocument.items.forEach(item => {
        const image = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
        this.dataService.getImage(image).subscribe(
          (resData: any) => {
            const objectURL = URL.createObjectURL(resData);
            item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
            console.log('Existing Image loaded successfully:', item.image);
            this.images.push(item.image); // Add the image URL to images array
          },
          (error) => {
            console.error('Error loading existing image:', error);
          }
        );
      });
    }
    console.log('Final images array after loading existing images:', this.images);
  }
  
  // Function to upload new images and store them
  async uploadAndStoreNewImages($event: any) {
    this.loaderFlag = true;
  
    if ($event.target.files && $event.target.files.length) {
      const files = $event.target.files;
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        reader.onload = (e: any) => {
          const imageObjectURL = e.target.result;
          this.images.push(imageObjectURL); 
          console.log(`File ${i + 1}/${files.length} loaded:`, file.name);
        };
        reader.readAsDataURL(file);
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
        const myObj = {
          name: fileupload.name,
          mimetype: fileupload.mimeType,
          pathURL: fileupload.uploadUrl,
          pathStore: "CDF",
          evidenceDocumentId: fileupload.id + "",
          description: ""
        };
        if (this.editData && this.editData ["evidenceDocument"] && this.editData ["evidenceDocument"]["items"].length > 0) {
          myObj["externalId"] = this.editData ["evidenceDocument"]["items"][0]["externalId"];
        }
        this.evidenceObj.push(myObj);
      }
    }
  
    this.loaderFlag = false;
    console.log('Final images array after uploading new images:', this.images);
  }
  cancelClick() {
    console.log(history.state.pageFrom)
    if (history.state.pageFrom == "FieldWalk List") {
      this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
    } else {
      this.router.navigate(["observations/observation"]);
    }

  }
  backClick() {
    if (history.state.pageFrom == "FieldWalk List") {
      this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
    } else {
      this.router.navigate(["observations/observation"]);
    }
  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
}
