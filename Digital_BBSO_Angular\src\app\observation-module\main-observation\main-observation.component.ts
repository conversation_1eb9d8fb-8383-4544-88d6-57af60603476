import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { environment } from 'src/environments/environment';
import { FieldWalkPopupComponent } from '../field-walk-popup/field-walk-popup.component';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { NgZone } from '@angular/core';
import { LanguageService } from 'src/app/services/language.service';
@Component({
  selector: 'app-main-observation',
  templateUrl: './main-observation.component.html',
  styleUrls: ['./main-observation.component.scss']
})
export class MainObservationComponent implements OnInit, AfterViewInit {

  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();
  processList =[]
  selectedProcess: any;
  homeTitle: string;
  
  @Input() site: Observable<void>;
  @Input() preFilled:any;
  labels = {};
  selectedSite:any;
  userAccessMenu: any;
  createObservation: any;
  createFieldWalk: any;
  createAudit: any;
  // @Input() processSubject: Observable<void>;
  constructor(public dialog: MatDialog,private dataService:DataService,
    private changeDetector: ChangeDetectorRef, private sanitizer: DomSanitizer, private commonService: CommonService, private route: ActivatedRoute, private router: Router, public translationService: TranslationService, private languageService: LanguageService, public ngZone: NgZone,) {
  
    this.homeTitle  = this.dataService.homeTitle;
    // this.observationList   = this.dataService.homeObservationList
    console.log('commonService label', this.commonService.labelObject)
    console.log('commonService label', this.commonService.selectedLanguage)
    
    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'mainTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'mainTitle'] || 'mainTitle',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
      }
    }
    console.log('COmponent Label Object: ', this.labels)

  }
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'mainTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'mainTitle'] || 'mainTitle',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    _this.site.subscribe((site: any) => {
  console.log('site',site)
  if(site){
    var processList = _this.commonService.processList.filter(e => {
      return e.processType == "Core Principles" && ((e.refSite && e.refSite.externalId) == site.externalId);
    })
    console.log('processList',processList)
    var myList = [];
    _.each(processList,function(eData){
      if(eData.name == "Stewardship"){
        eData["sequence"] = eData.sequence ? eData.sequence : 1;
      }else
      if(eData.name == "Quality"){
        eData["sequence"] = eData.sequence ? eData.sequence : 2;
      }else
      if(eData.name == "Reliability"){
        eData["sequence"] = eData.sequence ? eData.sequence : 2;
      }else{
        eData["sNo"] = 4;
      }
      myList.push(eData);
    })
    
     processList = _.sortBy(myList, ['sequence']);
    _this.processList = processList;
    _this.getUserMenuConfig();

    _this.changeDetector.detectChanges()

  }
      
    });
    _this.selectedProcess = _this.preFilled;

    var _this = this;
  
    _this.userAccessMenu = _this.commonService.userIntegrationMenu;
    //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
    if(_this.userAccessMenu){
      _this.getUserMenuConfig();
    }

 
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      console.log("fiterType>",fiterType)
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
    
      
    })

  }

  ngAfterViewInit(): void {
    this.changeDetector.detectChanges();
    console.log('After View Init labels', this.labels );
  }

  getUserMenuConfig(){
    var _this = this
    
 
      if(_this.commonService.menuFeatureUserIn.length>0){
       var homeMenu = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.homeMenu);
       if( !homeMenu || (homeMenu && homeMenu.featureAccessLevelCode == "NoAccess")){
       _this.router.navigateByUrl('noaccess/no-access', {})
       }
       if( _this.processList.length > 0){
        this.processList.forEach((element)=>{
          var objVal = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == element.code);
          console.log('objVal',objVal)
          if(objVal){
            if(objVal.featureAccessLevelCode == "ViewAccess" || objVal.featureAccessLevelCode == "EditAccess"  ){
              element.visible = true
            }else{
              element.visible = false
            }
          }else{
            element.visible = false
          }
         })
         this.changeDetector.detectChanges();
      }
      }else{
        this.processList.forEach((element)=>{
          element.visible = false
         })
         this.changeDetector.detectChanges();
      }
    
  }

  selectProcess(item){
    var _this = this;
    _this.selectedProcess= item;
    _this.newItemEvent.emit({"type":"Next","processType":"Core Principles","selectedOFWAProcess":this.selectedProcess});
    // this.selectedProcess= item;
    // if(item.name == "Field Walk"){
    //   _this.router.navigateByUrl('observations/field-visit', {
    //     state: { "process": _this.selectedProcess }
    //   })
    // }else if(item.name == "Audit"){
    //   _this.router.navigateByUrl('observations/audit-list', {
    //     state: { }
    //   })
    // }else{
    //   this.goCreateObserve();
    // }
  }
 
  goPage(page) {
    this.router.navigate([page]);
  }

  cancelClick(){
    this.selectedProcess =  null;
    this.newItemEvent.emit({"type":"Cancel","processType":"Core Principles"});
  }
  // goCreateObserve(){
  //   if(this.selectedProcess){
  //     this.newItemEvent.emit({"next":true,process:this.selectedProcess});
  //   }else{
  //     this.commonService.triggerToast({ type: 'error', title: "", msg: "Please choose process type" });
  //   }
 
  // }

}
