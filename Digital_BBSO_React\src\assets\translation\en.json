{"PAGINATION": {"ROWS_PER_PAGE": "Rows per page", "PREV": "Previous", "NEXT": "Next"}, "AUDIT": {"ID": "ID", "TITLE": "Title", "YEAR": "Year", "QUARTER": "Quarter", "PRIORITY": "Priority", "START_DATE": "Start Date", "END_DATE": "End Date", "STATUS": "Status", "CREATED_ON": "Created On", "CREATED_BY": "Created By", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "ACTIONS": "Actions", "SHIFT": "Shift", "UNIT": "Unit", "REFERENCE_LOCATION": "Location", "OBS_ON_BEHALF_OF": "Observed By", "DATE": "Date", "MONTH": "Month"}, "ACTION_LIST": {"ID": "ID", "DESC": "Description", "APP_NAME": "Application Name", "ASSIGNED_TO": "Assigned To", "SITE": "Site", "UNIT": "Unit", "REPORTING_LOCATION": "Reporting Location", "ASSIGN_DATE": "Assigned Date", "DUE_DATE": "Due Date", "OBJ_TYPE": "Object Type", "OBJ_ID": "Object ID", "PRIORITY": "Priority", "ACTIONS": "Actions"}, "FIELD_WALK": {"ID": "ID", "OBS_DATE": "Observation Date", "SITE": "Site", "UNIT": "Unit", "CREATED_ON": "Created On", "CREATED_BY": "Created By", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "ACTIONS": "Actions"}, "OBS_LIST": {"ID": "ID", "OBS_DATE": "Observation Date", "CRAFT": "Craft", "SITE": "Site", "DESC": "Description", "OBSERVATION_STATUS": "Observation Status", "CREATED_ON": "Created On", "CREATED_BY": "Created By", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "ACTIONS": "Actions", "WEEK": "Week"}, "AUDIT_PLAN": {"ID": "ID", "TITLE": "Title", "AUDIT_NUMBER": "Audit Number", "YEAR": "Year", "QUARTER": "Quarter", "PRIORITY": "Priority", "STATUS": "Status", "CREATED_ON": "Created Time", "CREATED_BY": "Created By", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "ACTIONS": "Actions", "OCCURRENCE": "Occurrence", "OBS": "Observer"}, "CONFIG_LIST": {"SITE": "Site", "PROCESS": "Process", "CORE_PRINCIPLE": "Core Principle", "SUB_PROCESS": "Sub Process", "CATEGORY": "Category", "SUB_CATEGORY": "Sub Category", "CREATED_ON": "Created On", "CREATED_BY": "Created By", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "ACTIONS": "Actions"}, "QUESTION_LIST": {"ID": "ID", "QUESTION": "Question #", "DESC": "Description", "CREATED_AT": "Created On", "CREATED_BY": "Created By", "DATE": "Date", "ACTIONS": "Actions", "UNIT": "Unit", "SHORTDESC": "Short Description", "OBS_TYPE": "Observation Type", "CATEGORY": "Category", "SUB_CATEGORY": "Sub Category"}, "TABLE": {"NO_RESULTS": "No results"}, "ICONS": {"VIEW": "View", "EDIT": "Edit", "ASSETS": "Assets", "CREATE_ACTION": "Create Action", "QUESTION": "Question", "CREATE": "Create", "SEND": "Send", "SCORE_CARD": "Score Card", "AUDIT_SUMMARY": "<PERSON>t <PERSON>", "VIEW_360": "360 View", "VIEW_THREAT": "Threat View", "SCH_DETAILS": "Schedule Details", "DISABLE": "Disable", "SEND_ACTION": "Send to Action", "OBS_HISTORY": "Observation History", "CREATE_OBS": "Create Observation", "DELETE": "Delete", "SEND_MAIL": "Share"}}