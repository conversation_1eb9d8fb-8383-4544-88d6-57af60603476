'use strict';
var https = require('https');
var _ = require('lodash');
const request = require('request');
const { json } = require("express");
var querystring = require('querystring');
var fs = require('fs');
var mime = require('mime');
const superagent = require('superagent');
var async = require('async');
const nodemailer = require("nodemailer");
var fetch = require('node-fetch');
const { v1: uuidv1 } = require('uuid');
const { CogniteClient } = require('@cognite/sdk');
const constant = require('../constant/constant');

exports.space = function (req, res) {
    // #swagger.ignore = true
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/space" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/space')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.createEdge = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/space" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/createEdge')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}


exports.deleteInstance = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/space" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/deleteInstance')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.createInstanceByProperties = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/instance" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    console.log("BL start", new Date())
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/createInstanceByProperties')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log("BL end", new Date())
            return res.status(200).json(resp.body);
        })
}
exports.createInstance = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/instance" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */

    superagent
        .post(process.env.CDF_ENTITY + '/api/service/createInstanceByProperties')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log("BL end", new Date())
            return res.status(200).json(resp.body);
        })
}
exports.graphql = function (req, res) {
    // #swagger.ignore = true
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/graphql')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log("resp.body")
            console.log(resp.body)
            return res.status(200).json(resp.body);
        })
}
exports.classic = async function (req, res) {
    // #swagger.ignore = true
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/classic')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.instance = function (req, res) {
    // #swagger.ignore = true

    superagent
        .post(process.env.CDF_ENTITY + '/api/service/instance')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log(resp)
            return res.status(200).json(resp.body);
        })
}
exports.listCommonRefEnum = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/listCommonRefEnum" }
       } */

    /* #swagger.security = [{
          "bearerAuth": []
   }] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listCommonRefEnum')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.listGeoRegion = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listGeoRegion')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listCountry = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listCountry')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingLocation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingLocation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listBusinessLineByUnit = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listBusinessLineByUnit')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingSiteByCountry = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    console.log("listReportingSiteByCountry")
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingSiteByCountry')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingSiteFunctionalLocation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingSiteFunctionalLocation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingSiteCursor = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/endCursor" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingSiteCursor')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingUnitFunctionalLocation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingUnitFunctionalLocation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingUnitCursor = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/endCursor" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingUnitCursor')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingSiteById = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/byId" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingSiteById')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listReportingUnitById = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/byId" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listReportingUnitById')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listEquipmentByFunctionalLocation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listEquipmentByFunctionalLocation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listFunctionalLocation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listFunctionalLocation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listUnitByReportingSiteId = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listUnitByReportingSiteId')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listProcess = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listProcess')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}
exports.listProcessBySite = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listProcessBySite')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}
exports.listProcessConfigurationByProcess = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/byId" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listProcessConfigurationByProcess')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.listSubCategory = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listSubCategory')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}


exports.listQuestionBankByCategory = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listQuestionBankByCategory')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}


exports.listQuestionList = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listQuestionList')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}


exports.listObservation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listObservation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.listDepartment = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listDepartment')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.listSupplier = function (req, res) {
    console.log(req)
    console.log(res)
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listSupplier')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}
exports.aggregateObservation = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/aggregateObservation')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.aggregateFieldWalk = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/aggregateFieldWalk')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.aggregateAudit = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/aggregateAudit')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.aggregateAction = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/aggregateAction')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.aggregateChecklist = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/aggregateChecklist')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}
exports.listObservationById = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/byId" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listObservationById')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.listChecklistByCategory = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/byId" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listChecklistByCategory')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.listFieldWalk = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listFieldWalk')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}

exports.priviewImage = function (req, res) {
    // #swagger.ignore = true
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/priviewImage')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).sendFile(resp.body);
        })
}

exports.listVendor = function (req, res) {
    console.log("ffffff")
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listVendor')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.listSchedule = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listSchedule')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listScheduleDetail = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listScheduleDetail')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.scheduleSendToVedor = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */

    var host = "smtp.office365.com";
    var port = "587";
    var email = process.env.EMAIL;
    var password = process.env.EMAIL_PASSWORD;
    var reqBody = req.body;
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listAudit')
        .send({ "externalId": reqBody.auditId })
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log(resp.body);
            var auditResponse = resp.body;
            var auditData = auditResponse["data"]["list" + constant.configuration["typeAudit"]]["items"][0]
            console.log(auditData)
            console.log("host ", host)
            console.log("port ", port)
            console.log("email ", email)
            console.log("password ", password)
            console.log("to email", auditData["refOFWASchedule"]["refOFWAVendor"]["email"])
            var transporter = nodemailer.createTransport({
                service: "Outlook365",
                host: host,
                port: port,
                tls: {
                    ciphers: "SSLv3",
                    rejectUnauthorized: false,
                },
                auth: {
                    user: email, // From mail username
                    pass: password// From mail password
                }
            });
            var mailOptions = {
                from: email, // From mail id
                to: auditData["refOFWASchedule"]["refOFWAVendor"]["email"], //To mail id
                cc: "", //To mail id
                subject: "Celanese - Audit", // subject
                html: `
                <p>Hello, ${auditData["refOFWASchedule"]["refOFWAVendor"]["vendorName"]}</p>
                <p>Please click on the below URL to start filling the Audit form</p>
                <p><a href="${constant.configuration.VendorUrl}/observations/quality-assessment?id=${reqBody.auditId}">Click Here</a></p>
                <p>Thanks,</p>
                <p>Celanese Audit Team</p>
        
                ` // Without template using html
            };
            transporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log("error", error)
                    //   returnObj = error;
                } else {
                    //   returnObj = info;
                }
                //   returnObj['createdAt'] = new Date();
                //   returnObj['subject'] = emailSubject;
                //   returnObj['content'] = emailContent;
                return res.status(200).json(info);
            });
        })



}

exports.listSchedule = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listSchedule')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.listChecklist = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listChecklist')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.listAudit = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listAudit')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}



exports.listAuditSummary = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listAuditSummary')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}





exports.listScoreCard = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listScoreCard')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listApplication = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listApplication')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}
exports.listAction = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listAction')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.listUser = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listUser')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
        const items = resp.body?.data?.searchUser?.items || [];
            const filteredItems = items.filter(item => (item.firstName && item.firstName !== 'null') && (item.lastName && item.lastName !== 'null'));
        resp.body.data.searchUser.items = filteredItems;
            return res.status(200).json(resp.body);

        })
}

exports.searchUser = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/searchUser')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
        const items = resp.body?.data?.searchUserComplement?.items || [];
        const filteredUsers = items
        .filter(item => 
            item.userAzureAttribute?.user?.firstName && 
            item.userAzureAttribute.user.firstName !== 'null' && 
            item.userAzureAttribute.user.lastName && 
            item.userAzureAttribute.user.lastName !== 'null'
        )
        .map(item => item.userAzureAttribute.user);
        resp.body.data = {searchUser:{items:[]}};
        resp.body.data["searchUser"]["items"] = filteredUsers;
            return res.status(200).json(resp.body);
        })
}

exports.listAllUser = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
    .post(process.env.CDF_ENTITY + '/api/service/listUser')
    .send(req.body)
    .set('Content-Type', 'application/json')
    .set('Authorization', `${req.headers.authorization}`)
    .end((error, resp) => {
        const items = resp.body?.data?.searchUser?.items || [];
            const filteredItems = items.filter(item => (item.firstName && item.firstName !== 'null') && (item.lastName && item.lastName !== 'null'));
        resp.body.data.searchUser.items = filteredItems;
    return res.status(200).json(resp.body);

})


}

exports.listUserAzureAttribute = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listUserAzureAttribute')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
      
}
exports.listUserAzureAttributeAllData = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/searchUserAzureAttribute')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
    //     var listAllUserData = []
    // superagent
    //     .post(process.env.CDF_ENTITY + '/api/service/listUserAzureAttribute')
    //     .send({ "limit": 1000,})
    //     .set('Content-Type', 'application/json')
    //     .set('Authorization', `${req.headers.authorization}`)
    //     .end((error, resp) => {
    //       //  return res.status(200).json(resp.body);
    //         var resultTwo = resp.body;
      
    //           if (resultTwo.data && resultTwo.data.listUserAzureAttribute && resultTwo.data.listUserAzureAttribute.items.length > 0) {
    //               listAllUserData = listAllUserData.concat(resultTwo.data.listUserAzureAttribute.items)
    //               getNextData(resultTwo.data.listUserAzureAttribute)
    //           }else{
    //               listWorkHeader();
    //           }

    //     })
    //     async function getNextData(wolist) {
    //         console.log('Next')
    //         //endCursor
    //         console.log('wolist.pageInfo.hasNextPage ',wolist.pageInfo.hasNextPage )
    //         if (wolist.pageInfo.hasNextPage == true) {
    //             superagent
    //                 .post(process.env.CDF_ENTITY + '/api/service/listUserAzureAttribute')
    //                 .send({
    //                     "limit": 1000,
    //                     "cursor": wolist.pageInfo.endCursor
    //                 })
    //                 .set('Content-Type', 'application/json')
    //                 .set('Authorization', `${req.headers.authorization}`)
    //                 .end((error, resp) => {
    //                     // return res.status(200).json(resp.body);
    //                     var resultTwo = resp.body;
    //                     if (resultTwo.data && resultTwo.data.listUserAzureAttribute && resultTwo.data.listUserAzureAttribute.items.length > 0) {
    //                         listAllUserData = listAllUserData.concat(resultTwo.data.listUserAzureAttribute.items)
    //                         getNextData(resultTwo.data.listUserAzureAttribute)
    //                     }else{
    //                         listWorkHeader();
    //                     }
        
    //                 })
        
    //         } else {
    //             listWorkHeader();
    //         }
        
        
    //     }
    //     async function listWorkHeader() {
    //         //listAllUserData
    //         console.log('listAllUserData',listAllUserData.length)
    //         var sendObj = {
    //             "data": {
    //                 "listUserAzureAttribute": {
    //                     "items": listAllUserData
    //                 }
    //             }
    //         }
    //         return res.status(200).json(sendObj);
    //     }
}

exports.fetchAndDelete = function (req, res) {
    // #swagger.ignore = true
    var table = req.query.table ? req.query.table : "";
    var token = `${req.headers.authorization}`;
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listExternalIdsByTableName')
        .send({ "table": req.query.table })
        .set('Content-Type', 'application/json')
        .set('Authorization', token)
        .end((error, resp) => {
            var myData = resp.body.data["list" + table].items;
            console.log("graphURL : OK",myData.length);
            var deleteItems = {
                "items": []
            }
            async.eachSeries(myData, function (eData2, outCb2) {
                deleteItems["items"].push({
                    "instanceType": "node",
                    "externalId": eData2.externalId,
                    "space": eData2.space
                })
                outCb2(null)
            }, function (err) {
                console.log("instanceURL : Start", deleteItems["items"].length);
                superagent
                    .post(process.env.CDF_ENTITY + '/api/service/deleteInstance')
                    .send(deleteItems)
                    .set('Content-Type', 'application/json')
                    .set('Authorization', token)
                    .end((error, resp) => {
                        res.status(200).send({
                            code: 200,
                            status: deleteItems,
                            data: resp.body.data,
                            message: "Successfully"
                        })
                        // fetch(instanceURL, {
                        //     method: 'POST',
                        //     body: JSON.stringify(deleteItems),
                        //     headers: {
                        //         'accept': 'application/json',
                        //         'authorization': req.headers.authorization,
                        //         'Content-Type': 'application/json'
                        //     }
                        // })
                        //     .then(res2 => res2.json())
                        //     .then(json => {
                        //         res.status(200).send({
                        //             code: 200,
                        //             status: "success",
                        //             data: json,
                        //             message: "Successfully"
                        //         })
                    })
            })
        })
}

exports.integrationUser = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    //   superagent
    //       .post(process.env.UM_SERVICE + '/integration/user')
    //       .send(req.body)
    //       .set('Content-Type', 'application/json')
    //       .set('Authorization', `${req.headers.idtoken}`)
    //       .end((error, resp) => {
    //             return res.status(200).json(resp.body);
    //       })
    const result = {
        "displayName": "Nara, Venkata Surendra Reddy, Celanese",
        "firstName": "Venkata Surendra Reddy",
        "lastName": "Nara",
        "email": "<EMAIL>",
        "lanId": "DSCVN3",
        "companyName": "Celanese",
        "jobTitle": "Senior Specialist, IT Application Solutions",
        "department": "Information Technology, It Service Delivery",
        "avatar": "",
        "favoriteReportingSite": null,
        "favoriteLanguage": "EN",
        "sites": [
            {
                "siteId": "STS-BIS",
                "siteName": "Bishop, TX, USA",
                "siteCode": "BIS"
            },
            {
                "siteId": "STS-HYD",
                "siteName": "Hyderabad, India",
                "siteCode": "HYD"
            },
            {
                "siteId": "STS-WAS",
                "siteName": "Washington Works, WV, USA",
                "siteCode": "WAS"
            },
            {
                "siteId": "STS-CLK",
                "siteName": "Clear Lake, TX, USA",
                "siteCode": "CLK"
            },
            {
                "siteId": "STS-ENR",
                "siteName": "Enoree, SC, USA",
                "siteCode": "ENR"
            },
            {
                "siteId": "STS-COR",
                "siteName": "All Sites",
                "siteCode": "COR"
            }
        ],
        "units": [],
        "applications": [
            {
                "applicationCode": "APP-OFWA",
                "roles": [
                    {
                        "roleName": "BIS Business User",
                        "roleCode": "b2015a61-1aea-4af2-86f3-abb22b4745e7",
                        "siteCodes": [
                            "STS-BIS"
                        ],
                        "features": [
                            {
                                "featureCode": "APP-OFWA_ACTION_EDIT",
                                "featureName": "Edit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_ACTION_VIEW",
                                "featureName": "View",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_AUDIT_CREATE_ACTION",
                                "featureName": "Create Action",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_AUDIT_SCORECARD",
                                "featureName": "Scorecard",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_AUDIT_SEND_AUDIT",
                                "featureName": "Send Audit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_AUDIT_SUMMARY",
                                "featureName": "Summary",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_AUDIT_VIEW_AUDIT",
                                "featureName": "View Audit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_CONFIGURATION_ADD_QUESTION",
                                "featureName": "Add Question",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_CONFIGURATION_CONFIGURATION_LIST",
                                "featureName": "Configuration List",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_CONFIGURATION_QUESTION_LIST",
                                "featureName": "Question List",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_FIELD_WALK_CREATE_ACTION",
                                "featureName": "Create Action",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_FIELD_WALK_CREATE_FIELDWALK",
                                "featureName": "Create FieldWalk",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_FIELD_WALK_EDIT",
                                "featureName": "Edit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_FIELD_WALK_VIEW",
                                "featureName": "View",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_FIELD_WALK_VIEW_3D_ASSETS",
                                "featureName": "View 3D Assets",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_HOME_AUDIT",
                                "featureName": "Audit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_HOME_FIELD_WALK",
                                "featureName": "Field Walk",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_HOME_OBSERVATION",
                                "featureName": "Observation",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_OBSERVATION_CREATE_ACTION",
                                "featureName": "Create Action",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_OBSERVATION_CREATE_OBSERVATION",
                                "featureName": "Create Observation",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_OBSERVATION_EDIT",
                                "featureName": "Edit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_OBSERVATION_VIEW",
                                "featureName": "View",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_OBSERVATION_VIEW_3D_ASSETS",
                                "featureName": "View 3D Assets",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_SCHEDULE_CREATE_AUDITSCHEDULE",
                                "featureName": "AuditSchedule",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_SCHEDULE_CREATE_FIELD_WALK_SCHEDULE",
                                "featureName": "Field Walk Schedule",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_SCHEDULE_CREATE_OBSERVATIONSCHEDULE",
                                "featureName": "         ObservationSchedule",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_SCHEDULE_EDIT",
                                "featureName": "Edit",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            },
                            {
                                "featureCode": "APP-OFWA_SCHEDULE_VIEW",
                                "featureName": "View",
                                "featureAccessLevel": "View Access",
                                "featureAccessLevelCode": "ViewAccess"
                            }
                        ]
                    }
                ],
                "userSites": [
                    {
                        "siteId": "STS-BIS",
                        "siteName": "Bishop, TX, USA",
                        "siteCode": "BIS",
                        "businessSegments": [
                            "BUS-EM",
                            "BUS-NA",
                            "BUS-AC",
                            "BUS-CO"
                        ]
                    },
                    {
                        "siteId": "STS-CLK",
                        "siteName": "Clear Lake, TX, USA",
                        "siteCode": "CLK",
                        "businessSegments": [
                            "BUS-AC",
                            "BUS-NA",
                            "BUS-CO"
                        ]
                    },
                    {
                        "siteId": "STS-ENR",
                        "siteName": "Enoree, SC, USA",
                        "siteCode": "ENR",
                        "businessSegments": [
                            "BUS-AC"
                        ]
                    },
                    {
                        "siteId": "STS-HYD",
                        "siteName": "Hyderabad, India",
                        "siteCode": "HYD",
                        "businessSegments": [
                            "BUS-CO"
                        ]
                    },
                    {
                        "siteId": "STS-WAS",
                        "siteName": "Washington Works, WV, USA",
                        "siteCode": "WAS",
                        "businessSegments": [
                            "BUS-NA",
                            "BUS-EM"
                        ]
                    }
                ]
            }
        ]
    };
    return res.status(200).json(result);
}

exports.updateFavouriteLan = function (req, res) {
      // #swagger.ignore = true
// console.log('req.headers',req.headers)
if (req.body) {
    // superagent
    //     .put(process.env.UM_SERVICE + '/integration/upsert_favorite_language')
    //     .send(req.body)
    //     .set('Content-Type', 'application/json')
    //     .set('Authorization', `${req.headers.idtoken}`)
    //     .end((error, resp) => {
    //         return res.status(200).json(resp.body);
    //     })
    return res.status(200).json({});
}else{
    return res.status(200).json({});
}
    
}

exports.notificationevent = function (req, res) {
  // #swagger.ignore = true
  // console.log('req.headers',req.headers)
  superagent
    .post(process.env.NOTIFICATION + "/notification-event")
    .send(req.body)
    .set("Content-Type", "application/json")
    .set("Authorization", `${req.headers.idtoken}`)
    .end((error, resp) => {
      return res.status(200).json(resp.body);
    });
};

exports.notificationgroup = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    // superagent
    //   .get(process.env.NOTIFICATION + "/notification-application-group/APP-OFWA")
    //   .send()
    //   .set("Content-Type", "application/json")
    //   .set("Authorization", `${req.headers.idtoken}`)
    //   .end((error, resp) => {
    //     return res.status(200).json(
    //         resp.body
    //     );
    //   });
    return res.status(200).json({});
  };

exports.unreadNotificationCount = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
if(req.body){
    // superagent
    // .get(process.env.NOTIFICATION + "/notification-on-screen/notification_not_visualized_amount")
    // .send()
    // .set("Content-Type", "application/json")
    // .set("Authorization", `${req.headers.idtoken}`)
    // .end((error, resp) => {
    //   return res.status(200).json(resp.body);
    // });
    return res.status(200).json({});
}else{
    return res.status(200).json({});
}
   
  };
  exports.aggregateObservationOFWA = function (req, res) {
    var allData = [];
    if (req.body.apiType && req.body.apiType == "Aggregate") {
        superagent
            .post(process.env.CDF_ENTITY + '/api/service/aggregateObservationOFWA')
            .send(req.body)
            .set('Content-Type', 'application/json')
            .set('Authorization', `${req.headers.authorization}`)
            .end((error, resp) => {
                var response = resp.text;
                    return res.status(200).json( [resp.body] );
                
 
            })
    }else{
        return res.status(200).json({"value":0});
    }
   
}
async function getNextData(req, allData, apiEndpoint, table, cursor, cb) {
    var reqBody = req.body;
    var reqObj = reqBody;
    reqObj["cursor"] = cursor ? cursor : null;
    console.log(process.env.CDF_ENTITY + apiEndpoint)
    superagent
        .post(process.env.CDF_ENTITY + apiEndpoint)
        .send(reqObj)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
  
            var result = resp.body;
            if (!error && result.data && result.data["list" + [table]]) {
                console.log("data Length >> ", result.data["list" + [table]]["items"].length)
                allData = allData.concat(result.data["list" + [table]]["items"])
                console.log("hasNextPage", result.data["list" + [table]]["pageInfo"]["hasNextPage"])
                if (result.data["list" + [table]]["pageInfo"]["hasNextPage"]) {
                    getNextData(req, allData, apiEndpoint, table, result.data["list" + [table]]["pageInfo"]["endCursor"], function (resData) {
                        cb(resData);
                    })
                } else {
                    cb(allData);
                }
            } else {
                cb(allData);
            }
        })
  }
exports.listReportingLocationAll = function (req, res) {
    var allData = [];
    getNextData(req, allData, "/api/service/listReportingLocation", "ReportingLocation", null, function (resData) {
      return res.status(200).json({ data: resData });
  })
}



exports.listSetting = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    superagent
        .post(process.env.CDF_ENTITY + "/api/service/listSetting")
        .send(req.body)
        .set("Content-Type", "application/json")
        .set("Authorization", `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log(resp.body)
            return res.status(200).json(resp.body);
        });
};

exports.listOFWALog = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listOFWALog')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            return res.status(200).json(resp.body);
        })
}

exports.processData = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    var allData = [];
    getNextData(req, allData, "/api/service/processData", "OFWAProcess", null, function (resData) {
      return res.status(200).json({ data: resData });
  })
};
exports.getUnitInfo= function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    superagent
      .get(process.env.UM_SERVICE + "/users/get-unit-info?user="+req.body.user+"&site_id="+req.body.site_id)
      .set("Content-Type", "application/json")
      .set('Authorization', `${req.headers.idtoken}`)
      .end((error, resp) => {
        return res.status(200).json(resp.body);
      });
  };

    exports.getRolesInfo= function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    superagent
      .get(process.env.UM_SERVICE + "/roles/get-roles?getRolesInfo="+req.body.role+"&site_id="+req.body.site+"&application_id="+req.body.application)
      .set("Content-Type", "application/json")
      .set('Authorization', `${req.headers.idtoken}`)
      .end((error, resp) => {
        return res.status(200).json(resp.body);
      });
  };
  
exports.fieldWalkAttendee = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    var allData = [];
    var token = `${req.headers.authorization}`;
    getNextData(req, allData, "/api/service/listFieldWalk", "FieldWalk", null, function (resData) {

        
        async.eachSeries(resData, function (eData2, outCb2) {
            if(eData2.attendee){
                var edgeObj = {
                    "instanceType": "edge",
                    "space": eData2["space"],
                    "externalId": eData2["externalId"] + "-" + eData2["attendee"]["externalId"],
                    "type": {
                      "space": constant.configuration["DataModelSpace"],
                      "externalId": constant.configuration["typeFieldWalk"] + ".refAttendee"
                    },
                    "startNode": {
                      "space": eData2["space"],
                      "externalId": eData2["externalId"]
                    },
                    "endNode": {
                      "space": eData2["attendee"].space,
                      "externalId": eData2["attendee"].externalId
                    }
                  }
                  allData.push(edgeObj);
            }
          
            outCb2(null)
        }, function (err) {
           
            superagent
                .post(process.env.CDF_ENTITY + '/api/service/createEdge')
                .send(allData)
                .set('Content-Type', 'application/json')
                .set('Authorization', token)
                .end((error, resp) => {
                    res.status(200).send({
                        code: 200,
                        status: allData,
                        data: resp.body.data,
                        message: "Successfully"
                    })
                   
                })
        })
     // return res.status(200).json({ data: resData });
  })
};
  exports.listUserRoleSite = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
      superagent
          .post(process.env.CDF_ENTITY + '/api/service/listUserRoleSite')
          .send(req.body)
          .set('Content-Type', 'application/json')
          .set('Authorization', `${req.headers.authorization}`)
          .end((error, resp) => {
              if (!error) {
                  var userRoleList = resp.body["data"]["list" + constant.configuration["typeUserRoleSite"]]["items"];
                  var myRoleUsers = [];
                  async.eachSeries(userRoleList, function (eData1, outCb1) {
                      async.eachSeries(eData1.usersComplements["items"], function (eData2, outCb2) {
                          myRoleUsers.push(eData2);
                          outCb2();
                      }, function (err2) {
                          outCb1(null)
                      })
                  }, function (err1) {
                    const uniqueArray = myRoleUsers.filter((value, index, self) =>
                        index === self.findIndex((t) => (
                          t.userAzureAttribute.externalId === value.userAzureAttribute.externalId
                        ))
                      );
                    return res.status(200).json(uniqueArray);
                  })
              } else {
                  return res.status(200).json([]);
              }
          })
}

exports.getDataSetId = function (req, res) {
    // #swagger.ignore = true
    // console.log('req.headers',req.headers)
    superagent
        .post(process.env.CDF_ENTITY + "/api/service/getDataSetId")
        .send(req.body)
        .set("Content-Type", "application/json")
        .set("Authorization", `${req.headers.authorization}`)
        .end((error, resp) => {
            console.log(resp.body)
            return res.status(200).json(resp.body);
        });
};

exports.staticTranslation = function (req, res) {
    // #swagger.ignore = true
// console.log('req.headers',req.headers)
  superagent
      .post(process.env.TRANSLATION_SERVICE + '/static')
      .send(req.body)
      .set('Content-Type', 'application/json')
      .set('Authorization', `${req.headers.idtoken}`)
      .end((error, resp) => {
            return res.status(200).json(resp.body);
      })
}

exports.listWorkOrderHeader = function (req, res) {
  // #swagger.ignore = true
  // console.log('req.headers',req.headers)
  superagent
      .post(process.env.CDF_ENTITY + "/api/service/listWorkOrderHeader")
      .send(req.body)
      .set("Content-Type", "application/json")
      .set("Authorization", `${req.headers.authorization}`)
      .end((error, resp) => {
          console.log(resp.body)
          return res.status(200).json(resp.body);
      });
};

exports.dynamicTranslation = function (req, res) {
    // #swagger.ignore = true
// console.log('req.headers',req.headers)
console.log('req.body',req.body)
  superagent
      .post(process.env.TRANSLATION_SERVICE + '/dynamic')
      .send(req.body)
      .set('Content-Type', 'application/json')
      .set('Authorization', `${req.headers.idtoken}`)
      .end((error, resp) => {
            // return res.status(200).json(resp.body);
            // if (resp.body && resp.statusCode == 200) {
            //     return res.status(200).json(resp.body);
            // }
            return res.status(200).json(resp.body);
      })
}

exports.listCompExAsset = function (req, res) {
    /* 	#swagger.tags = ['Business']  */

    /*	#swagger.requestBody = {
        required: true,
        schema: { $ref: "#/definitions/body" }
       } */
    /* #swagger.security = [{
       "bearerAuth": []
}] */
    superagent
        .post(process.env.CDF_ENTITY + '/api/service/listCompExAsset')
        .send(req.body)
        .set('Content-Type', 'application/json')
        .set('Authorization', `${req.headers.authorization}`)
        .end((error, resp) => {
            
            return res.status(200).json(resp.body);
        })
}