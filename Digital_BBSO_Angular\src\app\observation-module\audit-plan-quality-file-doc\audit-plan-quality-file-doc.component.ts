import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-audit-plan-quality-file-doc',
  templateUrl: './audit-plan-quality-file-doc.component.html',
  styleUrls: ['./audit-plan-quality-file-doc.component.scss']
})
export class AuditPlanQualityFileDocComponent implements OnInit {

  uploadDocArray: any = [];
  selectedSite: any;
  auditData: any;
  loaderFlag: boolean;
  labels = {}
  constructor(private sanitizer: DomSanitizer, private router: Router, private dataService: DataService, private commonService: CommonService, private translate: TranslateService, private languageService: LanguageService, private ngZone: NgZone, private changeDetector: ChangeDetectorRef) { 
    this.labels = {
      'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
      'treeheaderDocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderDocument'] || 'treeheaderDocument',
      'treeheaderVerificationcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderVerificationcomments'] || 'treeheaderVerificationcomments',
      'documentUploaded': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'documentUploaded'] || 'documentUploaded',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
    }
  }

  ngOnInit(): void {
    var _this = this;
    console.log(history.state.data)
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
          'treeheaderDocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderDocument'] || 'treeheaderDocument',
          'treeheaderVerificationcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderVerificationcomments'] || 'treeheaderVerificationcomments',
          'documentUploaded': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'documentUploaded'] || 'documentUploaded',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
    _this.loaderFlag = true;
    if (history.state.data) {
      _this.auditData = history.state.data;
      var site = _this.commonService.siteList.find(e => e.externalId == history.state.data["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
      _this.selectedSite = site;
      _this.dataService.postData({ "name": "EvidenceChecklist" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
        var checkList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
        // _this.dataService.postData({ schedules: _this.auditData.refOFWASchedule.externalId }, _this.dataService.NODE_API + "/api/service/listEvidenceChecklist").subscribe(data => {
        //   var resData = data["data"]["list" + _this.commonService.configuration["typeEvidenceChecklist"]]["items"];
        var evidenceData = _this.auditData["evidenceDocument"]["items"];
        _this.loaderFlag = false;
        console.log(checkList)
        console.log(evidenceData)
        var myChecklist = _this.auditData["refOFWASchedule"]["evidenceChecklist"].length>0 ? _this.auditData["refOFWASchedule"]["evidenceChecklist"] :checkList
        _.each(myChecklist, function (params: any) {
          var myValue = evidenceData.filter(e => params == e.name);
          console.log(myValue)
          _this.loaderFlag = false;
          if (myValue.length > 0) {
            myValue.forEach(myValue => {
            var myData = {
              "externalId": myValue.externalId,
              "name": params,
              "upload": new FormControl(),
              "description": new FormControl(myValue.description),
              "isDocumentUploaded": new FormControl(myValue.evidenceDocumentId ? "Yes" : "No")
            }
            myData["document"] = myValue;

            _this.uploadDocArray.push(myData)
          });
          } else {
            _this.uploadDocArray.push({
              "name": params,
              "upload": new FormControl(),
              "description": new FormControl(""),
              "isDocumentUploaded": new FormControl("No")
            })
          }
        })
        // })
      })

      // _this.dataService.postData({ externalId: history.state.externalId }, _this.dataService.NODE_API + "/api/service/listEvidenceChecklist").subscribe(data => {
      //   var resData = data["data"]["list" + _this.commonService.configuration["typeEvidenceChecklist"]]["items"];
      //   console.log(resData);
      //   _.each(resData, function (params: any) {
      //     _this.uploadDocArray.push({
      //       "externalId": params.externalId,
      //       "upload": new FormControl(),
      //       "comment":new FormControl(params.comment),
      //       "isDocumentUploaded":params.isDocumentUploaded
      //     })
      //   })
      // })
    } else {
      _this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }
  }
  uploadFile($event) {
    console.log($event.target.files[0]); // outputs the first file
  }
  fileOpen(item) {
    console.log(item)
    var _this = this;
    _this.loaderFlag = true;
    var fileId = item["document"]["evidenceDocumentId"];
    _this.dataService.getImage(_this.commonService.configuration["AzureAudience"] + "/api/v1/projects/" + _this.commonService.configuration["Project"] + "/documents/" + fileId + "/preview/image/pages/1").
      subscribe((resData: any) => {
        let objectURL = URL.createObjectURL(resData);
        _this.loaderFlag = false;
        window.open(objectURL, '_blank');
        // _this.imageSrc = _this.sanitizer.bypassSecurityTrustUrl(objectURL);
      });
  }
  goPage(page) {
    this.router.navigate([page]);
  }

  submitClick() {
    var _this = this;
    _this.loaderFlag = false;
    console.log(this.uploadDocArray)
    var postObj = {
      "type": _this.commonService.configuration["typeEvidenceDocument"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": []
    }
    _.each(this.uploadDocArray, function (eData) {
      var myObj = {};
      if (eData.externalId && eData.externalId.length > 0) {
        myObj["externalId"] = eData.externalId;
      } else {
        myObj["name"] = eData.name;
      }
      myObj["description"] = eData.description.value;
      // myObj["isDocumentUploaded"] = eData.isDocumentUploaded.value && eData.isDocumentUploaded.value == "Yes" ? true : false;
      if (eData.description.value && eData.description.value.length > 0) {
        postObj.items.push(myObj);


      }
    })
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = true;
      if (data["items"].length > 0) {
        console.log(data["items"])
        _this.edgeCreation(data["items"]);
      } else {
        _this.loaderFlag = false;
        _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
        _this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
      }

    })
  }

  edgeCreation(edge) {
    var _this = this;
    var myEvidenceArray = [];
    _.each(edge, function (eData) {

      var edgeObj = {
        "instanceType": "edge",
        "space": _this["auditData"]["space"],
        "externalId": _this["auditData"]["externalId"] + "-" + eData["externalId"],
        "type": {
          "space": _this.commonService.configuration["DataModelSpace"],
          "externalId": _this.commonService.configuration["typeAudit"] + ".evidenceDocument"
        },
        "startNode": {
          "space": _this["auditData"]["space"],
          "externalId": _this["auditData"]["externalId"]
        },
        "endNode": {
          "space": eData["space"],
          "externalId": eData["externalId"]
        }
      }
      myEvidenceArray.push(edgeObj)
    });
    _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
      _this.loaderFlag = false;
      _this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterSavesuccess'] });
      _this.router.navigate(['observations/list'], { state: { listType: "Audit" } });

    });
  }
  goAudit(){
    this.router.navigate(['observations/list'], { state: {  listType: "Audit" } });
  }
}
