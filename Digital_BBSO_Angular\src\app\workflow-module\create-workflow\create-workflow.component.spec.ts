import { ComponentFixture, TestBed, async, fakeAsync } from '@angular/core/testing';

import { CreateWorkflowComponent } from './create-workflow.component';
import { AppModule } from 'src/app/app.module';

// describe('CreateWorkflowComponent', () => {
//   let component: CreateWorkflowComponent;
//   let fixture: ComponentFixture<CreateWorkflowComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ CreateWorkflowComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(CreateWorkflowComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });

fdescribe('Workflow test', () => {
  let component: CreateWorkflowComponent;
  let fixture: ComponentFixture<CreateWorkflowComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AppModule]
    })
    .compileComponents();
  });

  beforeEach(async(() => {
    fixture = TestBed.createComponent(CreateWorkflowComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should not valid', () => {
    expect(component).toBeTruthy();
  });
  it('should true false', () => {
    expect(true).toBe(true);
  });



});
