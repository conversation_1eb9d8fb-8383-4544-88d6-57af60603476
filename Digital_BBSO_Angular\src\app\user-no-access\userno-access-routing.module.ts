import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserNopageComponent } from './user-nopage/user-nopage.component';

const routes: Routes = [
    {
        path: '',
        component: UserNopageComponent
    },
    {
        path: 'no-access', component: UserNopageComponent
    }

];

@NgModule({
  imports: [
    RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserAccessRoutingModule { }