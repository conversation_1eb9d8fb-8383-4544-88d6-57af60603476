<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="audit-plan-unit-section schedular-tracker-section" style="float: right;">
            <div class="audit-head-icon">
                <div class="icon-bg-box schedular-tracker-back-icon" (click)="goAudit()">
                    <mat-icon>arrow_back_ios</mat-icon>
                </div>
            </div>
        </div>
        <div class="action-section-unit-section">
            <commom-label labelText="{{ labels['scorecard'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
        </div>

        <div class="scorecard-section mt-10">
            <div class="scorecard-card">
                <div class="dashboard-cst-card">
                    <div class="left-part">
                        <span class="unit">
                            <span>
                                {{scorePercent}}
                            </span> <span class="unit-key">%</span>
                        </span>
                        <!-- <span class="bt-text">{{ 'OBSERVATION.SCORECARD.CARDS.TITLE' | translate }}</span> -->
                    </div>
                    <div class="right-part">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" width="64.562" height="64.481"
                                viewBox="0 0 64.562 64.481">
                                <path id="sell_FILL0_wght400_GRAD0_opsz48"
                                    d="M118.56-816.968a4.771,4.771,0,0,1-3.5,1.449,4.771,4.771,0,0,1-3.5-1.449L81.369-847.156a4.223,4.223,0,0,1-1.087-1.69A6.07,6.07,0,0,1,80-850.7V-875.17a4.691,4.691,0,0,1,1.369-3.462A4.691,4.691,0,0,1,84.83-880H109.3a6.87,6.87,0,0,1,1.932.282,4.133,4.133,0,0,1,1.771,1.087L143.032-848.6a4.906,4.906,0,0,1,1.53,3.582,4.906,4.906,0,0,1-1.53,3.582Zm-3.3-3.3,24.472-24.472L109.3-875.17H84.83V-850.7ZM93.283-862.612a4.02,4.02,0,0,0,2.938-1.248,4.02,4.02,0,0,0,1.248-2.938,4.02,4.02,0,0,0-1.248-2.938,4.02,4.02,0,0,0-2.938-1.248,4.02,4.02,0,0,0-2.938,1.248A4.02,4.02,0,0,0,89.1-866.8a4.02,4.02,0,0,0,1.248,2.938A4.02,4.02,0,0,0,93.283-862.612ZM84.83-875.17Z"
                                    transform="translate(-80 880)" fill="#0095c7" opacity="0.15" />
                            </svg>
                        </i>
                    </div>
                </div>
            </div>

            <div fxLayout="row" fxLayoutGap.md="40px" fxLayoutGap.sm="40px" fxLayoutGap.xs="40px" fxLayout.sm="column"
                fxLayout.xs="column" fxLayout.md="column">
                <div fxFlex="50">
                    <div fxLayout="row" fxLayoutAlign="start center" class="listHeadBox">
                        <div fxFlex="50" class="paddingLeft-10" fxLayoutAlign="start center">
                            <span class="subheading-1">
                                {{ labels['tableheaderSubcats'] }}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10 padding-right_10" fxLayoutAlign="start center">
                            <span class="subheading-1">
                                {{ labels['tableheaderSatisfactory'] }}
                            </span>
                        </div>
                        <div fxFlex="10" class="paddingLeft-10 padding-right_10 " fxLayoutAlign="start center">
                            <span class="subheading-1">
                                {{ labels['tableheaderOfi'] }}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10 padding-right_10 " fxLayoutAlign="start center">
                            <span class="subheading-1">
                                {{ labels['tableheaderUnsatisfactory'] }} 
                            </span>
                        </div>


                    </div>
                    <div fxLayout="row" fxLayoutAlign="start center" class="blueLightBox marginTop padding-right_10"
                        *ngFor="let item of scoreCardArr">
                        <div fxFlex="50" class="paddingLeft-10">
                            <span class="caption" fxLayoutAlign="start center">
                                {{item.subCategory}}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10">
                            <span class="caption" fxLayoutAlign="center center">
                                {{item.satisfactory}}
                            </span>
                        </div>
                        <div fxFlex="10" class="paddingLeft-10">
                            <span class="caption" fxLayoutAlign="center center">
                                {{item.ofi}}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10">
                            <span class="caption" fxLayoutAlign="center center">
                                {{item.unSatisfactory}}
                            </span>
                        </div>


                    </div>
                    <div fxLayout="row" fxLayoutAlign="start center"
                        class="blueLightBox marginTop padding-right_10 bold">
                        <div fxFlex="50" class="paddingLeft-10">
                            <span class="body-2" fxLayoutAlign="start center">
                                {{ labels['tableheaderNofindings'] }}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10">
                            <span class="body-2" fxLayoutAlign="center center">
                                {{scoreArrayTotal.satisfactory}}
                            </span>
                        </div>
                        <div fxFlex="10" class="paddingLeft-10">
                            <span class="body-2" fxLayoutAlign="center center">
                                {{scoreArrayTotal.ofi}}
                            </span>
                        </div>
                        <div fxFlex="20" class="paddingLeft-10">
                            <span class="body-2" fxLayoutAlign="center center">
                                {{scoreArrayTotal.unSatisfactory}}
                            </span>
                        </div>


                    </div>
                </div>

                <div fxFlex="50">
                    <div style="width: 420px;float: right;">

                        <div>
                            <span class="subheading-2 bold observe-color">
                                {{ labels['tableheaderSummary']}}
                            </span>
                        </div>
                        <div>
                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="listHeadBox marginTop padding-right_10" >
                                <div class="paddingLeft-10">
                                    <span class="caption" fxLayoutAlign="start center">
                                        {{ labels['tableheaderNoauditq'] }} 
                                    </span>
                                </div>
                                <div class="paddingLeft-10 " fxLayoutAlign="start center">
                                    <span class="caption">
                                        {{checklistCount}}
                                    </span>
                                </div>
                            </div>

                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="listHeadBox marginTop padding-right_10" >
                                <div class="paddingLeft-10">
                                    <span class="caption" fxLayoutAlign="start center">
                                        {{ labels['tableheaderNoqaudit'] }}
                                    </span>
                                </div>
                                <div class="paddingLeft-10 " fxLayoutAlign="start center">
                                    <span class="caption">
                                        {{auditChecklistCount}}
                                    </span>
                                </div>
                            </div>

                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="listHeadBox marginTop padding-right_10" >
                                <div class="paddingLeft-10">
                                    <span class="caption" fxLayoutAlign="start center">
                                        {{ labels['tableheaderUnsatweightscore'] }}
                                    </span>
                                </div>
                                <div class="paddingLeft-10 " fxLayoutAlign="start center">
                                    <span class="caption">
                                        {{unSatisfactoryWeightage}}
                                    </span>
                                </div>
                            </div>

                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="listHeadBox marginTop padding-right_10" >
                                <div class="paddingLeft-10">
                                    <span class="caption" fxLayoutAlign="start center">
                                        {{ labels['tableheaderOfiweightscore'] }}
                                    </span>
                                </div>
                                <div class="paddingLeft-10 " fxLayoutAlign="start center">
                                    <span class="caption">
                                        {{ofiWeightage}}
                                    </span>
                                </div>
                            </div>

                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="listHeadBox marginTop padding-right_10" >
                                <div class="paddingLeft-10">
                                    <span class="caption" fxLayoutAlign="start center">
                                        {{ labels['tableheaderSatweightscore'] }}
                                    </span>
                                </div>
                                <div class="paddingLeft-10 " fxLayoutAlign="start center">
                                    <span class="caption">
                                        {{satisfactoryWeightage}}
                                    </span>
                                </div>
                            </div>

                        </div>

                    </div>



                </div>

            </div>
        </div>
    </div>
</div>
