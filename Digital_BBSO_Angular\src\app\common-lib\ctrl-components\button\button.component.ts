import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { TranslationService } from "src/app/services/TranslationService/translation.services";
import { ButtonType } from "src/enum/common.enum";


@Component({
    selector: 'common-lib-button',
    templateUrl: './button.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class ButtonComponent implements OnInit, AfterViewInit {
  @Input() icon:string = '';
  @Input() text:string = '';
  @Input() className:string = '';
  @Input() ButtonType:any = ButtonType.PRIMARY;
  @Input() disable:boolean = false;
  @Output() buttonAction = new EventEmitter<boolean>();
  constructor(public translationService:TranslationService, public changeDetector: ChangeDetectorRef) {
    console.log(ButtonType.PRIMARY)
  }
  
  ngOnInit(): void {
      this.changeDetector.detectChanges();
  }

  ngAfterViewInit(): void {
    this.changeDetector.detectChanges
  }

  onClickButton():void {
    this.buttonAction.emit(true);
  }
}
