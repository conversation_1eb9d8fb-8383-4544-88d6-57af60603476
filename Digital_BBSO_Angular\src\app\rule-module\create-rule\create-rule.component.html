<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="rule-section">
            <div class="action-section-unit-section">
                <commom-label [labelText]="'RULE.CREATE_RULE.TITLE'" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
            </div>

            <form [formGroup]="createRuleForm">
                <div class="marginTop">
                    <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.RULE_NAME' | translate }}</span>
                    <span class="start-color">
                        *
                    </span>
                </div>
                <div>
            
                    <mat-form-field appearance="outline" class="location-input">
            
                        <input type="text" formControlName="ruleName" class="input" value="" placeholder="" aria-span="Search"
                            matInput>
            
                    </mat-form-field>
                </div>
            
                <div class="marginTop" fxLayout="row" fxLayoutAlign="start center">
                    <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.ADD' | translate }}</span>
                    &nbsp; &nbsp; &nbsp; &nbsp;
                    <div (click)="enableForm()">
                        <mat-icon class="add-circle-icon" style="margin-top: 10px;">add_circle</mat-icon>
                    </div>
            
                </div>
                <div *ngIf="addorm==true">
                    <div fxLayout="row wrap" fxLayoutGap="30px" class="marginTop">
                        <div>
                            <div>
                                <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.WHEN' | translate }}</span>
                            </div>
                            <div>
            
                                <mat-form-field appearance="outline">
            
                                    <mat-select formControlName="schedule">
                                        <mat-option value="Schedule">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.SCHEDULE' | translate }}</mat-option>
            
                                    </mat-select>
                                </mat-form-field>
                            </div>
            
                        </div>
                        <div>
                            <div>
                                <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.OPERATOR' | translate }}</span>
                            </div>
                            <div>
            
                                <mat-form-field appearance="outline">
            
                                    <mat-select formControlName="operators">
                                        <mat-option value="Greater than">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.GREATER_THAN' | translate }}</mat-option>
            
                                    </mat-select>
                                </mat-form-field>
                            </div>
            
                        </div>
                        <div>
                            <div>
                                <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.VALUE' | translate }}</span>
                            </div>
                            <div>
            
                                <mat-form-field appearance="outline">
            
                                    <mat-select formControlName="dueDate">
                                        <mat-option value="Due date">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.DUE_DATE' | translate }}</mat-option>
            
                                    </mat-select>
                                </mat-form-field>
                            </div>
            
                        </div>
            
            
                    </div>
                    <br>
                    <div fxLayout="row wrap" fxLayoutGap="30px" class="marginTop">
                        <div>
                            <div>
                                <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.THEN' | translate }}</span>
                            </div>
                            <div>
            
                                <mat-form-field appearance="outline">
            
                                    <mat-select formControlName="thenAlert">
                                        <mat-option value="Alert">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.ALERT' | translate }}</mat-option>
                                        <mat-option value="Workflow">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.WORKFLOW' | translate }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
            
                        </div>
                        <div>
                            <div>
                                <span class="subheading-1 regular">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.CHOOSE_WORKFLOW' | translate }}</span>
                            </div>
                            <div>
            
                                <mat-form-field appearance="outline">
            
                                    <mat-select formControlName="chooseWorkflow">
                                        <mat-option value="Email">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.EMAIL' | translate }}</mat-option>
                                        <mat-option value="Text">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.TEXT' | translate }}</mat-option>
                                        <mat-option value="Teams">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.TEAMS' | translate }}</mat-option>
                                        <mat-option value="Email & Text">{{ 'RULE.CREATE_RULE.FORM_CONTROLS.EMAIL_TEXT' | translate }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
            
                        </div>
            
            
                    </div>
            
                </div>
            
                <div class="mt-150 btn-section">
                    <common-lib-button [className]="'cst-btn cancel'" [text]="'BUTTON.CANCEL'"
                         (buttonAction)="goPage('rule-list')"></common-lib-button>
                    <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.SAVE'"
                         (buttonAction)="saveRules()"></common-lib-button>
                </div>
            </form>
        </div>

    </div>
</div>