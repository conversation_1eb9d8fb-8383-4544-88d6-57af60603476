import { environment } from "src/environments/environment"
import { DataService } from "../data.service"

let dataService: DataService;
const APP_ID: string = environment.applicationId || 'APP-OFWA'
const TRANSLATION_URL = environment.translationUrl || 'http://127.0.0.1:8000/translation'

const staticTranslationURL = TRANSLATION_URL + "/static"
const dynamicTranslationURL = TRANSLATION_URL + "/dynamic"

export async function getStaticTranslationData(
    getAuthToken: () => Promise<string>,
    app: string = APP_ID,
    lang: string = 'LAN-EN'
) {
    const token = await getAuthToken()
    console.log('token translation:', token)
    // const translation: any = await fetch(
    //     staticTranslationURL, {
    //         method: 'POST',
    //         headers: {
    //             'Content-Type': 'application/json',
    //             Authorization: `Bearer ${token}`
    //         },
    //         body: JSON.stringify({
    //             application:app,
    //             language:lang
    //         }),
    //     }
    // ).catch((error) => {
    //     console.log('Error in getting translation data', error);
    // })

    dataService.postData({
                    application:app,
                    language:lang
                },
        staticTranslationURL
      )
      .subscribe((data:any) => {
        console.log("Translation data: ", data)
      });

    // if (translation.status == 403) {
        
    // }

} 


