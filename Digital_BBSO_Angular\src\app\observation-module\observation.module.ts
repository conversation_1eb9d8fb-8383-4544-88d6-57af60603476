import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { ObservationRoutingModule } from "./observation.routing.module";
import { ObservationsComponent } from "./observations/observations.component";
import { MainObservationComponent } from "./main-observation/main-observation.component";
import { ObservationListComponent } from "./observation-list/observation-list.component";
import { CreateObservationComponent } from "./create-observation/create-observation.component";
import { SubObservationComponent } from "./sub-observation/sub-observation.component";
import { BehaviourChecklistComponent } from "./behaviour-checklist/behaviour-checklist.component";
import { FieldVisitComponent } from "./field-visit/field-visit.component";
import { QualityAssessmentComponent } from "./quality-assessment/quality-assessment.component";
import { QualityUploadDocComponent } from "./quality-upload-doc/quality-upload-doc.component";
import { AuditPlanQualityAssComponent } from "./audit-plan-quality-ass/audit-plan-quality-ass.component";
import { AuditPlanQualityFileDocComponent } from "./audit-plan-quality-file-doc/audit-plan-quality-file-doc.component";
import { AuditPlanScorecardComponent } from "./audit-plan-scorecard/audit-plan-scorecard.component";
import { AuditPlanSummaryComponent } from "./audit-plan-summary/audit-plan-summary.component";
import { NgMultiSelectDropDownModule } from "ng-multiselect-dropdown";
import { FieldWalkListComponent } from "./fieldwalk-list/fieldwalk-list.component";
import { AuditListComponent } from "./audit-list/audit-list.component";
import { CompletedComponent } from "./completed/completed.component";
import { ListComponent } from "./list/list.component";
import { AngularSplitModule } from "angular-split";
import { ObservationSendMailComponent } from "./observation-send-mail/observation-send-mail.component";
import { fieldSendMailComponent } from "./fieldwalk-mail/field-send-mail.component";
import { checklistmetricsComponent } from "./Checklist-Metrics/checklist-metrics.component";
import { BarchartdetailsComponent } from "../barchartdetails/barchartdetails.component";
import { CustomPaginatorIntl } from "../services/custom-paginator-intl.service";
import { MatPaginatorIntl } from "@angular/material/paginator";
import { actionchecklistComponent } from "../actions-module/action-checklist/action-checklist.component";
import { generalinfoComponent } from "./general-info/general-infocomponent";
import { CriticalChecklistComponent } from "./critical-checklist/critical-checklist.component";
import {procedureauditchecklistComponent} from "./procedure-audit-checklist/procedure-audit-checklist.component";
@NgModule({
    declarations: [
        ObservationsComponent,
        MainObservationComponent,
        ObservationListComponent,
        CreateObservationComponent,
        SubObservationComponent,
        BehaviourChecklistComponent,
        actionchecklistComponent,
        CriticalChecklistComponent,
        procedureauditchecklistComponent,
        FieldVisitComponent,
        QualityAssessmentComponent,
        generalinfoComponent,
        QualityUploadDocComponent,
        AuditPlanQualityAssComponent,
        AuditPlanQualityFileDocComponent,
        AuditPlanScorecardComponent,
        checklistmetricsComponent,
        AuditPlanSummaryComponent,
        FieldWalkListComponent,
        AuditListComponent,
        CompletedComponent,
        ListComponent,
        ObservationSendMailComponent,
        fieldSendMailComponent,
        BarchartdetailsComponent
    ],
    imports: [
        CommonModule,
        ObservationRoutingModule,
        SharedModule,
        AngularSplitModule,
        NgMultiSelectDropDownModule.forRoot(),
    ],
    providers: [
        { provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }
      ],
  })
  export class ObservationModule { }
