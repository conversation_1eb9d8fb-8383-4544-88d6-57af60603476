'use strict';

module.exports.AppCode        = "OFWA";
module.exports.CMNAppCode     = "CMN";
module.exports.ActionCode     = "OFWA";
module.exports.AppTranslationCode     = "ATL";
module.exports.dataSpaceCode  = "DAT";
module.exports.CMNDataSpace   = "CMN-COR-ALL-DAT";
module.exports.CoreDataSpace  = "REF-COR-ALL-DAT";
module.exports.userDataSpace  = "UMG-COR-ALL-DAT";
module.exports.ActionSpace  = "AIM-COR-ALL-DMD";
module.exports.TranslationSpace  = "ATL-COR-ALL-DML";
module.exports.Project        = "celanese-dev"
module.exports.AzureAudience  = "https://az-eastus-1.cognitedata.com" 
module.exports.VendorUrl      = "https://app-dplantbbsoangular-d-ussc-01.azurewebsites.net"
//AIM-WAS-ALL-DAT


module.exports.allSiteCode  = "COR";
module.exports.allUnitCode  = "ALL";


module.exports.DataModelSpace      = "INO-COR-ALL-DML";
module.exports.DataModelExternalId = "OFWASOL";

module.exports.DataModelVersion    = "1_2_2";

module.exports.CMNDataModelSpace      = "INO-COR-ALL-DML";
module.exports.CMNDataModelExternalId = "CMNSOL";
module.exports.CMNDataModelVersion    = "1_0_12";

module.exports.USERDataModelSpace         = "UMG-COR-ALL-DMD";
module.exports.USERDataModelExternalId = "UserManagementDOM";
module.exports.USERDataModelVersion    = "8_5_1";

module.exports.MaintenanceDataModelSpace      = "EDG-COR-ALL-DMD";
module.exports.MaintenanceDataModelExternalId = "MaintenanceDOM";
module.exports.MaintenanceDataModelVersion    = "2_5_0";

module.exports.typeProcess = "OFWAProcess";
module.exports.typeProcessConfiguration = "OFWAProcessConfig";
module.exports.typeOFWAQuestion = "OFWAQuestion";
module.exports.typeObservation ="Observation";
module.exports.typeDepartment ="Department";
module.exports.typeChecklist ="OFWAChecklist";
module.exports.typeSchedule = "OFWASchedule";
module.exports.typeOFWAScheduleDetail = "OFWAScheduleDetail";
module.exports.typeOFWALog = "OFWALog";
module.exports.typeFieldWalk = "FieldWalk";
module.exports.typeAudit = "OFWAAudit";
module.exports.typeVendor = "OFWAVendor";
module.exports.typeSupplier = "Supplier";
module.exports.typeSummary = "OFWAAuditSummary";
module.exports.typeScoreCard = "OFWAScoreCard";
module.exports.typeAuditSummary = "OFWAAuditSummary";
module.exports.typeSetting = "OFWASetting";
module.exports.typeLabelTranslation = "LabelTranslation";
module.exports.typeWorkOrderHeader = "WorkOrderHeader";
module.exports.typeCompExAsset = "CompExAsset";

//New
module.exports.typeTracker = "OFWATracker";
//action
module.exports.typeAction = "Action";
module.exports.typeActionItemEvent = "ActionItemEvent";



module.exports.typeEvidenceDocument = "EvidenceDocument";

//Common
module.exports.typeTypeSequence = "TypeSequence";
module.exports.typeCommonRefEnum = "CommonRefEnum"; 

//AssetHierarchyDOM
module.exports.typeFunctionalLocation = "FunctionalLocation";
module.exports.typeEquipment = "Equipment";
module.exports.typeReportingSite = "ReportingSite";
module.exports.typeReportingUnit = "ReportingUnit";
module.exports.typeGeoRegion = "GeoRegion";
module.exports.typeReportingLocation = "ReportingLocation";
module.exports.typeCountry = "Country";
module.exports.typeBusinessLine = "BusinessLine";

module.exports.typeUser = "User";
module.exports.typeUserAzureAttribute = "UserAzureAttribute";
module.exports.typeUserComplement = "UserComplement";
module.exports.typeApplication = "Application";
module.exports.typeUserRoleSite = "UserRoleSite";


module.exports.codeObservation = "dd461788436eb7";
module.exports.codeFieldWalk = "4b655c3fcded9b";
module.exports.codeAudit = "28154b160cfd07";
module.exports.codeAction = "3b87efad8c05fe";
module.exports.codeChecklist = "9808929c92be44";

module.exports.dateFromat  = "MM/DD/YYYY";
module.exports.timeFromat  = "HH:mm";


module.exports.dataTypeList = {
  [module.exports.typeProcess]: module.exports.AppCode,
  [module.exports.typeProcessConfiguration] : module.exports.AppCode,
  [module.exports.typeOFWAQuestion] : module.exports.AppCode,
  [module.exports.typeObservation] : module.exports.AppCode,
  [module.exports.typeChecklist] : module.exports.AppCode,
  [module.exports.typeFieldWalk] : module.exports.AppCode,
  [module.exports.typeVendor] : module.exports.AppCode,
  [module.exports.typeSchedule] : module.exports.AppCode,
  [module.exports.typeOFWAScheduleDetail] : module.exports.AppCode,
  [module.exports.typeOFWALog] : module.exports.AppCode,
  [module.exports.typeScoreCard] : module.exports.AppCode,
  [module.exports.typeAuditSummary] : module.exports.AppCode,
  [module.exports.typeSetting] : module.exports.AppCode,
  [module.exports.typeCompExAsset] : module.exports.AppCode,

  [module.exports.typeAudit] : module.exports.AppCode,
  [module.exports.typeSummary] : module.exports.AppCode,

  [module.exports.typeTracker] : module.exports.AppCode,
  [module.exports.typeScoreCard] : module.exports.AppCode,
  [module.exports.typeLabelTranslation] : module.exports.AppTranslationCode,

  //CMN
  [module.exports.typeTypeSequence]: module.exports.CMNAppCode,
  [module.exports.typeCommonRefEnum]: module.exports.CMNAppCode,
  [module.exports.typeEvidenceDocument] : module.exports.CMNAppCode,

  //Action
  [module.exports.typeAction] : module.exports.ActionCode,
  [module.exports.typeActionItemEvent] : module.exports.ActionCode,
  


  [module.exports.AppCode] : module.exports.DataModelSpace,
  [module.exports.CMNAppCode] : module.exports.CMNDataModelSpace,
  // [module.exports.ActionCode] : module.exports.ActionSpace,
  [module.exports.AppTranslationCode] : module.exports.TranslationSpace,
  
}