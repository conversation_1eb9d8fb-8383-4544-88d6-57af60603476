<div fxLayout="column" *ngIf="configureDataDialog">
    <div>
        <commom-label *ngIf="configureDataDialog.type == 'craft' " labelText="{{ labels['craft'] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading mb-0'"></commom-label>
            <commom-label *ngIf="configureDataDialog.type == 'contractor' " labelText="{{ labels['contractor'] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading mb-0'"></commom-label>
    </div>
    <div>
       
        <div class="marginTop">
            <div>
                <mat-form-field appearance="outline" class="add_sub_drop">
                    <input *ngIf="configureDataDialog.type == 'craft' " type="text" [formControl]="craftControl" class="input"
                        placeholder="{{ labels['craft'] }}" aria-span="craft" matInput>
                    <input *ngIf="configureDataDialog.type == 'contractor' " type="text" [formControl]="craftControl" class="input"
                        placeholder="{{ labels['contractor'] }}" aria-span="craft" matInput>
                </mat-form-field>
            </div>
        </div>
        <div class="marginTop category-pop-up" fxLayout="row">
            <div fxFlex="100" fxLayoutAlign="end">
                <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                    (buttonAction)="cancel()"></common-lib-button>
                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                    (buttonAction)="savaData()"></common-lib-button>
            </div>
        </div>
    </div>
</div>
