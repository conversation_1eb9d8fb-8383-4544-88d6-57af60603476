<div *ngIf="loaderFlag" style="z-index: 10000;" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">

        <div class="ovservation-list-section configuration-section">
            <div class="audit-plan-unit-section">
                <commom-label labelText="{{ labels['menuConfiguration'] }}" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>
                <!-- <div class="audit-head-icon">
                    <div class="icon-bg-box" (click)="configListPage()"
                        *ngIf="configurationList && configurationList.featureAccessLevelCode !='NoAccess'">
                        <mat-icon class="configuration_list">list</mat-icon>
                    </div>
                </div> -->
            </div>
            <div class="outerbox" style="min-height: 54em !important">
            <div class="marginTop">
                <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                    <!-- <div fxLayout="row auto" fxLayoutAlign="start center">
                        <div class="treat-md">
                            <span class="semi-bold">{{ labels['site'] }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()"
                                    placeholder="{{ labels['commonfilterChoosesite'] }}"
                                    [formControl]="siteControl" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']"
                                        [displayMember]="'description'" [array]="siteList"
                                        (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div> -->

                    <div *ngIf="false" fxLayout="row auto" fxLayoutAlign="start center">
                        <div class="treat-md">
                            <span class="semi-bold">{{ labels['corePrincipleTitle'] }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()"
                                placeholder="{{ labels['commonfilterChoosecoreprinciple'] }}"
                                [formControl]="corePrinciplesControl" disableOptionCentering>
                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']"
                                    [displayMember]="'name'" [array]="corePrinciplesList"
                                    (filteredReturn)="filteredCorePrinciplesList =$event"></mat-select-filter>
                                <mat-option *ngFor="let item of filteredCorePrinciplesList"
                                    [value]="item.externalId">
                                    <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                                    {{ labels['cards'+item.name] }}
                                </mat-option>
                            </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <div *ngIf="false" fxLayout="row auto" fxLayoutAlign="start center">
                        <div class="treat-md">
                            <span class="semi-bold">{{ labels['process'] }}</span>
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()"
                            placeholder="{{ labels['commonfilterChooseprocess'] }}"
                            [formControl]="processControl" disableOptionCentering>
                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']"
                                [displayMember]="'name'" [array]="processList"
                                (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
                            <mat-option *ngFor="let item of filteredProcessList"
                                [value]="item.externalId">
                                <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                                 <span *ngIf="item.name != 'Field Walk'" >
                                    {{ labels[item.name.toLowerCase()] }}
                                 </span>
                                 <span *ngIf="item.name == 'Field Walk'" >
                                    {{ labels['fieldWalk'] }}
                                </span>
                            </mat-option>
                        </mat-select>
                            </mat-form-field>
                        </div>
                    </div>


                </div>

            </div>
            <div class="main-observation-section config-margin">
                <div>
                    <span class="semi-bold">{{ labels['corePrincipleTitle'] }}</span>
                </div>
                <div fxLayout="row">
                  <div fxFlex="100" class="d-flex flex-flow-wrap">
                    <div *ngFor="let item of corePrinciplesList" >
                      <mat-card class="grid-stl-config" (click)="selectCorePrinciple(item)" 
                      [ngClass]="(selectedCorePrinciple && selectedCorePrinciple.externalId) == item.externalId ? 'selectedItem' : ''"  >
                      <!-- <div class="grid-stl-ico">
                        <i [innerHTML]="item.iconImage | safeHtml"></i>
                      </div> -->
                      <div class="grid-stl-heading">
                      
                        <span class="subheading-2" >
                            <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                            {{ labels['cards'+item.name] }}
                        </span>
                      </div>
                    </mat-card>
                    </div>
            
            
                   
                  </div>
                </div>
              </div>
              <div class="main-observation-section config-margin">
                <div>
                    <span class="semi-bold">{{ labels['process'] }}</span>
                </div>
                <div fxLayout="row">
                  <div fxFlex="100" class="d-flex flex-flow-wrap">
                    <div *ngFor="let item of processList">
                      <mat-card class="grid-stl-config" (click)="selectProcess(item)"  
                      [ngClass]="(selectedProcess && selectedProcess.externalId) == item.externalId ? 'selectedItem' : ''"
                      >
                      <!--  <div class="grid-stl-ico">
                        <i [innerHTML]="item.iconImage | safeHtml"></i>
                      </div> -->
                      <div class="grid-stl-heading">
                            <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                            <span class="subheading-2" *ngIf="item.name != 'Field Walk' || item.name != 'Observation' || item.name != 'Audit'" >
                                {{ labels['cards'+item.name] }}
                             </span>
                             <span class="subheading-2" *ngIf="item.name == 'Field Walk'" >
                                {{ labels['fieldWalk'] }}
                            </span>
                             <span class="subheading-2" *ngIf="item.name == 'Observation'" >
                                {{ labels[item.name.toLowerCase()] }}
                            </span>
                             <span class="subheading-2" *ngIf="item.name == 'Audit'" >
                                {{ labels['audit'] }}
                            </span>
                      </div>
                    </mat-card>
                    </div>
            
            
                   
                  </div>
                </div>
              </div>
            <br>
            <br>
            <mat-tab-group mat-stretch-tabs  *ngIf="ifProcess"
                [(selectedIndex)]="selectedIndexBinding" #tabGroup (selectedTabChange)="selectTab($event)">
                <mat-tab *ngIf="configurationList && configurationList.processConfiguration" label="{{ labels['formcontrolsProcessconfig'] }}">

                    <div class="tab-height">
                        <!-- Core Principles -->
                        <!-- <div>
                            <div class="marginTop">
                                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.CORE_PRINCIPLE'"
                                    [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <br />
                            <div fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                                <div *ngFor="let item of corePrinciplesList" (click)="selectCorePrinciple(item)"
                                    fxLayout="row" fxLayoutAlign="center center">
                                    <div fxLayoutAlign="center center"
                                        [ngClass]="(selectedCorePrinciple && selectedCorePrinciple.externalId) == item.externalId ? 'category_box' : 'category_box_light'">
                                        <div fxFlexAlign="center">
                                            <span>
                                                {{item.name}}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div> -->

                        <!-- Process -->
                        <!-- <div>
                            <div class="marginTop">
                                <commom-label [labelText]="'CONFIGURATION.FORM_CONTROLS.PROCESS'" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <br *ngIf="ifCorePrinciple">
                            <div *ngIf="ifCorePrinciple" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                                <div *ngFor="let item of processList" (click)="selectProcess(item)" fxLayout="row"
                                    [ngClass]="(selectedProcess && selectedProcess.externalId) == item.externalId ? 'category_box' : 'category_box_light'"
                                    fxLayoutAlign="center center">
                                    <div fxFlexAlign="center">
                                        <span>
                                            {{item.name}}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div> -->

                        <!-- Sub Process -->
                        <div>
                            <div *ngIf="ifProcess && selectedProcess.name == 'Observation'" class="marginTop">
                                <commom-label labelText="{{ labels['observationTypes'] }}"
                                    [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div *ngIf="ifProcess && selectedProcess.name == 'Audit'" class="marginTop">
                                <commom-label labelText="{{ labels['formcontrolsAudittype'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div *ngIf="ifProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'"
                                class="marginTop">
                                <commom-label labelText="{{labels['subProcess']}}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div *ngIf="!ifProcess" class="marginTop">
                                <commom-label labelText="{{labels['subProcess']}}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <br *ngIf="ifProcess" />
                            <div *ngIf="ifProcess" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                                <div *ngFor="let item of subProcessListAll" fxLayout="row"
                                    fxLayoutAlign="center center">
                                    <div fxLayoutAlign="center center" (click)="selectSubProcess(item)" 
                                    [ngClass]="{'active-class': item.isActive == false, 'category_box': (selectedSubProcess && selectedSubProcess.externalId == item.externalId ) ,'category_box_light': (!selectedSubProcess || selectedSubProcess.externalId!=item.externalId )  }" >
                                        <div fxFlexAlign="center">
                                            <span>
                                                  {{item.name}}
                                            </span>
                                        </div>
                                    </div>
                                    <button matTooltip="{{ labels['edit'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onEdit(item)" style="width: auto;">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['disable'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onDisable(item,false)" style="width: auto;margin-left: 10px !important;">
                                        <mat-icon>visibility_off</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['enable'] }}" matTooltipPosition="above" *ngIf="item.isActive == false" mat-icon-button (click)="onDisable(item,true)" style="width: auto;">
                                        <mat-icon>visibility</mat-icon>
                                    </button>
                                </div>
                                <div fxLayout="row" class="category_box_light" (click)="addSubProcess(null)"
                                    fxLayoutAlign="center center">
                                    <div fxLayout="row" style="align-items: center;">
                                        <mat-icon class="add_Icon">add_circle</mat-icon>
                                        <span>
                                            {{ labels['formcontrolsAddsubprocess'] }} 
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category -->
                        <div>
                            <div class="marginTop">
                                <commom-label labelText="{{ labels['formcontrolsCategories'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <br *ngIf="ifSubProcess">
                            <div *ngIf="ifSubProcess" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                                <div *ngFor="let item of categoryListAll" fxLayout="row"
                                    fxLayoutAlign="center center">
                                    <div fxLayoutAlign="center center" (click)="selectCategory(item)"
                                        [ngClass]="{'active-class': item.isActive == false, 'category_box': (selectedCategory && selectedCategory.externalId) == item.externalId ,'category_box_light': (!selectedCategory || selectedCategory.externalId!=item.externalId )  }"  >
                                        <div fxFlexAlign="center">
                                            <span>
                                                {{item.name}}
                                            </span>
                                        </div>
                                    </div>
                                    <button matTooltip="{{ labels['edit'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onEdit(item)" style="width: auto;">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['disable'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onDisable(item,false)" style="width: auto;margin-left: 10px !important;">
                                        <mat-icon>visibility_off</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['enable'] }}" matTooltipPosition="above" *ngIf="item.isActive == false" mat-icon-button (click)="onDisable(item,true)" style="width: auto;">
                                        <mat-icon>visibility</mat-icon>
                                    </button>
                                </div>
                                <div fxLayout="row" class="category_box_light" (click)="addCategory(null)"
                                    fxLayoutAlign="center center">
                                    <div fxLayout="row" style="align-items: center;">
                                        <mat-icon class="add_Icon">add_circle</mat-icon>
                                        <span>
                                            {{ labels['addcategory'] }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sub Category -->
                        <div>
                            <div class="marginTop">
                                <commom-label labelText="{{ labels['formcontrolsSubcategories'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <br *ngIf="ifCategory">
                            <div *ngIf="ifCategory" fxLayout="row wrap" class="marginTop" fxLayoutGap="10px">
                                <div *ngFor="let item of subCategoryListAll"
                                    fxLayout="row" fxLayoutAlign="center center">
                                    <div fxLayoutAlign="center center" (click)="selectSubCategory(item)" class="category_box_light" [ngClass]="{'active-class': item.isActive == false}">
                                        <div fxFlexAlign="center" >
                                            <span>
                                                {{item.name}}
                                            </span>
                                        </div>
                                    </div>
                                    <button matTooltip="{{ labels['edit'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onEdit(item)" style="width: auto;">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['disable'] }}" matTooltipPosition="above" *ngIf="item.isActive == null || item.isActive == true" mat-icon-button (click)="onDisable(item,false)" style="width: auto;margin-left: 10px !important;">
                                        <mat-icon>visibility_off</mat-icon>
                                    </button>
                                    <button matTooltip="{{ labels['enable'] }}" matTooltipPosition="above" *ngIf="item.isActive == false" mat-icon-button (click)="onDisable(item,true)" style="width: auto;">
                                        <mat-icon>visibility</mat-icon>
                                    </button>
                                </div>
                                <div fxLayout="row" class="category_box_light" (click)="addSubCategory(null)"
                                    fxLayoutAlign="center center">
                                    <div fxLayout="row" style="align-items: center;">
                                        <mat-icon class="add_Icon">add_circle</mat-icon>
                                        <span>
                                            {{ labels['addSubCategory'] }}
                                        </span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.checklistQuestions" label="{{ labels['formcontrolsChecklistques'] }}">
                    <div class="tab-height">
                        <!-- <app-config-list></app-config-list> -->

                        <br>

                        <div fxLayout="row" class="overflow-section">
                            <div fxFlex="100">
                          
                              <div class="question-list-section">
                               
                          
                                <div class="action-section-unit-section">
                                  <!-- <commom-label [labelText]="'CONFIGURATION.QUESTION_LIST.TITLE'"  labelTextNoLan="Question" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading'"></commom-label> -->
                                    <div fxLayout="row wrap">
                                        <div fxFlex="100" fxLayoutAlign="start center">
                                            <!-- <Sub Process? -->
                                            <div class="d-flex align-item-center mr-10">
                                                <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Observation'">{{
                                                    labels['observationTypes'] }}</span>
                                                <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Audit'">{{
                                                    labels['formcontrolsAudittype'] }}</span>
                                                <span class="subheading-1 site-icon-text"
                                                    *ngIf="ifProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{
                                                    labels['subProcess'] }}</span>
                                    
                                                <mat-form-field appearance="outline" class="set-back-color">
                                                    <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
                                                        [formControl]="subProcessControl" disableOptionCentering>
                                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                            [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                                        <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                                            {{item.name }}
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                    
                                            <!-- <Category> -->
                                            <div class="d-flex align-item-center mr-10" *ngIf="categoryList.length>0">
                                                <span class="subheading-1 site-icon-text">{{ labels['formcontrolsCategories'] }}</span>
                                                <mat-form-field appearance="outline" class="set-back-color">
                                                    <mat-select (click)="filterClick()" placeholder="{{ labels['chooseCategory'] }}"
                                                        [formControl]="categoryControl" disableOptionCentering>
                                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                            [array]="categoryList" (filteredReturn)="filteredCategoryList =$event"></mat-select-filter>
                                                        <mat-option *ngFor="let item of filteredCategoryList" [value]="item.externalId">
                                                            {{item.name }}
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                    
                                            <!-- <Sub Category> -->
                                            <div class="d-flex align-item-center mr-10" *ngIf="subCategoryList.length>0">
                                                <span class="subheading-1 site-icon-text">{{ labels['formcontrolsSubcategories'] }}</span>
                                                <mat-form-field appearance="outline" class="set-back-color">
                                                    <mat-select (click)="filterClick()" placeholder="{{ labels['chooseSubCategory'] }}"
                                                        [formControl]="subCategoryControl" disableOptionCentering>
                                                        <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                            [array]="subCategoryList"
                                                            (filteredReturn)="filteredSubCategoryList =$event"></mat-select-filter>
                                                        <mat-option *ngFor="let item of filteredSubCategoryList" [value]="item.externalId">
                                                            {{item.name }}
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                    
                                    
                                        </div>
                                    </div>
                                    
                                    <div style="display: flex;">
                                    <common-lib-button *ngIf="subProcessControl?.value?.length>0 && supplieraduitFlag" [className]="'cst-btn no-translate'" text="{{labels['evidenceChecklist']}}"
                                    (buttonAction)="evidenceQuestion(subProcessControl);"></common-lib-button>
                                    <common-lib-button style="margin-left: 10px !important;" *ngIf="(categoryControl.value && subCategoryListAll.length==0) || (categoryControl.value && subCategoryControl.value) || (subProcessControl.value && categoryListAll.length==0 && subProcessControl.value)" [className]="'cst-btn'" text="{{labels['buttonAddquestion']}}" [icon]="'add'"
                                    (buttonAction)="addQuestion();"></common-lib-button>
                                    </div>
                                </div>
                          
                                <div class="action-section-unit-section justify-end">
                                    <div class="icon-bg-box" (click)="summaryClick()" matTooltip="{{ labels['reacttableColsummary'] }}">
                                      <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="button-stroke-svg-icon"
                                          d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
                                          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                      </svg>
                                    </div>
                                    <div class="icon-bg-box" (click)="settingClick()" matTooltip="{{ labels['reacttableColselection'] }}">
                                      <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
                                          d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
                                      </svg>
                                    </div>
                                  </div>

                                <div class="marginTop d-flex" *ngIf="selectedSubProcess">
                                  <iframe id="iFrameFormConfig" class="iFrameTable" [src]="url| safe" title="description">
                                  </iframe>
                                </div>
                          
                              </div>
                          
                            </div>
                          </div>


                    </div>
                </mat-tab>
                <mat-tab *ngIf="configurationList && configurationList.fieldConfiguration" label="{{ labels['formcontrolsFieldconfig'] }}">
                    <div class="tab-height">
                        <br>
                        <div style="margin-top: 15px;">
                            <!-- <div fxLayout="row wrap">
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="d-flex align-item-center mr-10">
                                        <span class="subheading-1 site-icon-text">{{
                                            'CONFIGURATION.FORM_CONTROLS.CORE_PRINCIPLE' | translate
                                            }}</span>
                                        <mat-form-field appearance="outline" class="set-back-color">
                                            <mat-select (click)="filterClick()"
                                                placeholder="{{ 'COMMONFILTER.CHOOSE_CORE_PRINCIPLE' | translate }}"
                                                [formControl]="corePrinciplesControl" disableOptionCentering>
                                                <mat-select-filter [placeholder]="labels['commonfilterSearch']"
                                                    [displayMember]="'name'" [array]="corePrinciplesList"
                                                    (filteredReturn)="filteredCorePrinciplesList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredCorePrinciplesList"
                                                    [value]="item.externalId">
                                                    {{item.name }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="d-flex align-item-center mr-10">
                                        <span class="subheading-1 site-icon-text">{{ 'CONFIGURATION.UNIT_SITE.PROCESS' |
                                            translate }}</span>
                                        <mat-form-field appearance="outline" class="set-back-color">
                                            <mat-select (click)="filterClick()"
                                                placeholder="{{ 'COMMONFILTER.CHOOSE_PROCESS' | translate }}"
                                                [formControl]="processControl" disableOptionCentering>
                                                <mat-select-filter [placeholder]="labels['commonfilterSearch']"
                                                    [displayMember]="'name'" [array]="processList"
                                                    (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredProcessList"
                                                    [value]="item.externalId">
                                                    {{item.name }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                             -->
                            <!-- <Sub Process? -->
                                <div class="d-flex align-item-center mr-10">
                                    <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Observation'">{{
                                        labels['observationTypes'] }}</span>
                                    <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Audit'">{{
                                        labels['formcontrolsAudittype'] }}</span>
                                    <span class="subheading-1 site-icon-text"
                                        *ngIf="ifProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{
                                        labels['subProcess'] }}</span>
                        
                                    <mat-form-field appearance="outline" class="set-back-color">
                                        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
                                            [formControl]="subProcessControl" disableOptionCentering>
                                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                            <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                                {{item.name }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>

                            <br *ngIf="processConfig">
                            <div fxLayout="row wrap" *ngIf="processConfig && selectedProcess.name !== 'Field Walk'">
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="marginTop">
                                        <commom-label labelText="{{ labels['formcontrolsChecklistfields'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading'"></commom-label>
                                    </div>
                                    <div class="toggle-switch">
                                        <mat-slide-toggle [(ngModel)]="checklistEnableValue"></mat-slide-toggle>
                                    </div>
                                </div>
                                <br>
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div fxFlex="8">
                                        <commom-label labelText="{{ labels['formcontrolsField'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['formcontrolsEnabled'] }} {{ labels['formcontrolsAtleastone'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['formcontrolsDispname'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <!-- <div fxFlex="25">
                                        <commom-label labelText="Enabled" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div> -->
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['formcontrolsDefvalue'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['injuryPotential'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['notesActive'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['notesMandatory'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                </div>
                                <div fxFlex="100" fxLayoutAlign="start center" *ngFor="let check of CheckList">
                                    <div fxFlex="8">
                                        <span class="confiq-sub-heading">{{ labels[check.translateKey] }}</span>
                                    </div>
                                    <div fxFlex="20">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="check.isEnabledControl" [disabled]="!checklistEnableValue">
                                                <mat-select-filter [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    <!-- {{'BUTTON.'+item.name.toUpperCase() | translate }}  -->
                                                    {{ labels['formcontrols'+item.name] }} 
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div fxFlex="20" *ngIf="check.key!='injuryPotential'" >

                                        <mat-form-field appearance="outline" class="location-input" class="behav-tree-select">
                                            <input type="text" [formControl]="check.displayNameControl" class="input" value="" placeholder="" aria-span="Search"
                                                matInput [disabled]="!check.isEnabledControl.value || !checklistEnableValue">
                                        </mat-form-field>
                                       
                                    </div>
                                    <!-- <div fxFlex="25">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="config.isEnabledControl">
                                                <mat-select-filter [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div> -->
                                    <div fxFlex="20" *ngIf="check.key!='injuryPotential'">
                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="check.isDefaultControl" [disabled]="!check.isEnabledControl.value || !checklistEnableValue">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{ labels['formcontrols'+item.name] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div fxFlex="20" *ngIf="check.key!='injuryPotential'">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="check.isInjuryPotential" [disabled]="!check.isEnabledControl.value || !checklistEnableValue">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoNullOption"
                                                    (filteredReturn)="filteredYesNoNullList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoNullList" [value]="item.value">
                                                    <span *ngIf="item.name != 'Nil'" >
                                                        {{ labels['formcontrols'+item.name] }}
                                                    </span>
                                                    <span *ngIf="item.name == 'Nil'" >
                                                        {{ labels['buttonNil'] }}
                                                    </span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div fxFlex="20" *ngIf="check.key!='injuryPotential'">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="check.isNotes" [disabled]="!check.isEnabledControl.value || !checklistEnableValue">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{ labels['formcontrols'+item.name] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div fxFlex="20" *ngIf="check.key!='injuryPotential'">
                                        <mat-form-field appearance="outline" class="behav-tree-select"> 
                                            <mat-select disableOptionCentering [formControl]="check.isNotesMandatory" [disabled]="!check.isNotes.value || !checklistEnableValue">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{ labels['formcontrols'+item.name] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                </div>

                            </div>
                            <div fxLayout="row wrap" *ngIf="processConfig">
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="marginTop">
                                        <commom-label labelText="{{ labels['formcontrolsListfields'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading'"></commom-label>
                                    </div>
                                </div>
                                <br>
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div fxFlex="25">
                                        <commom-label labelText="{{ labels['formcontrolsField'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="25">
                                        <commom-label labelText="{{ labels['formcontrolsDispname'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="25">
                                        <commom-label labelText="{{ labels['formcontrolsEnabled'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="25">
                                        <commom-label labelText="{{ labels['formcontrolsMandatory'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                </div>
                                <div fxFlex="100" fxLayoutAlign="start center" *ngFor="let config of configList">
                                    <div fxFlex="25" *ngIf="config.key!='injuryPotential'">
                                        <span class="confiq-sub-heading">{{labels[config.translateKey] }}</span>
                                    </div>
                                    <div fxFlex="25" *ngIf="config.key!='injuryPotential'">

                                        <mat-form-field appearance="outline" class="location-input" class="behav-tree-select">
                                            <input type="text" [disabled]="isDisabled" [value]="config.field"  [title]="config.displayNameControl.value" class="input" value="" placeholder="" aria-span="Search"
                                                matInput>
                                        </mat-form-field>
                                       
                                    </div>
                                    <div fxFlex="25" *ngIf="config.key!='injuryPotential'">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select disableOptionCentering [formControl]="config.isEnabledControl">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{ labels['formcontrols'+item.name] }} 
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div fxFlex="25" *ngIf="config.key!='injuryPotential'">
                                        <mat-form-field appearance="outline" class="behav-tree-select"> 
                                            <mat-select disableOptionCentering [formControl]="config.isMandatoryControl">
                                                <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                                    [array]="yesNoOption"
                                                    (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                                <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                                    {{ labels['formcontrols'+item.name] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                </div>
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div class="marginTop">
                                        <commom-label labelText="{{ labels['formcontrolsNotificationfields'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading'"></commom-label>
                                    </div>
                                    <div class="toggle-switch">
                                        <mat-slide-toggle [(ngModel)]="notificationEnableValue"></mat-slide-toggle>
                                    </div>
                                </div>
                                <div fxFlex="100" fxLayoutAlign="start center">
                                    <div fxFlex="8">
                                        <commom-label labelText="{{ labels['formcontrolsField'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    <div fxFlex="20">
                                        <commom-label labelText="{{ labels['formcontrolsEnabledGroup'] }}" [tagName]="'h4'"
                                            [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    </div>
                                    
                                </div>
                                <div fxFlex="100" fxLayoutAlign="start center" *ngFor="let check of notificationList">
                                    <div fxFlex="8">
                                        <span class="confiq-sub-heading">{{ labels[check.translateKey] }}</span>
                                    </div>
                                    <div fxFlex="20">

                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-select 
                                              disableOptionCentering 
                                              [formControl]="check.groupName" 
                                              (selectionChange)="onSelectionChange($event.value)" 
                                              [disabled]="!notificationEnableValue">
                                              
                                              <mat-option *ngFor="let item of notificationGroupData" [value]="item.externalId">
                                                {{ item.name }}
                                              </mat-option>
                                              
                                            </mat-select>
                                          </mat-form-field>
                                          

                                    </div>
                                    </div>
                                

                                <div fxFlex="100" fxLayoutAlign="end">
                                    <div class="create-schedular-fotter">
                                        <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave']}}"
                                            (buttonAction)="saveConfig();"></common-lib-button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </mat-tab>
               
                <mat-tab *ngIf="configurationList && configurationList.dashboardConfiguration" label="{{ labels['formcontrolsDashboardconfig'] }}">
                    <!-- <Sub Process? -->
                        <div  style="margin-top: 30px;" class="d-flex align-item-center mr-10">
                            <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Observation'">{{
                                labels['observationTypes'] }}</span>
                            <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Audit'">{{
                                labels['formcontrolsAudittype'] }}</span>
                            <span class="subheading-1 site-icon-text"
                                *ngIf="ifProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{
                                labels['subProcess'] }}</span>
                
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
                                    [formControl]="subProcessControl" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                        [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                        {{item.name }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    <br *ngIf="processConfig">
                    <div fxLayout="row wrap" *ngIf="processConfig">
                        <div fxFlex="100" fxLayoutAlign="start center">
                            <div class="marginTop">
                                <commom-label labelText="{{ labels['formcontrolsDashboardgraphs'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading'"></commom-label>
                            </div>
                        </div>
                        <br>
                        <div fxFlex="100" fxLayoutAlign="start center">
                            <div fxFlex="35">
                                <commom-label labelText="{{ labels['formcontrolsGraphname'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div fxFlex="35">
                                <commom-label labelText="{{ labels['formcontrolsGraphdispname'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div fxFlex="25">
                                <commom-label labelText="{{ labels['formcontrolsEnabled'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                        </div>
                        <div fxFlex="100" fxLayoutAlign="start center" *ngFor="let config of dashboardConfigList">
                            <div fxFlex="35">
                                <span class="confiq-sub-heading">{{ labels[config.translateKey]}}</span>
                            </div>
                            <div fxFlex="35">

                                <mat-form-field appearance="outline" class="location-input" class="behav-tree-select">
                                    <input type="text" [disabled]="isDisabled" [value]="config.field"  [title]="config.displayNameControl.value" class="input" value="" placeholder="" aria-span="Search"
                                        matInput>
                                </mat-form-field>
                               
                            </div>
                            <div fxFlex="25">

                                <mat-form-field appearance="outline" class="behav-tree-select">
                                    <mat-select disableOptionCentering [formControl]="config.isEnabledControl">
                                        <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch'] " [displayMember]="'name'"
                                            [array]="yesNoOption"
                                            (filteredReturn)="filteredYesNoList =$event"></mat-select-filter> -->
                                        <mat-option *ngFor="let item of filteredYesNoList" [value]="item.value">
                                            {{ labels['formcontrols'+item.name] }} 
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>

                            </div>
                        </div>

                        <div fxFlex="100" fxLayoutAlign="end">
                            <div class="create-schedular-fotter">
                                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                                    (buttonAction)="saveDashboard();"></common-lib-button>
                            </div>
                        </div>

                    </div>

                </mat-tab>

                <mat-tab *ngIf="configurationList && configurationList.columnConfiguration" label="{{ labels['formcontrolsListConfig'] }}">
                    <!-- <mat-tab *ngIf="configurationList && configurationList.dashboardConfiguration" label="List Configuration"> -->
                    <!-- <Sub Process? -->
                        <div style="margin-top: 30px;" class="d-flex align-item-center mr-10">
                            <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Observation'">{{
                                labels['observationTypes'] }}</span>
                            <span class="subheading-1 site-icon-text" *ngIf="ifProcess && selectedProcess.name == 'Audit'">{{
                                labels['formcontrolsAudittype'] }}</span>
                            <span class="subheading-1 site-icon-text"
                                *ngIf="ifProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{
                                labels['subProcess'] }}</span>
                
                            <mat-form-field appearance="outline" class="set-back-color">
                                <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
                                    [formControl]="subProcessControl" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                        [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                        {{item.name }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    <br *ngIf="processConfig">
                    <div fxLayout="row wrap" *ngIf="processConfig">
                        <div fxFlex="100" fxLayoutAlign="start center">
                            <div class="marginTop">
                                <commom-label labelText="{{ labels['formcontrolsConfiglistcolumns'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading'"></commom-label>
                                    <!-- <commom-label labelText="Configure List Columns" [tagName]="'h4'"
                                    [cstClassName]="'heading'"></commom-label> -->
                            </div>
                        </div>
                        <br>
                        <div fxFlex="100" fxLayoutAlign="start center">
                            <div fxFlex="35">
                                <commom-label labelText="{{ labels['formcontrolsColumnname'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                                    <!-- <commom-label labelText="Column Names" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label> -->
                            </div>
                            <!-- <div fxFlex="35">
                                <commom-label labelText="{{ labels['formcontrolsGraphdispname'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div> -->
                            <div fxFlex="25">
                                <commom-label labelText="{{ labels['formcontrolsEnabled'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                            <div fxFlex="15">
                                <commom-label labelText="{{ labels['formcontrolsSequence'] }}" [tagName]="'h4'"
                                    [cstClassName]="'heading unit-heading confiq-sub-heading'"></commom-label>
                            </div>
                        </div>
                        <div fxFlex="100" fxLayoutAlign="start center" *ngFor="let config of columnConfigList">
                            <div fxFlex="35" style="margin-top: 15px;">
                                <span class="confiq-sub-heading">{{labels[config.translateKey] }}</span>
                            </div>
                            <!-- <div fxFlex="35">

                                <mat-form-field appearance="outline" class="location-input" class="behav-tree-select">
                                    <input type="text" [disabled]="isDisabled" [value]="config.field"  [title]="config.displayNameControl.value" class="input" value="" placeholder="" aria-span="Search"
                                        matInput>
                                </mat-form-field>
                               
                            </div> -->
                            <div fxFlex="25" style="margin-top: 15px;">

                                <mat-slide-toggle [formControl]="config.isEnabledControl">
                                </mat-slide-toggle>
                                

                            </div>
                            <div fxFlex="15" style="margin-top: 15px;">
                                <mat-form-field appearance="outline" style="width: 100px !important;" class="location-input behav-tree-select">
                                    <input  [formControl]="config.sequenceControl" 
                                    class="input" value="" placeholder="" aria-span="Search"
                                        matInput>
                                </mat-form-field>
                            </div>
                        </div>

                        <div fxFlex="100" fxLayoutAlign="end">
                            <div class="create-schedular-fotter">
                                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                                    (buttonAction)="saveColumnconfig();"></common-lib-button>
                            </div>
                        </div>

                    </div>

                </mat-tab>
            </mat-tab-group>
        </div>

            <!--  <div fxLayout="row wrap" fxLayoutGap="20px" *ngIf="ifProcess">
                <div *ngIf="auditBool == false" class="marginTop">
                    <div fxLayout="row" fxLayoutAlign="start center">
                        <span class="body-2">
                            {{ 'CONFIGURATION.FORM_CONTROLS.FEEDBACK' | translate }}
                        </span>
                        <span class="body-2 bold">
                            &nbsp; {{ 'CONFIGURATION.FORM_CONTROLS.UNSAFE' | translate }}
                        </span>
                        <div>
                            &nbsp; &nbsp; &nbsp;
                            <mat-form-field appearance="outline" style="width: 110px;">

                                <mat-select [formControl]="feedbackControl">
                                    <mat-option value="true">{{ 'BUTTON.YES' | translate }}</mat-option>
                                    <mat-option value="false">{{ 'BUTTON.NO' | translate }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>
                <div *ngIf="ifProcess && auditBool == false" class="marginTop">
                    <div fxLayout="row" fxLayoutAlign="start center">
                        <span class="body-2">
                            {{ 'CONFIGURATION.FORM_CONTROLS.SIGNATURE' | translate }}
                        </span>

                        <div>
                            &nbsp; &nbsp; &nbsp;
                            <mat-form-field appearance="outline" style="width: 110px;">

                                <mat-select  [formControl]="signatureControl">
                                    <mat-option value="true">{{ 'BUTTON.YES' | translate }}</mat-option>
                                    <mat-option value="false">{{ 'BUTTON.NO' | translate }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>
                <div *ngIf="ifProcess && auditBool == false" class="marginTop">
                    <div fxLayout="row" fxLayoutAlign="start center">
                        <span class="body-2">
                            {{ 'CONFIGURATION.FORM_CONTROLS.TIME_CAPTURE' | translate }}
                        </span>

                        <div>
                            &nbsp; &nbsp; &nbsp;
                            <mat-form-field appearance="outline" style="width: 110px;">

                                <mat-select  [formControl]="timeCaptureControl">
                                    <mat-option value="true">{{ 'BUTTON.YES' | translate }}</mat-option>
                                    <mat-option value="false">{{ 'BUTTON.NO' | translate }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                </div>

            </div>



            <div *ngIf="ifProcess && auditBool == true" style="margin-top: 37px;">

            </div>
            <div *ngIf="ifProcess" class="create-schedular-fotter">
                <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.SAVE'"
                    (buttonAction)="saveConfig();"></common-lib-button>
            </div> -->

        </div>
    </div>
</div>

<!-- Checklist Popup -->
<div class="popup-backdrop" style="z-index: 9998;" *ngIf="isPopupVisible">
    <div class="popup-box">
      <h3 style="align-items: left;">{{ labels['evidenceChecklist'] }}</h3>
      <ul>
        <li *ngFor="let item of checkList" class="list-item">
          <span class="item-value">{{ item.value }}</span>
          <div class="item-actions">
            <!-- <button (click)="openEditPopup(item)">Edit</button> -->
            <common-lib-button  [className]="'cst-btn no-translate'" text="{{labels['edit']}}"
                                    (buttonAction)="openEditPopup(item);"></common-lib-button>
          </div>
        </li>
      </ul>
      <div class="popup-actions">
        <common-lib-button  [className]="'cst-btn no-translate'" text="{{labels['new']}}"
                                    (buttonAction)="openEditPopup();"></common-lib-button>
        <common-lib-button  style="margin-left: 10px !important;" [className]="'cst-btn no-translate'" text="{{labels['close']}}"
                                    (buttonAction)="closePopup();"></common-lib-button>
        <!-- <button (click)="openEditPopup()">New</button>
        <button (click)="closePopup()">Close</button> -->
      </div>
    </div>
  </div>
  
  <!-- Edit/Add Item Popup -->
  <div class="popup-backdrop" style="z-index: 9999;" *ngIf="isEditPopupVisible">
    <div class="popup-box">
      <h3>{{ isEditing ? labels['editEvidenceChecklist'] : labels['newEvidenceChecklist'] }}</h3>
      <label>
        {{ labels['formcontrolsQuestion'] }}:
        <input type="text" class="inputBox" [(ngModel)]="currentItem.value" />
      </label>
      <div class="popup-actions">
        <common-lib-button  [className]="'cst-btn no-translate'" text="{{labels['buttonSave']}}"
                                    (buttonAction)="saveItem();"></common-lib-button>
        <common-lib-button style="margin-left: 10px !important;"  [className]="'cst-btn no-translate'" text="{{labels['buttonCancel']}}"
                                    (buttonAction)="closeEditPopup();"></common-lib-button>
        <!-- <button (click)="saveItem()">Save</button>
        <button (click)="closeEditPopup()">Cancel</button> -->
      </div>
    </div>
  </div>
  