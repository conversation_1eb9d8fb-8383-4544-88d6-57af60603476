
// angular-mgl-timeline
@import '~@angular/material/theming';
// @import '~angular-mgl-timeline/theme';



$palette-color1:  #fff;
$palette-color2:  #1A2254; 
$palette-color2l: #b9d3ff85; 
$palette-color2d: #0c0d0f;  
$palette-color3:  #13ECA4;
$palette-color3l: #81ffd5;
$palette-color3d: #088d61;
$palette-color4:  #000;
$palette-color5:  #1A2254;
$palette-color5l: #1a2254b7;
$palette-color5d: #171e4c;
$palette-color6:  #ff0000;
$palette-color6l: #ff0000;
$palette-color6d: #ff0000;

$palette-color7: #fff;
$palette-color7l: #fff;
$palette-color7d: #fff;
$palette-color8: #2e52dd;
$palette-color8l: #aebbee;
$palette-color8d: #05175e;
$palette-color9: #fff;
$palette-color9l: #fff;
$palette-color9d: #fff;
$palette-color10: #01112F;
$palette-color10l: #01112F;
$palette-color10d: #01112F;
$palette-color11: #2e52dd;
$palette-color11l: #2e52dd;
$palette-color11d: #2e52dd;
$palette-color12:#0E2340;

$font-family:     "Roboto-regular";
$display-4-size:       112px;
$display-3-size:       56px;
$display-2-size:       45px;
$display-1-size:       22px;
$headline-size:        22px;
$title-size:           20px;
$subheading-2-size:    16px;
$subheading-1-size:    15px;
$cst-subheading-1-size:18px;
$body-2-size:          14px;
$body-1-size:          14px;
$caption-size:         14px;
$button-size:          14px;
$input-size:           14px;

:root {
    --palette-color1:   #{$palette-color1};
    --palette-color2:   #{$palette-color2};
    --palette-color2l:  #{$palette-color2l};
    --palette-color2d:  #{$palette-color2d};
    --palette-color3:   #{$palette-color3};
    --palette-color3l:  #{$palette-color3l};
    --palette-color3d:  #{$palette-color3d};
    --palette-color4:   #{$palette-color4};
    --palette87:        #{$palette-color4};
    --palette54:        #{$palette-color4};
    // --palette78:        #{$palette-color4};
    --palette12:        #{$palette-color10};
    --palette26:        #{$palette-color4};
    --palette-color5:   #{$palette-color5};
    --palette-color5l:  #{$palette-color5l};
    --palette-color5d:  #{$palette-color5d};
    --palette-color6:   #{$palette-color6}; 
    --palette-color6l:  #{$palette-color6l}; 
    --palette-color6d:  #{$palette-color6d}; 
    --palette-color7:   #{$palette-color7}; 
    --palette-color7l:  #{$palette-color7l}; 
    --palette-color7d:  #{$palette-color7d}; 
    --palette-color8:   #{$palette-color8}; 
    --palette-color8l:  #{$palette-color8l}; 
    --palette-color8d:  #{$palette-color8d}; 
    --palette-color9:   #{$palette-color9}; 
    --palette-color9l:  #{$palette-color9l}; 
    --palette-color9d:  #{$palette-color9d}; 
    --palette-color10:   #{$palette-color10}; 
    --palette-color10l:  #{$palette-color10l}; 
    --palette-color10d:  #{$palette-color10d};
    --palette-color11:   #{$palette-color11}; 
    --palette-color11l:  #{$palette-color11l}; 
    --palette-color11d:  #{$palette-color11d};
    --palette-color12: #{$palette-color12};

    --font-family:      #{$font-family}; 
    --display-4-size:        #{$display-4-size}; 
    --display-3-size:        #{$display-3-size}; 
    --display-2-size:        #{$display-2-size}; 
    --display-1-size:        #{$display-1-size}; 
    --headline-size:         #{$headline-size}; 
    --title-size:            #{$title-size}; 
    --subheading-2-size:     #{$subheading-2-size}; 
    --subheading-1-size:     #{$subheading-1-size};
    --body-2-size:           #{$body-2-size};
    --body-1-size:           #{$body-1-size};
    --caption-size:          #{$caption-size};
    --button-size:           #{$button-size}; 
    --input-size:            #{$input-size}; 

    //--c1: #6D4444;//--palette-color4 --c1: #F8CCCC;//--palette-color5 --c1: #414352; --c1: #FEAEAE;//--palette-color2 --c1: #3E0699;//--palette-color3 --c1: #F6C7C7;//--palette-color1 --c1: #4A4A4A; --c1: #CCAEAE; --c1: #f8e0e0;
  }
// $palette-color1: #161a21;// $palette-color2: #111419;// $palette-color3: #13ECA4;// $palette-color4: #414352;// $palette-color5: #63667B;
$palette-color1:    var(--palette-color1);
$palette-color2:    var(--palette-color2);
$palette-color2l:   var(--palette-color2l);
$palette-color2d:   var(--palette-color2d);
$palette-color3:    var(--palette-color3);
$palette-color3l:   var(--palette-color3l);
$palette-color3d:   var(--palette-color3d);
$palette-color4:    var(--palette-color4);
$palette-color5:    var(--palette-color5);
$palette-color5l:   var(--palette-color5l);
$palette-color5d:   var(--palette-color5d);
$palette-color6:    var(--palette-color6);
$palette-color6l:   var(--palette-color6l);
$palette-color6d:   var(--palette-color6d);
$palette-color7:    var(--palette-color7);
$palette-color7l:   var(--palette-color7l);
$palette-color7d:   var(--palette-color7d);
$palette-color8:    var(--palette-color8);
$palette-color8l:   var(--palette-color8l);
$palette-color8d:   var(--palette-color8d);
$palette-color9:    var(--palette-color9);
$palette-color9l:   var(--palette-color9l);
$palette-color9d:   var(--palette-color9d);

$palette-color10:    var(--palette-color10);
$palette-color10l:   var(--palette-color10l);
$palette-color10d:   var(--palette-color10d);
$palette-color11:    var(--palette-color11);
$palette-color11l:   var(--palette-color11l);
$palette-color11d:   var(--palette-color11d);
$palette-color12:   var(--palette-color12);

$font-family:       var(--font-family);

$display-4-size:        var(--display-4-size); 
$display-3-size:        var(--display-3-size); 
$display-2-size:        var(--display-2-size); 
$display-1-size:        var(--display-1-size); 
$headline-size:    var(--headline-size); 
$title-size:            var(--title-size); 
$subheading-2-size:     var(--subheading-2-size); 
$subheading-1-size:     var(--subheading-1-size); 
$body-2-size:           var(--body-2-size); 
$body-1-size:           var(--body-1-size); 
$caption-size:          var(--caption-size); 
$button-size:           var(--button-size); 
$input-size:            var(--input-size); 

@import '~@angular/material/theming';

// $custom-typography: mat-typography-config(
//   $font-family: 'Roboto, monospace',
//   $headline: mat-typography-level(32px, 48px, 700),
//   $body-1: mat-typography-level(16px, 24px, 500)
// );
$custom-typography: mat-typography-config(
  $font-family: $font-family,
  $display-4: mat-typography-level($display-4-size, 112px, 300, $letter-spacing: -0.05em),
  $display-3: mat-typography-level($display-3-size, 56px, 400, $letter-spacing: -0.02em),
  $display-2: mat-typography-level($display-2-size, 48px, 400, $letter-spacing: -0.005em),
  $display-1: mat-typography-level($display-1-size, 40px, 400),
  $headline: mat-typography-level($headline-size, 32px, 400),
  $title: mat-typography-level($title-size, 32px, 500),
  $subheading-2: mat-typography-level($subheading-2-size, 28px, 400),
  $subheading-1: mat-typography-level($subheading-1-size, 24px, 400),
  $body-2: mat-typography-level($body-2-size, 24px, 500),
  $body-1: mat-typography-level($body-1-size, 20px, 400),
  $caption: mat-typography-level($caption-size, 20px, 400),
  $button: mat-typography-level($button-size, 14px, 500),
  $input: mat-typography-level($input-size, 1.125, 400)
);

 @include mat-core($custom-typography);
//  @include mat-core();
// Light Theme Text
$dark-text: $palette-color4;
$dark-primary-text: $palette-color9;
$dark-accent-text: $palette-color5;
// $dark-disabled-text: var(--palette78);
$dark-dividers: var(--palette12);
$dark-focused: var(--palette12);

$mat-light-theme-foreground: (
  base:              $palette-color10,
  divider:           $dark-dividers,
  dividers:          $dark-dividers,
  disabled:          $dark-disabled-text,
  disabled-button:   var(--palette26),
  disabled-text:     $dark-disabled-text,
  elevation:         #000,
  secondary-text:    $dark-accent-text,
  hint-text:         $dark-disabled-text,
  accent-text:       $dark-accent-text,
  icon:              $dark-accent-text,
  icons:             $dark-accent-text,
  text:              $dark-primary-text,
  slider-min:        $dark-primary-text,
  slider-off:        var(--palette26),
  slider-off-active: $dark-disabled-text,
);

// Background config
// $light-background:    $palette-color1;
// $light-bg-darker-5:   darken($light-background, 5%);
// $light-bg-darker-10:  darken($light-background, 10%);
// $light-bg-darker-20:  darken($light-background, 20%);
// $light-bg-darker-30:  darken($light-background, 30%);
// $light-bg-lighter-5:  lighten($light-background, 5%);
// $dark-bg-tooltip:     lighten(#2c2c2c, 20%);
// $dark-bg-alpha-4:     rgba(#2c2c2c, 0.04);
// $dark-bg-alpha-12:    rgba(#2c2c2c, 0.12);
// Theme Config
$mat-primary: (
  main: $palette-color5,
  lighter: $palette-color5l,
  darker: $palette-color5d,
  200: $palette-color5, // For slide toggle,
  contrast : (
    main: $dark-primary-text,
    lighter: $dark-primary-text,
    darker: $dark-primary-text,
  )
);
$Innocart-primary: mat-palette($mat-primary, main, lighter, darker);
$mat-accent: (
  main: $palette-color10,
  lighter: $palette-color10l,
  darker: $palette-color10d,
  200: $palette-color10, // For slide toggle,
  contrast : ( main: $light-primary-text, lighter: $dark-primary-text, darker: $light-primary-text, )
);
$Innocart-accent: mat-palette($mat-accent, main, lighter, darker);
$mat-warn: (
  main: $palette-color6,
  lighter: $palette-color6l,
  darker: $palette-color6d,
  200: $palette-color6, // For slide toggle,
  contrast : (
    main: $light-primary-text,
    lighter: $dark-primary-text,
    darker: $light-primary-text,
  )
);
$Innocart-warn: mat-palette($mat-warn, main, lighter, darker);;
// Create the theme object (a Sass map containing all of the palettes).
$Innocart-theme: mat-light-theme($Innocart-primary, $Innocart-accent, $Innocart-warn);
@include angular-material-theme($Innocart-theme);