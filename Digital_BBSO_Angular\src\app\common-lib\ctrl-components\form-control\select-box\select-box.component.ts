import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ChangeDetectorRef, NgZone } from "@angular/core";
import { Options } from "src/app/modals/home.modal";
import { LanguageService } from 'src/app/services/language.service';
import { CommonService } from "src/app/services/common.service";
import { environment } from "src/environments/environment";

@Component({
    selector: 'commom-select-box',
    templateUrl: './select-box.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class CommonSelectBoxComponent implements OnInit {
 @Input() chooseText:string = ''
 @Input() options:any[] = [];
 @Input() className:string= '';
 @Output() selectedOption = new EventEmitter<any>();
 @Input() modeselect:any = '1';
 @Input() disable:boolean = false;
 labels = {}
  constructor(
    private languageService: LanguageService,
    private commonService: CommonService,
    private changeDetector: ChangeDetectorRef,
    private ngZone: NgZone
  ){
    this.labels = {
        'monthly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monthly'] || 'monthly',
        'weekly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'weekly'] || 'weekly',
        'daily': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'daily'] || 'daily',
        'onetime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onetime'] || 'onetime',
        'quarterly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarterly'] || 'quarterly',
    }
  }

   ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
        console.log('Out subscribe', _this.labels)
        _this.languageService.language$.subscribe((language) => {
          console.log('obs labels language', _this.commonService.selectedLanguage)
          console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
          this.labels = {
            'monthly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'monthly'] || 'monthly',
            'weekly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'weekly'] || 'weekly',
            'daily': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'daily'] || 'daily',
            'onetime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'onetime'] || 'onetime',
            'quarterly': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'quarterly'] || 'quarterly',
        }
          console.log('commonService label', _this.labels)
          this.changeDetector.detectChanges();
        })
        this.changeDetector.detectChanges();
      })
   }

   selectItem(event:any):void {
      this.selectedOption.emit(
          this.options.find(item => {
              return item.value == event;
          })
      )
   }
}
