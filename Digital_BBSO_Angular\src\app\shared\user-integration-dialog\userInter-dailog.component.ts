import { Component, OnInit, Inject, Ng<PERSON>one, ChangeDetectorRef } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from "@angular/router";
import { LINKOPTIONS, Options } from "src/app/modals/home.modal";
import { CommonService } from "src/app/services/common.service";
import { DataService } from "src/app/services/data.service";
import * as _ from "lodash";
import { LanguageService } from 'src/app/services/language.service';
import { environment } from "src/environments/environment";

@Component({
  selector: 'userInter-dailog',
  templateUrl: './userInter-dailog.component.html',
  styles: [`
    .example-form {
        min-width: 150px;
        max-width: 500px;
        width: 100%;
      }
      
      .example-full-width {
        width: 100%;
      }
    `]
})

export class UserInterDailogComponent implements OnInit {
  options: Options[] = LINKOPTIONS;
  selectedSite: any;
  title: any = '';
  labels ={}
  constructor(
    public dialogRef: MatDialogRef<UserInterDailogComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public router: Router,
    private dataService: DataService,
    private commonService: CommonService,
    private languageService: LanguageService,
    private ngZone:NgZone,
    private cd: ChangeDetectorRef
  ) {
    this.title = data.title
    this.labels = {
      'createactionAreyousure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionAreyousure'] || 'createactionAreyousure',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'schedulelistCancelmsg': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'schedulelistCancelmsg'] || 'schedulelistCancelmsg',
      'confirmationmessageUserintergrationtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'confirmationmessageUserintergrationtitle'] || 'confirmationmessageUserintergrationtitle',
    }
   }

  ngOnInit(): void {
    console.log(this.data)
    var _this = this
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'createactionAreyousure': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionAreyousure'] || 'createactionAreyousure',
          'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
          'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
          'schedulelistCancelmsg': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'schedulelistCancelmsg'] || 'schedulelistCancelmsg',
          'confirmationmessageUserintergrationtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'confirmationmessageUserintergrationtitle'] || 'confirmationmessageUserintergrationtitle',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })
  }

 
  cancel(): void {
    this.dialogRef.close('NO');
  }
  okClick(): void {
    this.dialogRef.close('YES');
  }

}
