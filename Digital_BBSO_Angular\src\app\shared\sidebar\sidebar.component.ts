
import { ViewportRuler } from "@angular/cdk/overlay";
import { Location } from "@angular/common";
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import de from "date-fns/esm/locale/de/index.js";
import { Subscription } from "rxjs";
import { Animations } from "src/app/animation/animation";
import { ParentToChildService } from "src/app/broadcast/parent-to-child.service";
import { RouterType } from "src/app/modals/home.modal";
import { CommonService } from "src/app/services/common.service";
import { DataService } from "src/app/services/data.service";
import { TranslationService } from "src/app/services/TranslationService/translation.services";
import { LanguageService } from 'src/app/services/language.service';
import { NgZone } from "@angular/core";
import { environment } from "src/environments/environment";


@Component({
    selector: 'app-sidebar',
    templateUrl: './sidebar.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class SideBarComponent implements OnInit {
  currentRoute:RouterType =  new RouterType();
  routerSubscription:Subscription;
  showMobileSideBarSubscription:Subscription;
  menuFlag = true;
  docReact:any
  menuList: any[] = [];
  vendorFlag: boolean;
  userAccessMenu: any;
  labels = {}
   
constructor(
  private location: Location,
  private dataService: DataService,
  private router: Router,
  public route:ActivatedRoute,
  private parentToChildService:ParentToChildService,
  private changeDetector: ChangeDetectorRef,
  private viewportRuler: ViewportRuler,
  private commonService: CommonService,
  public translationService: TranslationService,
  private languageService: LanguageService,
  private ngZone: NgZone,
  ){
    this.menuList = [  
      {
        "displayName": "Home",
        "name": "menuHome",
        "url": "observations/observation",
        "code":this.dataService.appMenuCode.homeMenu,
        "visible":false,
        "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="18.164" viewBox="0 0 16.145 18.164">
        <path id="home_FILL0_wght400_GRAD0_opsz48" d="M161.514-823.35H165.3v-6.307h5.55v6.307h3.784v-9.839l-6.559-4.919-6.559,4.919ZM160-821.837v-12.109L168.073-840l8.073,6.055v12.109h-6.811v-6.307h-2.523v6.307ZM168.073-830.742Z" transform="translate(-160 840)" fill="#fff"/>
        </svg>
      `
      },
      {
        "displayName": "Dashboards",
        "name": "menuDashboard",
        "url": "dashboard",
        "code":this.dataService.appMenuCode.dashboardMenu,
        "visible":false,
        "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="16.145" viewBox="0 0 16.145 16.145">
        <path id="insert_chart_FILL0_wght400_GRAD0_opsz48" d="M123.678-827.375h1.345v-6.167h-1.345Zm3.722,0h1.345v-9.1H127.4Zm3.722,0h1.345v-3.319h-1.345Zm-9.777,3.521a1.291,1.291,0,0,1-.942-.4,1.291,1.291,0,0,1-.4-.942v-13.454a1.291,1.291,0,0,1,.4-.942,1.291,1.291,0,0,1,.942-.4H134.8a1.291,1.291,0,0,1,.942.4,1.291,1.291,0,0,1,.4.942V-825.2a1.291,1.291,0,0,1-.4.942,1.291,1.291,0,0,1-.942.4Zm0-1.345H134.8v-13.454H121.345Zm0-13.454v0Z" transform="translate(-120 840)" fill="#fff"/>
      </svg>
      `
      },
      {
        "displayName": "List",
        "name": "observationlistList",
        "url": "observations/list",
        "code":this.dataService.appMenuCode.list,
        "visible":false,
        "icon": `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M8 6.00067L21 6.00139M8 12.0007L21 12.0015M8 18.0007L21 18.0015M3.5 6H3.51M3.5 12H3.51M3.5 18H3.51M4 6C4 6.27614 3.77614 6.5 3.5 6.5C3.22386 6.5 3 6.27614 3 6C3 5.72386 3.22386 5.5 3.5 5.5C3.77614 5.5 4 5.72386 4 6ZM4 12C4 12.2761 3.77614 12.5 3.5 12.5C3.22386 12.5 3 12.2761 3 12C3 11.7239 3.22386 11.5 3.5 11.5C3.77614 11.5 4 11.7239 4 12ZM4 18C4 18.2761 3.77614 18.5 3.5 18.5C3.22386 18.5 3 18.2761 3 18C3 17.7239 3.22386 17.5 3.5 17.5C3.77614 17.5 4 17.7239 4 18Z" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
      `
      },
      {
        "displayName": "Schedule",
        "name": "menuSchedule",
        "url": "schedule-list",
        "code":this.dataService.appMenuCode.scheduleMenu,
        "visible":false,
        "icon": `
       
  <svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="13" viewBox="0 0 20 20">
  <path id="Icon_material-schedule2" data-name="Icon material-schedule" d="M12.99,3A10,10,0,1,0,23,13,10,10,0,0,0,12.99,3ZM13,21a8,8,0,1,1,8-8A8,8,0,0,1,13,21Zm.5-13H12v6l5.25,3.15L18,15.92l-4.5-2.67Z" transform="translate(-3 -3)" fill="#fff"/>
  </svg>
  
      `
      },
      {
        "displayName": "Configuration",
        "name": "menuConfiguration",
        "url": "configuration",
        "code":this.dataService.appMenuCode.configuration,
        "visible":false,
        "icon": `
        <svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="22" viewBox="0 0 18 14">
          <path id="Icon_awesome-tools" data-name="Icon awesome-tools" d="M17.614,13.911,13.5,9.794a2.61,2.61,0,0,0-3-.489L6.748,5.558V3.375L2.248,0,0,2.25l3.375,4.5H5.556L9.3,10.5a2.616,2.616,0,0,0,.489,3l4.117,4.117a1.307,1.307,0,0,0,1.853,0l1.853-1.853a1.313,1.313,0,0,0,0-1.853Zm-5.955-6A3.7,3.7,0,0,1,14.292,9l.682.682a4.976,4.976,0,0,0,1.54-1.037,5.056,5.056,0,0,0,1.332-4.806.421.421,0,0,0-.707-.193L14.524,6.261l-2.387-.4-.4-2.387L14.355.861a.424.424,0,0,0-.2-.71,5.064,5.064,0,0,0-4.8,1.332A4.982,4.982,0,0,0,7.9,5.125l2.886,2.886a3.83,3.83,0,0,1,.868-.1ZM8.006,10.792,6.013,8.8.656,14.16a2.25,2.25,0,0,0,3.181,3.181L8.182,13a3.775,3.775,0,0,1-.176-2.2Zm-5.758,5.8a.844.844,0,1,1,.844-.844A.846.846,0,0,1,2.248,16.593Z" transform="translate(0.004)" fill="#fff"/>
        </svg>
        
      `
      },
      {
        "displayName": "Settings",
        "name": "menuSettings",
        "url": "settings",
        "code":this.dataService.appMenuCode.settings,
        "visible":false,
        "icon": `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(0)matrix(-1, 0, 0, -1, 0, 0)" stroke="#ffffff"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="0.384"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M14.2788 2.15224C13.9085 2 13.439 2 12.5 2C11.561 2 11.0915 2 10.7212 2.15224C10.2274 2.35523 9.83509 2.74458 9.63056 3.23463C9.53719 3.45834 9.50065 3.7185 9.48635 4.09799C9.46534 4.65568 9.17716 5.17189 8.69017 5.45093C8.20318 5.72996 7.60864 5.71954 7.11149 5.45876C6.77318 5.2813 6.52789 5.18262 6.28599 5.15102C5.75609 5.08178 5.22018 5.22429 4.79616 5.5472C4.47814 5.78938 4.24339 6.1929 3.7739 6.99993C3.30441 7.80697 3.06967 8.21048 3.01735 8.60491C2.94758 9.1308 3.09118 9.66266 3.41655 10.0835C3.56506 10.2756 3.77377 10.437 4.0977 10.639C4.57391 10.936 4.88032 11.4419 4.88029 12C4.88026 12.5581 4.57386 13.0639 4.0977 13.3608C3.77372 13.5629 3.56497 13.7244 3.41645 13.9165C3.09108 14.3373 2.94749 14.8691 3.01725 15.395C3.06957 15.7894 3.30432 16.193 3.7738 17C4.24329 17.807 4.47804 18.2106 4.79606 18.4527C5.22008 18.7756 5.75599 18.9181 6.28589 18.8489C6.52778 18.8173 6.77305 18.7186 7.11133 18.5412C7.60852 18.2804 8.2031 18.27 8.69012 18.549C9.17714 18.8281 9.46533 19.3443 9.48635 19.9021C9.50065 20.2815 9.53719 20.5417 9.63056 20.7654C9.83509 21.2554 10.2274 21.6448 10.7212 21.8478C11.0915 22 11.561 22 12.5 22C13.439 22 13.9085 22 14.2788 21.8478C14.7726 21.6448 15.1649 21.2554 15.3694 20.7654C15.4628 20.5417 15.4994 20.2815 15.5137 19.902C15.5347 19.3443 15.8228 18.8281 16.3098 18.549C16.7968 18.2699 17.3914 18.2804 17.8886 18.5412C18.2269 18.7186 18.4721 18.8172 18.714 18.8488C19.2439 18.9181 19.7798 18.7756 20.2038 18.4527C20.5219 18.2105 20.7566 17.807 21.2261 16.9999C21.6956 16.1929 21.9303 15.7894 21.9827 15.395C22.0524 14.8691 21.9088 14.3372 21.5835 13.9164C21.4349 13.7243 21.2262 13.5628 20.9022 13.3608C20.4261 13.0639 20.1197 12.558 20.1197 11.9999C20.1197 11.4418 20.4261 10.9361 20.9022 10.6392C21.2263 10.4371 21.435 10.2757 21.5836 10.0835C21.9089 9.66273 22.0525 9.13087 21.9828 8.60497C21.9304 8.21055 21.6957 7.80703 21.2262 7C20.7567 6.19297 20.522 5.78945 20.2039 5.54727C19.7799 5.22436 19.244 5.08185 18.7141 5.15109C18.4722 5.18269 18.2269 5.28136 17.8887 5.4588C17.3915 5.71959 16.7969 5.73002 16.3099 5.45096C15.8229 5.17191 15.5347 4.65566 15.5136 4.09794C15.4993 3.71848 15.4628 3.45833 15.3694 3.23463C15.1649 2.74458 14.7726 2.35523 14.2788 2.15224ZM12.5 15C14.1695 15 15.5228 13.6569 15.5228 12C15.5228 10.3431 14.1695 9 12.5 9C10.8305 9 9.47716 10.3431 9.47716 12C9.47716 13.6569 10.8305 15 12.5 15Z" fill="#ffffff"></path> </g></svg>
        
      `
      },
      {
        "displayName": "Actions",
        "name": "actions",
        "url": "action",
        "code":this.dataService.appMenuCode.actionMenu,
        "visible":false,
        "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.638" height="12.146" viewBox="0 0 16.638 12.146">
        <path id="checklist_FILL0_wght400_GRAD0_opsz48" d="M82.953-785.854,80-788.808l.873-.873,2.08,2.059,3.723-3.723.873.894Zm0-6.655L80-795.463l.873-.873,2.08,2.059L86.676-798l.873.894Zm6.2,5.075v-1.248h7.487v1.248Zm0-6.655v-1.248h7.487v1.248Z" transform="translate(-80 798)" fill="#fff"/>
      </svg>
      `
      },
      {
        "displayName": "Alerts",
        "name": "menuAlert",
        "url": "alert",
        "visible":false,
        "code":"",
        "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="13.944" viewBox="0 0 16.145 13.944">
        <path id="warning_FILL0_wght400_GRAD0_opsz48" d="M40-866.056,48.073-880l8.073,13.944Zm1.908-1.1H54.237L48.073-877.8Zm6.241-1.046a.525.525,0,0,0,.391-.159.541.541,0,0,0,.156-.394.525.525,0,0,0-.159-.391.542.542,0,0,0-.394-.156.525.525,0,0,0-.391.159.542.542,0,0,0-.156.395.525.525,0,0,0,.159.391A.542.542,0,0,0,48.149-868.2Zm-.554-2.036h1.1v-4.11H47.6ZM48.073-872.478Z" transform="translate(-40 880)" fill="#fff"/>
      </svg>
      `
      },
      {
        "name": "menuInfield",
        "displayName": "Infield",
        "url": "infield",
        "visible":false,
        "code":this.dataService.appMenuCode.infieldMenu,
        "icon": `<svg width="18.145" height="15.944" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M496 320.307L191.693 495.999L16.0011 191.692L320.308 16L496 320.307Z" fill="url(#paint0_linear_3010_77)"/>
<path d="M496 320.307L191.693 495.999L16.0011 191.692L320.308 16L496 320.307Z" fill="url(#paint1_linear_3010_77)"/>
<rect x="404.571" y="295.809" width="217.523" height="217.523" rx="2" transform="rotate(150 404.571 295.809)" fill="#ABC9FF"/>
<rect x="404.571" y="295.809" width="217.523" height="217.523" rx="2" transform="rotate(150 404.571 295.809)" fill="url(#paint2_radial_3010_77)"/>
<path d="M256 256L495.999 320.307L191.692 495.999L256 256Z" fill="url(#paint3_linear_3010_77)" fill-opacity="0.25"/>
<path d="M256 256L495.999 320.307L191.692 495.999L256 256Z" fill="url(#paint4_linear_3010_77)" fill-opacity="0.25"/>
<path d="M255.999 256L16 191.692L320.307 16.0003L255.999 256Z" fill="url(#paint5_linear_3010_77)" fill-opacity="0.55"/>
<path d="M255.999 256L16 191.692L320.307 16.0003L255.999 256Z" fill="url(#paint6_linear_3010_77)" fill-opacity="0.55"/>
<path d="M256 256L191.692 495.999L16.0001 191.692L256 256Z" fill="url(#paint7_linear_3010_77)" fill-opacity="0.15"/>
<path d="M256 256L191.692 495.999L16.0001 191.692L256 256Z" fill="url(#paint8_linear_3010_77)" fill-opacity="0.15"/>
<path d="M256 255.999L320.307 16L495.999 320.307L256 255.999Z" fill="url(#paint9_linear_3010_77)" fill-opacity="0.35"/>
<path d="M256 255.999L320.307 16L495.999 320.307L256 255.999Z" fill="url(#paint10_linear_3010_77)" fill-opacity="0.35"/>
<defs>
<linearGradient id="paint0_linear_3010_77" x1="7.26621" y1="191.963" x2="504.26" y2="325.132" gradientUnits="userSpaceOnUse">
<stop stop-color="#4967FB"/>
<stop offset="1" stop-color="#23D8ED"/>
</linearGradient>
<linearGradient id="paint1_linear_3010_77" x1="197.633" y1="482.879" x2="323.136" y2="14.4927" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.6"/>
</linearGradient>
<radialGradient id="paint2_radial_3010_77" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(513.332 404.57) rotate(133.874) scale(101.305)">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_3010_77" x1="256" y1="256" x2="343.846" y2="408.153" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_3010_77" x1="256" y1="256" x2="343.846" y2="408.153" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_3010_77" x1="258.091" y1="259.623" x2="170.245" y2="107.469" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_3010_77" x1="258.091" y1="259.623" x2="170.245" y2="107.469" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_3010_77" x1="256" y1="256" x2="103.846" y2="343.846" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_3010_77" x1="256" y1="256" x2="103.846" y2="343.846" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_3010_77" x1="252.377" y1="258.091" x2="404.53" y2="170.245" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_3010_77" x1="252.377" y1="258.091" x2="404.53" y2="170.245" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>`
      },
      // {
      //   "displayName": "Rules",
      //   "name": "menuHome",
      //   "url": "rule",
      //   "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="22" viewBox="0 0 24 18.87">
      //   <path id="rule_FILL0_wght400_GRAD0_opsz48" d="M80,275.18v-1.8H90.8v1.8Zm0-9.6v-1.8H90.8v1.8Zm14.64,13.29-1.26-1.26,3.36-3.33-3.36-3.33,1.26-1.26L98,273.02l3.33-3.33,1.26,1.26-3.33,3.33,3.33,3.33-1.26,1.26L98,275.54Zm2.82-11.01-4.08-4.08,1.26-1.26,2.79,2.79,5.31-5.31,1.26,1.29Z" transform="translate(-80 -260)" fill="#fff"/>
      // </svg>
      
      // `
      // },
      // {
      //   "displayName": "Workflow",
      //   "name": "menuHome",
      //   "url": "workflow",
      //   "icon": `<svg xmlns="http://www.w3.org/2000/svg" width="16.145" height="22" viewBox="0 0 24 21.6">
      //   <path id="account_tree_FILL0_wght400_GRAD0_opsz48" d="M95.72,237.6v-3.75H91.1v-12.3H88.31v3.9H80V216h8.31v3.75h7.41V216H104v9.45H95.72v-3.9H92.9v10.5h2.82v-3.9H104v9.45ZM81.8,217.8v0Zm15.72,12.15v0Zm0-12.15v0Zm0,5.85h4.68V217.8H97.52Zm0,12.15h4.68v-5.85H97.52ZM81.8,223.65h4.71V217.8H81.8Z" transform="translate(-80 -216)" fill="#fff"/>
      // </svg>
      
      // `
      // }
    ]
    this.labels = {
      'menuHome': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuHome'] || 'menuHome',
      'menuDashboard': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuDashboard'] || 'menuDashboard',
      'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
      'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
      'menuConfiguration': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuConfiguration'] || 'menuConfiguration',
      'menuSettings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSettings'] || 'menuSettings',
      'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
      'menuAlert': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuAlert'] || 'menuAlert',
      'menuInfield': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuInfield'] || 'menuInfield',
    }
    this.routerSubscription = this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
           this.currentRoute = event;
           this.changeDetector.markForCheck();
      }
    })
    this.docReact = this.viewportRuler.getViewportRect();
  }

  ngOnInit():void{
    this.showMobileSideBarSubscription =this.parentToChildService.showMobileSideBar$.subscribe(state=>{
        this.menuFlag = false;
        this.changeDetector.markForCheck();
   })

   var _this = this;

   _this.ngZone.run(() => {
    console.log('Out subscribe', _this.labels)
    _this.languageService.language$.subscribe((language) => {
      console.log('obs labels language', _this.commonService.selectedLanguage)
      console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
      this.labels = {
        'menuHome': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuHome'] || 'menuHome',
        'menuDashboard': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuDashboard'] || 'menuDashboard',
        'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
        'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
        'menuConfiguration': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuConfiguration'] || 'menuConfiguration',
        'menuSettings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSettings'] || 'menuSettings',
        'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
        'menuAlert': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuAlert'] || 'menuAlert',
        'menuInfield': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuInfield'] || 'menuInfield',
      }
      console.log('commonService label', _this.labels)
      _this.changeDetector.detectChanges();
    })
    _this.changeDetector.detectChanges();
  })

   var path = this.location.path();
   const myArray = path.split("?");
   let word = myArray[0];
   var vendorPage1 = _this.dataService.vendorPage1 || "/observations/quality-assessment";
   var vendorPage2 = _this.dataService.vendorPage2 ||"/observations/upload-doc";
   var vendorPage3 = _this.dataService.vendorPage3 ||"/observations/completed";
   if (word == vendorPage1 || word == vendorPage2 || word == vendorPage3) {
     _this.vendorFlag = true;
   } else {
     _this.vendorFlag = false;
   }
   console.log(word,_this.vendorFlag)

   
    _this.userAccessMenu = _this.commonService.userIntegrationMenu;
  //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
  if(_this.userAccessMenu){
    _this.getUserMenuConfig();
  }

  _this.commonService.filterListSubject.subscribe((fiterType: any) => {
    if (fiterType) {
  
      if(fiterType == "userAccess"){
        _this.userAccessMenu = _this.commonService.userIntegrationMenu;
        console.log('_this.userAccessMenu===>',_this.userAccessMenu)
        _this.getUserMenuConfig();
      }
  
    }
  })
 }

 getUserMenuConfig(){
  var _this = this
  
  if(_this.commonService.menuFeatureUserIn.length>0){

    this.menuList.forEach((element)=>{
     var objVal = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == element.code);

     if(objVal){
       if(objVal.featureAccessLevelCode == "ViewAccess" || objVal.featureAccessLevelCode == "EditAccess"  ){
         element.visible = true
       }else{
         element.visible = false
       }
     }else{
       element.visible = false
     }
    })
    console.log(' this.menuList', this.menuList)
    this.changeDetector.detectChanges();
   }else{
     this.menuList.forEach((element)=>{
       element.visible = false
      })
      console.log(' this.menuList', this.menuList)
      this.changeDetector.detectChanges();
   }
}



 
 ngOnDestroy():void {
   this.routerSubscription && this.routerSubscription.unsubscribe();
   this.showMobileSideBarSubscription && this.showMobileSideBarSubscription.unsubscribe();
 }
 
 expandClick():void {
       this.menuFlag = !this.menuFlag;
       this.parentToChildService.broadcastSidebarAction(this.menuFlag); 
 }


   menuClick(menuItem) {
    this.router.navigateByUrl('/noaccess', { skipLocationChange: true }).then(() => {
     this.goPage(menuItem.url);
     if (menuItem.name === 'menuInfield') {
      // Wait for the navigation to complete (delay can be adjusted if needed)
      setTimeout(() => {
        // Find the "Alerts" menu item
        const homeMenuItem = this.menuList.find(item => item.name === 'Home');

        // Trigger the "Alerts" click event
        if (homeMenuItem) {
          this.menuClick(homeMenuItem);  
        }
      }, 500);  
    }
    });
     
     
       if(!this.menuFlag){
         this.menuFlag = !this.menuFlag;
         this.parentToChildService.broadcastSidebarAction(this.menuFlag);
       }
   }
 
   goPage(page) {
     if(page.includes('action')){
        this.parentToChildService.broadcastSubHeaderdata('Create Action');
     }
     else{
       this.parentToChildService.broadcastSubHeaderdata('OBSERVATION.SUB_HEADER.TITLE');
     }
     this.router.navigate([page]);
   }

}
