import * as Constants from '../Constant'
import dayjs from 'dayjs'

const TranslationCacheName = 'TranslationData';
const TranslationExpirationCacheName = 'TranslationExpiration'
const translationAppId = 'APP-OFWA'

export const availableLanguages = [
    {
      externalId: 'English',
      language: 'English',
      code: 'EN',
    },
    {
      externalId: 'French',
      language: 'French',
      code: 'FR',
    },
    {
      externalId: 'Spanish',
      language: 'Spanish',
      code: 'ES',
    },
    {
      externalId: 'Portuguese',
      language: 'Portuguese',
      code: 'PT',
    },
    {
      externalId: 'Italian',
      language: 'Italian',
      code: 'IT',
    },
    {
      externalId: 'Mandarin',
      language: 'Mandarin',
      code: 'ZH',
    },
    {
      externalId: 'Japanese',
      language: 'Japanese',
      code: 'JA',
    },
    {
      externalId: 'Korean',
      language: 'Korean',
      code: 'KO',
    },
    {
      externalId: 'Dutch',
      language: 'Dutch',
      code: 'NL',
    },
    {
      externalId: 'German',
      language: 'German',
      code: 'DE',
    },
]

const APP_ID = 'APP-OFWA'
const TRANSLATION_URL ='http://127.0.0.1:8000/translation'

const staticTranslationURL = TRANSLATION_URL + "/static" // BL
const dynamicTranslationURL = TRANSLATION_URL + "/dynamic"

function GetTranslationCacheName(locale) {
    return translationAppId + TranslationCacheName + locale.toUpperCase();
}

function GetTranslationExpirationCacheName() {
    return translationAppId + TranslationExpirationCacheName
}
  
function isTranslationInCache(locale) {
    const cacheName = GetTranslationCacheName(locale)
  
    return (
      typeof window !== 'undefined' &&
      window.localStorage.getItem(cacheName) !== null &&
      window.localStorage.getItem(cacheName) !== '0'
    )
}

function isTranslationExpired() {
    const cacheName = GetTranslationExpirationCacheName()
    const expiredLocal = window.localStorage.getItem(cacheName)
    const expired =
      typeof window === 'undefined' ||
      expiredLocal === null ||
      expiredLocal === '0' ||
      Number(expiredLocal) < dayjs().valueOf()
    return expired
}

// Get the translation from the Data Model (Calls the Apps Translation /static API endpoint)
    // This method is similar to getMessages of Celanese-SDK
    export async function getLabels(locale, token, idToken) {
        const translationCacheName = GetTranslationCacheName(locale)
        const translationExpirationCacheName = GetTranslationExpirationCacheName()
        let labels = undefined

        // If the translation is in cache, return it
        if (isTranslationInCache(locale)) {
            labels = JSON.parse(window.localStorage.getItem(translationCacheName))
        }

        if (!labels || isTranslationExpired()) {
            const dataLabels = await getStaticTranslationData(
                token,
                'LAN-' + locale.toUpperCase(),
                idToken
            );
            console.log('Data Labels:', dataLabels);

            if (dataLabels) {
                labels = dataLabels
                window.localStorage.setItem(translationCacheName, JSON.stringify(labels))
                window.localStorage.setItem(translationExpirationCacheName, dayjs().add(1, 'day').valueOf().toString())
            }
        }

    }

    export async function getStaticTranslationData(
        token,
        lang= 'LAN-EN',
        idToken
    ) {
        console.log('token translation:', token, idToken);

        // Fetch the translation data and convert the Observable to a Promise
        const downloadedLabels = await fetch(
            Constants.NODE_API + '/api/service/staticTranslation',
            {
              method: 'POST',
              headers: {
                Authorization: 'Bearer ' + token,
                Accept: 'application/json',
                'Content-type': 'application/json; charset=UTF-8',
                idtoken: idToken,
              },
              body: JSON.stringify({
                application:translationAppId,
                language:lang
              }),
            }
          )

        console.log("Translation data:", downloadedLabels); // Log the downloaded labels
        return downloadedLabels; // Ensure to return the data
    }

    export function translate(label) { //category
      const locale = localStorage.getItem('LocaleData') // Get the locale from the local storage

      const key = translationAppId + '.' + locale + '.' +'stLabel.' + label //APP-OFWA.EN.tableCols.category
      console.log('Label Key:', locale);
      // console.log('Language Labels:', _this.commonService.labelObject[locale]);
      // const keyValue = JSON.parse(window.localStorage.getItem(cacheName))[key]
      const cacheName = translationAppId + TranslationCacheName + locale.toUpperCase()
      return JSON.parse(window.localStorage.getItem(cacheName))[key] ? JSON.parse(window.localStorage.getItem(cacheName))[key] : label


  }
