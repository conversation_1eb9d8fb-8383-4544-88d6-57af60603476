@use "../abstract/mixins" as mixins;

.background-shapes {
  // flex-basis: 100%;

  @include mixins.responsive(xs) {
    display: none;
  }

  .box1 {
    position: absolute;
    top: 20%;
    left: 10%;
    width: 15px;
    height: 15px;
    background-color: transparent;
    border: 3px solid #3f43fd;
    border-radius: 0.35rem;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;
  }

  .box2 {
    position: absolute;
    top: 80%;
    left: 60%;
    width: 15px;
    height: 15px;
    background-color: transparent;
    border: 3px solid #3f43fd;
    border-radius: 0.35rem;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(sm) {
      top: 75%;
      left: 75%;
    }
  }

  .box3 {
    position: absolute;
    top: 30%;
    left: 90%;
    width: 15px;
    height: 15px;
    background-color: transparent;
    border: 3px solid #3f43fd;
    border-radius: 0.35rem;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;
  }

  .dot1 {
    position: absolute;
    top: 77%;
    left: 6%;
    width: 8px;
    height: 8px;
    background-color: #3f43fd;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;
  }

  .dot2 {
    position: absolute;
    top: 18%;
    right: 4%;
    width: 8px;
    height: 8px;
    background-color: #f4284a;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(sm) {
      top: 22%;
      left: 80%;
    }

    @include mixins.responsive(md) {
      top: 15%;
    }
  }

  .dot3 {
    position: absolute;
    top: 90%;
    left: 43%;
    width: 8px;
    height: 8px;
    background-color: #f4284a;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;
  }

  .dot4 {
    position: absolute;
    top: 91%;
    right: 5%;
    width: 8px;
    height: 8px;
    background-color: #3f43fd;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;
  }

  .heart1 {
    font-size: 1.5rem;
    position: absolute;
    top: 51%;
    left: 13%;
    z-index: -1;
    color: #f4284a;
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(sm) {
      top: 40%;
    }

    @include mixins.responsive(lg) {
      top: 40%;
      left: 5%;
    }
  }

  .heart2 {
    font-size: 1.5rem;
    position: absolute;
    top: 55%;
    right: 8%;
    z-index: -1;
    color: #f4284a;
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(lg) {
      top: 60%;
      right: 5%;
    }

    @include mixins.responsive(xlg) {
      top: 55%;
      right: 5%;
    }
  }

  .circle1 {
    position: absolute;
    top: 80%;
    left: 21%;
    width: 15px;
    height: 15px;
    border: 3px solid #f4284a;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(sm) {
      top: 90%;
    }
  }

  .circle2 {
    position: absolute;
    top: 24%;
    left: 52%;
    width: 15px;
    height: 15px;
    border: 3px solid #f4284a;
    border-radius: 50%;
    z-index: -1;
    transform: rotate(360deg);
    animation: box-shape linear 5s infinite;

    @include mixins.responsive(md) {
      top: 10%;
    }

    @include mixins.responsive(lg) {
      top: 15%;
    }
  }
}
