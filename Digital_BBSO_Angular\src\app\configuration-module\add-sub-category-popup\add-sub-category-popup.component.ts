import { Component, Inject, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-add-sub-category-popup',
  templateUrl: './add-sub-category-popup.component.html',
  styleUrls: ['./add-sub-category-popup.component.scss']
})
export class AddSubCategoryPopupComponent implements OnInit {
  category:FormControl = new FormControl('1');
  categoryOption = []
  configureDataDialog: any;
  categoryInput:FormControl = new FormControl('');
  descriptionInput:FormControl = new FormControl('');
  sequenceControl:FormControl = new FormControl('');
  selectedFile: any;
  loaderFlag:boolean;
  fileUrl: any;
  labels = {}
  constructor(@Inject(MAT_DIALOG_DATA) private _data: any,
  public dialogRef: MatDialogRef<AddSubCategoryPopupComponent>,
  private translate: TranslateService,
  private commonService:CommonService,
  private dataService:DataService,
  private languageService: LanguageService,
  private ngZone: NgZone,
  private changeDetector: ChangeDetectorRef
  ) { 
    console.log('selected Language: ', this.commonService.selectedLanguage)
    console.log('labelObject: ', this.commonService.labelObject)
    this.labels = {
      'addobservationtype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addobservationtype'] || 'addobservationtype',
      'formcontrolsObservationtypename': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObservationtypename'] || 'formcontrolsObservationtypename',
      'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
      'formcontrolsAddaudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddaudittype'] || 'formcontrolsAddaudittype',
      'formcontrolsAuditname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAuditname'] || 'formcontrolsAuditname',
      'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
      'formcontrolsAddsubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddsubprocess'] || 'formcontrolsAddsubprocess',
      'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
      'formcontrolsSubprocessname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubprocessname'] || 'formcontrolsSubprocessname',
      'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
      'addcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addcategory'] || 'addcategory',
      'formcontrolsCategoryname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategoryname'] || 'formcontrolsCategoryname',
      'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
      'addSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addSubCategory'] || 'addSubCategory',
      'formcontrolsSubCategoryName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubCategoryName'] || 'formcontrolsSubCategoryName',
      'formcontrolsSubcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategory'] || 'formcontrolsSubcategory',
      'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
        'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        'toasterAlreadyexists': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterAlreadyexists'] || 'toasterAlreadyexists',
        'formcontrolsGuidelinedoc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGuidelinedoc'] || 'formcontrolsGuidelinedoc',
        'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile',
        'formcontrolsCategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategories'] || 'formcontrolsCategories',
        'formcontrolsSubcategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategories'] || 'formcontrolsSubcategories',
    }
  }

  ngOnInit(): void {
    var _this = this;
    // _this.ngZone.run(() => {
    //   console.log('Out subscribe', _this.labels)
    //   _this.languageService.language$.subscribe((language) => {
    //     console.log('obs labels language', _this.commonService.selectedLanguage)
    //     console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
    //     this.labels = {
    //       'addobservationtype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addobservationtype'] || 'addobservationtype',
    //       'formcontrolsObservationtypename': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObservationtypename'] || 'formcontrolsObservationtypename',
    //       'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
    //       'formcontrolsAddaudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddaudittype'] || 'formcontrolsAddaudittype',
    //       'formcontrolsAuditname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAuditname'] || 'formcontrolsAuditname',
    //       'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
    //       'formcontrolsAddsubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddsubprocess'] || 'formcontrolsAddsubprocess',
    //       'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
    //       'formcontrolsSubprocessname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubprocessname'] || 'formcontrolsSubprocessname',
    //       'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
    //       'addcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addcategory'] || 'addcategory',
    //       'formcontrolsCategoryname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategoryname'] || 'formcontrolsCategoryname',
    //       'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
    //       'addSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addSubCategory'] || 'addSubCategory',
    //       'formcontrolsSubCategoryName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubCategoryName'] || 'formcontrolsSubCategoryName',
    //       'formcontrolsSubcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategory'] || 'formcontrolsSubcategory',
    //       'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
    //         'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
    //         'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
    //         'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
    //         'description': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'description'] || 'description',
    //         'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
    //         'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
    //         'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
    //         'toasterAlreadyexists': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterAlreadyexists'] || 'toasterAlreadyexists',
    //         'formcontrolsGuidelinedoc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsGuidelinedoc'] || 'formcontrolsGuidelinedoc',
    //     'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile',
    //     'formcontrolsCategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCategories'] || 'formcontrolsCategories',
    //     'formcontrolsSubcategories': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategories'] || 'formcontrolsSubcategories',
    //     }
        
    //     console.log('commonService label', _this.labels)
    //     _this.changeDetector.detectChanges();
    //   })
    //   _this.changeDetector.detectChanges();
    // })
    this.configureDataDialog = this._data
    this.categoryOption = [this.configureDataDialog.selectedValue];
    console.log(this.configureDataDialog)
    console.log(this.configureDataDialog.list)
    console.log(this.configureDataDialog.sequence)
    this.categoryInput.setValue(this.configureDataDialog.name ? this.configureDataDialog.name :"")
    this.descriptionInput.setValue(this.configureDataDialog.description ? this.configureDataDialog.description :"")
    
    if(this.configureDataDialog.sequence == undefined){
      // var mySeq = this.configureDataDialog.list[this.configureDataDialog.list.length-1];
      // console.log(mySeq)
      // var seqNumber = parseInt((mySeq && mySeq["sequence"]) ? mySeq["sequence"] : 0)+1;
      // if(!seqNumber){
      //   seqNumber = 1;
      // }
      // this.sequenceControl.setValue(seqNumber);
    }else{
      this.sequenceControl.setValue(this.configureDataDialog.sequence)
    }
    
    this.category.setValue(this.configureDataDialog.selectedValue.externalId);
    this.category.disable();
    console.log(this.configureDataDialog.list)
    this.sequenceControl.valueChanges.subscribe(sequence => {
      var finded = this.configureDataDialog.list.find(e => (e.sequence+"") == (sequence+""))
      if(finded && finded.sequence!= this.configureDataDialog.sequence){
        _this.commonService.triggerToast({ type: 'error', title: "", msg: this.labels['toasterAlreadyexists'] });
      }
    });
  }

  savaData() {
    var _this = this;
    var finded = this.configureDataDialog.list.find(e => (e.sequence + "") == (this.sequenceControl.value + ""))
    var findedCategory = this.configureDataDialog.list.find(e => (e.name + "").toLocaleLowerCase() == (this.categoryInput.value + "").toLocaleLowerCase())
    if (findedCategory && (findedCategory.name.toLocaleLowerCase() != (this.configureDataDialog.name && this.configureDataDialog.name.toLocaleLowerCase()))) {
      _this.commonService.triggerToast({ type: 'error', title: "", msg: this.labels['toasterAlreadyexists'] });
   return true;
    }
    if (finded && finded.sequence!= this.configureDataDialog.sequence) {
      _this.sequenceControl.setErrors({ duplicate: true });
      _this.commonService.triggerToast({ type: 'error', title: "", msg: this.labels['toasterAlreadyexists'] });
    } else {
      if (_this.category.value && this.categoryInput.value != '') {
        var configData = { name: this.categoryInput.value, sequence: this.sequenceControl.value,description : this.descriptionInput.value };
        if(_this.selectedFile && (_this.configureDataDialog.type == 'category' || _this.configureDataDialog.type == 'subCategory')){
          configData["file"] = _this.selectedFile;
        }
        _this.dialogRef.close(configData)
      }else{
        _this.category.setErrors({ error: true });
        _this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterSomethingwentwrong'] });
      }
    }
  }

  cancel():void {
    this.dialogRef.close();
  }

  async onFileChange($event: any) {
    var _this = this;
    const fileInput = $event.target;
    const files = fileInput.files;
    console.log(files)
    if(files.length>0){
      this.selectedFile = files[0];
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.fileUrl = e.target.result;
      };
      reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
    }
  }

  imageView() {
    var _this = this;
    if (_this.selectedFile && _this.fileUrl) {
      console.log(_this.fileUrl)
      window.open(_this.fileUrl, '_blank');
  
    } else {
      var fileId = _this.configureDataDialog.guidelineDocument[0]["id"];
      _this.loaderFlag = true;
      _this.dataService.getImage(_this.commonService.configuration["AzureAudience"] + "/api/v1/projects/" + _this.commonService.configuration["Project"] + "/documents/" + fileId + "/preview/image/pages/1").
        subscribe((resData: any) => {
          let objectURL = URL.createObjectURL(resData);
          _this.loaderFlag = false;
          window.open(objectURL, '_blank');
        });
    }
  }
}
