import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import {  CalendarDateFormatter, CalendarEvent, CalendarView, } from "angular-calendar";
import { CustomDateFormatter } from "../../services/custom-date-formatter.provider";

@Component({
    selector: 'angular-event-calender',
    templateUrl: './angular-event-calender.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
    providers: [
        {
          provide: CalendarDateFormatter,
          useClass: CustomDateFormatter,
        },
      ],
})

export class AngularEventCalenderComponent implements OnInit {
    view: CalendarView = CalendarView.Day;
    CalendarView = CalendarView;
    @Input() viewDate: Date = new Date();
    @Input() activeDayIsOpen:boolean = false;
    @Input() events:any[] = [];
    @Input() cstClassName:string = '';
    @Output() dayClickedEvent:EventEmitter<any> = new EventEmitter();
    @Output() eventClicked:EventEmitter<any> = new EventEmitter();
    @Output() eventTimesChanged:EventEmitter<any> = new EventEmitter();
    modalData: {
        action: string;
        event: CalendarEvent;
    };
    constructor(){

    }

    ngOnInit():void{
        
    }

    dayClicked(event):void{
      this.dayClickedEvent.emit(event.day);
    }

    handleEvent(event):void{
       event['view'] = this.view;
       this.eventClicked.next(event);
    }

    dayEventTimesChanged(event):void{
       this.eventTimesChanged.emit(event);
    }

}