<div class="event-calender-main">
    <div class="event-calender-button">
        <ul [ngClass]="view != 'month' ? 'cst-view-btns' : ''">
            <li *ngIf="view != 'month'">
                <div class="left-arrow" mwlCalendarPreviousView [view]="view" [(viewDate)]="viewDate">
                    <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                        <path id="Icon_ionic-ios-arrow-dropleft-circle" data-name="Icon ionic-ios-arrow-dropleft-circle"
                            d="M18,3.375A14.625,14.625,0,1,0,32.625,18,14.623,14.623,0,0,0,18,3.375ZM21.052,23.7a1.362,1.362,0,0,1,0,1.92,1.34,1.34,0,0,1-.956.394,1.364,1.364,0,0,1-.963-.4l-6.609-6.63a1.355,1.355,0,0,1,.042-1.87l6.708-6.729a1.357,1.357,0,1,1,1.92,1.92L15.434,18Z"
                            transform="translate(-3.375 -3.375)" fill="#0e2340" />
                    </svg>
                </div>
            </li>
            <li><a (click)="view = CalendarView.Day" [class.selected]="view === CalendarView.Day">Day</a></li>
            <li><a (click)="view = CalendarView.Week" [class.selected]="view === CalendarView.Week">Week</a></li>
            <li><a (click)="view = CalendarView.Month" [class.selected]="view === CalendarView.Month">Month</a></li>
            <li *ngIf="view != 'month'">
                <div class="left-arrow right-arrow" mwlCalendarNextView [view]="view" [(viewDate)]="viewDate">
                    <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                        <path id="Icon_ionic-ios-arrow-dropright-circle" data-name="Icon ionic-ios-arrow-dropright-circle"
                            d="M3.375,18A14.625,14.625,0,1,0,18,3.375,14.623,14.623,0,0,0,3.375,18Zm17.191,0-5.759-5.7a1.357,1.357,0,0,1,1.92-1.92l6.708,6.729a1.356,1.356,0,0,1,.042,1.87l-6.609,6.63a1.355,1.355,0,1,1-1.92-1.912Z"
                            transform="translate(-3.375 -3.375)" fill="#0e2340" />
                    </svg>
                </div>
            </li>
        </ul>
    </div>
    <div class="d-flex width100 justify-center">
        <div [class]="cstClassName" [ngSwitch]="view" [ngClass]="view != 'month' ? 'cst-view' : ''">
        
            <div class="selected-month-year">
                <h3>{{ viewDate | calendarDate:(view + 'ViewTitle') }}</h3>
            </div>
            <mwl-calendar-month-view  *ngSwitchCase="CalendarView.Month"
                [viewDate]="viewDate" [events]="events" [activeDayIsOpen]="activeDayIsOpen"
                [weekStartsOn]="1" (dayClicked)="dayClicked($event)" (eventClicked)="handleEvent($event)"
                (eventTimesChanged)="dayEventTimesChanged($event)">
            </mwl-calendar-month-view>
        
            <mwl-calendar-week-view
            *ngSwitchCase="CalendarView.Week"
            [viewDate]="viewDate"
            [events]="events"
            (dayClicked)="dayClicked($event)"
            (eventClicked)="handleEvent($event)"
            (eventTimesChanged)="dayEventTimesChanged($event)">
            </mwl-calendar-week-view>
        
            <mwl-calendar-day-view
            *ngSwitchCase="CalendarView.Day"
            [viewDate]="viewDate"
            [events]="events"
            (dayClicked)="dayClicked($event)"
            (eventClicked)="handleEvent($event)"
            (eventTimesChanged)="dayEventTimesChanged($event)">
            </mwl-calendar-day-view>
        
            <div class="left-arrow" mwlCalendarPreviousView [view]="view" [(viewDate)]="viewDate" *ngIf="view == 'month'">
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropleft-circle" data-name="Icon ionic-ios-arrow-dropleft-circle"
                        d="M18,3.375A14.625,14.625,0,1,0,32.625,18,14.623,14.623,0,0,0,18,3.375ZM21.052,23.7a1.362,1.362,0,0,1,0,1.92,1.34,1.34,0,0,1-.956.394,1.364,1.364,0,0,1-.963-.4l-6.609-6.63a1.355,1.355,0,0,1,.042-1.87l6.708-6.729a1.357,1.357,0,1,1,1.92,1.92L15.434,18Z"
                        transform="translate(-3.375 -3.375)" fill="#0e2340" />
                </svg>
            </div>
            <div class="left-arrow right-arrow" mwlCalendarNextView [view]="view" [(viewDate)]="viewDate" *ngIf="view == 'month'">
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropright-circle" data-name="Icon ionic-ios-arrow-dropright-circle"
                        d="M3.375,18A14.625,14.625,0,1,0,18,3.375,14.623,14.623,0,0,0,3.375,18Zm17.191,0-5.759-5.7a1.357,1.357,0,0,1,1.92-1.92l6.708,6.729a1.356,1.356,0,0,1,.042,1.87l-6.609,6.63a1.355,1.355,0,1,1-1.92-1.912Z"
                        transform="translate(-3.375 -3.375)" fill="#0e2340" />
                </svg>
            </div>
        </div>
    </div>
</div>