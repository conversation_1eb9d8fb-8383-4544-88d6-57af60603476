import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-create-rule',
  templateUrl: './create-rule.component.html',
  styleUrls: ['./create-rule.component.scss']
})
export class CreateRuleComponent implements OnInit {
  ruleName:FormControl = new FormControl('')
  addorm: boolean = false;

  createRuleForm:FormGroup;

  constructor(private router:Router,private commonService: CommonService,private fb: FormBuilder,private translate: TranslateService) { }

  ngOnInit(): void {
    this.createRuleForm = this.fb.group({
      ruleName: ['', Validators.required],
      schedule: ['Schedule', ],
      operators: ['Greater than', ],
      dueDate: ['Due date', ],
      thenAlert: ['', ],
      chooseWorkflow: ['', ],
   
    } );
  }

  enableForm(){
    if(this.addorm == true){
      this.addorm = false;

    }else{
      this.addorm = true;
    }
  }

  
  goPage(page) {
    this.router.navigate([page]);
  }

  saveRules(){
    if(this.createRuleForm.valid){
      this.goPage('rule-list')
    }else{
      this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterPleasefilldetails'] });
  
    }
 
  }
}
