.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3); /* Semi-transparent background */
    backdrop-filter: blur(5px); /* Blur effect */
    z-index: 999; /* Ensure overlay is below the popup but above other content */
  }
  .cst-btn.disabled-btn {
    opacity: 0.5; /* Adjust opacity for a greyed-out look */
    cursor: not-allowed; /* Changes cursor to indicate it's disabled */
    background-color: #d3d3d3; /* A lighter grey color */
    color: #808080; /* Grey text */
    background:#9b9fa1 !important;
  }
  
  
  .popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1000px;
    height: 500px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    z-index: 1000; /* Ensures popup is above the overlay */
    padding: 20px; /* Adds space inside the popup */
    box-sizing: border-box; /* Includes padding and border in the element's total width and height */
  }
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
  }
  
  .search-box {
    width: calc(100% - 100px); /* Adjust width to accommodate for button space */
    padding: 8px;
    border: 1px solid #083D5B;
    border-radius: 4px;
    height: 40%;
    width: 30%;
  }
  
  .behaviour-checklist-fotter {
    display: flex;
    align-items: center;
    margin-top: 10px; /* Adds space above the footer */
  }
  
  .popup-content {
    flex: 1;
    overflow-y: auto; /* Allows vertical scrolling */
    padding: 10px; /* Adds space around the table */
    margin-top: 10px; /* Adds space between table and top of the scroll area */
  }
  
  .popup-table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden; /* Ensures rounded corners */
  }
  
  .popup-table th, .popup-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0; /* Subtle horizontal lines */
  }
  
  .popup-table th {
    background: #083D5B; /* Updated background color */
    color: white; /* White text color for contrast */

    letter-spacing: 1px;
  }
  
  .popup-table tr:hover {
    background: #f1f1f1; /* Subtle hover effect */
  }
  
  #cst-btn {
    position: relative !important;
    top: -10px !important;
  }
  
  .icon-bg-box {
    cursor: pointer;
  }

  

  
  /* Media Query for Tablets in Portrait Mode (1000x1920) */
  @media only screen and (max-width: 1000px) and (min-height: 1920px) {
    .popup {
      width: 90%; /* Adjust width to be responsive */
      height: auto; /* Allow height to adjust automatically */
      max-height: 80vh; /* Prevent it from being too tall */
      padding: 15px; /* Adjust padding for smaller screens */
    }
  
    .search-box {
      width: calc(100% - 80px); /* Adjust width to accommodate for button space */
      font-size: 14px; /* Adjust font size for better readability */
    }
  
    .popup-header {
      flex-direction: column; /* Stack header items vertically */
      align-items: flex-start; /* Align items to start */
      gap: 5px; /* Reduce gap between elements */
    }
  
    .behaviour-checklist-fotter {
      flex-direction: column; /* Stack footer items vertically */
      gap: 10px; /* Adjust spacing between buttons */
    }
  
    .popup-table th, .popup-table td {
      padding: 12px; /* Adjust padding for smaller screens */
      font-size: 14px; /* Adjust font size for better readability */
    }
  }
  