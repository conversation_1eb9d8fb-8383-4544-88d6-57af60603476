// export default App;
import React, { useRef, useEffect, useState, useMemo } from "react";
import { Runtime, Inspector } from "@observablehq/runtime";
import notebook from "../assets/.innoart-table/my-table";
import { useSearchParams } from 'react-router-dom';
import { html } from "htl";
import Asset_JSON from '../assets/data/cognite_data.json';
import Popup from "reactjs-popup";
import format from 'date-fns/format'
import axios from "axios";
import { CogniteClient } from "@cognite/sdk";
import { PublicClientApplication } from "@azure/msal-browser";
import Pagination from "./pagination/Pagination";
import * as Constants from '../Constant'
var _ = require('lodash');

let main;
let limitSet = 10;
let firstPageIndex = 0;
let currentPageNumber = 1;
let listData = [];
let pageInfo = [];
let allListData = [];
let colSummary = {};
let displayedColumns = [
  "name",
  "jan",
  "feb",
  "mar",
  "apr",
  "may",
  "jun",
  "jul",
  "aug",
  "sep",
  "oct",
  "nov",
  "dec",
  "total"
]
var paginationCursor = [];
let site;
let year;
let search;
let startDate;
let endDate;
let initFlag;
let token;
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
function ScheduleTracker() {
  const viewofSelectionRef = useRef();
  const [currentPage, setCurrentPage] = useState(1);
  const [dataCount, setDataCount] = useState(0);
  const [limit, setLimitCount] = useState(10);
  const [id, setId] = React.useState("5");
  function rowDroDownChange(e) {
    setLimitCount(e.target.value);
    setId(e.target.value)
    limitSet = e.target.value;
    filterData();

  }
  window.onmessage = function (e) {
    if (e.data.type && e.data.type == "AuthToken") {
      token = e.data.data;
    }
    if (e.data.type && e.data.type == "ScheduleTracker") {
      if (e.data.action == "Column") {
        displayedColumns = e.data.data;
        colFun();
      } else if (e.data.action == "Filter") {
        site = e.data.sites;
        year = e.data.years;
        setting();
        getData();
      } else
        if (e.data.action == "Summary") {
          colSummary = e.data.data;
          colFun();
        } else
          if (e.data.action == "PageRows") {
            setCurrentPage(1);
            setLimitCount(parseInt(e.data.data))
            limitSet = parseInt(e.data.data);
            paginationCursor = [];
            getData();
          }

    }

  };


  const [searchParams, setSearchParams] = useSearchParams();


  function action1(x, i) {
    return html`<div style=" display: flex;
    flex-direction: row;align-item-center;">

    <div onClick=${() => sendClick(i)} title="Send" style="height:18px;margin-right:12px;cursor: pointer;" >
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"  x="0" y="0" viewBox="0 0 32 32" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="m28.91 4.417-11 24a1 1 0 0 1-1.907-.334l-.93-11.157-11.156-.93a1 1 0 0 1-.334-1.906l24-11a1 1 0 0 1 1.326 1.326z" fill="#1A2254" data-original="#000000"></path></g></svg>
    </div>
      </div>
      `
  }

  function sendClick(index) {
    window.parent.postMessage({ "type": "Audit", "action": "EMailSend", "data": listData[parseInt(index)] }, "*");
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine("configuration", {
      columns: displayedColumns,
      header: {
        "name": "Name",
        "jan": "Jan",
        "feb": "Feb",
        "mar": "Mar",
        "apr": "Apr",
        "may": "May",
        "jun": "Jun",
        "jul": "Jul",
        "aug": "Aug",
        "sep": "Sep",
        "oct": "Oct",
        "nov": "Nov",
        "dec": "Dec",
        "total": "Total"
      },
      headerSummary: colSummary,
      format: {
        createdTime: x => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: x => format(new Date(x),  dateFormat+" "+timeFormat),
        id: x => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i)
      },
      align: {
        "name": "left",
        "jan": "right",
        "feb": "right",
        "mar": "right",
        "apr": "right",
        "may": "right",
        "jun": "right",
        "jul": "right",
        "aug": "right",
        "sep": "right",
        "oct": "right",
        "nov": "right",
        "dec": "right",
        "total": "right"
      },
      rows: 25,
      width: {
        "name": 200,
        "jan": 200,
        "feb": 200,
        "mar": 200,
        "apr": 200,
        "may": 200,
        "jun": 200,
        "jul": 200,
        "aug": 200,
        "sep": 200,
        "oct": 200,
        "nov": 200,
        "dec": 200,
        "total": 200

      },
      maxWidth: "100vw",
      layout: "auto",
    });
  }



  async function getData() {
    console.log(year)
    fetch(Constants.NODE_API + "/api/service/listSchedule",
      {
        method: 'POST',
        headers: {
          'Authorization': "Bearer " + token,
          'Accept': 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          "sites": site,
          "years":year
        })
      })
      .then(res => res.json())
      .then(
        (result) => {

          var resData = result["data"]["list" + Constants.typeSchedule]["items"];
          listData = [];
          console.log(resData);
          window.parent.postMessage(
            {
              type: 'Audit',
              action: 'Loading',
              data: true,
            },
            '*'
          )
          var monthData = [];
          _.each(resData, function (eData) {
            eData["month"] = new Date(eData.createdTime).getMonth();
            monthData.push(eData);
          })
          var myData = [];
          var userGroup = _.groupBy(monthData, function (b) { return b.performerAzureDirectoryUserID.firstName });
          console.log(userGroup)
          for (const key in userGroup) {
            var myObj = {
              name: key,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
              total: 0
            }
            if (userGroup.hasOwnProperty(key)) {
              var monthGroup = _.groupBy(userGroup[key], function (b) { return b.month+"" });
              // myObj["bymonth"] = monthGroup;
              console.log(monthGroup)
              console.log(monthGroup["9"])
              console.log(monthGroup["10"])
              
              var str9 = monthGroup["9"] ? monthGroup["9"].length : 0 
              var str10 = monthGroup["10"].length
              console.log(str10,monthGroup["10"].length)
              myObj["jan"] = monthGroup["1"] && monthGroup["9"].length>0  ? monthGroup["1"].length : 0;
              myObj["feb"] = monthGroup["2"] && monthGroup["2"].length>0  ? monthGroup["2"].length : 0;
              myObj["mar"] = monthGroup["3"] && monthGroup["3"].length>0  ? monthGroup["3"].length : 0;
              myObj["apr"] = monthGroup["4"] && monthGroup["4"].length>0  ? monthGroup["4"].length : 0;
              myObj["may"] = monthGroup["5"] && monthGroup["5"].length>0  ? monthGroup["5"].length : 0;
              myObj["jun"] = monthGroup["6"] && monthGroup["6"].length>0  ? monthGroup["6"].length : 0;
              myObj["jul"] = monthGroup["7"] && monthGroup["7"].length>0  ? monthGroup["7"].length : 0;
              myObj["aug"] = monthGroup["8"] && monthGroup["8"].length>0  ? monthGroup["8"].length : 0;
              myObj["sep"] = monthGroup["9"] && monthGroup["9"].length>0  ? monthGroup["9"].length : 0;;
              myObj["oct"] = str10;
              myObj["nov"] = monthGroup["11"] && monthGroup["11"].length>0  ? monthGroup["11"].length : 0;
              myObj["dec"] = monthGroup["12"] && monthGroup["12"].length>0  ? monthGroup["12"].length : 0;
              myObj["total"] = userGroup[key] ? userGroup[key].length : 0;
              myData.push(myObj)
              listData.push(myObj)
              filterData();
            }
            
            
          }

          // listData = [];
          // listProcess.forEach(element => {
          //   element["actions"] = "";
          //   element["priority"] = element["refOFWASchedule"] ? element["refOFWASchedule"]["priority"] : "";
          //   element["quarter"] = element["refOFWASchedule"] ? element["refOFWASchedule"]["quarter"] : "";
          //   element["year"] = element["refOFWASchedule"] ? element["refOFWASchedule"]["year"] : "";
          //   element["title"] = element["refOFWASchedule"] ? element["refOFWASchedule"]["title"] : "";

          //   listData.push(element);
          // });
          //  main.redefine("data", listData);
          setCurrentPage(1);
          initFlag = true
          filterData();
          // colFun();
        })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }
  function filterData() {

    var currentList = [];
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter(obj => {
          return JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !== -1;
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(firstPageIndex, (firstPageIndex + limitSet));
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, (firstPageIndex + limitSet));
      }

    }

    if (initFlag) {
      main.redefine("data", currentList);
      colFun();
    }
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit;
    const lastPageIndex = firstPageIndex + limit;
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage]);

  useEffect(() => {



    const runtime = new Runtime();
    main = runtime.module(notebook, name => {
      if (name === "viewof selection1") return new Inspector(viewofSelectionRef.current);
      if (name === "selection") {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage({ "type": "Assets", "action": "Select", "data": [], "selected": value }, "*");
          },
          // rejected(error) { console.error(error); }
        };
      }
    });
    setDataCount(1);
    colFun();
    getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose();
  }, []);



  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className="tableBottom">
        <div>
        </div>
        <Pagination
          className="pagination-bar"
         // assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={page => setCurrentPage(page)}
        />
        <div className="numberRows">
          <span className="numRowsText">
            Rows per page: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>

            <option >10</option>
            <option >20</option>
            <option >50</option>
          </select>
        </div>
      </div>
    </>
  );
}



export default ScheduleTracker;