import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { TranslateService } from '@ngx-translate/core';
import { UserInterDailogComponent } from 'src/app/shared/user-integration-dialog/userInter-dailog.component';
import { formatDate } from '@angular/common';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';

@Component({
  selector: 'app-schedule-list',
  templateUrl: './schedule-list.component.html',
  styleUrls: ['./schedule-list.component.scss']
})
export class ScheduleListComponent implements OnInit {


  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");

  corePrincipleList: any[];
  filteredCorePrincipleList: any[];
  corePrincipleControl = new FormControl();

  processList: any[];
  filteredProcessList: any[];
  processControl = new FormControl();

  subProcessList: any[];
  filteredSubProcessList: any[];
  subProcessControl = new FormControl();
  labels = {}


  yearControl: FormControl = new FormControl("2023");
  yearArray = [
    {
      name: "2023",
    },
    {
      name: "2022",
    },
    {
      name: "2021",
    }
  ]
  filteredYearOptions: Observable<any[]>
  mainSchedule: boolean = true;
  createScedule: boolean = false;
  trackerScedule: boolean = false;


  displayedColumns: any = [];

  allColumns = [

    { name: 'externalId', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name: 'title', displayName: "Title", key: "title", activeFlag: true, summary: false },
    { name: 'auditNumber', displayName: "Audit Number", key: "tablecolsAuditnumber", activeFlag: true, summary: false },
    { name: 'corePrinciple', displayName: "Core Principle", key: "corePrinciple", activeFlag: false, summary: false },
    { name: 'process', displayName: "Process", key: "process", activeFlag: false, summary: false },
    { name: 'observationType', displayName: "Observation Type", key: "observationType", activeFlag: false, summary: false },
    { name: 'occurrence', displayName: "Occurrence", key: "occurrence", activeFlag: true, summary: false },
    { name: 'observationStartDate', displayName: "Stat Date", key: "startDate", activeFlag: true, summary: false },
    { name: 'observationEndDate', displayName: "Due Date", key: "tablecolsDuedate", activeFlag: true, summary: false },
    { name: 'year', displayName: "Year", key: "year", activeFlag: true, summary: false },
    { name: 'quarter', displayName: "Quarter", key: "quarter", activeFlag: true, summary: false },
    { name: 'observer', displayName: "Observer", key: "observer", activeFlag: true, summary: false },
    { name: 'priority', displayName: "Priority", key: "priority", activeFlag: false, summary: false },
    { name: 'status', displayName: "Status", key: "status", activeFlag: true, summary: false },
    { name: 'createdTime', displayName: "Created Time", key: "tablecolsCreatedtime", activeFlag: true, summary: false },
    { name: 'createdBy', displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: 'lastUpdatedTime', displayName: "Updated On", key: "updatedOn", activeFlag: false, summary: false },
    { name: 'modifiedBy', displayName: "Updated By", key: "updatedBy", activeFlag: false, summary: false },
    { name: 'actions', displayName: "Actions", key: "actions", activeFlag: true, summary: false },
 


  ]
  url: any = "";
  url2: any = "";
  listFlag: string = "Observation";
  siteList: any[];
  filteredSiteList = [];
  initialDataFlag: any = 0;
  userAccessMenu: any;
  createschedule_obj: any;
  loaderFlag: boolean;

  constructor(private tokenService: TokenService, private router: Router, private commonService: CommonService, private dataService: DataService, private dialog: MatDialog, private translate: TranslateService, public translationService: TranslationService, private languageService: LanguageService, private ngZone: NgZone, private changeDetectorRef: ChangeDetectorRef) {
    this.url = this.dataService.React_API + "/scheduleList";

    this.url2 = this.dataService.React_API + "/auditPlan";
    this.labels = {
      'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
      'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
      'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
      'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship']||'cardsStewardship',
      'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality']||'cardsQuality',
      'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] ||'cardsReliability',
      'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement']||'cardsEngagement',
      'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess']||'commonfilterChooseprocess',
      'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType']||'observationType',
      'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear']||'buttonClear',
      'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply']||'buttonApply',
      'createscheduleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createscheduleTitle']||'createscheduleTitle',
      'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
      'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
    }

  }

  ngOnInit(): void {
    // var iframe = document.getElementById('iFrameScheduleList');
    // if (iframe == null) return;
    // var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    // iWindow?.postMessage(
    //   {
    //     type: 'Language',
    //     action: 'Language',
    //     LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
    //     idToken: this.tokenService.getIDToken(),
    //     labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
    //   },
    //   '*'
    // );
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'menuSchedule': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'menuSchedule'] || 'menuSchedule',
          'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship']||'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality']||'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] ||'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement']||'cardsEngagement',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess']||'commonfilterChooseprocess',
          'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType']||'observationType',
          'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear']||'buttonClear',
          'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply']||'buttonApply',
          'createscheduleTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createscheduleTitle']||'createscheduleTitle',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetectorRef.detectChanges();
      })
      _this.changeDetectorRef.detectChanges();
    })
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "AuditPlan") {
        if (e.data.action == "Edit") {
          console.log('e.data.data', e.data.data)
          _this.router.navigateByUrl('schedule-list/create-schedule', {
            state: { "data": e.data.data, "action": "Edit" }
          });
        } 
        else if ( e.data.action == "Loading") {
          _this.loaderFlag = false
        }
        else if(e.data.action =="disableClick"){
          console.log('disableClick',e.data.data)
         _this.alertMesg(e.data.data);
         }
        else if (e.data.action == "View") {
          _this.router.navigateByUrl('schedule-list/create-schedule', {
            state: { "data": e.data.data, "action": "View" }
          });
        }
        else if(e.data.action == "scheduleDetails"){
          console.log(' e.data.data', e.data.data)
          _this.router.navigateByUrl('schedule-list/schedule-details', {
            state: { "data": e.data.data, "action": "View" }
          });
          
        }
        else if (e.data.action == "createAction") {
          console.log("HIIIIIIIIIIIIIi",e.data.data)
          var scheduledetails= e.data.data
          if(scheduledetails.title.length>=10 && scheduledetails.title.length<=200){
          //  if((scheduledetails?.refReportingLocation?.externalId) !='null'){
          //listRecurrenceType
          _this.dataService.postData({ objectExternalId:scheduledetails?.externalId ?? '' }, _this.dataService.NODE_API + "/api/service/listAction").
          subscribe((resDataAct: any) => {
            var actionData = resDataAct["data"]["list" + _this.commonService.configuration["typeAction"]]["items"]
            if(actionData.length == 0){
             _this.dataService.postData({ email: scheduledetails?.performerAzureDirectoryUserID?.externalId ?? '' }, _this.dataService.NODE_API + "/api/service/listUserAzureAttribute").
                subscribe((resData: any) => {
                    if (resData["data"] && resData["data"]["list" + _this.commonService.configuration["typeUserAzureAttribute"]]["items"].length > 0) {
                        var userItems = resData["data"]["list" + _this.commonService.configuration["typeUserAzureAttribute"]]["items"][0];
                        var assignId = userItems.externalId;
                        var actionItemObj = {
                          title: scheduledetails?.title ?? '',
                          description: scheduledetails?.title ?? '',
                          assignedToIds: [assignId],
                          assignedTo: assignId,
                        
                          assignmentDate: formatDate(
                            scheduledetails?.observationStartDate ?? new Date(),
                            'yyyy-MM-dd',
                            'en-US'
                          ),
                          // reportingUnitId: "UNT-CLKAAN",
                          // reportingLocationId: "LOC-CLKAANAAN",
                          dueDate: formatDate(
                            scheduledetails?.observationEndDate ?? new Date(),
                            'yyyy-MM-dd',
                            'en-US'
                          ),
                          objectType: "OFWASchedule",
                          objectId: scheduledetails?.externalId ?? '',
                          sourceId: scheduledetails?.externalId ?? '',
                          sourceType: {
                            "externalId":"AST-OFWA-OFWAEvent",
                            "space":"AIM-COR-ALL-REF",
                            "name":"OFWAEvent",
                            "description":""
                          },
                          sourceTypeId:"AST-OFWA-OFWAEvent",
                          priority: scheduledetails?.priority ?? 'Medium',
                          createdById: _this.dataService.userInfo.externalId,
                        };
                       
                        if(scheduledetails.refReportingLocation){
                          actionItemObj["reportingLocationId"] = scheduledetails.refReportingLocation.externalId;
                          if(scheduledetails.refReportingLocation.reportingUnit){
                            actionItemObj["reportingUnitId"] = scheduledetails.refReportingLocation.reportingUnit.externalId;
                          }
                        }
                          // reportingUnitId: scheduledetails?.refReportingLocation?.externalId ?? '',
                          // reportingLocationId: scheduledetails?.refReportingLocation?.externalId ?? '',
                        console.log("actionItemObj", actionItemObj);
                       
                        var mainObj = {
                          application: {
                            externalId: _this.commonService.applicationInfo?.externalId ?? '',
                            space: _this.commonService.applicationInfo?.space ?? '',
                          },
                          reportingSite: {
                            externalId: e?.data?.data?.refSite?.externalId ?? '',
                            space: e?.data?.data?.refSite?.space ?? '',
                          },
                          eventMetadata: actionItemObj,
                        };
                       
                        var postObj = {
                          type: _this.commonService.configuration['typeActionItemEvent'] ?? '',
                          siteCode: _this.dataService.siteId.split('-')[1] ?? '',
                          unitCode: _this.commonService.configuration['allUnitCode'] ?? '',
                          items: [mainObj],
                        };
                      console.log('postObj',postObj);
                      console.log("HIIIIIIIIIIIIIIIIIII")
                      _this.dataService
                              .postData(
                                postObj,
                                _this.dataService.NODE_API +
                                  '/api/service/createInstanceByProperties'
                              )
                              .subscribe((data) => {
                                console.log("HIIIIIIIIIIIIIIIIIII")
                                _this.commonService.triggerToast({
                                  type: 'success',
                                  title: '',
                                  msg: _this.commonService.toasterLabelObject['toasterActionitemcreatedsuccessfully'],
                               });
                              });
                    }else{
                      _this.commonService.triggerToast({
                        type: 'error',
                        title: '',
                        msg: _this.commonService.toasterLabelObject['toasterUsernotfound'],
                      });
                    }
                  
                  })
              
              
            

            }else{
              _this.commonService.triggerToast({
                type: 'error',
                title: '',
                msg: _this.commonService.toasterLabelObject['toasterActionalreadycreated'],
              });
            }
          })
        
 
           
 
          // // _this.goPage('action/create-action')
          //  _this.router.navigateByUrl('action/create-action', {
          //    state: { "data": e.data.data,
          //            "pageFrom":_this.commonService.configuration["typeObservation"],
          //            "action":"Create Action" }
          //  });
                // }
                // else{_this.commonService.triggerToast({
                //   type: 'error',
                //   title: '',
                //   msg: "Cannot create action without reporting location and unit",
                // });
                 
                // }
              
              }
              else{
                _this.commonService.triggerToast({
                  type: 'error',
                  title: '',
                  msg: _this.commonService.toasterLabelObject['toasterActionitemcreatefailed'],
                });
             
            }
         }

      };

    }

    var _this = this;
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if(_this.userAccessMenu){
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      console.log("fiterType>",fiterType)
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        console.log("initialDataFlag>",_this.initialDataFlag)
        if (_this.initialDataFlag > 1) {
          console.log("2222>")

          _this.siteControl.setValue(_this.dataService.siteId);
          _this.commonService.getProcessConfiguration(
            _this.siteControl.value,
            function (data) {
              _this.corePrincipleFind(_this.siteControl.value);
            }
          );
         
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      console.log(value)
      _this.dataService.siteId = value;
      _this.commonService.getProcessConfiguration(
        _this.siteControl.value,
        function (data) {
          _this.corePrincipleFind(_this.siteControl.value);
        }
      );
      setTimeout(function () {
        _this.applyFilter();
      }, 2000);
    });
    setTimeout(function () {
      _this.applyFilter();
    }, 2000);

    _this.corePrincipleControl.valueChanges.subscribe(value => {
      _this.processList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value);
      })
      _this.filteredProcessList = _this.processList.slice();
      
    });
    _this.processControl.valueChanges.subscribe(data => {
      // if(data=="Audit"){
      _this.listFlag = "Audit";
      // }else{
      //   this.listFlag = "Observation";
      // }
    
      _this.subProcessList = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == data);
      })
      _this.subProcessList = _this.subProcessList.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      _this.filteredSubProcessList = _this.subProcessList.slice();
    })
    
    _this.subProcessControl.valueChanges.subscribe(data => {
    })

  }
  corePrincipleFind(siteId) {
    var _this = this;
    
    console.log(siteId)
    console.log(_this.commonService.processList)
    _this.corePrincipleList = _this.commonService.processList.filter(e => {
      return ((e.refSite && e.refSite.externalId) == siteId && e.processType == "Core Principles") ;
    })
    _this.filteredCorePrincipleList = _this.corePrincipleList.slice();
  

  }

  alertMesg(data){
    var _this =this
    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {  title:'schedulelistCancelmsg'}
   });
   dialogRef.afterClosed().subscribe(result => {
     if(result == 'YES'){
      var postObjDetail = {
        externalId:data.externalId,
        status: "Disabled",
        isEnable:false
      }
      var instanceAuditObj = {
        "type":  _this.commonService.configuration["typeSchedule"],
        "siteCode": data.space?data.space.split('-')[1]:"COR",
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          postObjDetail
        ]
        }
        console.log('instanceAuditObj',instanceAuditObj);
        _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
          _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterDisabsuccess'] });
          _this.applyFilter() 
        })
      }
   })
  }
  ngAfterViewInit(): void {

    var _this = this;
    _this.loaderFlag = true
    var iframe = document.getElementById('iFrameScheduleList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", 
      "data": _this.tokenService.getToken(),
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase(),
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
    }, '*');
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
    iWindow?.postMessage({ "type": "FormConfig", "action": "Column", "data": this.displayedColumns }, '*');
    // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');
    _this.getUserMenuConfig();
    this.filteredSiteOptions = this.siteControl.valueChanges.pipe(
      startWith('', asyncScheduler),
      map(value => this.commonService._siteFilter(value || '')),
    );

    this.filteredUnitOptions = this.unitControl.valueChanges.pipe(
      startWith('', asyncScheduler),
      map(value => this.commonService._unitFilter(value || '')),
    );
   
  }

  getUserMenuConfig(){
    var _this = this
    
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
      if(_this.commonService.menuFeatureUserIn.length>0){
      var ScheduleView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleView);
      var ScheduleEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleEdit);
      var ScheduleTracker = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleTracker);
       var ScheduleCreateAudit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleAuditSchedule);
      var ScheduleCreateFieldWalk = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateFieldWalk);
      var ScheduleCreateObservation = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.scheduleCreateObservation);
      var iframe = document.getElementById('iFrameAuditScheduleList');
           if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ 
        "type": "AuthToken", 
        "data": _this.tokenService.getToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
        "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
      }, '*');
      iWindow?.postMessage({ "type": "AuditPlan", "action": "AccessMenu", "data": {ScheduleView:ScheduleView,ScheduleEdit:ScheduleEdit} }, '*');
        
      }else{
        var iframe = document.getElementById('iFrameAuditScheduleList');
        if (iframe == null) return;
   var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
   iWindow?.postMessage({ 
    "type": "AuthToken", 
    "data": _this.tokenService.getToken(),
    "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
    "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
  }, '*');
   iWindow?.postMessage({ "type": "AuditPlan", "action": "AccessMenu", "data": {ScheduleView:{},ScheduleEdit:{}} }, '*');
     
      }
    
  }



  ngOnDestroy(): void {

  }

  scheduleSendToVedor(vendor) {

    var _this = this;
    _this.dataService.postData({ email: vendor.email }, _this.dataService.NODE_API + "/api/service/scheduleSendToVedor").subscribe(data => {
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
      // if (data["items"].length > 0) {
      //   _this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });

      // } else {
      //   _this.commonService.triggerToast({ type: 'error', title: "", msg: "Failed" });
      // }
    })
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
     console.log('_this.displayedColumns',_this.displayedColumns);
      // _this.loaderFlag = true
      var iframe = document.getElementById('iFrameAuditScheduleList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ 
        "type": "AuthToken", 
        "data": _this.tokenService.getToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
        "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
      }, '*');
      iWindow?.postMessage({ "type": "AuditPlan", "action": "Column", "data": _this.displayedColumns }, '*');
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    // this.columnSummarysubject.next(summaryCol);
    console.log('summaryCol',summaryCol);
    var iframe = document.getElementById('iFrameAuditScheduleList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", 
      "data": _this.tokenService.getToken(),
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
    }, '*');
    iWindow?.postMessage({ "type": "AuditPlan", "action": "Summary", "data": summaryCol }, '*');
 
  }

  clearFilter(filter) {
    var _this = this;
    if (filter == 'All') {
      _this.corePrincipleControl.reset();
      _this.processControl.reset();
      _this.subProcessControl.reset();
      _this.applyFilter();
    }

    if (filter == 'Country') {
      _this.siteControl.reset();
    }
    if (filter == 'Site') {
      _this.unitControl.reset();
    }

    if (filter == 'Unit') {
    }
  }

  applyFilter() {
    var _this = this;
    setTimeout(function () {
    var iframe = document.getElementById('iFrameAuditScheduleList');
    console.log('iframe', iframe)
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", 
      "data": _this.tokenService.getToken(),
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
    }, '*');
    console.log(_this.siteControl.value)
    var sites = []
    if (_this.siteControl.value) {
      sites = [_this.siteControl.value]                                                 
    }
    iWindow?.postMessage({ "type": "AuditPlan", "action": "Filter", "subCategory": "","corePrincipleId":_this.corePrincipleControl.value,"processId":_this.processControl.value,"subProcessId":_this.subProcessControl.value,   sites: sites, LanguageCode: `${_this.commonService.selectedLanguage}`, }, '*');
    _this.getUserMenuConfig();
  }, 1500);


  }

  private _metanormalizeValue(value: any): any {
    return value.toLowerCase().replace(/\s/g, '');
  }

  processSelected(process) {
  }
  createSchedule() {
    this.mainSchedule = false;
    this.createScedule = true;
    this.trackerScedule = false;

  }

  goTracker() {
    this.mainSchedule = false;
    this.createScedule = false;
    this.trackerScedule = true;
  }
  backToList() {
    this.mainSchedule = true;
    this.createScedule = false;
    this.trackerScedule = false;
  }
  goPage(page) {
    this.router.navigate([page]);
  }


  yearSelected(event) {

  }





  getNextevent(event) {
    if (event.schedule == true) {
      this.mainSchedule = true;
      this.createScedule = false;
      this.trackerScedule = false;
    }
    if (event.tracker == true) {
      this.mainSchedule = false;
      this.createScedule = false;
      this.trackerScedule = true;

    }
  }

}
