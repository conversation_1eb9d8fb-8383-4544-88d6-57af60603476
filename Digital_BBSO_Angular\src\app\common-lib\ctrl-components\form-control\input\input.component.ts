import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";


@Component({
    selector: 'common-lib-input',
    templateUrl: './input.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class InputComponent implements OnInit {
    @Input() placeHolder:string = '';
    @Input() value:any = '';
    @Input() cstClassName:string = '';
    @Output() inputValue: EventEmitter<any> = new EventEmitter<any>();
    @Input() disable:boolean = false;
    constructor() {}
    ngOnInit(): void {
        
    }

    getInputValue(event:any):void {
        this.inputValue.emit(Number(this.value));
    }
}