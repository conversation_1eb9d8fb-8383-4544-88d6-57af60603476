<div class="main-observation">
  <commom-label *ngIf="processList.length>0" labelText="{{ labels['mainTitle'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading mb-50'" style="font-size: 22px !important;"></commom-label>

  <div class="main-observation-section outerbox" style="padding-top: 182px !important; padding-bottom: 159px !important;">
    <div fxLayout="row">
      <div fxFlex="100" class="d-flex flex-flow-wrap justify-center">
        <div *ngFor="let item of processList" >
          <mat-card class="grid-stl" (click)="selectProcess(item)"  
          [ngClass]="(selectedProcess && selectedProcess.externalId) == item.externalId ? 'selectedItem' : ''"  >
          <div class="grid-stl-ico">
            <i [innerHTML]="item.iconImage | safeHtml"></i>
          </div>
          <div class="grid-stl-heading">
            <span class="subheading-2" *ngIf="item.name!='Observation' && item.name != 'Field Walk' && item.name != 'Audit' ">
              <!-- {{ 'OBSERVATION.MAIN.CARDS.'+item.name |  translate }} -->
                {{ labels['cards'+item.name] }}
            </span>
            <span class="subheading-2" *ngIf="item.name=='Observation'">
              <!-- {{ 'OBSERVATION.MAIN.CARDS.OBSERVATION' | translate }} -->
                {{ labels['observation'] }}
            </span>
            <span class="subheading-2" *ngIf="item.name=='Field Walk'">
              <!-- {{ 'OBSERVATION.MAIN.CARDS.OBSERVATION' | translate }} -->
                {{ labels['cardsFieldwalks'] }}
            </span>
            <span class="subheading-2" *ngIf="item.name=='Audit'">
              <!-- {{ 'OBSERVATION.MAIN.CARDS.OBSERVATION' | translate }} -->
                {{ labels['audit'] }}
            </span>
          </div>
        </mat-card>
        </div>


       
      </div>
    </div>
  </div>

  <div class="main-obervation-fotter">
    <!-- <common-lib-button [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'" (buttonAction)="cancelClick()"></common-lib-button> -->
    <!-- <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.NEXT'"  (buttonAction)="goCreateObserve()"></common-lib-button> -->
  </div>

</div>




    
     
      <!-- <div fxLayout="row">
        <div fxFlex="100" fxLayoutAlign="center center">
       
          <button (click)="goCreateObserve()" id="ob_next_btn" mat-raised-button color="primary" style="margin: 20px; width: 114px;"></button>
        </div>
      </div> -->


