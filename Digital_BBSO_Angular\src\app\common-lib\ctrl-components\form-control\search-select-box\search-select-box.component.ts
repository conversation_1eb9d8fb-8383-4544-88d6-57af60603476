import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Options } from "src/app/modals/home.modal";

@Component({
    selector: 'commom-search-select-box',
    templateUrl: './search-select-box.component.html',
    changeDetection:ChangeDetectionStrategy.OnPush,
})

export class CommonSearchSelectBoxComponent implements OnInit {
 @Input() chooseText:string = ''
 @Input() options:Options[] = [];
 @Input() className:string= '';
 @Output() selectedOption = new EventEmitter<any>();
 modeselect:any = '1'
 filteredVariables:any;
  constructor(){}

   ngOnInit(): void {
    this.filteredVariables = [...this.options];
   }

   selectItem(event:any):void {
      this.selectedOption.emit(
          this.options.find(item => {
              return item.value == event.value;
          })
      )
   }
}