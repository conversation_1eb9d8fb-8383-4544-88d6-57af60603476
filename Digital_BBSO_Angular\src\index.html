<!doctype html>
<html lang="en" style="overflow: hidden;">
<head>
  <meta charset="utf-8">
  <title>OFA</title>
  <base href="/">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1">-->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- <link rel="icon" type="image/x-icon" href="favicon.ico"> -->
  <link rel="icon" type="image/x-icon"  href="./assets/favicon.ico" />
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <!-- <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"> -->
  <link href="./assets/mat-icon/css2.css" rel="stylesheet">
  <link href="./assets/mat-icon/icon.css" rel="stylesheet">

  <link rel="stylesheet" type="text/css" href="https://unpkg.com/@observablehq/notebook-inspector@1/dist/notebook-inspector-style.css">
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/d3-org-chart@2"></script>
  <script src="https://cdn.jsdelivr.net/npm/d3-flextree@2.1.2/build/d3-flextree.js"></script>
  <script src="https://storage.ko-fi.com/cdn/scripts/overlay-widget.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
  <script src="https://unpkg.com/topojson-client@3"></script>
  <script
  src="https://code.jquery.com/jquery-3.6.3.min.js"
  integrity="sha256-pvPw+upLPUjgMXY0G+8O0xUf+/Im1MZjXxxgOcBQBXU="
  crossorigin="anonymous"></script>

  <!-- <script src="https://unpkg.com/globe.gl@2.26.12/dist/globe.gl.min.js"></script> -->

  <script src="https://www.webglearth.com/v2/api.js"></script>
  
  <!-- <script src="../src/assets/js/Javascriptfun.js" type="module"></script> -->
  <!-- <script src="https://cdn.jsdelivr.net/npm/@observablehq/runtime@5/dist/runtime.js"></script> -->
  

  <!-- <script type="module">
    import {Runtime, Inspector} from "https://cdn.jsdelivr.net/npm/@observablehq/runtime@5/dist/runtime.js";
    import define from "https://api.observablehq.com/@observablehq/input-table.js?v=3";
    console.log(define)
    new Runtime().module(define, name => {
      console.log(name)
      if (name === "viewof selection") return new Inspector(document.querySelector("#observablehq-viewof-selection-c14393cc"));
    });
    </script> -->
</head>
<body class="mat-typography" style="overflow: hidden;">
  <app-root></app-root>
  <app-redirect></app-redirect>
</body>
</html>
