import { NgModule } from "@angular/core";
import { ButtonComponent } from "./ctrl-components/button/button.component";
import { MaterialModule } from "./material-module";
import { ColumnChartComponent } from "./ctrl-components/chart/column-chart/column-chart.component";
import { NgApexchartsModule } from "ng-apexcharts";
import { ColumnChartService } from "./services/column-chart.service";
import { AreaChartComponent } from "./ctrl-components/chart/area-charts/area-charts.component";
import { CommonLabelComponent } from "./ctrl-components/label/label.component";
import { CommonModule } from "@angular/common";
import { AngularEventCalenderComponent } from "./ctrl-components/angular-event-calender/angular-event-calender";
import { CalendarModule, DateAdapter } from "angular-calendar";
import { adapterFactory } from "angular-calendar/date-adapters/date-fns";
import { CommonSelectBoxComponent } from "./ctrl-components/form-control/select-box/select-box.component";
import { CommonMatTableComponent } from "./ctrl-components/mat-table/mat-table.component";
import { FormsModule } from "@angular/forms";
import { AngleChartComponent } from "./ctrl-components/chart/angle-chart/angle-chart.component";
import { TextAreaComponent } from "./ctrl-components/form-control/text-area/text-area.component";
import { MatDatePickerComponent } from "./ctrl-components/mat-date-picker/mat-date-picker.component";
import { CommonRadioButtonComponent } from "./ctrl-components/form-control/radio-button/radio-button.component";
import { NgCircleProgressModule } from "ng-circle-progress";
import { NgProgressBarComponent } from "./ctrl-components/ng-progress-bar/progress-bar.component";
import { RangeAreaChartComponent } from "./ctrl-components/chart/range-area-chart/range-area-chart.component";
import { InputComponent } from "./ctrl-components/form-control/input/input.component";
import { TranslateModule } from "@ngx-translate/core";
import { CommonSearchSelectBoxComponent } from "./ctrl-components/form-control/search-select-box/search-select-box.component";
import { MatSelectFilterModule } from "mat-select-filter";
import { SafeHtmlPipe } from "../shared/pipes/safeHtml.pipe";
import { SafePipe } from "../shared/pipes/safe.pipe";



@NgModule({
  declarations: [
    ButtonComponent,
    ColumnChartComponent,
    AreaChartComponent,
    CommonLabelComponent,
    AngularEventCalenderComponent,
    CommonSelectBoxComponent,
    CommonSearchSelectBoxComponent,
    CommonMatTableComponent,
    AngleChartComponent,
    MatDatePickerComponent,
    CommonRadioButtonComponent,
    TextAreaComponent,
    NgProgressBarComponent,
    RangeAreaChartComponent,
    InputComponent,
    SafeHtmlPipe,
    SafePipe
  ],
  imports: [
    CommonModule,
    FormsModule,
    MaterialModule,
    NgApexchartsModule,
    TranslateModule,
    MatSelectFilterModule ,
    CalendarModule.forRoot({
      provide: DateAdapter,
      useFactory: adapterFactory,
  }),
  NgCircleProgressModule.forRoot({
    radius: 100,
    outerStrokeWidth: 16,
    innerStrokeWidth: 8,
    outerStrokeColor: "#78C000",
    innerStrokeColor: "#C7E596",
    animationDuration: 300,
    lazy: false
   })
  ],
  exports:[ 
    MaterialModule,
    SafeHtmlPipe,
    SafePipe,
    ButtonComponent,
    NgApexchartsModule,
    ColumnChartComponent,
    AreaChartComponent,
    AngleChartComponent,
    CommonLabelComponent,
    AngularEventCalenderComponent,
    CommonSelectBoxComponent,
    CommonSearchSelectBoxComponent,
    CommonMatTableComponent,
    MatDatePickerComponent,
    TextAreaComponent,
    CommonRadioButtonComponent,
    CalendarModule,
    NgProgressBarComponent,
    NgCircleProgressModule,
    RangeAreaChartComponent,
    InputComponent,
    TranslateModule,
    MatSelectFilterModule
],
  providers: [ColumnChartService],
})
export class CommonLibModule { }

