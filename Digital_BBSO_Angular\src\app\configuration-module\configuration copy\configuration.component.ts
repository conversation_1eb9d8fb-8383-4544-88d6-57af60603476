import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { AddSubCategoryPopupComponent } from '../add-sub-category-popup/add-sub-category-popup.component';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ParentToChildService } from 'src/app/broadcast/parent-to-child.service';

@Component({
  selector: 'app-configuration',
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.scss']
})
export class ConfigurationComponent implements OnInit {
  searchControl: FormControl = new FormControl("");

  siteControl: FormControl = new FormControl("All");
  siteList = [];
  filteredSiteList = [];


  processControl: FormControl = new FormControl("");
  filteredProcessOptions: Observable<any[]>
  // process = []
  processList = [];
  filteredProcessList = [];


  feebackControl: FormControl = new FormControl("Yes");

  corePrincipleList = [];
  // categoryList = []
  observationList = []
  auditList = []
  categoriesList = []
  subCategoriesList = []
  selectedTasks: any = 1;
  observeSelected: any = 1;
  categoriesSelected: any = 1;
  auditSelected = 1;
  subSelected: any;
  auditBool: boolean = false;
  selectedProcess: any;
  selectedCorePrinciple: any;
  selectedSubProcess: any;
  constructor(
    private dataService: DataService,
    public dialog: MatDialog,
    private commonService: CommonService,
    private parentchildService: ParentToChildService,
    private router: Router,
  ) {

    var _this = this;
    // this.process = this.dataService.process
    // this.categoryList = this.dataService.categoryList
    // this.observationList = this.dataService.observationList
    // this.auditList = this.dataService.auditList
    // this.categoriesList = this.dataService.categoriesList;
    // this.subCategoriesList = this.dataService.subCategoriesList


    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          var processList = _this.commonService.processList.filter(e => {
            return e.processType == "Process" && e.isConfigurable;
          })
          _this.processList = processList;
          _this.filteredProcessList = _this.processList.slice();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
        }
      }
    })

  }

  ngOnInit(): void {
    var _this = this;
    this.processControl.valueChanges.subscribe(async process => {
      var selectedProcess = _this.commonService.processList.find(e => {
        return e.externalId == process;
      })
      var childrenProcess = _this.commonService.processList.filter(e => {
        return (e.refProcess && e.refProcess.externalId) == process;
      })
      _this.selectedProcess = selectedProcess;
      if (selectedProcess.name == "Observation") {
        _this.selectedTasks = 1;
        _this.auditBool = false;
        _this.corePrincipleList = childrenProcess;
      }
      if (selectedProcess.name == "Audit") {
        _this.selectedTasks = 2;
        _this.auditBool = true;
        _this.corePrincipleList = childrenProcess;
      }

      // if (process == 'Audit') {
      //   this.selectedTasks = 2
      //   this.auditBool = true;

      //   var auditcate = [
      //     {
      //       id: 1,
      //       name: "Industrial automative"
      //     },
      //     {
      //       id: 2,
      //       name: "RM & CM GMP+MT"
      //     },
      //     {
      //       id: 3,
      //       name: "Logistic"
      //     },

      //   ]
      //   this.categoriesList = auditcate;
      //   var auditSubcate = [
      //     {
      //       id: 1,
      //       name: "QMS"
      //     },
      //     {
      //       id: 2,
      //       name: "Raw Material Receipt & Storage"
      //     },
      //     {
      //       id: 3,
      //       name: "Ingredient Controls & Production"
      //     },
      //     {
      //       id: 4,
      //       name: "Ingredient Controls & Production(Metal Detection)"
      //     },
      //     {
      //       id: 5,
      //       name: "Drying (if applicable)"
      //     },
      //     {
      //       id: 6,
      //       name: "Packaging and  Labeling"
      //     },
      //     {
      //       id: 7,
      //       name: "Tesring and  Release"
      //     },
      //     {
      //       id: 8,
      //       name: "General"
      //     },


      //   ]
      //   this.subCategoriesList = auditSubcate;
      // } else if (process == 'Field Walk') {
      //   this.auditBool = false;
      //   this.observationList = [];
      //   this.categoriesList = [];
      //   this.subCategoriesList = [];
      //   this.categoriesList = []

      // }
      // else {
      //   this.selectedTasks = 1
      //   this.auditBool = false;
      //   this.observationList = [
      //     {
      //       id: 1,
      //       name: "Behaviour"
      //     },
      //     {
      //       id: 2,
      //       name: "Hazards"
      //     },
      //     {
      //       id: 3,
      //       name: "Incidents"
      //     },

      //   ]
      //   this.categoriesList = [
      //     {
      //       id: 1,
      //       name: "PPE"
      //     },
      //     {
      //       id: 2,
      //       name: "Tool & Equipment"
      //     },
      //     {
      //       id: 3,
      //       name: "Ergonomics"
      //     },
      //     {
      //       id: 4,
      //       name: "Work Environment"
      //     },

      //   ]
      //   this.subCategoriesList = [
      //     {
      //       id: 1,
      //       name: "Head"
      //     },
      //     {
      //       id: 2,
      //       name: "Eye-Protection"
      //     },
      //     {
      //       id: 3,
      //       name: "Hearing"
      //     },
      //     {
      //       id: 4,
      //       name: "Body"
      //     },
      //     {
      //       id: 5,
      //       name: "Hands and arms"
      //     },
      //     {
      //       id: 6,
      //       name: "Feet and legs"
      //     },
      //     {
      //       id: 7,
      //       name: "Quality of PPE"
      //     }
      //   ]
      // }
    })

  }
  ngAfterViewInit(): void {

  }


  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredProcessList = _this.processList.slice();
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  selectCorePriciple(item) {
    var _this = this;
    this.selectedCorePrinciple = item;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return (e.refProcess && e.refProcess.externalId) == item.externalId;
    })
    _this.observationList = childrenProcess;
  }

  selectSubProcess(item) {
    var _this = this;
    this.selectedSubProcess = item;
    var childrenProcess = _this.commonService.processList.filter(e => {
      return (e.refProcess && e.refProcess.externalId) == item.externalId;
    })
    // _this.observationList = childrenProcess;
  }

  highlightColor(app) {
    this.selectedTasks = app.id
    if (app.id == 1 && this.processControl.value == "Observation") {
      this.categoriesList = this.dataService.categoriesList
      this.subCategoriesList = this.dataService.subCategoriesList

      this.observationList = this.dataService.observationList
      this.selectedTasks = 1;
      this.observeSelected = 1;
      this.categoriesSelected = 1;

    } else
      if (app.id == 2 && this.processControl.value == "Audit") {
        this.auditList = this.dataService.auditList
        var auditcate = [
          {
            id: 1,
            name: "Industrial automative"
          },
          {
            id: 2,
            name: "RM & CM GMP+MT"
          },
          {
            id: 3,
            name: "Logistic"
          },

        ]
        this.categoriesList = auditcate;
        var auditSubcate = [
          {
            id: 1,
            name: "QMS"
          },
          {
            id: 2,
            name: "Raw Material Receipt & Storage"
          },
          {
            id: 3,
            name: "Ingredient Controls & Production"
          },
          {
            id: 4,
            name: "Ingredient Controls & Production(Metal Detection)"
          },
          {
            id: 5,
            name: "Drying (if applicable)"
          },
          {
            id: 6,
            name: "Packaging and  Labeling"
          },
          {
            id: 7,
            name: "Tesring and  Release"
          },
          {
            id: 8,
            name: "General"
          },


        ]
        this.subCategoriesList = auditSubcate;


      } else {
        this.categoriesList = []
        this.subCategoriesList = []
        this.observationList = []
        this.auditList = []
      }

  }
  observehighlightColor(app) {
    this.observeSelected = app.id

    if (app.id == 1) {
      this.categoriesList = [
        {
          id: 1,
          name: "PPE"
        },
        {
          id: 2,
          name: "Tool & Equipment"
        },
        {
          id: 3,
          name: "Ergonomics"
        },
        {
          id: 4,
          name: "Work Environment"
        },

      ]
      this.subCategoriesList = [
        {
          id: 1,
          name: "Head"
        },
        {
          id: 2,
          name: "Eye-Protection"
        },
        {
          id: 3,
          name: "Hearing"
        },
        {
          id: 4,
          name: "Body"
        },
        {
          id: 5,
          name: "Hands and arms"
        },
        {
          id: 6,
          name: "Feet and legs"
        },
        {
          id: 7,
          name: "Quality of PPE"
        }
      ]
      this.categoriesSelected = 1
    } else {
      this.categoriesList = []
      this.subCategoriesList = []
    }
  }
  audithighlightColor(app) {
    this.auditSelected = app.id
    if (app.id == 1) {
      var auditcate = [
        {
          id: 1,
          name: "Industrial automative"
        },
        {
          id: 2,
          name: "RM & CM GMP+MT"
        },
        {
          id: 3,
          name: "Logistic"
        },

      ]
      this.categoriesList = auditcate;
      var auditSubcate = [
        {
          id: 1,
          name: "QMS"
        },
        {
          id: 2,
          name: "Raw Material Receipt & Storage"
        },
        {
          id: 3,
          name: "Ingredient Controls & Production"
        },
        {
          id: 4,
          name: "Ingredient Controls & Production(Metal Detection)"
        },
        {
          id: 5,
          name: "Drying (if applicable)"
        },
        {
          id: 6,
          name: "Packaging and  Labeling"
        },
        {
          id: 7,
          name: "Tesring and  Release"
        },
        {
          id: 8,
          name: "General"
        },


      ]
      this.subCategoriesList = auditSubcate;
    } else {
      this.categoriesList = []
      this.subCategoriesList = []
    }

  }
  categorieshighlightColor(app) {
    this.categoriesSelected = app.id;
    if (app.id == 1 && this.processControl.value != 'Audit') {
      this.subCategoriesList = [
        {
          id: 1,
          name: "Head"
        },
        {
          id: 2,
          name: "Eye-Protection"
        },
        {
          id: 3,
          name: "Hearing"
        },
        {
          id: 4,
          name: "Body"
        },
        {
          id: 5,
          name: "Hands and arms"
        },
        {
          id: 6,
          name: "Feet and legs"
        },
        {
          id: 7,
          name: "Quality of PPE"
        }
      ]

    } else if (app.id == 2 && this.processControl.value != 'Audit') {
      this.subCategoriesList = [
        {
          id: 1,
          name: "Selection",
        },
        {
          id: 2,
          name: "Use",

        },

        {
          id: 3,
          name: "Storage",

        },
        {
          id: 4,
          name: "Barriers and Warnings",

        },



      ]

    }
    else if (app.id == 1 && this.processControl.value == 'Audit') {
      var auditSubcate = [
        {
          id: 1,
          name: "QMS"
        },
        {
          id: 2,
          name: "Raw Material Receipt & Storage"
        },
        {
          id: 3,
          name: "Ingredient Controls & Production"
        },
        {
          id: 4,
          name: "Ingredient Controls & Production(Metal Detection)"
        },
        {
          id: 5,
          name: "Drying (if applicable)"
        },
        {
          id: 6,
          name: "Packaging and  Labeling"
        },
        {
          id: 7,
          name: "Tesring and  Release"
        },
        {
          id: 8,
          name: "General"
        },


      ]
      this.subCategoriesList = auditSubcate;
    }
    else {
      this.subCategoriesList = []
    }

    this.subSelected = 1;


  }
  subighlightColor(app) {
    this.subSelected = app.id
  }

  processSelected(process) {

    console.log('process', process)
    if (process == 'Audit') {
      this.selectedTasks = 2
      this.auditBool = true;

      var auditcate = [
        {
          id: 1,
          name: "Industrial automative"
        },
        {
          id: 2,
          name: "RM & CM GMP+MT"
        },
        {
          id: 3,
          name: "Logistic"
        },

      ]
      this.categoriesList = auditcate;
      var auditSubcate = [
        {
          id: 1,
          name: "QMS"
        },
        {
          id: 2,
          name: "Raw Material Receipt & Storage"
        },
        {
          id: 3,
          name: "Ingredient Controls & Production"
        },
        {
          id: 4,
          name: "Ingredient Controls & Production(Metal Detection)"
        },
        {
          id: 5,
          name: "Drying (if applicable)"
        },
        {
          id: 6,
          name: "Packaging and  Labeling"
        },
        {
          id: 7,
          name: "Tesring and  Release"
        },
        {
          id: 8,
          name: "General"
        },


      ]
      this.subCategoriesList = auditSubcate;
    } else {
      this.selectedTasks = 1
      this.auditBool = false;
      this.categoriesList = [
        {
          id: 1,
          name: "PPE"
        },
        {
          id: 2,
          name: "Tool & Equipment"
        },
        {
          id: 3,
          name: "Ergonomics"
        },
        {
          id: 4,
          name: "Work Environment"
        },

      ]
      this.subCategoriesList = [
        {
          id: 1,
          name: "Head"
        },
        {
          id: 2,
          name: "Eye-Protection"
        },
        {
          id: 3,
          name: "Hearing"
        },
        {
          id: 4,
          name: "Body"
        },
        {
          id: 5,
          name: "Hands and arms"
        },
        {
          id: 6,
          name: "Feet and legs"
        },
        {
          id: 7,
          name: "Quality of PPE"
        }
      ]
    }

  }

  goPage(page) {
    this.router.navigate([page]);
  }
  addSubCatrgory() {

    var dataPass = {
      type: "subCategory",
      headername: "Add Sub-Category",
      firstKey: "Category",
      firstValue: this.categoriesList,
      secondKey: "Sub-Category Name",
      secondPlaceholder: "Sub-Category"

    }

    const dialogRef = this.dialog.open(AddSubCategoryPopupComponent, {
      data: dataPass,
      height: '319px',
      width: '427px',
      panelClass: 'add-sub-category-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed');

      console.log(result)
      if (result != true) {
        var obj = {
          id: this.subCategoriesList.length + 1,
          name: result
        }

        this.subCategoriesList.push(obj)
      }

    });
  }

  addCategories() {
    var dataPass = {
      type: "category",
      headername: "Add Category",
      firstKey: "Observation Type",
      firstValue: this.observationList,
      secondKey: "Category Name",
      secondPlaceholder: "Category"
    }

    const dialogRef = this.dialog.open(AddSubCategoryPopupComponent, {
      data: dataPass,
      height: '319px',
      width: '427px',
      panelClass: 'add-sub-category-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed');
      if (result != true) {
        var obj = {
          id: this.categoriesList.length + 1,
          name: result
        }

        this.categoriesList.push(obj)
      }

    });


  }
  addObservation() {
    var dataPass = {
      type: "observation",
      headername: "Add Observation Type",
      firstKey: "Core Principles",
      firstValue: this.corePrincipleList,
      secondKey: "Observation Name",
      secondPlaceholder: "Observation"
    }

    const dialogRef = this.dialog.open(AddSubCategoryPopupComponent, {
      data: dataPass,
      height: '319px',
      width: '427px',
      panelClass: 'add-sub-category-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed',result);
      //observationList
      if (result != true) {
        var obj = {
          id: this.observationList.length + 1,
          name: result
        }
        this.observationList.push(obj)
      }
    });
  }

  addAudit() {
    var dataPass = {
      type: "audit",
      headername: "Add Audit Type",
      firstKey: "Core Principles",
      firstValue: this.corePrincipleList,
      secondKey: "Audit Name",
      secondPlaceholder: "Audit"
    }

    const dialogRef = this.dialog.open(AddSubCategoryPopupComponent, {
      data: dataPass,
      height: '319px',
      width: '427px',
      panelClass: 'add-sub-category-popup'
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed');
      //observationList
      if (result != true) {
        var obj = {
          id: this.auditList.length + 1,
          name: result
        }

        this.auditList.push(obj)
      }

    });

  }

  saveConfig() {



    //this.auditSelected = 1;
    if (this.auditBool == false) {
      if (this.selectedTasks && this.categoriesSelected && this.observeSelected && this.subSelected) {
        this.goPage('configuration/config-list')
      } else {
        this.commonService.triggerToast({ type: 'error', title: "", msg: "Please choose categories" });

      }
    }
    if (this.auditBool == true) {
      if (this.selectedTasks && this.categoriesSelected && this.auditSelected && this.subSelected) {
        this.goPage('configuration/config-list')
      } else {
        this.commonService.triggerToast({ type: 'error', title: "", msg: "Please choose categories" });

      }
    }



  }
  formConfig() {
    // this.goPage('form-list')



    var findObj = this.observationList.find(x => x.id === this.observeSelected);
    console.log('findObj', findObj)

    this.router.navigateByUrl('configuration/form-list', {
      state: findObj
    });

  }

}
