<div *ngIf="loaderFlag" class="spinner-actionpopup">
    <mat-spinner class="spinneractionpopup"></mat-spinner>
  </div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="action-section">
            
            <div class="mb-20"  fxLayout="row">
                <commom-label labelText="{{ labels['createAction'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
            </div>

            <div class="marginTop"  >
                <form [formGroup]="createObservedForm">
                    <div fxLayout="row wrap" fxLayout.sm="column" fxLayout.xs="column" fxLayout.md="column" class="marginTop"  fxLayoutGap="30px">
                        <div fxFlex="50" fxLayout="column" fxLayoutGap="30px">
                            <div  fxLayout="row wrap">
                                <div fxFlex="30">
                                    <span class="body-1">{{ labels['actionDesc'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                              
                                        <textarea matInput formControlName="observeDesc"
                                                  cdkTextareaAutosize
                                              
                                                  cdkAutosizeMinRows="4"
                                                  cdkAutosizeMaxRows="4"></textarea>
                                      </mat-form-field>
                                      <span style="font-size: 12px;">
                                        *{{ labels['createactionDescmessage'] }}
                                    </span>
                                </div>
                               
                            </div>
                            <!-- <div  fxLayout="row wrap">
                                <div fxFlex="30">
                                    <span class="body-1">{{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.SUGGESTION_FEEDBACK' | translate }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" style="width: 100%;height: 90px;">
                              
                                        <textarea matInput formControlName="feedBack"
                                                  cdkTextareaAutosize
                                              
                                                  cdkAutosizeMinRows="4"
                                                  cdkAutosizeMaxRows="5"></textarea>
                                      </mat-form-field>
                                </div>
                            </div> -->
                            <!-- <div  fxLayout="row wrap">
                                <div fxFlex="30">
                                    <span class="body-1">{{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.UPLOAD_FILES' | translate }}</span>
                                </div>
                                <div fxFlex="70">
                                   
                                    <input 
                                    hidden 
                                    formControlName="uploadFile"
                                    type="file" 
                                    #uploader
                                    (change)="uploadFile($event)"
                                />
                                <div class="uploadFileBox"  fxLayoutAlign="center center" >
                                    <div  fxLayout="row ">
                                        <span class="subheading-1">
                                            {{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.DRAG_DROP' | translate }}
                                        </span>
                                        &nbsp;&nbsp;
                                        <span class="subheading-1 uploadLink cursor" (click)="uploader.click()">
                                            {{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.CHOOSE_FILE' | translate }}
                                        </span>
                                    </div>
                                </div>
                                              
                                
                                     
                             
                                     
                                </div>
                            </div> -->
        
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">{{ labels['priority'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" style="width: 180px;">
                                    
                                        <mat-select value="Assigned" formControlName="status">
                                        
                                          <mat-option *ngFor="let item of statusList" [value]="item.name">
                                          {{ labels[item.value.toLowerCase()] }} 
                                        </mat-option>
                                        </mat-select>
                                      </mat-form-field>
                                </div>
                            </div>

                            <div>
                                <div fxFlex="30">
                                    <span class="body-1">{{labels['formcontrolsAddAttachment']}}</span>
                                </div>
                                <div fxLayout="column" fxLayoutAlign="start stretch" fxFlex="70">
                                    <div>
                                        <input hidden type="file" #uploader (change)="onFileChange($event)" />
                                        <span class="uploadLink cursor" (click)="uploader.click()">
                                            {{labels['chooseFile']}}
                                        </span>
                                    </div>
                                    
                                    <!-- Show selected file name below the 'Choose File' header -->
                                    <div *ngIf="selectedFile" style="margin-top: 10px;">
                                        <span>{{selectedFile.name}}</span>
                                    </div>
                            
                                    <div>
                                        <svg *ngIf="!selectedFile && (configureDataDialog && configureDataDialog.guidelineDocument && configureDataDialog.guidelineDocument.length>0)"
                                            (click)="imageView()" xmlns="http://www.w3.org/2000/svg" version="1.1"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                                            x="0" y="0" viewBox="0 0 8.467 8.467" style="margin-left: 30px;width: 25px;height: 25px;"
                                            xml:space="preserve" class="">
                                            <g>
                                                <path d="M2.357.53a.844.844 0 0 0-.858.832v5.742c0 .459.38.833.858.833h3.751c.478 0 .86-.374.86-.833V2.822l-2.06-.337a.75.75 0 0 1-.615-.919L4.56.53zm2.462.13-.25.978a.462.462 0 0 0 .385.568l1.733.281zm-.58 2.788a.4.4 0 0 1 .343.193c.119.16.128.367.084.577a2.59 2.59 0 0 1-.236.601c.18.3.384.606.537.838.129-.019.256-.031.376-.037.235-.01.446.006.616.097a.478.478 0 0 1 .227.595.44.44 0 0 1-.269.248.57.57 0 0 1-.362.008c-.26-.08-.478-.334-.688-.594l-1.396.325c-.173.358-.328.668-.567.814a.45.45 0 0 1-.232.065.461.461 0 0 1-.288-.107c-.183-.17-.171-.463 0-.656.204-.23.545-.272.9-.356.274-.36.588-.813.816-1.228-.013-.023-.028-.039-.04-.062a2.457 2.457 0 0 1-.25-.61c-.043-.194-.038-.395.092-.54a.471.471 0 0 1 .338-.171zm-.003.286H4.23a.14.14 0 0 0-.116.074c-.038.056-.061.139-.028.288.025.111.097.257.166.4.049-.116.116-.243.135-.337.036-.17.018-.285-.034-.351-.03-.04-.066-.075-.118-.074zm.032 1.36c-.156.265-.353.557-.542.843l.935-.227c-.117-.18-.26-.402-.393-.615zm1.145.808-.057.002a.716.716 0 0 0-.131.02c.15.154.313.342.414.373.075.027.245.003.28-.088.037-.099.006-.186-.097-.242a.936.936 0 0 0-.409-.065zm-2.383.55c-.227.038-.347.072-.431.152-.082.093-.073.207-.02.257a.139.139 0 0 0 .175.01c.065-.04.177-.257.276-.418z"
                                                    fill="#083d5b" data-original="#000000" class="" style="cursor: pointer;"></path>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                           

                        </div>
                        <div fxFlex="40" fxLayout="column"  fxLayoutGap="30px">
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1"> {{
                                        labels['unit'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" class="behav-tree-select">
                      
                                        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChooseunit'] }}" [formControl]="unitControl"
                                            disableOptionCentering >
                                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="unitList"
                                                (filteredReturn)="filteredUnitList =$event"></mat-select-filter>
                                            <mat-option value="0"
                                                (click)="commonService.toggleAllSelection(unitControl,unitList)">All</mat-option>
                                            <mat-option *ngFor="let item of filteredUnitList" [value]="item.externalId">
                                                {{item.name}} - {{item.description}}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">  {{ labels['locationObserved'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" class="behav-tree-select">
                                        <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}"  [formControl]="reportingLocationControl"
                                          disableOptionCentering>
                                          <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'"
                                            [array]="reportingLocationList"
                                            (filteredReturn)="filteredReportingLocationList =$event"></mat-select-filter>
                                          <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item.externalId">
                                            {{item.description}}
                                          </mat-option>
                                        </mat-select>
                                      </mat-form-field>
                                </div>
                            </div>
                       
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">{{ labels['formcontrolsAssignedto']}}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" class="behav-tree-select">
                                        <mat-select placeholder="{{ labels['commonfilterChooseassigned'] }}"  formControlName="observedBy"  disableOptionCentering>
                                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']"
                                             [displayMember]="'name'" 
                                                [array]="userList"
                                                (filteredReturn)="onFilterChange($event)"
                                                ></mat-select-filter>
                                                <!-- (filteredReturn)="filteredUserList =$event" -->
                                            <mat-option *ngFor="let item of filteredUserList" [value]="item">
                                                {{item.name}} 
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <!-- <mat-form-field appearance="outline" style="width: 180px;">
                                    
                                        <mat-select value="Assigned" formControlName="status">
                                          <mat-option value="Assigned">{{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.ASSIGNES' | translate }}</mat-option>
                                        
                                        </mat-select>
                                      </mat-form-field> -->
                                </div>
                            </div>
                            <!-- <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">{{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.STATUS' | translate }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" style="width: 180px;">
                                    
                                        <mat-select value="Assigned" formControlName="status">
                                          <mat-option value="Assigned">{{ 'ACTION.CREATE_ACTION.FORM_CONTROLS.ASSIGNES' | translate }}</mat-option>
                                        
                                        </mat-select>
                                      </mat-form-field>
                                </div>
                            </div> -->
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">{{ labels['startDate'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" class="set-back-color-action" style="width: 180px;">
                                        <input autocomplete="off" matInput formControlName="startDate"  [matDatepicker]="releasedAtPicker22"
                                            (click)="releasedAtPicker22.open()">
                                        <mat-datepicker-toggle matSuffix [for]="releasedAtPicker22">
                                        </mat-datepicker-toggle>
                                        <mat-datepicker #releasedAtPicker22>
                                        </mat-datepicker>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div  fxLayout="row wrap" fxLayoutAlign="start center">
                                <div fxFlex="30">
                                    <span class="body-1">{{ labels['formcontrolsDuedate'] }}</span>
                                </div>
                                <div fxFlex="70">
                                    <mat-form-field appearance="outline" class="set-back-color-action" style="width: 180px;">
                                        <input autocomplete="off" matInput formControlName="endDate"  [matDatepicker]="releasedAtPicker24"
                                            (click)="releasedAtPicker24.open()">
                                        <mat-datepicker-toggle matSuffix [for]="releasedAtPicker24">
                                        </mat-datepicker-toggle>
                                        <mat-datepicker #releasedAtPicker24>
                                        </mat-datepicker>
                                    </mat-form-field>
                                </div>
                            </div>
                        </div>
        
                    </div>
    
                </form>
              
            
                  
                  
              </div>
               
              <div class="mt-40 btn-section">
                <common-lib-button [className]="'cst-btn cancel'" text="{{ labels['buttonCancel'] }}"
                     (buttonAction)="locationClick('')"></common-lib-button>
                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                     (buttonAction)="submitClick()"></common-lib-button>
              </div>
             
                  
             
        </div>

    
    </div>
  </div>

