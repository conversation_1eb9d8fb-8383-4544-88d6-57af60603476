{"name": "bbso", "version": "1.2.1", "private": true, "dependencies": {"@azure/msal-browser": "^2.32.2", "@azure/msal-react": "^1.5.2", "@celanese/celanese-sdk": "^1.5.1", "@cognite/sdk": "^7.15.0", "@mui/material": "^5.15.19", "@observablehq/runtime": "5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.2.2", "classnames": "^2.3.2", "date-fns": "^2.29.3", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "htl": "^0.3.1", "htm": "^3.1.1", "i18next": "^23.10.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.1.0", "react-router-dom": "^6.6.1", "react-scripts": "5.0.1", "reactjs-popup": "^2.0.5", "web-vitals": "^2.1.4", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/roboto": "^5.0.13", "@mui/icons-material": "^5.15.19", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/jest": "^29.5.12", "@types/node": "^20.14.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "dayjs": "^1.11.12", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "rollup": "^4.18.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "semantic-release": "^23.1.1", "styled-components": "^6.1.11", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "tslib": "^2.6.2", "typescript": "^5.2.2"}, "scripts": {"start": "set PORT=3900&& set HOST=0.0.0.0&& react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}