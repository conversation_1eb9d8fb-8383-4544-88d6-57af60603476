<div *ngIf="loaderFlag || !configDetail || loaderCount>0" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>

<div class="behaviour-checklist-section outerbox" *ngIf="configDetail">
    <form [formGroup]="observedForm">
        <commom-label *ngIf="process && (process.name == 'Observation' ) " labelText="{{ labels['behaviourchecklistCreateanobervation'] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading'"></commom-label>
           
           
        <commom-label *ngIf="process && (process.name == 'Hazard' || process.name == 'Hazards' ) " labelText="{{ labels['behaviourchecklistCreateanhazards'] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading'"></commom-label>    
        <commom-label *ngIf="process && (process.name == 'Audit' ) " labelText="{{ labels['behaviourchecklistCreateanaudit'] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading'"></commom-label>    
            

        <div fxLayout="row wrap" fxLayoutGap="20px">

            <div>
                <div class="marginTop" fxLayout="row">
                    
                </div>
                <mat-form-field appearance="outline">
                    <mat-label  >
                        <!-- {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CORE_PRINCIPLE' | translate }} -->
                        {{ labels['corePrincipleTitle'] }}
                    </mat-label>
                    <input type="text" disabled='true' class="input" [value]="labels['cards'+corePrinciple.name]"
                        placeholder="" aria-span="Search" matInput>
                </mat-form-field>
            </div>
            <div>
                <div class="marginTop" fxLayout="row">
                    

                </div>
                <mat-form-field appearance="outline">
                    <mat-label  >
                        <!-- {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.PROCESS' | translate }} -->
                        {{ labels['process'] }}
                    </mat-label>
                    <input *ngIf="process.name != 'Observation' && process.name != 'Audit'" type="text" disabled='true' class="input" [value]="labels['cards'+process.name] " placeholder=""
                        aria-span="Search" matInput>
                    <input *ngIf="process.name == 'Observation'" type="text" disabled='true' class="input" [value]="labels['observation'] " placeholder=""
                        aria-span="Search" matInput>
                    <input *ngIf="process.name == 'Audit'" type="text" disabled='true' class="input" [value]="labels['audit'] " placeholder=""
                        aria-span="Search" matInput>
                    <!-- <input type="text" disabled='true' class="input" [value]="'OBSERVATION.MAIN.CARDS.'+process.name | translate " placeholder=""
                        aria-span="Search" matInput> -->
                </mat-form-field>
            </div>
            <div>
                <div class="marginTop" fxLayout="row">
                    

                </div>
                <mat-form-field appearance="outline">
                    <mat-label  >
                        <!-- {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.OBSERVATION_TYPE' | translate }} -->
                        {{ labels['observationType'] }}
                    </mat-label>
                    <input type="text" disabled='true' class="input" [value]="subProcess.name  "
                        placeholder="" aria-span="Search" matInput>
                        <!-- check -->
                </mat-form-field>

            </div>
        </div>



        <div fxLayout="row wrap" fxLayout.md="column" fxLayout.sm="column" fxLayout.xs="column">
            <div class="marginTop" fxFlex="70" fxLayout="column" fxLayoutGap="20px">

                <div fxLayout="row wrap" fxLayoutGap="20px">
                    <div class="marginTop" *ngIf="configDetail && configDetail.unit.isEnabled">
                        <div>
                            <!-- <span class="start-color" *ngIf="configDetail && configDetail.unit.isMandatory">
                                *
                            </span> -->
                        </div>
                        <div>

                            <!-- <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-select placeholder="Choose Unit" formControlName="unit"
                                    disableOptionCentering>
                                    <mat-select-filter [placeholder]="'Search'" [displayMember]="'description'"
                                        [array]="reportingLocationList"
                                        (filteredReturn)="filteredReportingLocationList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field> -->
                            <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-label  > 
                                    {{ labels['unit'] }}
                                </mat-label>
                                <mat-select (click)="filterClick()"
                                            placeholder="{{ labels['commonfilterChooseunit'] }}"
                                            [formControl]="unitControl" disableOptionCentering [disabled]="isView" [ngClass]="{'disabled': isView}">
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch'] "
                                                       [displayMember]="'description'" [array]="matchingUnitList"
                                                       (filteredReturn)="filteredUnitList1=$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredUnitList1" [value]="item.externalId">
                                        {{item.name}} - {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>

                    </div>
                    <div class="marginTop" *ngIf="configDetail && configDetail.locationObserved.isEnabled">
                        <div>
                            
                            <!-- <span class="start-color" *ngIf="configDetail && configDetail.locationObserved.isMandatory">
                                *
                            </span> -->
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-label  >
                                    {{ labels['locationObserved'] }}
                                </mat-label>
                                <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}"
                                    formControlName="locationObserve" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch'] "
                                        [displayMember]="'description'" [array]="reportingLocationList"
                                        (filteredReturn)="filteredReportingLocationList =$event"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredReportingLocationList"
                                        [value]="item.externalId">
                                        {{item.description}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    

                    <div class="marginTop" *ngIf="configDetail && configDetail.observedOnBehalfOf.isEnabled">
                        <div>
                            
                            <!-- <span class="start-color" *ngIf="configDetail && configDetail.observedOnBehalfOf.isMandatory">
                                *
                            </span> -->
                        </div>
                        <div>
                            <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-label  >
                                    {{ labels['observedOnBehalfOf'] }}
                                </mat-label>
                                <mat-select placeholder="{{ labels['commonfilterChoosebehalf'] }}"
                                    formControlName="behalf" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch'] "
                                        [displayMember]="'name'" [array]="behalfList"
                                        (filteredReturn)="onBehalfChange($event)"></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredBehalfList" [value]="item.externalId">
                                        {{item.name}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                


                    <div *ngIf="configDetail && configDetail.date && configDetail.date.isEnabled">
                        <div style="padding-top: 10px;">
                            
                            <!-- <span class="start-color" *ngIf="configDetail && configDetail.date.isMandatory">
                                *
                            </span> -->
                        </div>
                        <div>

                            <mat-form-field appearance="outline" class="set-back-color-action">
                                <mat-label  >
                                    {{ labels['date'] }}
                                </mat-label>
                                <input autocomplete="off" matInput formControlName="datetime"
                                    [matDatepicker]="releasedAtPicker2" (click)="releasedAtPicker2.open()">
                                <mat-datepicker-toggle matSuffix [for]="releasedAtPicker2">
                                </mat-datepicker-toggle>
                                <mat-datepicker #releasedAtPicker2>
                                </mat-datepicker>
                            </mat-form-field>
                        </div>

                    </div>
                    <div *ngIf="configDetail && configDetail.startTime && configDetail.startTime.isEnabled">
                        <mat-form-field appearance="outline" style="width: 150px; padding-top: 10px; ">
                            <mat-label>{{ labels['startTime'] }}</mat-label>
                            <input matInput  [format]=+hourFormat formControlName="startTime"
                                [ngxTimepicker]="toggleIcon" [disableClick]="true" placeholder="Select start time">
                            <ngx-material-timepicker-toggle matSuffix [for]="toggleIcon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                    <path id="Icon_simple-clockify" data-name="Icon simple-clockify"
                                        d="M11.486,5.892l3.885-3.939L16.483,3.08,12.6,7.019,11.485,5.892ZM9.912,9.917a1.365,1.365,0,0,1-.974-.412,1.419,1.419,0,0,1-.4-.989,1.376,1.376,0,1,1,2.752,0,1.42,1.42,0,0,1-.4.989,1.367,1.367,0,0,1-.974.412ZM16.5,13.986l-1.112,1.128L11.5,11.174l1.113-1.127Zm-6.546.589a5.8,5.8,0,0,0,2.212-.439l1.9,1.925a8.317,8.317,0,0,1-4.11,1.084A8.514,8.514,0,0,1,1.5,8.572,8.514,8.514,0,0,1,9.954,0a8.318,8.318,0,0,1,4.074,1.062L12.162,2.955a5.806,5.806,0,0,0-2.209-.438,5.976,5.976,0,0,0-5.92,6.029A5.975,5.975,0,0,0,9.954,14.575Z"
                                        transform="translate(-1.5)" fill="#1A2254" />
                                </svg>
                            </ngx-material-timepicker-toggle>
                        </mat-form-field>
                        
                        <!-- Cancel and Confirm Buttons for Timepicker -->
                        <ngx-material-timepicker #toggleIcon [cancelBtnTmpl]="cancelBtn"
                            [confirmBtnTmpl]="confirmBtn"></ngx-material-timepicker>
                        <div class="behaviour-checklist-footer">
                            <ng-template #cancelBtn>
                                <button mat-raised-button class="cancel cst-btn" color="primary">
                                    <span>{{ labels['buttonCancel'] }}</span>
                                </button>
                            </ng-template>
                            <ng-template #confirmBtn>
                                <button mat-raised-button color="primary" class="ok-btn cst-btn">
                                    <span style="color: #fff !important">{{ labels['buttonOk'] }}</span>
                                </button>
                            </ng-template>
                        </div>
                    </div>

                    <div *ngIf="configDetail && configDetail.endTime && configDetail.endTime.isEnabled">
                        <mat-form-field appearance="outline" style="width: 150px; padding-top: 10px; ">
                            <mat-label>{{ labels['endTime'] }}</mat-label>
                            <input matInput [format]=+hourFormat formControlName="endTime"
                                [ngxTimepicker]="toggleIcon2" [disableClick]="true" placeholder="Select end time">
                            <ngx-material-timepicker-toggle matSuffix [for]="toggleIcon2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                    <path id="Icon_simple-clockify" data-name="Icon simple-clockify"
                                        d="M11.486,5.892l3.885-3.939L16.483,3.08,12.6,7.019,11.485,5.892ZM9.912,9.917a1.365,1.365,0,0,1-.974-.412,1.419,1.419,0,0,1-.4-.989,1.376,1.376,0,1,1,2.752,0,1.42,1.42,0,0,1-.4.989,1.367,1.367,0,0,1-.974.412ZM16.5,13.986l-1.112,1.128L11.5,11.174l1.113-1.127Zm-6.546.589a5.8,5.8,0,0,0,2.212-.439l1.9,1.925a8.317,8.317,0,0,1-4.11,1.084A8.514,8.514,0,0,1,1.5,8.572,8.514,8.514,0,0,1,9.954,0a8.318,8.318,0,0,1,4.074,1.062L12.162,2.955a5.806,5.806,0,0,0-2.209-.438,5.976,5.976,0,0,0-5.92,6.029A5.975,5.975,0,0,0,9.954,14.575Z"
                                        transform="translate(-1.5)" fill="#1A2254" />
                                </svg>
                            </ngx-material-timepicker-toggle>
                        </mat-form-field>
                        
                        <!-- Cancel and Confirm Buttons for Timepicker -->
                        <ngx-material-timepicker #toggleIcon2 [cancelBtnTmpl]="cancelBtn"
                            [confirmBtnTmpl]="confirmBtn"></ngx-material-timepicker>
                        <div class="behaviour-checklist-fotter">
                            <ng-template #cancelBtn>
                                <button mat-raised-button class="cancel cst-btn" color="primary">
                                    <span>{{ labels['buttonCancel'] }}</span>
                                </button>
                            </ng-template>
                            <ng-template #confirmBtn>
                                <button mat-raised-button color="primary" class="ok-btn cst-btn">
                                    <span style="color: #fff !important">{{ labels['buttonOk'] }}</span>
                                </button>
                            </ng-template>
                        </div>
                    </div>

                    <div fxLayout="row wrap" fxLayoutGap="20px">
                        <div  *ngIf="configDetail && configDetail.shift.isEnabled">
                            <div>
                              
                                <!-- <span class="start-color" *ngIf="configDetail && configDetail.shift.isMandatory">
                                    *
                                </span> -->
                            </div>
                            <div>
                              <mat-form-field appearance="outline" style="width: 150px; padding-top: 10px;" class="behav-tree-select">
                                <mat-label >
                                    {{  labels['shift'] }}
                              </mat-label>
                                <mat-select  placeholder="{{ labels['formcontrolsChooseshift'] }}" formControlName="shift">
                                  <mat-option *ngFor="let item of filteredshiftList" [value]="item.value">
                                    <!-- check with karguvel once -->
                                    <!-- {{  'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.'+ item.value.toUpperCase() | translate }} -->
                                    {{ labels[item.description] }}
                                  </mat-option>
                                </mat-select>
    
                              </mat-form-field>
                            </div>
                          </div>
                    
                       
                    </div>

                </div>

                <!-- <div fxLayout="row wrap" fxLayoutGap="10px"> -->
                <div *ngIf="configDetail && configDetail?.operationalLearning?.isEnabled">
                    <div>
                        <span class="body-2 regular">

                            {{ labels[configDetail.operationalLearning.translateKey]  }}
                              <!-- {{ labels }} -->
                            <!-- {{ configDetail && configDetail.operationalLearning.displayName | translate }} -->                                        
                        </span>
                        <span class="start-color"
                        *ngIf="configDetail && configDetail.operationalLearning.isMandatory">
                            *
                        </span>
                        <span class="info-button">
                            <mat-icon
                                            matTooltip="{{ labels['operationalLearningTooltip'] }}"
                                            [matTooltipPosition]="'right'"
                                            class="custom-tooltip-style"
                                            [matTooltipClass]="'custom-tooltip'"
                                        >
                                            info
                                        </mat-icon>
                            <!-- <div class="tooltip">
                              {{ labels['operationalLearningTooltip'] }}
                            </div> -->
                          </span>
                    </div>
                    <div>
                        <mat-radio-group formControlName="operationalLearning">
                            <mat-radio-button *ngFor="let item of filteredContractorList" [value]="item.value" style="margin-right: 16px;margin-top: 3px;">
                                {{  labels['formcontrols' + item.value] }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                

                
                    <div fxLayout="row wrap" fxLayoutGap="10px" *ngIf="observedForm.get('operationalLearning').value == 'Yes' && configDetail && configDetail?.operationalLearningDescription?.isEnabled">
                        <div style="width: 100%"
                            *ngIf="configDetail && configDetail.operationalLearningDescription.isEnabled">
                            <div>
                                
                                <!-- <span class="start-color"
                                    *ngIf="configDetail && configDetail.operationalLearningDescription.isMandatory">
                                    *
                                </span> -->
                            </div>
                            <div>
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;"
                                    class="behav-tree-select">
                                    <mat-label>
                                        {{ labels[ configDetail.operationalLearningDescription.translateKey] }}
                                            <!-- {{ configDetail && configDetail.operationalLearningDescription.displayName ? 'TABLE_COLS.'+configDetail.operationalLearningDescription.displayName : configDetail.operationalLearningDescription.displayName  |
                                            translate }} -->
                                        </mat-label>

                                    <textarea matInput id="ob_textarea" formControlName="operationalLearningDescription"
                                        cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                        cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>

                            </div>

                        </div>

                    </div>
                
                <!-- </div> -->
               



                <!-- <div fxLayout="row wrap" fxLayoutGap="10px">
                    <div *ngIf="configDetail && configDetail.contractor.isEnabled">
                      <div>
                        <span class="subheading-1 regular">
                          {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CONTRACTOR' | translate }}
                        </span>
                            <span class="start-color" *ngIf="configDetail && configDetail.contractor.isMandatory">
                                *
                            </span>
                      </div>
                      <div>
                        <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-select
                                    placeholder="{{'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CHOOSE_CONTRACTOR' | translate }}"
                                    formControlName="contractor" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch'] " "
                                        [displayMember]="'value'" [array]="contractorList"
                                        (filteredReturn)="filteredContractorList =$event"></mat-select-filter>
                            <mat-option *ngFor="let item of filteredContractorList" [value]="item.value">
                              {{ 'BUTTON.' + item.value.toUpperCase() | translate }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                    
                    <div *ngIf="observedForm.get('contractor').value == 'Yes' && configDetail && configDetail.contractorDetail.isEnabled">
                      <div>
                        <span class="subheading-1 regular">
                          {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CONTRACTOR_DETAIL' | translate }}
                        </span>
                        <span class="start-color" *ngIf="configDetail && configDetail.contractorDetail.isMandatory">
                          *
                        </span>
                      </div>
                      <div>
                        <mat-form-field appearance="outline" class="location-input" class="behav-tree-select">
                          <input type="text" formControlName="contractorDetails" class="input" value="" placeholder="" aria-span="Search" matInput>
                        </mat-form-field>
                      </div>
                    </div>

                    
                   
                  </div> -->

                <!-- <div fxLayout="row wrap" fxLayoutGap="20px">
                    <div fxFlex="20" *ngIf="configDetail && configDetail.shift.isEnabled">
                        <div>
                          <span class="subheading-1 regular">
                                {{  configDetail &&  configDetail.shift.displayName | translate }}
                          </span>
                            <span class="start-color" *ngIf="configDetail && configDetail.shift.isMandatory">
                                *
                            </span>
                        </div>
                        <div>
                          <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">
                            <mat-select  placeholder="{{'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.CHOOSE_SHIFT' | translate }}" formControlName="shift">
                              <mat-option *ngFor="let item of filteredshiftList" [value]="item.value">
                                {{  'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.'+ item.value.toUpperCase() | translate }}
                              </mat-option>
                            </mat-select>

                          </mat-form-field>
                        </div>
                      </div>
                
                   
                </div> -->
               
               


            

                <!-- <div fxLayout="row wrap">
                <div *ngIf="configDetail && configDetail.urgency.isEnabled" fxLayout="row wrap" class="marginTop" style="margin-bottom: -20px !important;">
                    <span class="subheading-1 regular">
                        {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.URGENCY' | translate }}

                    </span>
                </div>
                </div>
                <div *ngIf="configDetail && configDetail.urgency.isEnabled && urgencyList.length>0" class="marginTop" fxLayout="row wrap" fxLayoutGap="10px" [ngClass]="{'disabled': isView}">
                    <div fxLayout="row" *ngFor="let item of urgencyList">
                        <div fxLayout="column">
                            <div (click)="selectUrgency(item)"
                                [ngClass]="(selectedUrgency && selectedUrgency) == item ? 'category_box' : 'category_box_light'"
                                fxLayoutAlign="center center">
                                <div fxFlexAlign="center">
                                    <span>
                                        {{'OBSERVATION.BEHAVIOUR_CHECKLIST.FORM_CONTROLS.'+item | translate }}
                                    </span>
                                </div>
                            </div>
                            <span class="urgent-label-font" *ngIf="item == 'High'">
                                {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.CHIPS.STOP_WORK' | translate }}
                            </span>
                            <span class="urgent-label-font" *ngIf="item == 'Medium'">
                                {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.CHIPS.USE_CAUTION' | translate }}
                            </span>
                            <span class="urgent-label-font" *ngIf="item == 'Low'">
                                {{ 'OBSERVATION.BEHAVIOUR_CHECKLIST.CHIPS.CONTINUE_REPORT' | translate }}
                            </span>


                        </div>

                    </div>


                </div>

                <div *ngIf="selectedUrgency == 'High' && configDetail && configDetail.stopWorkAuthority && configDetail.stopWorkAuthority.isEnabled">
                    <div  *ngIf="configDetail && configDetail.eventType && configDetail.eventType.isEnabled" fxLayout="row wrap" fxLayoutGap="10px" class="marginTop">
                        <div style="width: 100%">
                            <div class="marginTop">
                                <span class="subheading-1 regular">
                                    {{configDetail && configDetail.eventType.displayName | translate }}
                                </span>
                                <span class="start-color" *ngIf="configDetail && configDetail.eventType && configDetail.eventType.isMandatory">
                                    *
                                </span>
                            </div>
                            <div class="marginTop">
                                <mat-radio-group [style]="'gap: 1rem !important;'" formControlName="eventType" fxFlex="100">
                                    <div fxFlex="20" fxLayoutAlign="start center" class="radio-btn" >
                                        <mat-radio-button value="Unsafe Act/Behavior" name="Unsafe Act/Behavior" [disabled]="isView">
                                            {{ 'Unsafe Act/Behavior' | translate }}
                                        </mat-radio-button>
                                    </div>
                                    <div fxFlex="20" fxLayoutAlign="start center" class="radio-btn" >
                                        <mat-radio-button value="Unsafe Condition" name="Unsafe Condition" [disabled]="isView">
                                            {{ 'Unsafe Condition' | translate }}
                                        </mat-radio-button>
                                    </div>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                
                    <div *ngIf="configDetail && configDetail.eventDesccription && configDetail.eventDesccription.isEnabled" fxLayout="row wrap" fxLayoutGap="10px" class="marginTop">
                        <div style="width: 100%" class="marginTop">
                            <div>
                                <span class="subheading-1 regular">
                                    {{configDetail && configDetail.eventDesccription.displayName | translate }}
                                </span>
                                <span class="start-color" *ngIf="configDetail && configDetail.eventDesccription && configDetail.eventDesccription.isMandatory">
                                    *
                                </span>
                            </div>
                            <div class="marginTop">
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;" class="behav-tree-select">
                
                                    <textarea matInput formControlName="eventDesccription" cdkTextareaAutosize
                                        #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>
                
                            </div>
                
                        </div>
                
                    </div>
                
                    <div *ngIf="configDetail && configDetail.correctiveActionFlag && configDetail.correctiveActionFlag.isEnabled" fxLayout="row wrap" fxLayoutGap="10px" class="marginTop">
                        <div style="width: 100%" class="marginTop">
                            <div>
                                <span class="subheading-1 regular">
                                    {{configDetail && configDetail.correctiveActionFlag.displayName | translate }}
                                </span>
                                <span class="start-color" *ngIf="configDetail && configDetail.correctiveActionFlag.isMandatory">
                                    *
                                </span>
                            </div>
                            <div class="marginTop">
                                <mat-radio-group formControlName="correctiveActionFlag" fxFlex="100">
                                    <div fxFlex="10" fxLayoutAlign="start center">
                                        <mat-radio-button value="Yes" name="Yes" [disabled]="isView">
                                            {{ 'BUTTON.YES' | translate }}
                                        </mat-radio-button>
                                    </div>
                                    <div fxFlex="10" fxLayoutAlign="start center">
                                        <mat-radio-button value="No" name="No" [disabled]="isView">
                                            {{ 'BUTTON.NO' | translate }}
                                        </mat-radio-button>
                                    </div>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                
                    <div *ngIf="configDetail && configDetail.descripeCorrectiveActionTaken && configDetail.descripeCorrectiveActionTaken.isEnabled && observedForm.get('correctiveActionFlag').value == 'Yes' " fxLayout="row wrap" fxLayoutGap="10px" class="marginTop">
                        <div style="width: 100%" class="marginTop">
                            <div>
                                <span class="subheading-1 regular">
                                    {{configDetail && configDetail.descripeCorrectiveActionTaken.displayName | translate }}
                                </span>
                                <span class="start-color"
                                    *ngIf="configDetail && configDetail.descripeCorrectiveActionTaken && configDetail.descripeCorrectiveActionTaken.isMandatory">
                                    *
                                </span>
                            </div>
                            <div class="marginTop">
                                <mat-form-field appearance="outline" style="width: 100%;height: 90px;" class="behav-tree-select">
                
                                    <textarea matInput formControlName="descripeCorrectiveActionTaken" cdkTextareaAutosize
                                        #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div *ngIf="categoryList.length>0">
                <div class="marginTop" fxLayout="row" fxLayoutGap="3px">
                    <span class="subheading-1 regular" (click)="switchCategory(null)">
                      {{ labels['category'] }}
                    </span>
                    <span class="start-color" *ngIf="false">*</span>
                    <span>
                      <p *ngIf="!isView">({{ labels['formcontrolsPleasechoosecat'] }})</p>
                    </span>
                  </div>
                  
                  <div class="marginTop category-container test">
                    <div *ngFor="let item of categoryList" class="category-item">

                      <mat-checkbox (change)="handleCategoryCheckboxChange($event.checked, item)" [checked]="isSelected(item)" [disabled]="isView"></mat-checkbox>

                      <span class="clickable-text" (click)="isSelected(item) ? switchCategory(item) : null">
                        {{ item.name }}
                      </span>
                    </div>
                  </div></div>
                  
                  <!-- Subcategory Selection -->
                  <div class="marginTop" fxLayout="row" fxLayoutGap="3px" *ngIf="lastSelectedCategory && isSelected(lastSelectedCategory) && subCategoryList.length > 0">
                    <span class="subheading-1 regular">
                      {{ labels['subCategory'] }}
                    </span>
                    <span class="start-color" *ngIf="false">*</span>
                    <span>
                      <p *ngIf="!isView">({{ labels['formcontrolsPleassechoosesubcat'] }})</p>
                    </span>

                    <mat-checkbox class="select-all-checkbox" (change)="toggleSelectAllSubCategories($event.checked)" [checked]="isAllSubCategoriesSelected()" style="margin-left: 30px" [disabled]="isView">

                        {{ labels['formcontrolsSelectall'] }}
                      </mat-checkbox>
                  </div>
                  
                  <div class="marginTop category-container test" *ngIf="lastSelectedCategory && isSelected(lastSelectedCategory) && subCategoryList.length > 0">
                    
                    <div *ngFor="let item of subCategoryList" class="category-item">

                      <mat-checkbox (change)="selectSubCategory(item)" [checked]="item.selected" [disabled]="isView">

                        {{ item.name }}
                      </mat-checkbox>
                    </div>
                  </div>
                  


            </div>
          
          

        </div>

        <div *ngIf="configDetail.checklistEnableValue.isEnabled" fxLayout="row" class="marginTop" style="width: 99%;margin-top: 30px;" fxLayoutAlign="space-between end">
            <div>
                <span class="subheading-1 regular">
                    {{ labels['formcontrolsBehaviourchecklist'] }}
                </span>

            </div>

        </div>
        <div>
            <div *ngIf="configDetail.checklistEnableValue.isEnabled" class="listHeadBox-behaviour">
                <div fxLayout="row" fxLayoutAlign="start center" class="listHeadBox marginTop">
                    <div fxFlex="35" class="paddingLeft-10">
                        <span class="expandHead">
                            {{ labels['formcontrolsBehaviourchecklist'] }}
                        </span>
                    </div>
                    <div *ngIf="showSafe" fxFlex="8" class="paddingLeft-10 leftBorder" fxLayoutAlign="center center">
                        <span class="expandHead">
                            {{configDetail && configDetail.safe.displayName }}
                        </span>
                    </div>
                    <div *ngIf="showNotSafe" fxFlex="8" class="paddingLeft-10 leftBorder" fxLayoutAlign="center center">
                        <span class="expandHead">
                            {{configDetail && configDetail.notsafe.displayName}}
                            
                        </span>
                    </div>
                    <div *ngIf="showNotObserved" fxFlex="12" class="paddingLeft-10 leftBorder" fxLayoutAlign="center center">
                        <span class="expandHead">
                            {{configDetail && configDetail.notobserved.displayName }}
                        </span>
                    </div>
                    <div fxFlex="12" class="paddingLeft-10 leftBorder" fxLayoutAlign="center center">
                        <span class="expandHead">
                            {{ labels['formcontrolsActionlvl'] }}
                        </span>
                    </div>
                    <div fxFlex="25" class="paddingLeft-10 leftBorder" fxLayoutAlign="center center">
                        <span class="expandHead">
                            {{ labels['notes'] }}
                        </span>
                    </div>
                </div>
                <div>
                    <mat-tree #treeExpand [dataSource]="dataSource" [treeControl]="treeControl">
                        <!-- This is the tree node template for leaf nodes -->
                        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding matTreeNodePaddingIndent="0">
                            <!-- use a disabled button to provide padding for tree leaf -->
                            <div [formGroup]="node.formGroup" fxLayout="row" style="width: 100%;"
                                fxLayoutAlign="start center" [ngClass]="{'behaviour-list-tree': node.bold == false }">

                                <div fxFlex="35" style="width: 445px;" fxLayout="row" fxLayoutAlign="start center">
                                    <button mat-icon-button disabled></button>
                                    <span fxFlex="20" [ngClass]="node.bold == true ? 'semi-bold' : 'caption'">
                                        {{node.name}}
                                        <svg *ngIf="node.guidelineDocument && node.guidelineDocument.length>0"
                                            (click)="imageView(node)" xmlns="http://www.w3.org/2000/svg" version="1.1"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                                            x="0" y="0" viewBox="0 0 8.467 8.467" style="margin-left: 24px;top: 2px;position: relative;"
                                            xml:space="preserve" class="">
                                            <g>
                                                <path
                                                    d="M2.357.53a.844.844 0 0 0-.858.832v5.742c0 .459.38.833.858.833h3.751c.478 0 .86-.374.86-.833V2.822l-2.06-.337a.75.75 0 0 1-.615-.919L4.56.53zm2.462.13-.25.978a.462.462 0 0 0 .385.568l1.733.281zm-.58 2.788a.4.4 0 0 1 .343.193c.119.16.128.367.084.577a2.59 2.59 0 0 1-.236.601c.18.3.384.606.537.838.129-.019.256-.031.376-.037.235-.01.446.006.616.097a.478.478 0 0 1 .227.595.44.44 0 0 1-.269.248.57.57 0 0 1-.362.008c-.26-.08-.478-.334-.688-.594l-1.396.325c-.173.358-.328.668-.567.814a.45.45 0 0 1-.232.065.461.461 0 0 1-.288-.107c-.183-.17-.171-.463 0-.656.204-.23.545-.272.9-.356.274-.36.588-.813.816-1.228-.013-.023-.028-.039-.04-.062a2.457 2.457 0 0 1-.25-.61c-.043-.194-.038-.395.092-.54a.471.471 0 0 1 .338-.171zm-.003.286H4.23a.14.14 0 0 0-.116.074c-.038.056-.061.139-.028.288.025.111.097.257.166.4.049-.116.116-.243.135-.337.036-.17.018-.285-.034-.351-.03-.04-.066-.075-.118-.074zm.032 1.36c-.156.265-.353.557-.542.843l.935-.227c-.117-.18-.26-.402-.393-.615zm1.145.808-.057.002a.716.716 0 0 0-.131.02c.15.154.313.342.414.373.075.027.245.003.28-.088.037-.099.006-.186-.097-.242a.936.936 0 0 0-.409-.065zm-2.383.55c-.227.038-.347.072-.431.152-.082.093-.073.207-.02.257a.139.139 0 0 0 .175.01c.065-.04.177-.257.276-.418z"
                                                    fill="#083d5b" data-original="#000000" class="" style="cursor: pointer;"></path>
                                            </g>
                                        </svg>
                                    </span>
                                    <span *ngIf="node.question" class="caption">
                                        {{node.question}}
                                    </span>
                                </div>
                                <!-- <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button value="1" name="{{node.id}}" [checked]="true"></mat-radio-button>
                                </div> -->
                                <div [fxFlex]="outercheckboxcss" *ngIf="!node.bold" fxLayoutAlign="center center">
                                    <mat-radio-group 
                                      formControlName="isSafe" 
                                      [fxFlex]="checkboxcss" 
                                      (change)="onCheckboxChange(node.formGroup)">
                                        
                                      <div *ngIf="showSafe" [fxFlex]="safeFxFlex" fxLayoutAlign="center center" (click)="spliceFeedback(node)">
                                        <mat-radio-button value="safe" [name]="node.id" [disabled]="isView"></mat-radio-button>
                                      </div>
                                      
                                      <div *ngIf="showNotSafe" [fxFlex]="notSafeFxFlex" fxLayoutAlign="center center" (click)="radioButton(node)">
                                        <mat-radio-button value="unsafe" [name]="node.id" [disabled]="isView"></mat-radio-button>
                                      </div>
                                      
                                      <div *ngIf="showNotObserved" [fxFlex]="notObservedFxFlex" fxLayoutAlign="center center" (click)="spliceFeedback(node)">
                                        <mat-radio-button value="notObserved" [name]="node.id" [disabled]="isView"></mat-radio-button>
                                      </div>
                                      
                                    </mat-radio-group>
                                  </div>
                                  
                                  <div fxLayout="row wrap" fxLayoutGap="20px">
                                    <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">
                                      <mat-select
                                        placeholder="{{ labels['formcontrolsChooseaction'] }}"
                                        formControlName="actionLevel"
                                        (selectionChange)="onSelectionChange($event, node)"
                                        [matTooltip]="getSelectedOptionText(node)"
                                        [matTooltipPosition]="'above'"
                                        [disabled]="!isNodeSelected(node)">
                                        <mat-option *ngFor="let item of filteredActionList" [value]="item.value">
                                          {{ labels[item.description] }}
                                        </mat-option>
                                      </mat-select>
                                    </mat-form-field>
                                  </div>
                                  
                                  
                                  
                                  

                                <div [fxFlex]="notescss" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">
                                        <textarea formControlName="note" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>

                        </mat-tree-node>
                        <!-- This is the tree node template for expandable nodes -->
                        <mat-tree-node *matTreeNodeDef="let node;when: hasChild" matTreeNodePadding
                            matTreeNodePaddingIndent="0">
                            <div [formGroup]="node.formGroup" fxLayout="row" style="width: 100%;"
                                fxLayoutAlign="start center" [ngClass]="{'behaviour-list-tree': node.bold == false }">

                                <div fxFlex="35" style="width: 445px;" fxLayout="row" fxLayoutAlign="start center">
                                    <button [ngClass]="!node.isCategory ? 'ml-10' : ''" mat-icon-button
                                        matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                                        <!-- <mat-icon class="mat-icon-rtl-mirror">
                        {{treeControl.isExpanded(node) ? 'expand_circle_down' : 'chevron_right'}}
                      </mat-icon> -->
                                        <div *ngIf="treeControl.isExpanded(node)">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                                viewBox="0 0 24 24" style="enable-background:new 0 0 512 512"
                                                xml:space="preserve" class="">
                                                <g>
                                                    <path
                                                        d="M12 1a11 11 0 1 0 11 11A11.013 11.013 0 0 0 12 1zm5.707 9.707-5 5a1 1 0 0 1-1.414 0l-5-5a1 1 0 0 1 1.414-1.414L12 13.586l4.293-4.293a1 1 0 0 1 1.414 1.414z"
                                                        data-name="Layer 2" fill="#1A2254" data-original="#000000"
                                                        class="">
                                                    </path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div *ngIf="!treeControl.isExpanded(node)">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                xmlns:svgjs="http://svgjs.com/svgjs" width="19" height="19" x="0" y="0"
                                                viewBox="0 0 512 512" style="enable-background:new 0 0 512 512"
                                                xml:space="preserve" class="">
                                                <g>
                                                    <path
                                                        d="M256 0C114.837 0 0 114.837 0 256s114.837 256 256 256 256-114.837 256-256S397.163 0 256 0zm79.083 271.083L228.416 377.749A21.275 21.275 0 0 1 213.333 384a21.277 21.277 0 0 1-15.083-6.251c-8.341-8.341-8.341-21.824 0-30.165L289.835 256l-91.584-91.584c-8.341-8.341-8.341-21.824 0-30.165s21.824-8.341 30.165 0l106.667 106.667c8.341 8.341 8.341 21.823 0 30.165z"
                                                        fill="#1A2254" data-original="#000000" class=""></path>
                                                </g>
                                            </svg>
                                        </div>

                                    </button>

                                    <span [ngClass]="node.bold == true ? 'semi-bold' : 'caption'">
                                        {{node.name}}

                                        <svg *ngIf="node.guidelineDocument && node.guidelineDocument.length>0"
                                            (click)="imageView(node)" xmlns="http://www.w3.org/2000/svg" version="1.1"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                                            x="0" y="0" viewBox="0 0 8.467 8.467" style="margin-left: 24px;top: 2px;position: relative;"
                                            xml:space="preserve" class="">
                                            <g>
                                                <path
                                                    d="M2.357.53a.844.844 0 0 0-.858.832v5.742c0 .459.38.833.858.833h3.751c.478 0 .86-.374.86-.833V2.822l-2.06-.337a.75.75 0 0 1-.615-.919L4.56.53zm2.462.13-.25.978a.462.462 0 0 0 .385.568l1.733.281zm-.58 2.788a.4.4 0 0 1 .343.193c.119.16.128.367.084.577a2.59 2.59 0 0 1-.236.601c.18.3.384.606.537.838.129-.019.256-.031.376-.037.235-.01.446.006.616.097a.478.478 0 0 1 .227.595.44.44 0 0 1-.269.248.57.57 0 0 1-.362.008c-.26-.08-.478-.334-.688-.594l-1.396.325c-.173.358-.328.668-.567.814a.45.45 0 0 1-.232.065.461.461 0 0 1-.288-.107c-.183-.17-.171-.463 0-.656.204-.23.545-.272.9-.356.274-.36.588-.813.816-1.228-.013-.023-.028-.039-.04-.062a2.457 2.457 0 0 1-.25-.61c-.043-.194-.038-.395.092-.54a.471.471 0 0 1 .338-.171zm-.003.286H4.23a.14.14 0 0 0-.116.074c-.038.056-.061.139-.028.288.025.111.097.257.166.4.049-.116.116-.243.135-.337.036-.17.018-.285-.034-.351-.03-.04-.066-.075-.118-.074zm.032 1.36c-.156.265-.353.557-.542.843l.935-.227c-.117-.18-.26-.402-.393-.615zm1.145.808-.057.002a.716.716 0 0 0-.131.02c.15.154.313.342.414.373.075.027.245.003.28-.088.037-.099.006-.186-.097-.242a.936.936 0 0 0-.409-.065zm-2.383.55c-.227.038-.347.072-.431.152-.082.093-.073.207-.02.257a.139.139 0 0 0 .175.01c.065-.04.177-.257.276-.418z"
                                                    fill="#083d5b" data-original="#000000" class="" style="cursor: pointer;"></path>
                                            </g>
                                        </svg>
                                    </span>
                                </div>
                                <div [fxFlex]="outercheckboxcsschild" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-group formControlName="isSafe" [fxFlex]="checkboxcss"  (change)="onCheckboxChange(node.formGroup)">
                                        <div *ngIf="showSafe" [fxFlex]="safeFxFlex" fxLayoutAlign="center center" (click)="spliceFeedback(node)">
                                            <mat-radio-button value="safe" name="{{node.id}}" [disabled]="isView"></mat-radio-button>

                                        </div>
                                        <div *ngIf="showNotSafe" [fxFlex]="notSafeFxFlex" fxLayoutAlign="center center" (click)="radioButton(node)">

                                            <mat-radio-button value="unsafe" name="{{node.id}}" [disabled]="isView"></mat-radio-button>

                                        </div>
                                        <div *ngIf="showNotObserved" [fxFlex]="notObservedFxFlex" fxLayoutAlign="center center" (click)="spliceFeedback(node)">

                                            <mat-radio-button value="notObserved" name="{{node.id}}" [disabled]="isView"></mat-radio-button>

                                        </div>
                                    </mat-radio-group>
                                </div>
                                <!-- <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button formControlName="safe" value="1" name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button formControlName="unsafe" value="1" (click)="radioButton(node)"
                                        name="{{node.id}}"></mat-radio-button>
                                </div>
                                <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-radio-button formControlName="notObserved" value="1" name="{{node.id}}" [checked]="true"></mat-radio-button>
                                </div> -->
                                <div fxFlex="10" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <mat-select value="No" formControlName="isInjuryPotential">
                                            <mat-option value="Yes">{{ labels['formcontrolsYes']}}</mat-option>
                                            <mat-option value="No">{{ labels['formcontrolsNo']}}</mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>
                                <div [fxFlex]="notescsschild" *ngIf="node.bold == false" fxLayoutAlign="center center">
                                    <mat-form-field appearance="outline" style="width: 100%;" class="behav-tree-select">

                                        <textarea formControlName="note" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>


                        </mat-tree-node>
                    </mat-tree>


                </div>
            </div>


            <div *ngIf="configDetail && configDetail.feedback.isEnabled && tabsListArr.length > 0" fxLayout="row"
                fxLayoutAlign="start center" class="listHeadBox marginTop">
                <div fxFlex="35" class="paddingLeft-10">
                    <span class="subheading-1">
                        {{ configDetail && labels[configDetail.feedback.translateKey] }}
                    </span>
                </div>

            </div>
            <div *ngIf="configDetail && configDetail.feedback.isEnabled && tabsListArr.length > 0">
                <mat-tab-group mat-stretch-tabs="false" class="bahaviour-tabs" mat-align-tabs="start">
                    <mat-tab *ngFor="let tabs of tabsListArr" label="{{tabs.name}}">
                        <div [formGroup]="tabs.formGroup" style="height: 650px;">
                            <div class="marginTop" fxLayout="row" fxLayoutAlign="end end">
                            </div>
                            <div fxLayout="row" *ngIf="configDetail && configDetail.activity.isEnabled">
                                <!-- <div fxFlex="20"> -->
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.activity.isMandatory">
                                        *
                                    </span> -->
                                <!-- </div> -->
                                <div fxFlex="100">
                                    <mat-label class="body-2 margin-left">
                                        {{ configDetail && labels[configDetail.activity.translateKey] }}
                                    </mat-label>
                                    <mat-form-field appearance="outline" style="width: 100%;height: 72px;">
                                        <textarea formControlName="activity" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>
                            <div class="marginTop-20" fxLayout="row" *ngIf="configDetail && configDetail.riskyAction.isEnabled">
                                <!-- <div fxFlex="20"> -->
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.riskyAction.isMandatory">
                                        *
                                    </span> -->
                                <!-- </div> -->
                                <div fxFlex="100">
                                    <mat-label class="body-2 margin-left">
                                        {{ configDetail && labels[configDetail.riskyAction.translateKey] }}
                                    </mat-label>
                                    <mat-form-field appearance="outline" style="width: 100%;height: 72px;">
                                        <textarea formControlName="riskyAction" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>
                            <div class="marginTop-20" fxLayout="row" *ngIf="configDetail && configDetail.risk.isEnabled">
                                <!-- <div fxFlex="20"> -->
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.risk.isMandatory">
                                        *
                                    </span> -->
                                <!-- </div> -->
                                <div fxFlex="100">
                                    <mat-label class="body-2 margin-left">
                                        {{ configDetail && labels[configDetail.risk.translateKey] }}
                                    </mat-label>
                                    <mat-form-field appearance="outline" style="width: 100%;height: 72px;">
                                        <textarea formControlName="risk" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>
                            <div class="marginTop-20" fxLayout="row" *ngIf="configDetail && configDetail.riskAgreement.isEnabled">
                                <div fxFlex="20">
                                    <span class="body-2 margin-left">
                                        {{ configDetail && labels[configDetail.riskAgreement.translateKey ] }}
                                       
                                    </span>
                                    <span class="start-color" *ngIf="configDetail && configDetail.riskAgreement.isMandatory">
                                        *
                                    </span>
                                </div>
                                <div fxFlex="80">
                                    <mat-radio-group formControlName="riskAgreement" aria-label="Select an option"
                                        fxLayoutGap="20px">
                                        <mat-radio-button name="risk-agree" [checked]="true" value="Yes">{{ labels['formcontrolsYes']}}</mat-radio-button>
                                        <mat-radio-button name="risk-agree" value="No">{{ labels['formcontrolsNo']}}</mat-radio-button>
                                    </mat-radio-group>
                                </div>

                            </div>
                            <div class="marginTop-20" fxLayout="row" *ngIf="configDetail && configDetail.reasonForAction.isEnabled">
                                <!-- <div fxFlex="20"> -->
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.reasonForAction.isMandatory">
                                        *
                                    </span> -->
                                <!-- </div> -->
                                <div fxFlex="100">
                                    <mat-form-field appearance="outline" style="width: 100%;height: 72px;">
                                        <mat-label class="body-2 margin-left">
                                            {{ configDetail && labels[configDetail.reasonForAction.translateKey] }}
                                        </mat-label>
                                        <textarea formControlName="reasonForAction" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>
                            <div class="marginTop-20 marginTop" fxLayout="row" *ngIf="configDetail && configDetail.safeBehaviour.isEnabled">
                                <div fxFlex="20">
                                    <span class="body-2 margin-left">
                                        {{ configDetail && labels[configDetail.safeBehaviour.displayName] }}
                                    </span>
                                    <span class="start-color" *ngIf="configDetail && configDetail.safeBehaviour.isMandatory">
                                        *
                                    </span>
                                </div>
                                <div fxFlex="80" fxLayout="row" fxLayoutGap="20px">
                                    <mat-radio-group formControlName="safeBehaviour" aria-label="Select an option"
                                        fxLayoutGap="20px">
                                        <mat-radio-button name="safeBeha" value="Possible">
                                            {{ labels['formcontrolsPossible'] }}
                                        </mat-radio-button>
                                        <mat-radio-button name="safeBeha" value="Difficult">
                                            {{ labels['formcontrolsDifficult'] }}
                                        </mat-radio-button>
                                        <mat-radio-button name="safeBeha" value="Imposible">
                                            {{ labels['formcontrolsImposible'] }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>

                            </div>

                            <div class="marginTop-20" fxLayout="row" *ngIf="configDetail && configDetail.suggestedSolution.isEnabled">
                                <!-- <div fxFlex="20"> -->
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.suggestedSolution.isMandatory">
                                        *
                                    </span> -->
                                <!-- </div> -->
                                <div fxFlex="100">
                                    <mat-form-field appearance="outline" style="width: 100%;height: 72px;">
                                        <mat-label class="body-2 margin-left">
                                            {{ configDetail && labels[configDetail.suggestedSolution.translateKey] }}
                                          
                                        </mat-label>

                                        <textarea formControlName="suggestedSolution" matInput cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="4"
                                            cdkAutosizeMaxRows="5"></textarea>
                                    </mat-form-field>
                                </div>

                            </div>
                        </div>




                    </mat-tab>
                </mat-tab-group>
            </div>



            <div class="marginTop" *ngIf="configDetail && configDetail.signature.isEnabled">
                

            </div>
            <div class="marginTop" *ngIf="configDetail && configDetail.signature.isEnabled">
                <mat-form-field appearance="outline" style="width: 273px;height: 90px;">
                    <mat-label  >
                        {{ configDetail && labels[configDetail.signature.translateKey] }}
                    </mat-label>
                    <textarea formControlName="signature" matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                        cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                </mat-form-field>

            </div>
            <div class="behaviour-checklist-fotter">
                <!-- <common-lib-button *ngIf="!isView" [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'"
                    (buttonAction)="cancelClick()"></common-lib-button> -->
                <common-lib-button [className]="'cancel cst-btn'" text="{{labels['buttonBack']}} "
                    (buttonAction)="backClick()"></common-lib-button>
                <common-lib-button *ngIf="!isView" [className]="'cst-btn'" text="{{ labels['buttonSubmit'] }}"
                    (buttonAction)="submitClick()"></common-lib-button>

            </div>

            <!-- date: 18-6-2024  --  This code addeed for success popup after submit button is passed - by venkatesh  -->
            <div class="container">
                <!-- Success Popup -->
                <!-- <div *ngIf="showSuccessPopup" style="text-align: left;" class="success-popup">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 18px; color: white" >👍 {{ labels['toasterSuccess'] }}</span>
                        <button style="margin-left: 10px; color: white" (click)="closeSuccessPopup()" style=" font-size: 16px; background: none; border: none; color: white; cursor: pointer;"> -->
                            <!-- {{ labels['buttonClose'] }} -->
                            <!-- <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m336-280 144-144 144 144 56-56-144-144 144-144-56-56-144 144-144-144-56 56 144 144-144 144 56 56ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z"/></svg>
                        </button>
                    </div>
                    <p style=" font-size: 16px; margin-left: 10px; margin-top: 10px; color: white">{{ labels['toasterRespsuccess'] }}</p>
                </div> -->
                
                

                <!-- failure popup if we need failure popup in screen you can use it by making it true -->

                <!-- <div *ngIf="showFailurePopup" style="text-align: left;" class="failure-popup">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 18px; color: white">👎 {{ labels['toasterFailure'] }} !!!</span>
                        <button style="margin-left: 10px; color: white" (click)="closeSuccessPopup2()" style="font-size: 16px; background: none; border: none; color: white; cursor: pointer;"> -->
                            <!-- {{ labels['buttonClose'] }} -->
                            <!-- <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m336-280 144-144 144 144 56-56-144-144 144-144-56-56-144 144-144-144-56 56 144 144-144 144 56 56ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z"/></svg>
                        </button>
                    </div>
                    <p style=" font-size: 16px; margin-left: 10px; margin-top: 10px; color: white">{{ labels['toasterRespfailed'] }}</p>
                </div> -->
                <div *ngIf="showSuccessPopup" style="text-align: left;" class="success-popup">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 18px; color: white" >👍 {{ labels['toasterSuccess'] }}</span>
                        <button style="margin-left: 10px; color: white" (click)="closeSuccessPopup()" style=" font-size: 16px; background: none; border: none; color: white; cursor: pointer;">
                            <!-- {{ labels['buttonClose'] }} -->
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m336-280 144-144 144 144 56-56-144-144 144-144-56-56-144 144-144-144-56 56 144 144-144 144 56 56ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z"/></svg>
                        </button>                                
                    </div>
                    <p style=" font-size: 16px; margin-left: 10px; margin-top: 10px; color: white">{{ labels['toasterRespsuccess'] }}</p>
                </div>
                <!-- failure popup if we need failure popup in screen you can use it by making it true -->

                <div *ngIf="showFailurePopup" style="text-align: left;" class="failure-popup">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 18px; color: white">👎 {{ labels['toasterFailure'] }} !!!</span>
                        <button style="margin-left: 10px; color: white" (click)="closeSuccessPopup2()" style="font-size: 16px; background: none; border: none; color: white; cursor: pointer;">
                            <!-- {{ labels['buttonClose'] }} -->
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m336-280 144-144 144 144 56-56-144-144 144-144-56-56-144 144-144-144-56 56 144 144-144 144 56 56ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z"/></svg>
                        </button>                                
                    </div>
                    <p style=" font-size: 16px; margin-left: 10px; margin-top: 10px; color: white">{{ labels['toasterRespfailed']  }}</p>
                </div>
                

            </div>


        </div>
    </form>
</div>
