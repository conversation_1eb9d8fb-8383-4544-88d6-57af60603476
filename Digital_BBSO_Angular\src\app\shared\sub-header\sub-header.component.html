<div [fxLayout]="docReact.width > 600 ? 'row' : 'column'">
    <div [fxFlex]="docReact.width > 600 ? 60 : 100" class="d-flex align-item-center" [ngClass]="docReact.width <= 600 ? 'justify-center': ''">
      <span class="display-1 cst-sub-header-title">{{headerText | translate}}</span>
    </div>
    <div [fxFlex]="docReact.width > 600 ? 40 : 100" fxLayoutAlign="end start" [ngClass]="docReact.width <= 600 ? 'justify-center mt-10': ''">
      <mat-form-field appearance="outline" class="search-input">
        <img matPrefix src="../../assets/icons/search.svg" width="13.94" height="13.94">
        <input class="input" [formControl]="searchControl" matInput placeholder="Search...">
      </mat-form-field>
    </div>
  </div>