// export default App;
import React, { useRef, useEffect, useState, useMemo } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import { translate } from '@celanese/celanese-sdk'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let allListData = []
let colSummary = {}
let displayedColumns = [
  'externalId',
  'title',
  // 'corePrinciple',
  // 'process',
  // 'observationType',
  'observationStartDate',
  'observationEndDate',
  'occurrence',
  'year',
  'quarter',
  // 'priority',
  'observer',
  'status',
  'createdTime',
  'createdBy',
  // "lastUpdatedTime",
  // "modifiedBy",
  'actions',
]

let headersKeyVal = {
  externalId: 'ID',
  title: 'Title',
  corePrinciple:'Core Principle',
  process:'Process',
  observationType:'Observation Type',
  occurrence:'Occurrence',
  observationStartDate:'Start Date',
  observationEndDate:'Due Date',
  year: 'Year',
  quarter: 'Quarter',
  priority: 'Priority',
  observer:'Observer',
  status: 'Status',
  createdTime: 'Created On',
  createdBy: 'Created By',
  lastUpdatedTime: 'Updated On',
  modifiedBy: 'Updated By',
  actions: 'Actions',
}

var paginationCursor = []
let site
let unit
let search
let startDate
let endDate
let initFlag
let token
let userAccessMenu
let scheduleId
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
function ScheduleDetails() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')

  const [selectedLanguage, setSelectedLanguage] = useState('en')

  const [t, i18n] = useTranslation('global')

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if(i18n == undefined){
      window.parent.postMessage(
        {
          type: 'AuditPlan',
          action: 'LangError',
          data: true,
        },
        '*'
      )
    }
    if(i18n){
    i18n.changeLanguage(newValue)
    console.log('Selected Language: ', selectedLanguage)
    console.log('i18n.language: ', i18n)
    }
  }

  function rowDroDownChange(e) {
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value
    filterData()
  }

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })
    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        token = e.data.data
      }
      if (e.data.type && e.data.type == 'AuditPlan') {
        if (e.data.action == 'Column') {
          window.parent.postMessage(
            {
              type: 'AuditPlan',
              action: 'Loading',
              data: true,
            },
            '*'
          )
          displayedColumns = e.data.data
          console.log('e.data.data',e.data.data);
          colFun()
        } else if (e.data.action == 'Filter') {
          site = e.data.sites
          scheduleId = e.data.scheduleId
          console.log('Filter: ', e.data)
          setting();
          // language
          handleLanguageChange(e.data.LanguageCode)
          headersKeyVal = {
            externalId: translate('stLabel.id'),
            title: translate('stLabel.title'),
            corePrinciple:translate('stLabel.corePrinciple'),
            process:translate('stLabel.tablecolsProcess'),
            observationType:translate('stLabel.observationType'),
            occurrence:translate('stLabel.occurrence'),
            observationStartDate:translate('stLabel.startDate'),
            observationEndDate:translate('stLabel.tablecolsDuedate'),
            year: translate('stLabel.year'),
            quarter: translate('stLabel.quarter'),
            priority: translate('stLabel.priority'),
            observer:translate('stLabel.observer'),
            status: translate('stLabel.status'),
            createdTime: translate('stLabel.tablecolsCreatedtime'),
            createdBy: translate('stLabel.createdBy'),
            lastUpdatedTime: translate('stLabel.updatedOn'),
            modifiedBy: translate('stLabel.updatedBy'),
            actions: translate('stLabel.actions'),
          }
          console.log('OBSList headersKeyVal', headersKeyVal)
          colFun()
          getData()
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu scheduleList', userAccessMenu)
          colFun()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          console.log('e.data.data',e.data.data);
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        console.log('Language', e.data)
        // remove prev language data
        const prevLang = localStorage.getItem('LocaleData')
        localStorage.removeItem('LocaleData')
        localStorage.removeItem('APP-OFWATranslationData'+prevLang)
        localStorage.setItem('LocaleData', e.data.LanguageCode)
        localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        handleLanguageChange(e.data.LanguageCode)

        headersKeyVal = {
          externalId: translate('stLabel.id'),
          title: translate('stLabel.title'),
          corePrinciple:translate('stLabel.corePrinciple'),
          process:translate('stLabel.tablecolsProcess'),
          observationType:translate('stLabel.observationType'),
          occurrence:translate('stLabel.occurrence'),
          observationStartDate:translate('stLabel.startDate'),
          observationEndDate:translate('stLabel.tablecolsDuedate'),
          year: translate('stLabel.year'),
          quarter: translate('stLabel.quarter'),
          priority: translate('stLabel.priority'),
          observer:translate('stLabel.observer'),
          status: translate('stLabel.status'),
          createdTime: translate('stLabel.tablecolsCreatedtime'),
          createdBy: translate('stLabel.createdBy'),
          lastUpdatedTime: translate('stLabel.updatedOn'),
          modifiedBy: translate('stLabel.updatedBy'),
          actions: translate('stLabel.actions'),
        }

        console.log('OBSLIst headersKeyVal', headersKeyVal)
        colFun()
        getData()
        console.log('Get data called')
      }
    }
    setDataCount(1)
    colFun()
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose()
  }, [])

  const [searchParams, setSearchParams] = useSearchParams()

  
  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    return html`
    <div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
     

       <div    onClick=${() => disableClick(cInd)}
                  title=${translate('stLabel.disable')}
                  style="height:18px;margin-right:12px;cursor: pointer;">
       <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"   width="15"
                    height="15"x="0" y="0" viewBox="0 0 384 384" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M192 384c105.863 0 192-86.129 192-192 0-51.328-19.96-99.55-56.207-135.793C291.543 19.961 243.328 0 192 0 86.129 0 0 86.129 0 192c0 51.328 19.96 99.55 56.207 135.793C92.449 364.039 140.672 384 192 384zm0-32c-37.39 0-72.8-12.71-101.297-36.078L315.922 90.703C339.289 119.2 352 154.61 352 192c0 88.223-71.777 160-160 160zm0-320c37.383 0 72.8 12.71 101.29 36.078L68.077 293.297C44.711 264.8 32 229.39 32 192c0-88.223 71.777-160 160-160zm0 0" fill="#1A2254" opacity="1" data-original="#000000" class=""></path></g></svg>
       </div>
       
                <div   onClick=${() => ObservationClick(cInd)}
                  title=${translate('stLabel.behaviourchecklistCreateanobervation')}
                  style="height:18px;margin-right:12px;cursor: pointer;">
                <svg id='Capa_1' enable-background='new 0 0 511.797 511.797' height='20' viewBox='0 0 511.797 511.797' width='20' xmlns='http://www.w3.org/2000/svg'><g><path d='m394.839 205.338 116.53-43.304-60.213-162.034-116.53 43.304 7.337 19.743-231.311 85.957 6.389 17.192-49.966 18.568-11.06-29.761 14.325-5.323-10.45-28.121-56.773 21.096 10.45 28.121 14.325-5.323 11.06 29.761-38.524 14.316 32.762 88.163 116.611-43.334 6.389 17.192 54.799-20.364c.212 13.237 6.163 25.097 15.474 33.204l-85.354 216.399 27.907 11.008 32.77-83.081h108.371l32.77 83.081 27.907-11.008-85.354-216.398c9.481-8.256 15.492-20.401 15.492-33.931 0-10.956-3.94-21.007-10.472-28.819l97.003-36.047zm-153.868 45.122c0-8.271 6.729-15 15-15s15 6.729 15 15-6.729 15-15 15-15-6.729-15-15zm57.352 148.257h-84.705l40.744-103.297c.535.019 1.069.041 1.609.041s1.074-.022 1.609-.041zm135.162-360.146 39.313 105.792-60.288 22.403-39.313-105.791zm-294.133 187.667-88.49 32.883-11.862-31.92 88.49-32.883zm237.7-68.765-203.19 75.506-24.639-66.305 203.19-75.506z'  fill="#1A2254"/> </g></svg>
                </div>

    </div> `
  }
  function ObservationClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'observationClick', data: listData[parseInt(index)] },
      '*'
    )
  }
  function disableClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'disableClick', data: listData[parseInt(index)] },
      '*'
    )
  }
  
  function sendClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'createAction', data: listData[parseInt(index)] },
      '*'
    )
  }
  function detailsClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'scheduleDetails', data: listData[parseInt(index)] },
      '*'
    )
  }

  function editClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'Edit', data: listData[parseInt(index)] },
      '*'
    )
  }
  function viewClick(index) {
    window.parent.postMessage(
      { type: 'AuditPlan', action: 'View', data: listData[parseInt(index)] },
      '*'
    )
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    console.log('displayedColumns',displayedColumns)
    console.log('colSummary',colSummary);
    main.redefine('configuration', {
      columns: displayedColumns,
      header: headersKeyVal,
      headerSummary: colSummary,
      format: {
        createdTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        observationStartDate: (x) => format(new Date(x), dateFormat),
        observationEndDate: (x) => format(new Date(x), dateFormat),
        status: (x, i, j) => {
          if (j[i].status == 'Disabled') {
            return html`<div>
          <span style="color:red" > ${j[i].status}</span>
         
           </div>`
          }else{
            return html`<div>
            <span style="color:blsck" > ${j[i].status}</span>
           
             </div>`
          } 
        },
        id: (x) => {
          return x.toString()
        },
        actions: (x, i, j) => {
          if(j[i].status == "Disabled"){
            return html`<div></div>`;
          }else{
            return action1(x, i);
          }
        },
      },
      align: {
        externalId: 'left',
        title: 'left',
        corePrinciple:'left',
        process:'left',
        observationType:'left',
        occurrence:'left',
        observationStartDate:'left',
        observationEndDate:'left',
        year: 'left',
        quarter: 'left',
        observer:'Observer',
        priority: 'left',
        status: 'left',
        createdTime: 'left',
        createdBy: 'left',
        lastUpdatedTime: 'left',
        modifiedBy: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        externalId: 200,
        createdOn: 200,
        createdBy: 200,
        updatedOn: 200,
        modifiedBy: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }
  function getQuarter(dateString) {

    const date = new Date(dateString);
    
    // Ensure the date is valid
    if (isNaN(date)) {
        return 'Invalid date';
    }

    // Get the month (0-based index)
    const monthIndex = date.getMonth();

    // Determine the quarter
    let quarter;
    if (monthIndex >= 0 && monthIndex <= 2) {
        quarter = 'Q1';
    } else if (monthIndex >= 3 && monthIndex <= 5) {
        quarter = 'Q2';
    } else if (monthIndex >= 6 && monthIndex <= 8) {
        quarter = 'Q3';
    } else if (monthIndex >= 9 && monthIndex <= 11) {
        quarter = 'Q4';
    } else {
        return 'Invalid date';
    }

    return quarter;
}

  async function getData() {
    console.log('getData called')
    console.log(startDate, endDate)
    fetch(Constants.NODE_API + '/api/service/listScheduleDetail', {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + token,
        Accept: 'application/json',
        'Content-type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify({
        sites: site,
        startDate: startDate,
        endDate: endDate,
        scheduleId:scheduleId
      }),
    })
      .then((res) => res.json())
      .then((result) => {
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   console.log('temp before', temp[1].childNodes[0].nodeValue)
        //   temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //   console.log('temp after', temp[1].childNodes[0].nodeValue)
        // }
        var listProcess =
          result['data']['list' + Constants.typeOFWAScheduleDetail]['items']
        listData = []
        listProcess.forEach((element) => {
          element['actions'] = ''
          // element["site"] = element["refSite"] ? element["refSite"]["description"] : "";
          var sDate = new Date(element['startDate'])
         
          // externalId: 'ID',
          // title: 'Title',
          // corePrinciple:'Core Principle',
          // process:'Process',
          // observationType:'Observation Type',
          // occurrence:'Occurrence',
          // observationStartDate:'Start Date',
          // observationEndDate:'Due Date',
          // year: 'Year',
          // quarter: 'Quarter',
          // priority: 'Priority',
          // observer:'Observer',
          // status: 'Status',
          // createdTime: 'Created On',
          // createdBy: 'Created By',
          // lastUpdatedTime: 'Updated On',
          // modifiedBy: 'Updated By',
          element['title'] =   element['refOFWASchedule']['title'];
          element['priority'] =   element['refOFWASchedule']['priority'];
          element['observationStartDate'] =element['startDate'];
          element ['observationEndDate'] =element['endDate'];
          element['status'] = element['status'];
          element['occurrence'] = element['refOFWASchedule']['occurrence'];
          element['corePrinciple'] =  element['refOFWASchedule']['refOFWACorePrinciple']?element['refOFWASchedule']['refOFWACorePrinciple'].name:'';
          element['process'] =  element['refOFWASchedule']['refOFWAProcess']?element['refOFWASchedule']['refOFWAProcess'].name:'';
          element['observationType'] =  element['refOFWASchedule']['refOFWAObservationType']?element['refOFWASchedule']['refOFWAObservationType'].name:'';

          element['year'] = element['refOFWASchedule']['year']? element['refOFWASchedule']['year'] + '':sDate.getFullYear()+ '';
          element['quarter'] =  element['refOFWASchedule']['quarter']? element['refOFWASchedule']['quarter']: getQuarter(element.observationStartDate);
          element['observer'] = element['refOFWASchedule']['performerAzureDirectoryUserID']? element['refOFWASchedule']['performerAzureDirectoryUserID']['firstName']+' '+element['refOFWASchedule']['performerAzureDirectoryUserID']['lastName']:'';
          listData.push(element)
        })
        window.parent.postMessage(
          {
            type: 'AuditPlan',
            action: 'Loading',
            data: true,
          },
          '*'
        )

        //  main.redefine("data", listData);
        setCurrentPage(1)
        initFlag = true
        filterData()
        // colFun();
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  function filterData() {
    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }

  
  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
          //assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default ScheduleDetails
