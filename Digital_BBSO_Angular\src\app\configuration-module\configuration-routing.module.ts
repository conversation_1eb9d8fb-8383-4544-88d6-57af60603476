import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ConfigurationComponent } from './configuration/configuration.component';
import { FormConfigListComponent } from './form-config-list/form-config-list.component';
import { ConfigListComponent } from './config-list/config-list.component';
import { QuestionColumnConfigComponent } from './question-column-config/question-column-config.component';


const routes: Routes = [

    {
        path: '',
        component: ConfigurationComponent
    },
    {
        path: 'form-list', component: FormConfigListComponent
    },
    {
        path: 'question-config', component: QuestionColumnConfigComponent
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ConfigurationRoutingModule { }