'use strict';

const express = require('express');
const app = express();
const http = require('http').createServer(app);
const io = require('socket.io')(http,{ 
  cors: {    
    origin: "*",    
    methods: ["GET", "POST"]  
  }});
app.get('/', (req, res) => {
  res.send('Socket server working')
});

var socketMap = [];
http.listen(process.env.SOCKET_PORT,()=>{
  console.log("listning to port "+process.env.SOCKET_PORT);
});
io.on('connection',(socket)=>{
    // console.log("user connected.");
    socketMap.push(socket);
  // //Whenever someone disconnects this piece of code executed
  socket.on('disconnect', function (socket) {
    //  console.log('A user disconnected');
    //  console.log(socket);
  });
});

exports.socketPost = function(name,data){
  for(let socketMapObj of socketMap){
    socketMapObj.emit(name,data);
 }
}

