const swaggerAutogen = require('swagger-autogen')({ openapi: '3.0.1' })


const doc = {
  openapi: "3.0.3",
  info: {
    version: "1.0.0",
    title: "Celanese"
  },
  host: "app-dplantbbsonodeservice-d-ussc-01.azurewebsites.net",
  basePath: "/",
  schemes: ['http', 'https'],
  consumes: ['application/json'],
  produces: ['application/json'],
  tags: [
   
    {
      "name": "Business"
    },
  ],
  servers: [
    {
      url: 'https://app-dplantbbsonodeservice-d-ussc-01.azurewebsites.net/',
      description: 'Business Logic Server'
    },

  ],
  securityDefinitions: {
    bearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      name: 'Authorization',
    }
  },
  definitions: {

    instance: {
      "type": "string",
      "siteCode": "string",
      "unitCode": "string",
      "items": [
        {
          "property-identifier1": "string",
          "property-identifier2": "string"
        }
      ]
    },
    body: {

    },
    byId: {
      "externalId": "string"
    },
    listCommonRefEnum: {
      "name": "string"
    }

  }
}

const outputFile = './autojson/swagger-output.json'
const endpointsFiles = ['./server.js']

// swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
//     require('./app.js')           // Your project's root file
// })

swaggerAutogen(outputFile, endpointsFiles, doc).then(async () => {
  require('./server')   // Your project's root file
});