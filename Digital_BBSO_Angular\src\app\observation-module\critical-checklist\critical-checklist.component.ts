import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Inject, Input, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {  trigger, state, style, transition, animate, sequence } from '@angular/animations'; 
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { MsalService } from '@azure/msal-angular';
import { CogniteAuthentication, CogniteClient } from '@cognite/sdk';
import { TokenService } from 'src/app/services/token.service';
import { DomSanitizer, SafeHtml, SafeUrl } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ActionPopupComponent } from 'src/app/observation-module/action-popup/action-popup.component';
import { MatSelectChange } from '@angular/material/select';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';

interface ExampleFlatNode {
  expandable: boolean;
  bold: boolean;
  name: string;
  level: number;
  guidelineDocument:object;
}

export interface ImageItem {
  image: SafeUrl; 
}
import { MatDateFormats } from '@angular/material/core';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};




@Component({
  selector: 'app-critical-checklist',
  templateUrl: './critical-checklist.component.html',
  styleUrls: ['./critical-checklist.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},]
})
export class CriticalChecklistComponent implements OnInit {


  labels = {};
  observedForm: FormGroup;
  imageSrcs: string[] = [];
  showSuccessPopup = false;
  isSuccess: boolean = true;
  showFailurePopup = false;
  selectedCategory: any = 1;
  subTaskSelected = []
  selectedCategories: any[] = [];
  lastSelectedCategory: any; 
  isEditMode: boolean = true;
  @Input() process: any;
  @Input() corePrinciple: any;
  @Input() subProcess: any;
  @Input() observation: any;
  @Input() scheduleDetails:any;
  @Input() id: any;
  fileUP: any;
  client: CogniteClient;
  timeFormat = "";
  hourFormat = "";
  dateFromat = "";
  maxImages: number = 50;
  maxMedia = 50;
  showOverlayIndex: number | null = null;
  viewedImageIndex: number | null = null;
  showModal = false;
  modalImage = '';
  evidenceObj: any[] = [];
  images: ImageItem[] = [];
  videos: any[] = [];
  viewedVideoIndex: number | null = null;


  private _transformer = (node: any, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      bold: node.bold,
      isCategory:node.isCategory,
      id: node.id,
      question: node.question,
      sequence: node.sequence,
      level: level,
      formGroup: node.formGroup,
      checkListId: node.externalId,
      checkListSpace: node.space,
      guidelineDocument:node.guidelineDocument
    };
  };

  treeControl = new FlatTreeControl<any>(
    node => node.level,

    node => node.expandable,
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children,
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();
  feedBackEnable: boolean = false;


  checkListArray = [];

  incNum = 1

  urgencyList: any[] = [];
  selectedUrgency:any = "Medium";
  tabsListArr = []
  categoryList: any[];
  selectedSubCategory: any;
  subCategoryList: any[];
  @ViewChild('treeExpand') treeExpand;


  reportingLocationControl: FormControl = new FormControl("");
  reportingLocationList = [];
  filteredReportingLocationList = this.reportingLocationList.slice();

  craftList = [];
  filteredCraftList = this.craftList.slice();

  craftList2 = [];
  filteredCraftList2 = this.craftList2.slice();
  departmentList = [];
  filteredDepartmentList = this.departmentList.slice();

  behalfList = [];
  filteredBehalfList = this.behalfList.slice();
  filteredUnitList1: any;
  matchingUnitList: any;
  contractorList = [];
  filteredContractorList = this.contractorList.slice();
  selectedRegion: any;
  selectedCountry: any;
  selectedSite: any;
  selectedUnit: any;
  selectedReportingLine: any;
  selectedBusinessLine: any;
  selectedReportingLocation:any;
  questionList: any = [];
  // evidenceObj: any = [];
  checklistSubCategoryArray: any = [];
  checklistCategoryArray: any = [];
  imageSrc: SafeUrl;
  checklistAnswer: any = [];

  loaderFlag: Boolean;
  isView: boolean;
  processList:any=[];

  // shiftList = [];
  // filteredshiftList = this.shiftList.slice();
  WBAlist = [{"value": "Partnercompany"},{"value": "Celanese"}];
  workPermitlist= [
    {"value": "formcontrolsFallhazardsElevatedWorkplace"},
    {"value": "formcontrolsSpecialDangers"},
    {"value": "formcontrolsEnteringContainersConfinedsSpaces"},
    {"value": "formcontrolsEarthworks"},
    {"value": "formcontrolsHighpressureCleaning"},
    {"value": "formcontrolsCranework"},
    {"value": "formcontrolsIgnitionhazards"}
  ]
  ;


  unitControl: FormControl = new FormControl("");
  unitList = [];
  filteredUnitList = [];
  filterFlag: string;
  tempLocation = [];
  fl_searchVal: any;
  langChangeSubscription: Subscription;
  configDetail: any;
  obRes: any;
  userEmail: any;
  defaultItem: any;
  monthControl: FormControl = new FormControl("");
  monthList = [
    { name: 'January', value: 'January' },
    { name: 'February', value: 'February' },
    { name: 'March', value: 'March' },
    { name: 'April', value: 'April' },
    { name: 'May', value: 'May'},
    { name: 'June', value: 'June' },
    { name: 'July', value: 'July' },
    { name: 'August', value: 'August'},
    { name: 'September', value: 'September' },
    { name: 'October', value: 'October' },
    { name: 'November', value: 'November' },
    { name: 'December', value: 'December' }
  ];
  filteredMonthList = this.monthList.slice();

  weekControl: FormControl = new FormControl("");
  weekList = [
    { key: 1, name: 'Week 1', value: "Week 1" },
    { key: 2,  name: 'Week 2', value: 'Week 2' },
    { key: 3,  name: 'Week 3', value: 'Week 3' },
    { key: 4,  name: 'Week 4', value: 'Week 4' },
    { key: 5,  name: 'Week 5', value: 'Week 5'},
  
  ];
  filteredWeekList = this.weekList.slice();
  monthWeekBool: boolean = true;
  newConfigDetail: { safe: any; notsafe: any; notobserved: any; };
  loaderCount = 0;
  matchingUnits: any = [];
  editEquipment: any[] =[];
  dataSetImageId: any;
  actionPopupResponse: any;
  showSafe: any=true;
  showNotSafe: any=true;
  showNotObserved: any=true;
  checkboxcss: any= '100';
  isSafeNotesMandatory: any;
  isNotSafeNotesMandatory: any;
  isNotObservedNotesMandatory: any;
  notescss: any= '36';
  notescsschild: any= '38';
  marginLeft: string='80px';
  nonotobserveredcss: string='48px';
  outercheckboxcss: string='25';
  outercheckboxcsschild: string='30';
  zeroflex: string;
  safeFxFlex: string='33';
  notSafeFxFlex: string='33';
  notObservedFxFlex: string='33';
  z: number;
  form: any;
  nodes: any;
  currentNodeId: any;
  userbehalf: any;
  notificationGroupEnable: any;
  notificationGroup: any;
  getWeekOfMonth(date) {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const dayOfMonth = date.getDate();
    const dayOfWeek = startOfMonth.getDay();
    const adjustedDate = dayOfMonth + dayOfWeek - 1;
    
    return Math.ceil(adjustedDate / 7);
  }
  auditResult:any=true
  isEditValueSet:boolean = true;
  constructor(private sanitizer: DomSanitizer, private router: Router, private fb: FormBuilder, public commonService: CommonService,
    private dataService: DataService, private tokenService: TokenService,
    private cdRef: ChangeDetectorRef,
    private translate: TranslateService,private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string,
    private cd: ChangeDetectorRef,public dialog: MatDialog, private languageService: LanguageService, private ngZone:NgZone
    ) {

      this.labels={
        'behaviourchecklistCreateanaudit': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanaudit'
        ]||'behaviourchecklistCreateanaudit',
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'
        ]||'behaviourchecklistCreateanhazards',
        'behaviourchecklistCreateanobervation': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'
        ]||'behaviourchecklistCreateanobervation',
        'corePrincipleTitle': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'
        ]||'corePrincipleTitle',
        'process': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'
        ]||'process',
        'observationType': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'
        ]||'observationType',
        'unit': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'
        ]||'unit',
        'commonfilterChooseunit': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'
        ]||'commonfilterChooseunit',
        'commonfilterNoresults': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'
        ]||'commonfilterNoresults',
        'commonfilterSearch': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'
        ]||'commonfilterSearch',
        'locationObserved': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'
        ]||'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'
        ]||'commonfilterChooselocation',
        'observedOnBehalfOf': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'
        ]||'observedOnBehalfOf',
        'commonfilterChoosebehalf': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'
        ]||'commonfilterChoosebehalf',
        'date': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'
        ]||'date',
        'cardsStewardship': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'
        ]||'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'
        ]||'cardsQuality',
        'cardsReliability': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'
        ]||'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'
        ]||'cardsEngagement',
        'cardsFieldwalks': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'
        ]||'cardsFieldwalks',
        'observation': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'
        ]||'observation',
        'audit': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'
        ]||'audit',
        'cardsHazards': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'
        ]||'cardsHazards',
        'startTime': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'
        ]||'startTime',
        'endTime': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'
        ]||'endTime',
        'buttonCancel': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'
        ]||'buttonCancel',
        'buttonSubmit': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'
        ]||'buttonSubmit',
        'buttonOk': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'
        ]||'buttonOk',
        'buttonClose': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClose'
        ]||'buttonClose',
        'formcontrolsChoosetype': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosetype'
        ]||'formcontrolsChoosetype',
        'formcontrolsCelanese': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCelanese'
        ]||'formcontrolsCelanese',
        'formcontrolsPartnercompany': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPartnercompany'
        ]||'formcontrolsPartnercompany',
        'formcontrolsWp': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsWp'
        ]||'formcontrolsWp',
        'formcontrolsIgnitionhazards': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsIgnitionhazards'
        ]||'formcontrolsIgnitionhazards',
        'formcontrolsCranework': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCranework'
        ]||'formcontrolsCranework',
        'formcontrolsFallhazardsElevatedWorkplace': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFallhazardsElevatedWorkplace'
        ]||'formcontrolsFallhazardsElevatedWorkplace',
        'formcontrolsSpecialDangers': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSpecialDangers'
        ]||'formcontrolsSpecialDangers',
        'formcontrolsEnteringContainersConfinedsSpaces': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnteringContainersConfinedsSpaces'
        ]||'formcontrolsEnteringContainersConfinedsSpaces',
        'formcontrolsEarthworks': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEarthworks'
        ]||'formcontrolsEarthworks',
        'formcontrolsHighpressureCleaning': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHighpressureCleaning'
        ]||'formcontrolsHighpressureCleaning',
        'category': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'
        ]||'category',
        'formcontrolsPleasechoosecat': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleasechoosecat'
        ]||'formcontrolsPleasechoosecat',
        'subCategory': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'
        ]||'subCategory',
        'formcontrolsPleassechoosesubcat': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleassechoosesubcat'
        ]||'formcontrolsPleassechoosesubcat',
        'formcontrolsSelectall': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSelectall'
        ]||'formcontrolsSelectall',
        'formcontrolsBehaviourchecklist': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'
        ]||'formcontrolsBehaviourchecklist',
        'notes': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'
        ]||'notes',
        'formcontrolsYes': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'
        ]||'formcontrolsYes',
        'formcontrolsNo': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'
        ]||'formcontrolsNo',
        'formcontrolsPossible': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPossible'
        ]||'formcontrolsPossible',
        'formcontrolsDifficult': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDifficult'
        ]||'formcontrolsDifficult',
        'formcontrolsImposible': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsImposible'
        ]||'formcontrolsImposible',
        'tableheadingNeedimprovement': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingNeedimprovement'
        ]||'tableheadingNeedimprovement',
        'tableheadingAcceptable': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAcceptable'
        ]||'tableheadingAcceptable',
        'tableheadingExcellent': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingExcellent'
        ]||'tableheadingExcellent',
        'buttonBack': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'
        ]||'buttonBack',
        'toasterSuccess': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSuccess'
        ]||'toasterSuccess',
        'toasterRespsuccess': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespsuccess'
        ]||'toasterRespsuccess',
        'toasterFailure': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailure'
        ]||'toasterFailure',
        'toasterRespfailed': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespfailed'
        ]||'toasterRespfailed',
        'toasterNoconfig': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterNoconfig'
        ]||'toasterNoconfig',
        'toasterUploadreached': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterUploadreached'
        ]||'toasterUploadreached',
        'toasterImguploadsuccess': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterImguploadsuccess'
        ]||'toasterImguploadsuccess',
        'toasterImguploadfailed': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterImguploadfailed'
        ]||'toasterImguploadfailed',
        'toasterUploadsuccess': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterUploadsuccess'
        ]||'toasterUploadsuccess',
        'toasterFailed': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailed'
        ]||'toasterFailed',
        'toasterFailedtosaveobs': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailedtosaveobs'
        ]||'toasterFailedtosaveobs',
        'toasterPleasefillreqfields': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterPleasefillreqfields'
        ]||'toasterPleasefillreqfields',
        'commonfilterNodata': this.commonService.labelObject[
          this.commonService.selectedLanguage
        ][
          environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'
        ]||'commonfilterNodata',
        'notRelevant': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notRelevant'
          ]||'notRelevant',
        'operationalLearning': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'
          ]||'operationalLearning',
        'operationalLearningDescription': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'
          ]||'operationalLearningDescription',
        'whoIsBeingAudited': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whoIsBeingAudited'
          ]||'whoIsBeingAudited',
        'nameOfCompany': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'nameOfCompany'
          ]||'nameOfCompany',
        'whichActivityIsBeingAudited': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whichActivityIsBeingAudited'
          ]||'whichActivityIsBeingAudited',
        'workPermits': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workPermits'
          ]||'workPermits',
        'workReleaseNumber': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workReleaseNumber'
          ]||'workReleaseNumber',
        'feedback': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'
          ]||'feedback',
        'activity': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'
          ]||'activity',
        'riskyAction': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'
          ]||'riskyAction',
        'risk': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'
          ]||'risk',
        'riskAgreement': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'
          ]||'riskAgreement',
        'reasonForAction': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'
          ]||'reasonForAction',
        'safeBehaviour': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'
          ]||'safeBehaviour',
        'suggestedSolution': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'
          ]||'suggestedSolution',
        'signature': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'
          ]||'signature',
        'discussionPoint': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'discussionPoint'
          ]||'discussionPoint',
        'safe': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'
          ]||'safe',
        'notSafe': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'
          ]||'notSafe',
        'notObserved': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'
          ]||'notObserved',
        'operationalLearningTooltip': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'
          ]||'operationalLearningTooltip',
        'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'
          ]||'doYouHaveAnyOpportunityForOperationalLearning',
        'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'
          ]||'describeTheOperationalLearningOpportunitiesYouFound',
        
      }

    var _this = this;
    _this.setDateFormat();
    _this.timeFormat = _this.commonService.configuration["dateFormat"];
    _this.hourFormat = _this.commonService.configuration["hourFormat"];
    _this.dateFromat = _this.commonService.configuration["dateFormat"];;
    _this.processList = _.clone(_this.commonService.processList);
    _this.processList.forEach(element => {
      element.selected = false
      if(element.refOFWAProcess){
        element.refOFWAProcess.selected = false
      }
    });
    this.dataSource.data = [];
    _this.getInialTokel(tokenService.getToken());
    if (history.state.action == "View") {
      _this.isView = true;
    }
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();
      // console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo)
      // console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo.externalId)
     
     
      // if(_this.observation && _this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }

    })

    
    _this.dataService.postData({ "name": "Priority" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      var priorityList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _.each(priorityList,function(eData){
        _this.urgencyList.push(eData.value);
      })
    })
    _this.commonService.notificationGroup();

 
  }

  
  getDataSetId(){
    var _this =this
    var objPost = {
      "items": [
        {
          "externalId": `${_this.commonService.configuration["AppCode"]}-${_this["selectedSite"].siteCode}-${_this.commonService.configuration["allUnitCode"]}-${_this.commonService.configuration["dataSpaceCode"]}`
        }
      ],
      "ignoreUnknownIds": false
    }
  _this.dataService.postData(objPost, _this.dataService.NODE_API + "/api/service/getDataSetId").subscribe((resData: any) => {
    console.log('resData',resData)
    
    if(resData && resData.items && resData.items.length > 0){
      _this.dataSetImageId = resData.items[0].id

    }

    })
  }
  hasChild = (_: number, node: ExampleFlatNode) => node.expandable;

  dropdownSettings: IDropdownSettings = {
    "singleSelection": false,
    "defaultOpen": false,
    "idField": "externalId",
    "textField": "nameDesc",
    "selectAllText": "Select All",
    "unSelectAllText": "UnSelect All",
    "enableCheckAll": false,
    "itemsShowLimit": 3,
    "allowSearchFilter": true,
    "limitSelection": -1
  };
  assetsList: any;
  assetsListDeSelect: any
  isFeedback = false;
  isSignature = false;
  isTimeCapture = false;
  
  isInjuryPotentialOptions:any=[];

  
  
  
  
  floorOption = [
    1,2,3,4,5,6,7,8,9,10
  ]
  selectedValue: string = 'No1';
  ngOnInit(): void {

    var _this = this;
    // this.isInjuryPotentialOptions=this.commonService.isInjuryPotentialOptions 
    // _this.dropdownSettings.searchPlaceholderText = _this.translate.instant('COMMONFILTER.SEARCH')
    
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels={
          'behaviourchecklistCreateanaudit': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanaudit'
          ]||'behaviourchecklistCreateanaudit',
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'
          ]||'behaviourchecklistCreateanhazards',
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'
          ]||'behaviourchecklistCreateanobervation',
          'corePrincipleTitle': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrincipleTitle'
          ]||'corePrincipleTitle',
          'process': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'
          ]||'process',
          'observationType': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'
          ]||'observationType',
          'unit': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'
          ]||'unit',
          'commonfilterChooseunit': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'
          ]||'commonfilterChooseunit',
          'commonfilterNoresults': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'
          ]||'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'
          ]||'commonfilterSearch',
          'locationObserved': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'
          ]||'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'
          ]||'commonfilterChooselocation',
          'observedOnBehalfOf': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedOnBehalfOf'
          ]||'observedOnBehalfOf',
          'commonfilterChoosebehalf': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'
          ]||'commonfilterChoosebehalf',
          'date': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'date'
          ]||'date',
          'cardsStewardship': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'
          ]||'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'
          ]||'cardsQuality',
          'cardsReliability': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'
          ]||'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'
          ]||'cardsEngagement',
          'cardsFieldwalks': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'
          ]||'cardsFieldwalks',
          'observation': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'
          ]||'observation',
          'audit': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'
          ]||'audit',
          'cardsHazards': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'
          ]||'cardsHazards',
          'startTime': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startTime'
          ]||'startTime',
          'endTime': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endTime'
          ]||'endTime',
          'buttonCancel': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'
          ]||'buttonCancel',
          'buttonSubmit': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'
          ]||'buttonSubmit',
          'buttonOk': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonOk'
          ]||'buttonOk',
          'buttonClose': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClose'
          ]||'buttonClose',
          'formcontrolsChoosetype': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsChoosetype'
          ]||'formcontrolsChoosetype',
          'formcontrolsCelanese': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCelanese'
          ]||'formcontrolsCelanese',
          'formcontrolsPartnercompany': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPartnercompany'
          ]||'formcontrolsPartnercompany',
          'formcontrolsWp': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsWp'
          ]||'formcontrolsWp',
          'formcontrolsIgnitionhazards': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsIgnitionhazards'
          ]||'formcontrolsIgnitionhazards',
          'formcontrolsCranework': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsCranework'
          ]||'formcontrolsCranework',
          'formcontrolsFallhazardsElevatedWorkplace': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsFallhazardsElevatedWorkplace'
          ]||'formcontrolsFallhazardsElevatedWorkplace',
          'formcontrolsSpecialDangers': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSpecialDangers'
          ]||'formcontrolsSpecialDangers',
          'formcontrolsEnteringContainersConfinedsSpaces': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnteringContainersConfinedsSpaces'
          ]||'formcontrolsEnteringContainersConfinedsSpaces',
          'formcontrolsEarthworks': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEarthworks'
          ]||'formcontrolsEarthworks',
          'formcontrolsHighpressureCleaning': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHighpressureCleaning'
          ]||'formcontrolsHighpressureCleaning',
          'category': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'
          ]||'category',
          'formcontrolsPleasechoosecat': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleasechoosecat'
          ]||'formcontrolsPleasechoosecat',
          'subCategory': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subCategory'
          ]||'subCategory',
          'formcontrolsPleassechoosesubcat': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPleassechoosesubcat'
          ]||'formcontrolsPleassechoosesubcat',
          'formcontrolsSelectall': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSelectall'
          ]||'formcontrolsSelectall',
          'formcontrolsBehaviourchecklist': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'
          ]||'formcontrolsBehaviourchecklist',
          'notes': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notes'
          ]||'notes',
          'formcontrolsYes': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'
          ]||'formcontrolsYes',
          'formcontrolsNo': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'
          ]||'formcontrolsNo',
          'formcontrolsPossible': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsPossible'
          ]||'formcontrolsPossible',
          'formcontrolsDifficult': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDifficult'
          ]||'formcontrolsDifficult',
          'formcontrolsImposible': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsImposible'
          ]||'formcontrolsImposible',
          'tableheadingNeedimprovement': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingNeedimprovement'
          ]||'tableheadingNeedimprovement',
          'tableheadingAcceptable': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingAcceptable'
          ]||'tableheadingAcceptable',
          'tableheadingExcellent': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingExcellent'
          ]||'tableheadingExcellent',
          'buttonBack': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'
          ]||'buttonBack',
          'toasterSuccess': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSuccess'
          ]||'toasterSuccess',
          'toasterRespsuccess': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespsuccess'
          ]||'toasterRespsuccess',
          'toasterFailure': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailure'
          ]||'toasterFailure',
          'toasterRespfailed': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterRespfailed'
          ]||'toasterRespfailed',
          'toasterNoconfig': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterNoconfig'
          ]||'toasterNoconfig',
          'toasterUploadreached': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterUploadreached'
          ]||'toasterUploadreached',
          'toasterImguploadsuccess': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterImguploadsuccess'
          ]||'toasterImguploadsuccess',
          'toasterImguploadfailed': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterImguploadfailed'
          ]||'toasterImguploadfailed',
          'toasterUploadsuccess': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterUploadsuccess'
          ]||'toasterUploadsuccess',
          'toasterFailed': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailed'
          ]||'toasterFailed',
          'toasterFailedtosaveobs': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterFailedtosaveobs'
          ]||'toasterFailedtosaveobs',
          'toasterPleasefillreqfields': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterPleasefillreqfields'
          ]||'toasterPleasefillreqfields',
          'commonfilterNodata': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNodata'
          ]||'commonfilterNodata',
          'notRelevant': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notRelevant'
            ]||'notRelevant',
          'operationalLearning': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'
            ]||'operationalLearning',
          'operationalLearningDescription': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningDescription'
            ]||'operationalLearningDescription',
          'whoIsBeingAudited': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whoIsBeingAudited'
            ]||'whoIsBeingAudited',
          'nameOfCompany': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'nameOfCompany'
            ]||'nameOfCompany',
          'whichActivityIsBeingAudited': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'whichActivityIsBeingAudited'
            ]||'whichActivityIsBeingAudited',
          'workPermits': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workPermits'
            ]||'workPermits',
          'workReleaseNumber': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workReleaseNumber'
            ]||'workReleaseNumber',
          'feedback': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'feedback'
            ]||'feedback',
          'activity': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'activity'
            ]||'activity',
          'riskyAction': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskyAction'
            ]||'riskyAction',
          'risk': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'risk'
            ]||'risk',
          'riskAgreement': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'riskAgreement'
            ]||'riskAgreement',
          'reasonForAction': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reasonForAction'
            ]||'reasonForAction',
          'safeBehaviour': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safeBehaviour'
            ]||'safeBehaviour',
          'suggestedSolution': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'suggestedSolution'
            ]||'suggestedSolution',
          'signature': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'signature'
            ]||'signature',
          'discussionPoint': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'discussionPoint'
            ]||'discussionPoint',
          'safe': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'safe'
            ]||'safe',
          'notSafe': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notSafe'
            ]||'notSafe',
          'notObserved': this.commonService.labelObject[
              this.commonService.selectedLanguage
            ][
              environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'notObserved'
            ]||'notObserved',
            'operationalLearningTooltip': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearningTooltip'
          ]||'operationalLearningTooltip',
          'doYouHaveAnyOpportunityForOperationalLearning': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'doYouHaveAnyOpportunityForOperationalLearning'
          ]||'doYouHaveAnyOpportunityForOperationalLearning',
        'describeTheOperationalLearningOpportunitiesYouFound': this.commonService.labelObject[
            this.commonService.selectedLanguage
          ][
            environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'describeTheOperationalLearningOpportunitiesYouFound'
          ]||'describeTheOperationalLearningOpportunitiesYouFound',
          
        }
        _this.dropdownSettings= {
          "singleSelection": false,
          "defaultOpen": false,
          "idField": "externalId",
          "textField": "nameDesc",
          "selectAllText": "Select All",
          "unSelectAllText": "UnSelect All",
          "enableCheckAll": false,
          "itemsShowLimit": 3,
          "allowSearchFilter": true,
          "limitSelection": -1,
          "searchPlaceholderText": _this.labels['commonfilterSearch'],
          "noDataAvailablePlaceholderText":_this.labels['commonfilterNodata'],
          "noFilteredDataAvailablePlaceholderText":_this.labels['commonfilterNodata']
        };
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      _this.cdRef.detectChanges();
    })

    _this.langChangeSubscription = _this.translate.onLangChange.subscribe((lan) => {
      console.log('lan',lan)
    });
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      _this.filterInit(fiterType);
    })

    var currDate = new Date();
    const monthName = currDate.toLocaleString('default', { month: 'long' });
    this.monthControl.setValue(monthName)

    const currentWeek = this.getWeekOfMonth(currDate);
    console.log(currentWeek);  // Output: The current week number (1-5)
    var weekFind = _this.weekList.find(e => e.key == currentWeek);
    this.weekControl.setValue(weekFind.value)

    
  

    // })
    _this.dataService.postData({ "name": "Contractor" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      _this.contractorList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      _this.filteredContractorList = _this.contractorList.slice();
    })
// _this.dataService.postData({ "name": "shift" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
//       _this.shiftList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
//       _this.filteredshiftList = _this.shiftList.slice();
//     })

    _this.loaderFlag = true;
    _this.dataService.postData({ "sites": _this.subProcess.refSite?_this.subProcess.refSite.externalId:''}, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
      var listCraft = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
      console.log(listCraft)
      _this.craftList = [];
      if (listCraft.length > 0) {
        var settingData = listCraft[0];
          console.log("settingData")
          console.log(settingData)
          _this.dateFromat = settingData["dateFormat"];
          _this.timeFormat = settingData["timeFormat"];
          _this.hourFormat = settingData["hourFormat"];
          var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
          _this._locale = dateFormat.local;
          _this.commonService.dateFormat = dateFormat;
          _this.setDateFormat();
          _this._adapter.setLocale(_this._locale);
          _this.cd.detectChanges();

        if(listCraft[0]["craft"]){
          _.each(listCraft[0]["craft"],function(eData){
            _this.craftList.push({"value":eData})
            
          })
        }
        
        _this.craftList = _this.craftList.sort(function (a, b) {
          if (a.value < b.value) { return -1; }
          if (a.value > b.value) { return 1; }
          return 0;
        })
        _this.craftList2 = _this.craftList.slice();
        _this.filteredCraftList2 = _this.craftList2.slice();
        _this.filteredCraftList = _this.craftList.slice();
        _this.cd.detectChanges();

      }else{
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
        _this._locale = dateFormat.local;
        _this.commonService.dateFormat = dateFormat;
        _this._adapter.setLocale(_this._locale);
        _this.setDateFormat();
        _this.cd.detectChanges();
      }
      
    })
    _this.dataService.postData({ "externalId": _this.process.externalId? _this.subProcess.externalId:'' }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
      
      // if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
        
      var processConfig;
      if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
        processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
      }else{
        processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.process.name)
      }
        var configDetail = processConfig["configDetail"];
        const { high, medium, low } = configDetail;
        var myUrgencyList = [];
        console.log(">>>>>>>>>>>>>>>>>>>")
        console.log(_this.urgencyList)
        console.log(high)
        console.log(medium)
        console.log(low)
        _this.urgencyList.filter(e=>{
          if((e == "High" && (high && high.isEnabled)) || (e == "Medium" && (medium && medium.isEnabled)) || (e == "Low" && (low && low.isEnabled)) ){
            myUrgencyList.push(e)
          }
        })
        console.log(myUrgencyList)
        _this.notificationGroupEnable = configDetail?.notificationEnableValue
        _this.notificationGroup = configDetail?.notificationGroup
        _this.urgencyList = myUrgencyList;
        _this.showSafe= configDetail?.safe?.isEnabled
        _this.showNotSafe= configDetail?.notsafe?.isEnabled
        _this.showNotObserved= configDetail?.notobserved?.isEnabled
        if(configDetail?.safe?.isNotesMandatory){
          _this.isSafeNotesMandatory= true;
        }
        if(configDetail?.notsafe?.isNotesMandatory ){
          _this.isNotSafeNotesMandatory= true;
        }
        if(configDetail?.notobserved?.isNotesMandatory){
          _this.isNotObservedNotesMandatory= true;
        }
        const { safe, notsafe, notobserved } = configDetail;
        _this.newConfigDetail = { safe, notsafe, notobserved };
        _this.defaultItem = Object.values(_this.newConfigDetail).find(item => item.isDefault);
        if (_this.defaultItem) {
          if (_this.defaultItem.field === "Safe") {
            _this.defaultItem.value = "safe";
          } else if (_this.defaultItem.field === "Not Safe") {
            _this.defaultItem.value = "unsafe";
          } else if (_this.defaultItem.field === "Not Observed") {
            _this.defaultItem.value = "notObserved";
          }
      }
      
      // Alter true/false to "Yes"/"No"
      for (const key in _this.defaultItem) {
        if(_this.defaultItem[key] =="null"){
          console.log("hii")
        }
          else if (_this.defaultItem[key] === true) {
            _this.defaultItem[key] = "Yes";
          } else if (_this.defaultItem[key] === false) {
            _this.defaultItem[key] = "No";
          }
          // else if(_this.defaultItem[key] == "null"){
          //   _this.defaultItem[key] = "null";
          // }
      }

        const currentTime = new Date();
        const formattedTime = this.formatTime(currentTime);

        _this.observedForm = _this.fb.group({
          locationObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.locationObserved.isMandatory ? Validators.required : undefined],
          behalf: [{ value: '', disabled: _this.isView },configDetail && configDetail.auditedBy.isMandatory ? Validators.required : undefined],
         // crafts:  [{ value: '', disabled: _this.isView },configDetail && configDetail.crafts.isMandatory ? Validators.required : undefined],
          // department:  [{ value: '', disabled: _this.isView },configDetail && configDetail.department.isMandatory ? Validators.required : undefined],
          datetime: [{ value: new Date(), disabled: _this.isView },configDetail && configDetail.date && configDetail.date.isMandatory ? Validators.required : undefined],
          startTime:  [{ value: formattedTime, disabled: _this.isView },configDetail && configDetail.startTime && configDetail.startTime.isMandatory ? Validators.required : undefined],
          endTime:  [{ value: '', disabled: _this.isView },configDetail && configDetail.endTime && configDetail.endTime.isMandatory ? Validators.required : undefined],
          // assets:  [{ value: '', disabled: _this.isView },configDetail && configDetail.assets.isMandatory ? Validators.required : undefined],
          contractor:  [{ value: 'No', disabled: _this.isView },configDetail && configDetail.contractor.isMandatory ? Validators.required : undefined],
          // contractorDetails:  [{ value: '', disabled: _this.isView },configDetail && configDetail.contractorDetail.isMandatory ? Validators.required : undefined],
          operationalLearning:  [{ value: 'No', disabled: _this.isView },configDetail && configDetail?.operationalLearning?.isMandatory ? Validators.required : undefined],
          operationalLearningDescription :  [{ value: '', disabled: _this.isView },configDetail && configDetail?.operationalLearningDescription?.isMandatory ? Validators.required : undefined],
         // observedCraft:  [{ value: '', disabled: _this.isView },configDetail && configDetail.observedCraft.isMandatory ? Validators.required : undefined],
          // describeObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.describeTheObservations.isMandatory ? Validators.required : undefined],
          discussionPoint:  [{ value: '', disabled: _this.isView },configDetail && configDetail.discussionPoint.isMandatory ? Validators.required : undefined],
         // commentsObserve:  [{ value: '', disabled: _this.isView },configDetail && configDetail.additionalCommentsOnObservation.isMandatory ? Validators.required : undefined],
          signature:  [{ value: '', disabled: _this.isView },configDetail && configDetail.signature.isMandatory ? Validators.required : undefined],
          // shift:  [{ value: '', disabled: _this.isView },configDetail && configDetail.shift.isMandatory ? Validators.required : undefined],
          WBA:  [{ value: '', disabled: _this.isView },configDetail && configDetail.WBA.isMandatory ? Validators.required : undefined],
          // auditedBy :  [{ value: '', disabled: _this.isView },configDetail && configDetail.auditedBy.isMandatory ? Validators.required : undefined],
          nameOfCompany:  [{ value: '', disabled: _this.isView },configDetail && configDetail.nameOfCompany.isMandatory ? Validators.required : undefined],
          WABA:  [{ value: '', disabled: _this.isView },configDetail && configDetail.WABA.isMandatory ? Validators.required : undefined],
          workPermit:  [{ value: '', disabled: _this.isView },configDetail && configDetail.workPermit.isMandatory ? Validators.required : undefined],
          workReleaseNumber:  [{ value: '', disabled: _this.isView },configDetail && configDetail.workReleaseNumber.isMandatory ? Validators.required : undefined],
          result: [{ value: '', disabled: _this.isView }],
      measure: [{ value: '', disabled: _this.isView }],
      needimprovement: [{ value: '', disabled: _this.isView }],
      acceptable: [{ value: '', disabled: _this.isView }],
      excellent: [{ value: '', disabled: _this.isView }]
          //shortDescription:  [{ value: '', disabled: _this.isView },configDetail && configDetail.shortDescription.isMandatory ? Validators.required : undefined],
          //floor:  [{ value: '', disabled: _this.isView },configDetail && configDetail.floor.isMandatory ? Validators.required : undefined],
          //eventType:  [{ value: 'Unsafe Act/Behavior', disabled: _this.isView },configDetail && configDetail.eventType && configDetail.eventType.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          //eventDesccription:  [{ value: '', disabled: _this.isView },configDetail && configDetail.eventDesccription && configDetail.eventDesccription.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          //correctiveActionFlag:  [{ value: 'Yes', disabled: _this.isView },configDetail && configDetail.correctiveActionFlag && configDetail.correctiveActionFlag.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          //isInjuryPotential:  [{ value: '', disabled: _this.isView },configDetail && configDetail.isInjuryPotential && configDetail.isInjuryPotential.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined],
          
          //descripeCorrectiveActionTaken:  [{ value: '', disabled: _this.isView },configDetail && configDetail.descripeCorrectiveActionTaken && configDetail.descripeCorrectiveActionTaken.isMandatory && configDetail.stopWorkAuthority.isEnabled ? Validators.required : undefined]
        });

        if(_this.observation){

          _this.isEditValueSet = false;
        }

       //_this.afterSettingInit()

        
  //       _this.showSafe= configDetail.safe.isEnabled
  //       _this.showNotSafe= configDetail.notsafe.isEnabled
  //       _this.showNotObserved= configDetail.notobserved.isEnabled
  //       // _this.checkboxcss='66.3'
  //       // _this.notescss='30'
  //       // _this.nonotobserveredcss='4px';
  //       // _this.outercheckboxcss='16.5';


  //       if (!_this.showSafe && !_this.showNotSafe) {
  //         _this.checkboxcss = (parseFloat(_this.checkboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.notescss = (parseFloat(_this.notescss) * 1.33).toString(); // Increase by 33%
  //         if (!_this.showNotObserved) {
  //           _this.nonotobserveredcss = '4px';
  //         }
  //       } else if (!_this.showSafe || !_this.showNotSafe || !_this.showNotObserved) {
  //         _this.checkboxcss = (parseFloat(_this.checkboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * 0.67).toString(); // Decrease by 33%
  //         _this.notescss = (parseFloat(_this.notescss) * 1.33).toString(); // Increase by 33%
  //         if (!_this.showNotObserved) {
  //           _this.nonotobserveredcss = '4px';
  //         }
  //       }else if (!_this.showSafe && !_this.showNotSafe && !_this.showNotObserved) {
  //          // Decrease values by 33% for each disabled option
  // const factor = Math.pow(0.67, 3); // 0.67 * 0.67 * 0.67

  // _this.checkboxcss = (parseFloat(_this.checkboxcss) * factor).toString(); // Adjust checkboxcss
  // _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * factor).toString(); // Adjust outercheckboxcss
  // _this.notescss = (parseFloat(_this.notescss) * Math.pow(1.33, 3)).toString(); // Increase notescss by 33% for each disabled option
  // _this.nonotobserveredcss = '4px'; // Set nonotobserveredcss to 4px
  //       }
        // Modify fxFlex for disabled radio buttons
if (!_this.showSafe) {
  _this.safeFxFlex = '0';
}

if (!_this.showNotSafe) {
  _this.notSafeFxFlex = '0';
}

if (!_this.showNotObserved) {
  _this.notObservedFxFlex = '0';
}
        // Further adjustments if multiple boxes are disabled
        let disabledCount = 0;
        if (!_this.showSafe) disabledCount++;
        if (!_this.showNotSafe) disabledCount++;
        if (!_this.showNotObserved) disabledCount++;
        
        if (disabledCount >= 1) {
          const factor = Math.pow(0.67, disabledCount); // Adjust by 33% for each disabled box
          _this.checkboxcss = (parseFloat(_this.checkboxcss) * factor).toString();
          _this.outercheckboxcss = (parseFloat(_this.outercheckboxcss) * factor).toString();
          _this.outercheckboxcsschild = (parseFloat(_this.outercheckboxcsschild) * factor).toString();
          _this.notescss = (parseFloat(_this.notescss) * Math.pow(1.31, disabledCount)).toString();
          _this.notescsschild = (parseFloat(_this.notescsschild) * Math.pow(1.31, disabledCount)).toString();
          if (!_this.showNotObserved) {
            _this.nonotobserveredcss = '-44px';
          }
          else if(!_this.showSafe && !_this.showNotSafe)
          {
            _this.nonotobserveredcss = '10px';
          }
        }
        console.log(this.observedForm.get('InjuryPotential'));

        // _this.observedForm.get("correctiveActionFlag").valueChanges.subscribe(value => {
        //   if (value == "No" && history.state.action != "View" && _this.isEditValueSet) {
        //     const dialogConfig = new MatDialogConfig();
        //     dialogConfig.data = {
        //       process:_this.process.name,
        //       site: _this.subProcess.refSite.externalId,
        //       unit: _this.unitControl.value,
        //       location: _this.observedForm.get("locationObserve").value
        //     };
        //     dialogConfig.disableClose = true; // Prevents closing on outside click
        //     dialogConfig.position = { top: "50px", }
        //     dialogConfig.panelClass = "react-table-modalbox"
        //     const dialogRef = this.dialog.open(ActionPopupComponent, dialogConfig);
        //     dialogRef.afterClosed().subscribe(result => {
        //       console.log("result")
        //       console.log(result)
        //       if(result && history.state.action != "Edit"){
        //         _this.actionPopupResponse = result;
        //       } else if(result && _this.observation && _this.observation.correctiveActionFlag && _this.observation.correctiveActionFlag == "Yes"){
        //         _this.actionPopupResponse = result;
        //       } else if(result && _this.observation && !_this.observation.correctiveActionFlag){
        //         _this.actionPopupResponse = result;
        //       } else{
        //         _this.observedForm.get("correctiveActionFlag").setValue("Yes");
        //       }
              
        //     });
        //   }
        // })
console.log("HIIIIIIIIIIIIIIIIIIIIIIIIIIII")
        


        

        
        _this.configDetail = processConfig["configDetail"];
        _this.observedForm.get("datetime").valueChanges.subscribe(x => {
          console.log(x)
          if(history.state.action != "Edit" || history.state.action != "View"){
            var currDate = new Date(x);
            const monthName = currDate.toLocaleString('default', { month: 'long' });
            _this.monthControl.setValue(monthName)
            const currentWeek = _this.getWeekOfMonth(currDate);
            var weekFind = _this.weekList.find(e => e.key == currentWeek);
            _this.weekControl.setValue(weekFind.value)
          }
         
        });
        // _this.loaderFlag = false;
        _this.cd.detectChanges();
        _this.afterSettingInit();
       
        // _this.isFeedback = processConfig["isFeedback"] == true ? true : false;
        // _this.isSignature = processConfig["isSignature"] == true ? true : false;
        // _this.isTimeCapture = processConfig["isTimeCapture"] == true ? true : false;
      // } else {
      //   _this.configDetail = undefined;
      //   _this.loaderFlag = false;
      //   _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.labels['toasterNoconfig'] });
      // }
      
  console.log("id____________________.",_this.subProcess)
  this.questionFun("SubProcess", _this.subProcess.externalId, _this.subProcess);
  })
  console.log("subprocess",this.subProcess.refSite)
    var site = _this.commonService.siteList.find(e =>_this.subProcess.refSite && e.externalId == _this.subProcess.refSite.externalId )
    _this.selectedSite = site;
    console.log('_this.selectedSite ',_this.selectedSite)
    _this.getDataSetId();
     //_this.siteChanged()
    _this.selectedCountry = site["country"];
    if (site["country"] && site["country"]["parent"]) {
      _this.selectedRegion = site["country"]["parent"];
    }
    if (site && site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      console.log('reportingLocationList',_this.reportingLocationList)
    }
    if(_this.selectedSite){
      _this.commonService.siteList.filter(function (e) {
        if (_this.selectedSite.externalId.indexOf(e.externalId) > -1) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
            console.log('unitList',_this.unitList)
          }
  
       
        }
        //return selectedSite.indexOf(e.externalId) > -1;
      });
    }
    

    // _this.commonService.siteChanges$.subscribe((sit: any) => {
    // console.log('sit',sit)
    // })
 
    _this.unitControl.valueChanges.subscribe(xArray => {
      console.log('xArray',xArray,_this.tempLocation)
      var tempArray =_this.tempLocation.filter(item => item.reportingUnit?.externalId ==  xArray);
      console.log('tempArray',tempArray)
     
      _this.reportingLocationList =tempArray
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      console.log(' _this.reportingLocationList', _this.reportingLocationList)
     // _this.cd.dete

     });

    
    var childrenCategory = _this.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.subProcess.externalId);
    })
    _this.categoryList = childrenCategory;
    _this.categoryList  = _this.categoryList .sort(function(a, b){
      if((a.sequence ? a.sequence : 0) < (b.sequence ? b.sequence : 0)) { return -1; }
      if((a.sequence ? a.sequence : 0) > (b.sequence ? b.sequence : 0)) { return 1; }
      return 0;
  })
  }


  expandAll(){
    var _this =this;
    _this.cd.detectChanges();
    if(_this.loaderCount == 0){
      // _this.updateBoldProperties();
      setTimeout(function(){
        _this.treeExpand.treeControl.expandAll();
      _this.cd.detectChanges();
      }, 500);
    }
  }

  updateResult(category: string): void {
    const measureValue = this.observedForm.get(category.toLowerCase()).value;
    if (measureValue) {
      this.observedForm.get('result').setValue(category);
      this.observedForm.get('measure').setValue(measureValue);
    }
  }

  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  afterSettingInit(){
    var _this = this;
    
    var locationObserve = "";
    var behalf = "";
    var datetime = new Date();
    // var startTime = new Date();
    // var endTime = new Date();
    var assets = [];
    var contractor = "";
    var contractorDetails = "";
    var operationalLearning = "";
    var operationalLearningDescription = "";
    var describeObserve = "";
    // var shift = "";
    var WBA = "";
    var isInjuryPotential=''

    var shortDescription = "";
    var auditedBy = "";
    var nameOfCompany = "";
    var WABA = "";
    var workPermit = "";
    var workReleaseNumber = "";
    var floor = "";
    
    var commentsObserve = "";
    var signature = "";
    var result = "";
    var measure = "";
    var discussionPoint = "";
   
    _this.observedForm.get("locationObserve").valueChanges.subscribe(value => {
      var location = _this.reportingLocationList.find(e => e.externalId == value);
      _this.selectedReportingLocation = location;
    });
  
    _this.observedForm.get("behalf").valueChanges.subscribe(x => {
      console.log(x,_this.selectedSite.externalId )
 
    //   _this.dataService.postData({user: x,site_id: _this.selectedSite.externalId},_this.dataService.NODE_API + '/api/service/getUnitInfo')
    // .subscribe((details:any) => 
    let details=_this.commonService.userUnitList
       _this.matchingUnits = this.unitList.filter(unit =>
    details.some(detailsUnit => detailsUnit.unitCode === unit.externalId)
);
if(_this.matchingUnits.length==0){
  _this.matchingUnits = this.unitList;
}
console.log('_this.matchingUnits',_this.matchingUnits)
if (_this.matchingUnits.length > 0) {
  _this.loaderFlag = false;
  if(this.isView){
    _this.unitControl.disable();  
    _this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
  }

   if(_this.observation && _this.observation.refUnit && _this.observation.refUnit.externalId){
   
    _this.unitControl.setValue(_this.observation.refUnit.externalId)
    _this.filteredUnitList1=_this.unitList
    console.log("filteredunitlist-------->",this.filteredUnitList1)
  _this.matchingUnitList=_this.unitList
  _this.z=0;
  }
  else {
    _this.unitControl.setValue(_this.matchingUnits[0].externalId);
  _this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
  _this.z=1; 
}
} 
else {
//   _this.unitControl.setValue(_this.unitList[0].externalId);
_this.filteredUnitList1=_this.unitList
  _this.matchingUnitList=_this.unitList
}
    
  // )      
    });

    // _this.observedForm.get("assets").valueChanges.subscribe(x => {
    //   // console.log("ASSET ", x)
    // });

    if (_this.observation) {
      console.log('_this.observation',_this.observation)
      _this.loaderFlag = false;
      locationObserve = "";
      behalf = "";
      // var myDate = new Date(_this.observation.date);
      // datetime = new Date(myDate.setDate(new Date().getDate()));
      datetime = _this.observation.date
      // startTime = new Date(_this.observation.startTime);
      // endTime = new Date(_this.observation.endTime);
      assets = [];
      contractor = _this.observation.isContractor ? "Yes" : "No";
      contractorDetails = _this.observation.contractorDetails;
      operationalLearning = _this.observation.isOperationalLearning?"Yes":"No";
      operationalLearningDescription = _this.observation.operationalLearningDescription;
      // describeObserve = _this.observation.description;
      // shift = _this.observation.shift;
      WBA = _this.observation.companyType;
      shortDescription = _this.observation.shortDescription;
      // auditedBy = _this.observation.auditedBy;
      workPermit = _this.observation.workPermitType;
      nameOfCompany = _this.observation.companyName;
      WABA = _this.observation.activityAudited;
      workReleaseNumber = _this.observation.workPermitNumber;
      

     // floor = _this.observation.floor;
      //_this.selectedUrgency = _this.observation.urgency ? _this.observation.urgency:"";
      //commentsObserve = _this.observation.comments;
      signature = _this.observation.signature;
      result = _this.observation.result;
      measure = _this.observation.measure;
      discussionPoint = _this.observation.discussionPoint;
      locationObserve = _this.observation.refReportingLocation ? _this.observation.refReportingLocation.externalId : "";

      if(_this.observation && _this.observation.refUnit && _this.observation.refUnit.externalId){
   
        _this.unitControl.setValue(_this.observation.refUnit.externalId)
        _this.filteredUnitList1=_this.unitList
        console.log("filteredunitlist-------->",this.filteredUnitList1)
      _this.matchingUnitList=_this.unitList
      
      }

      console.log("startTime",_this.observation.startTime)
      console.log("endtime",_this.observation.endTime)
      // "11:15 pm"
      if(_this.observation.startTime || _this.observation.endTime){
            _this.observedForm.get("startTime").setValue(_this.observation.startTime?.split("T")[1].split(":")[0]+":"+_this.observation.startTime?.split("T")[1].split(":")[1]);
            _this.observedForm.get("endTime").setValue(_this.observation.endTime?.split("T")[1].split(":")[0]+":"+_this.observation.endTime?.split("T")[1].split(":")[1]);
        }
        console.log("dateee------",datetime)
          _this.observedForm.get("datetime").setValue(datetime);
      
        // console.log("shift---------",shift)
        console.log("WBA---------",WBA)
      //_this.observedForm.get("contractor").setValue(contractor);
      //_this.observedForm.get("contractorDetails").setValue(contractorDetails);
      _this.observedForm.get("operationalLearning").setValue(operationalLearning);
      _this.observedForm.get("operationalLearningDescription").setValue(operationalLearningDescription);
    //  _this.observedForm.get("describeObserve").setValue(describeObserve);
      // _this.observedForm.get("shift").setValue(shift);
      _this.observedForm.get("WBA").setValue(WBA);
      _this.observedForm.get("workPermit").setValue(workPermit);
      //_this.observedForm.get("crafts").setValue(_this.observation.crafts);
      //_this.observedForm.get("shortDescription").setValue(shortDescription);
      // _this.observedForm.get("auditedBy").setValue(auditedBy);
      _this.observedForm.get("nameOfCompany").setValue(nameOfCompany);
      _this.observedForm.get("WABA").setValue(WABA);
      _this.observedForm.get("workReleaseNumber").setValue(workReleaseNumber);
      //_this.observedForm.get("floor").setValue(parseInt(floor));
      //_this.observedForm.get("commentsObserve").setValue(commentsObserve);
      _this.observedForm.get("signature").setValue(signature);
      // _this.observedForm.get("result").setValue(result);
      // _this.observedForm.get("measure").setValue(measure);
      console.log("result",discussionPoint)
      if (result === 'NEEDIMPROVEMENT') {
        this.observedForm.get('needimprovement').setValue(measure);
      } else if (result === 'ACCEPTABLE') {
        this.observedForm.get('acceptable').setValue(measure);
      } else if (result === 'EXCELLENT') {
        this.observedForm.get('excellent').setValue(measure);
      }
      _this.observedForm.get("discussionPoint").setValue(discussionPoint);
     // _this.observedForm.get("observedCraft").setValue(_this.observation.observedCraft);
     // _this.observedForm.get("eventType").setValue(_this.observation.eventType ? _this.observation.eventType :"Unsafe Act/Behavior");
     // _this.observedForm.get("eventDesccription").setValue(_this.observation.eventDesccription ? _this.observation.eventDesccription :"");
     // _this.observedForm.get("correctiveActionFlag").setValue(_this.observation.correctiveActionFlag ? _this.observation.correctiveActionFlag :"Yes");
      //_this.observedForm.get("descripeCorrectiveActionTaken").setValue(_this.observation.descripeCorrectiveActionTaken ? _this.observation.descripeCorrectiveActionTaken :"");
      // if(_this.observation.refDeparment){
      //   _this.observedForm.get("department").setValue(_this.observation.refDeparment.externalId);
      // }
     
      console.log("unit---->", _this.observation.refUnit)
      if(_this.observation.refUnit){
        _this.unitControl.setValue(_this.observation.refUnit.externalId);
      }
      console.log("locationobserve", locationObserve)
      _this.observedForm.get("locationObserve").setValue(locationObserve);
      _this.isEditValueSet = false;
      // if(_this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }
   
      async function processMediaList(mediaList) {
        let delayTime = 500; // Initial delay time in milliseconds
        const maxDelayTime = 16000; // Maximum delay time in milliseconds
        const maxRetries = 5; // Maximum number of retries for each request
    
        for (let i = 0; i < mediaList.length; i++) {
            const item = mediaList[i];
            const mediaType = item.mimetype.split('/')[0]; // Get the type (image or video)
            let mediaUrl;
            let retries = 0; // Retry counter
    
            console.log(`Processing item ${i + 1} of ${mediaList.length}`);
    
            if (mediaType === 'image') {
                mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
                console.log("media url for image", mediaUrl);
    
                while (retries < maxRetries) {
                    try {
                        const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                        console.log("image data", resData);
                        const objectURL = URL.createObjectURL(resData);
                        item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                        console.log('Image loaded successfully:', item.image);
                        this.images.push(item.image);
                        console.log('Images:', this.images);
                        this.cd.detectChanges(); // Trigger change detection after image is loaded
    
                        // Reset delay time after a successful request
                        delayTime = 500;
                        break; // Exit retry loop on success
                    } catch (error) {
                        console.error('Error loading image:', error);
                        if (error.status === 429) {
                            console.error('Rate limit exceeded. Retrying after delay...');
                            await delay(delayTime);
                            delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                            retries++; // Increment retry counter
                        } else {
                            // Handle other errors
                            break; // Exit retry loop on other errors
                        }
                    }
                }
            } else if (mediaType === 'video') {
                console.log("hi");
                mediaUrl = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
                console.log("mediaUrl for video", mediaUrl);
    
                while (retries < maxRetries) {
                    try {
                        const resData: any = await this.dataService.getImage(mediaUrl).toPromise();
                        console.log("video data", resData);
                        const objectURL = URL.createObjectURL(resData);
                        item.video = this.sanitizer.bypassSecurityTrustUrl(objectURL);
                        console.log('Video loaded successfully:', item.video);
                        this.videos.push(item.video);
                        console.log('Videos:', this.videos);
                        this.cd.detectChanges(); // Trigger change detection after video is loaded
    
                        // Reset delay time after a successful request
                        delayTime = 500;
                        break; // Exit retry loop on success
                    } catch (error) {
                        console.error('Error loading video:', error);
                        if (error.status === 429) {
                            console.error('Rate limit exceeded. Retrying after delay...');
                            await delay(delayTime);
                            delayTime = Math.min(delayTime * 2, maxDelayTime); // Exponential backoff
                            retries++; // Increment retry counter
                        } else {
                            // Handle other errors
                            break; // Exit retry loop on other errors
                        }
                    }
                }
            }
    
            // Fixed delay between requests
            await delay(delayTime);
        }
    
        console.log('All items processed');
        console.log('Total images:', this.images.length);
        console.log('Total videos:', this.videos.length);
    }
    
    function delay(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    if (this.observation.evidenceDocument && this.observation.evidenceDocument.items.length > 0) {
        const mediaList = this.observation.evidenceDocument.items;
        processMediaList.call(this, mediaList);
    }
    
    
    
    
    
    
      _this.editView();
    }

    if(_this.dataService.userInfo.externalId){
    //   _this.observedForm.get("behalf").setValue(_this.dataService.userInfo.user.externalId);
    // }
    if(_this.observation?.observedOnBehalfOf?.externalId){
      var user = _this.behalfList.find(u => u.externalId === "_this.observation.observedOnBehalfOf.externalId");
      var userId= _this.observation.observedOnBehalfOf.externalId
    }
    else{
      var user = _this.behalfList.find(u => u.externalId === "_this.dataService.userInfo.user.externalId");
      var userId= _this.dataService.userInfo.user.externalId
    }
        if (!user) {
          // If user not found in the first 1000 records, perform search with externalId
          _this.dataService.postData({ searchUser: userId }, _this.dataService.NODE_API + "/api/service/listAllUser")
            .subscribe((userData: any) => {
              console.log('userData', userData);
              const searchResult = userData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
              if (searchResult.length > 0) {
                const selectedUser = searchResult[0];
                selectedUser.name = selectedUser.firstName + ' ' + selectedUser.lastName;
                _this.behalfList.push(selectedUser);
                _this.filteredBehalfList = _this.behalfList.slice();
                try {
                  _this.userbehalf=selectedUser.externalId
                  _this.observedForm.get("behalf").setValue(selectedUser.externalId);
                  console.log("HIIIIIIIIIIIIIIIIIIIIIIi")
                  _this.z=1
                } catch (error) {
                  console.error('Error setting value in behalf form control:', error);
                }
              }
            });
        }
        else{
          try {
            _this.observedForm.get("behalf").setValue(user.externalId);
          } catch (error) {
            console.error('Error setting value in behalf form control:', error);
          }

        }}
    
        _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "" ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
        subscribe((resData: any) => {

          var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
          asset.forEach(element => {
            element.nameDesc = element.name+' / '+element.description
            
          });
          _this.assetsList = asset
          var mapAss = undefined;
         if(_this.observation){
          mapAss = _this.observation.refEquipment && _this.observation.refEquipment["items"].map(item => item.externalId)
         }
          _this.dataService.postData({ externalId:mapAss?mapAss:[] ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
          subscribe((resData: any) => {
            var assetMap = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
            assetMap.forEach(element => {
              element.nameDesc = element.name+' / '+element.description
            });
            _this.assetsList = _.unionBy(assetMap, _this.assetsList, 'externalId');
            if(_this.observation) {

              var asL = [];
              _.each(_this.observation.refEquipment &&_this.observation.refEquipment["items"], function (eAsset) {

                eAsset.nameDesc = eAsset.name+' / '+eAsset.description
                asL.push(eAsset)

              })
              _this.editEquipment =  _.cloneDeep(asL);  
          
              _this.observedForm.get("assets").setValue(asL);
            }
          })
        
        })
        
        if(_this.userbehalf && _this.userbehalf.length>0){
          _this.observedForm.get("behalf").setValue(_this.userbehalf);
        }
    // _this.treeExpand.treeControl.expandAll();
  }
  getDepartments(){
    var _this =this;
    _this.dataService.postData({ "sites":_this["selectedSite"]?[_this["selectedSite"].externalId]:[] }, _this.dataService.NODE_API + "/api/service/listDepartment").subscribe((resData: any) => {
      _this.departmentList = resData["data"]["list" + _this.commonService.configuration["typeDepartment"]]["items"]
      _this.departmentList =  _this.departmentList.sort(function(a, b){
        if(a.description < b.description) { return -1; }
        if(a.description > b.description) { return 1; }
        return 0;
      })
      _this.filteredDepartmentList = _this.departmentList.slice();
   
    })
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredUnitList1 = _this.matchingUnitList.slice();
    }
    if(_this.observation){
      if(_this.matchingUnits.length==0){
        _this.matchingUnits = this.unitList;
      }
      _this.filteredUnitList1=this.unitList
      _this.matchingUnitList=this.unitList
    }
  }
  ngAfterViewInit() {

   
    var _this = this;
    
    if (_this.commonService.filterListFetched) {
   
      _this.filterInit("Unit");

    }

    _this.getDepartments()
  }
  siteChanged() {
    var _this = this;
    console.log('    _this.selectedSite ')
    var selectedSite = _this.commonService.getSelectedValue(_this.selectedSite.externalId);
    _this.unitList = [];
    _this.filteredUnitList = _this.unitList.slice();

  
    if (selectedSite.length > 0) {

      var myCountries = [];
      var myRegions = [];
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e["country"]) {

            myCountries.push(e["country"]["externalId"])
            if (e["country"]["parent"])
              myRegions.push(e["country"]["parent"]["externalId"])

          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
      _this.filterFlag = "Site";
 
    } else {
      selectedSite = _this.commonService.siteList.map(eItem => (eItem.externalId))
    }

    if (selectedSite.length > 0) {
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
            _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }

       
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
    } else {

      if (_this.commonService.siteList.length > 0) {
        _this.unitList = _this.commonService.unitList;
        _this.filteredUnitList = _this.unitList.slice();
      }

  
    }
  }

filterInit(fiterType) {
    var _this = this;
 
    // if (fiterType == "Unit") {
    //   _this.unitList = _this.commonService.unitList;
    //   _this.filteredUnitList = _this.commonService.unitList.slice();

    // }
    // console.log('   _this.unitList',   _this.unitList)
   

  }

  editView() {
    console.log("Edit");

    const handleItems = (items) => {
        items.forEach(item => {
            const selCategory = this.processList.find(e => e.externalId === item.refOFWAProcess.externalId);
            if (selCategory) {
                this.selectCategory(selCategory);
            }

            this.loaderFlag = false;
            const selSubCategory = this.processList.find(e => e.externalId === item.externalId);
            if(selSubCategory) {
              setTimeout(() => {
                this.selectSubCategory(selSubCategory);
            }, 500);
            }
            
        });
    };

    const handleItems2 = (items) => {
      items.forEach(item => {
          this.loaderFlag = false;
          const selCategory = this.processList.find(e => e.externalId === item.externalId);
          setTimeout(() => {
              this.selectCategory(selCategory);
          }, 500);
      });
  };

    if (this.observation["refOFWAProcess"] && this.observation["refOFWAProcess"]["items"].length > 0) {
        handleItems(this.observation["refOFWAProcess"]["items"]);
    }

    if (this.observation["refCategory"] && this.observation["refCategory"]["items"].length > 0) {
        handleItems2(this.observation["refCategory"]["items"]);
    } else {
        this.loaderFlag = false;
    }
}

  infieldClick() {
    window.open('https://cognite-infield.cogniteapp.com/observation', '_blank', 'noopener, noreferrer')
  }
  async onFilterChange(item: any) {
    var _this = this;
    _this.dataService.postData({ "functionalLocationName": "", "equipmentName": item,"siteCode":_this["selectedSite"].siteCode }, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {
        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
      })

  }
  resetSelection() {
  //  this.selectedItemsRoot = []; 
  var _this =this;
  _this.dataService.postData({ "functionalLocationName": "", "equipmentName": "" ,"siteCode":_this["selectedSite"].siteCode}, _this.dataService.NODE_API + "/api/service/listEquipmentByFunctionalLocation").
      subscribe((resData: any) => {
        var asset = resData["data"]["list" + _this.commonService.configuration["typeEquipment"]]["items"]
        asset.forEach(element => {
          element.nameDesc = element.name+' / '+element.description
          
        });
        _this.assetsList = asset
      })       
    }
  async onBehalfChange(item: any) {
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.behalfList = [];
    _this.filteredBehalfList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.behalfList.forEach((element)=>{
          element.name =  element.firstName+' '+element.lastName
        })
        _this.filteredBehalfList = _this.behalfList.slice();
      
  
      })
      _this.cdRef.detectChanges()
      
  }
}
getColor(node: any): string {
  if (node.bold && node.level == 0) {
    return; // Example: Red for bold and level 0
  }
  return '#017BA4'; // Default color
}
  onItemSelect(item: any) {
  }
  onSelectAll(items: any) {
  }
  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }

  async uploadFile($event: any) {
    this.loaderFlag = true;
    this.cd.detectChanges();  // Trigger change detection
    
    const fileInput = $event.target;
    const files = fileInput.files;
  
    // Check if files are selected
    if (!files || files.length === 0) {
        this.loaderFlag = false;
        this.cd.detectChanges();  // Trigger change detection
        console.log('No files selected.');
        return;
    }
  
    const totalFiles = files.length + this.images.length + this.videos.length;
    if (totalFiles > this.maxMedia) {
        this.loaderFlag = false;
        this.cd.detectChanges();  // Trigger change detection
        this.commonService.triggerToast({ type: 'error', title: '', msg: this.labels['toasterUploadreached'] });
        return;
    }
  
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        const isImage = file.type.startsWith('image/');
        const isVideo = file.type.startsWith('video/');
  
        // Using a promise to handle file reading
        await new Promise<void>((resolve, reject) => {
            reader.onload = (e: any) => {
                if (isImage) {
                    this.images.push(e.target.result);
                    console.log(`Image ${i + 1}/${files.length} loaded:`, file.name);
                } else if (isVideo) {
                    this.videos.push(e.target.result);
                    console.log(`Video ${i + 1}/${files.length} loaded:`, file.name);
                }
                this.cd.detectChanges();  // Trigger change detection after each file is loaded
                resolve();
            };
            reader.onerror = (e: any) => {
                console.error(`Error loading file ${file.name}:`, e);
                reject(e);
            };
  
            reader.readAsDataURL(file);
        });
  
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
  
        const myObj = {
            name: fileupload.name,
            mimetype: fileupload.mimeType,
            pathURL: fileupload.uploadUrl,
            pathStore: "CDF",
            evidenceDocumentId: fileupload.id + "",
            description: ""
        };
  
        console.log('External ID of newly uploaded file:', myObj["externalId"]);
        this.evidenceObj.push(myObj);
        console.log("this.evidenceObj", this.evidenceObj);
        console.log("myobjjjj", myObj);
    }
  
    this.loaderFlag = false;
    this.cd.detectChanges();  // Trigger change detection after all files are processed
    
    // Reset file input value to allow re-selection of the same file
    fileInput.value = '';
}


  
  
  
  async saveImages() {
    console.log("saveImages called");
    if (this.evidenceObj.length === 0) {
      return; 
    }
  
    this.loaderFlag = true;
  
    const postObj = {
      type: this.commonService.configuration["typeEvidenceDocument"],
      siteCode: this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      unitCode: this.commonService.configuration["allUnitCode"],
      items: this.evidenceObj
    };

    console.log("postObj", postObj);
    console.log("this.evidenceObj")
    console.log(this.evidenceObj)
  
    try {
      const response = await this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").toPromise();
      console.log('All files data posted:', response);
      this.evidenceObj = response["items"];
      this.evidenceEdgeCreation(this.obRes);
  
      this.commonService.triggerToast({ type: 'success', title: "", msg: this.labels['toasterImguploadsuccess'] });
    } catch (error) {
      console.error('Error saving images:', error);
      this.commonService.triggerToast({ type: 'error', title: 'Error', msg: this.labels['toasterImguploadfailed'] });
    } finally {
      this.loaderFlag = false; 
    }
   
  }

  removeImage(index: number) {
    if (index >= 0 && index < this.images.length) {
      // Remove the image from the UI
      this.images.splice(index, 1);
      console.log('Image removed at index', index);
      this.evidenceObj.splice(index, 1);
      this.removeObservationImage(index);
    }
  }


  removeVideo(index: number) {
    this.videos.splice(index, 1);
  }


  viewVideo(index: number) {
    this.viewedVideoIndex = index;
  }

  closeViewedVideo() {
    this.viewedVideoIndex = null;
  }

  viewImage(index: number): void {
    this.viewedImageIndex = index;
  }

  updateServerData() {
    var _this = this;
    const postObj = {
      "type": this.commonService.configuration["typeEvidenceDocument"],
      "siteCode": this["selectedSite"] ? this["selectedSite"].siteCode : this.commonService.configuration["allSiteCode"],
      "unitCode": this.commonService.configuration["allUnitCode"],
      "items": this.evidenceObj
    };
  
    this.dataService.postData(postObj, this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        this.evidenceObj = data["items"];
      }
        _this.loaderFlag = false;
        this.commonService.triggerToast({ type: 'success', title: "", msg: _this.labels['toasterUploadsuccess'] });
      })
  }

  closeViewedImage(): void {
    this.viewedImageIndex = null;
  }


  removeObservationImage(index: number) {
    var _this = this;
    if (index >= 0 && index < this.observation.evidenceDocument.items.length) {
      const removedImage = this.observation.evidenceDocument.items[index];
      this.observation.evidenceDocument.items.splice(index, 1);
      console.log('Updated observation:', this.observation);


      const evidenceIndex = this.evidenceObj.findIndex(item => item.externalId === removedImage.externalId);
    if (evidenceIndex !== -1) {
      this.evidenceObj.splice(evidenceIndex, 1);
    }

    
      const disEdge = {
        items: [{
          instanceType: "edge",
          externalId: `${this.observation.externalId}-${removedImage.externalId}`,
          space: this.observation.space
        }]
      };
  
      console.log('disEdge object:', disEdge);
      if (this.dataService.postData) {
        console.log('Calling deleteInstance API...');
        _this.dataService.postData(disEdge, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(
          (response) => {
            console.log('Image deleted successfully from the table', response);
          },
          (error) => {
            console.error('Error deleting image from the table', error);
          }
        );
      } else {
        console.error('postData method not found in dataService');
      }
    } else {
      console.error('Invalid index for image removal:', index);
    }
  }

  closeModal() {
    this.showModal = false;
    this.modalImage = '';
  }

  

showOverlay(index: number): void {
  this.showOverlayIndex = index;
}

closeOverlay(): void {
  this.showOverlayIndex = null;
}
  selectUrgency(item) {
    var _this = this;
    _this.selectedUrgency = item;
  }

  previousClickedCategory: any = null;
  newClickedCategory: any = null;
  categorySubcategoryState = new Map();
  

  isSelected(item: any): boolean {
    return this.selectedCategories.some(selected => selected.externalId === item.externalId);
  }
  
  
  handleCategoryCheckboxChange(checked: boolean, item: any) {
    if (checked) {
      this.selectCategory(item);
    } else {
      this.deselectCategory(item);
    }
  }
  
  switchCategory(category: any) {
    if (category !== this.lastSelectedCategory) {
      // Store current subcategory state before switching
      if (this.lastSelectedCategory) {
        this.categorySubcategoryState.set(this.lastSelectedCategory.externalId, this.subCategoryList);
      }
  
      this.lastSelectedCategory = category;
      console.log('Switching to category:', category);
  
      // Retrieve or initialize subcategory list based on the selected category
      if (this.categorySubcategoryState.has(category.externalId)) {
        // Restore subcategory state if it exists
        this.subCategoryList = this.categorySubcategoryState.get(category.externalId);
      } else {
        // Fetch subcategories for the selected category if state doesn't exist
        this.subCategoryList = this.getSubcategoriesForCategory(category);
      }
  
      console.log('subCategoryList:', this.subCategoryList);
    }
  }
  

  selectCategory(item) {
    console.log("item>>>>>>>>>>>>>>>>>")
    console.log(item)
    const isSelected = this.isSelected(item);
    const actionMode = history.state.action;
    if (!isSelected || (actionMode === "Edit" || actionMode === "View")) {
      // Store current subcategory state before switching
      if (this.lastSelectedCategory && actionMode !== "Edit" && actionMode !== "View") {
        this.categorySubcategoryState.set(this.lastSelectedCategory.externalId, this.subCategoryList);
      }
  
      console.log('Switching to category:', item);
      if (!isSelected) {
        this.selectedCategories.push(item);
      }
      this.lastSelectedCategory = item;
  
      // Restore subcategory state if it exists
      if (item && this.categorySubcategoryState && this.categorySubcategoryState.has(item.externalId)) {
        this.subCategoryList = this.categorySubcategoryState.get(item.externalId);
      } else {
        const childrenSubCategory = _.clone(this.processList).filter(e => {
          return (e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId;
        });
  
        this.subCategoryList = JSON.parse(JSON.stringify(childrenSubCategory));
        this.subCategoryList = this.subCategoryList.sort((a, b) => {
          if (a.sequence < b.sequence) { return -1; }
          if (a.sequence > b.name) { return 1; }
          return 0;
        });
      }
  
      // If no sub-categories, directly call questionFun and set isChecked
      if (this.subCategoryList.length == 0) {
        this.questionFun("Category", item["externalId"], item);
        console.log("subCategoryList ---->", item);
        item.isChecked = true; // Set isChecked if no sub-categories
        this.cd.detectChanges();
      }
  
      console.log('selectedCategories:', this.selectedCategories);
      console.log('subCategoryList:', this.subCategoryList);
      if(this.subCategoryList.length==1 && !this.isView && this.z==1){
        this.toggleSelectAllSubCategories(true)
      }
    }
  }
  
  
  deselectCategory(item) {
    const isSelected = this.isSelected(item);
    const actionMode = history.state.action;
    var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
    if(myIndex!=-1){
      this.tabsListArr.splice(myIndex, 1);
    }
    if (isSelected) {
      console.log('Deselecting category and subcategories:', item);
  
      // Remove the category from selectedCategories
      this.selectedCategories = this.selectedCategories.filter(selected => selected.externalId !== item.externalId);
      this.lastSelectedCategory = null;
      this.removeCategoryAndRelatedItems(item);
  
      // Clear subcategory list
      this.subCategoryList = [];
  
      // Clear subcategory state from memory
      this.categorySubcategoryState.delete(item.externalId);
  
      // If in edit mode, also clear subcategory selections in processList
      if (actionMode === "Edit" || actionMode === "View") {
        const childrenSubCategory = this.processList.filter(e => {
          return (e.refOFWAProcess && e.refOFWAProcess.externalId) === item.externalId;
        });
  
        childrenSubCategory.forEach(element => {
          element.selected = false;
          var myIndex = this.tabsListArr.findIndex(e=> e.id == element.id);
          if(myIndex!=-1){
            this.tabsListArr.splice(myIndex, 1);
          }
        });
      }
  
      console.log('selectedCategories:', this.selectedCategories);
      console.log('subCategoryList:', this.subCategoryList);
  
      
    }
  }
  
  
  toggleSelectAllSubCategories(isSelected: boolean): void {
    if (isSelected) {
      var _this = this;
      // Select all subcategories if not already selected
      this.subCategoryList.forEach(item => {
        if (!item.selected) {
          item.selected = true;
          // Check if subcategory is already in checklist array
          if (!this.checkListArray.some(checkListItem => checkListItem.id === item.refOFWAProcess.externalId)) {
            // Add subcategory to checklist array
            const categoryFormGroup = new FormGroup({
              isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
        isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
              activity: new FormControl(''),
              riskyAction: new FormControl(''),
              risk: new FormControl(''),
              riskAgreement: new FormControl('No'),
              reasonForAction: new FormControl(''),
              safeBehaviour: new FormControl('Possible'),
              suggestedSolution: new FormControl('')
            });
            
            var myCategory  = _this.commonService.processList.find(e => e.externalId == item.refOFWAProcess.externalId);
  
            const parentObj = {
              name: item.refOFWAProcess.name,
              bold: true,
              isCategory: true,
              id: item.refOFWAProcess.externalId,
              children: [],
              formGroup: categoryFormGroup,
              guidelineDocument:myCategory.guidelineDocument ? myCategory.guidelineDocument: undefined
            };
  
            this.checkListArray.push(parentObj);
            setTimeout(function(){
              _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
              _this.cd.detectChanges();
            }, 2000);
          } else {
            setTimeout(function(){
              _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
              _this.cd.detectChanges();
            }, 2000);
          }
        }
      });
    } else {
      // Deselect all subcategories
      const selectedSubcategoriesBefore = this.subCategoryList.filter(sub => sub.selected);
      this.subCategoryList.forEach(item => {
        item.selected = false;
        var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
        if(myIndex!=-1){
          this.tabsListArr.splice(myIndex, 1);
        }
      });
  
      // Deselect all selected subcategories individually
      selectedSubcategoriesBefore.forEach(subcategory => {
        this.deselectSubcategory(subcategory);
      });
    }
  
    // Log total number of subcategories selected
    const selectedSubcategoriesAfter = this.subCategoryList.filter(sub => sub.selected);
    console.log(`Total selected items: ${selectedSubcategoriesAfter.length}`);
  
    // this.treeExpand.treeControl.expandAll();
  }
  
  deselectSubcategory(subCategory: any): void {
    // Find the index of the parent item in checkListArray
    const parentIndex = this.checkListArray.findIndex(e => e.id === subCategory.refOFWAProcess.externalId);
  
    if (parentIndex !== -1) {
      // Find the index of the subCategory item in children array
      const myIndex = this.checkListArray[parentIndex].children.findIndex(e => e.id === subCategory.externalId);
  
      if (myIndex !== -1) {
        // Remove the subCategory item from children array
        this.checkListArray[parentIndex].children.splice(myIndex, 1);
        console.log('Removed item externalId:', subCategory.externalId);
  
        // Update the checklist array if parent has no more children
        if (this.checkListArray[parentIndex].children.length === 0) {
          this.checkListArray.splice(parentIndex, 1);
        }
  
        let tempArr = [];
        this.checkListArray.forEach(ele => {
          if (ele.children.length > 0) {
            ele.children.forEach(ele2 => {
              if (ele2.children.length > 0) {
                ele2.bold = true;
              }
            });
          }else{
            ele.bold = true;
          }
          tempArr.push(ele);
        });
        this.dataSource.data = tempArr;
  
        //this.treeExpand.treeControl.expandAll();
        console.log('Updated checkListArray:', this.checkListArray);
  
        this.removeCategoryAndRelatedItems(myIndex);
      } else {
        console.log('Subcategory with externalId', subCategory.externalId, 'not found in children array.');
      }
    } else {
      console.log('Parent item with externalId', subCategory.refOFWAProcess.externalId, 'not found in checkListArray.');
    }
  }
  
  isAllSubCategoriesSelected(): boolean {
    return this.subCategoryList.every(item => item.selected);
  }
  
  
  
  
  
  
  
  selectSubCategory(item) {
    const _this = this;
 
    // Check if item is already selected and not in edit or view mode
    if (item.selected && (!history.state.action || (history.state.action !== "Edit" && history.state.action !== "View"))) {
        item.selected = false;
        var myIndex = _this.tabsListArr.findIndex(e=> e.id == item.externalId);
        if(myIndex!=-1){
          _this.tabsListArr.splice(myIndex, 1);
        }
        _this.selectedSubCategory = null;
        _this.removeSubcategory(item);
 
        // Reset selected status for relevant categories
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = false;
            }
        });
    } else if (item.selected && history.state.action && (history.state.action === "Edit" || history.state.action === "View")) {
        // Ensure deselection in edit mode
        item.selected = false;
        _this.selectedSubCategory = null;
        _this.removeSubcategory(item);
 
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = false;
            }
        });
    } else {
        item.selected = true;
 
        // Update selected status for relevant categories
        _this.processList.forEach(element => {
            if (element.externalId === item.refOFWAProcess.externalId) {
                element.selected = true;
            }
        });
 
        if (_this.subCategoryList.findIndex(e => e.externalId === item.externalId) !== -1) {
            _this.subCategoryList[_this.subCategoryList.findIndex(e => e.externalId === item.externalId)].selected = true;
        }
    }
 
    this.selectedSubCategory = item;
 
    // Ensure it only affects relevant data structures, not refOFWAChecklist
    if (!_this.checkListArray.find(e => e.id === item.refOFWAProcess.externalId) && item.selected) {
        const categoryFormGroup = new FormGroup({
            isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
            isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
            note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
            activity: new FormControl(''),
            riskyAction: new FormControl(''),
            risk: new FormControl(''),
            riskAgreement: new FormControl('No'),
            reasonForAction: new FormControl(''),
            safeBehaviour: new FormControl('Possible'),
            suggestedSolution: new FormControl('')
        });
 
        var myCategory  = _this.commonService.processList.find(e => e.externalId == item.refOFWAProcess.externalId);
        const parentObj = {
            name: item.refOFWAProcess.name,
            bold: true,
            isCategory: true,
            id: item.refOFWAProcess.externalId,
            children: [],
            formGroup: categoryFormGroup,
            guidelineDocument:myCategory.guidelineDocument ? myCategory.guidelineDocument: undefined
        };
 
        _this.checkListArray.push(parentObj);
        _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
    } else {
        _this.questionFun("Sub Category", item.refOFWAProcess.externalId, item);
    }
 
    _this.treeExpand.treeControl.expandAll();
}
  
  
  
  
  
  
  // Add this method to fetch subcategories for a given category
  getSubcategoriesForCategory(item) {
    const childrenSubCategory = _.clone(this.processList).filter(e => {
      return (e.refOFWAProcess && e.refOFWAProcess.externalId) == item.externalId;
    });
  
    let subCategories = JSON.parse(JSON.stringify(childrenSubCategory));
    return subCategories.sort((a, b) => {
      if (a.sequence < b.sequence) { return -1; }
      if (a.sequence > b.sequence) { return 1; }
      return 0;
    });
  }
  
  
  
  removeCategoryAndRelatedItems(category) {
    const _this = this;
    const actionMode = history.state.action;
    // Array to store indices of items to be removed
    const itemsToRemoveIndices = [];
    // Array to store external IDs of all related subcategories
    const relatedSubCategoryExternalIds = [];
  
    // Remove the category itself from checkListArray
    const parentIndex = _this.checkListArray.findIndex(e => e.id === category.externalId);
    if (parentIndex !== -1) {
      console.log('Removing category:', category);
      _this.checkListArray.splice(parentIndex, 1);
      itemsToRemoveIndices.push(parentIndex); // Add parentIndex to the removal list
    }
  
    // Find related items in processList and remove them from checkListArray
    const relatedItems = _this.processList.filter(item => {
      return item.refOFWAProcess && item.refOFWAProcess.externalId === category.externalId;
    });
  
    relatedItems.forEach(item => {
      var myIndex = this.tabsListArr.findIndex(e=> e.id == item.externalId);
      if(myIndex!=-1){
        this.tabsListArr.splice(myIndex, 1);
      }
      const itemIndex = _this.checkListArray.findIndex(e => e.id === item.externalId);
      if (itemIndex !== -1) {
        console.log('Removing related item:', item);
        _this.checkListArray.splice(itemIndex, 1);
        itemsToRemoveIndices.push(itemIndex); // Add itemIndex to the removal list
      }
      // Add all related subcategory external IDs to the array
      relatedSubCategoryExternalIds.push(item.externalId);
    });
  
    // Log all related subcategory external IDs
    console.log('Related subcategory external IDs:', relatedSubCategoryExternalIds);
  
    // Update the UI or data structure as needed
    var tempArr = [];
    _this.checkListArray.forEach(ele => {
        if (ele.children && ele.children.length > 0) {
            ele.children.forEach(ele2 => {
                if (ele2.children && ele2.children.length > 0) {
                    ele2.bold = true;
                }
            }); 
            tempArr.push(ele);
        } else {
            // Ensure categories without subcategories are not removed
            tempArr.push(ele);
        }
    });
  
    _this.dataSource.data = tempArr;
    _this.treeExpand.treeControl.expandAll();
    console.log('Updated checkListArray:', _this.checkListArray);
  
    // Call removeChecklistItem for each related item
    relatedItems.forEach(item => {
      const itemIndex = _this.checkListArray.findIndex(e => e.id === item.externalId);
      if (itemIndex !== -1) {
        _this.removeChecklistItem(itemIndex);
      }
    });
  
    
  //  _this.removeMainChecklistItem(parentIndex);
  
  if (actionMode === "Edit" ) {
    _this.deleteAllRelatedSubCategoryExternalIds(relatedSubCategoryExternalIds);
}
   
  }
  
  deleteAllRelatedSubCategoryExternalIds(externalIds: string[]) {
    externalIds.forEach(externalId => {
      // Perform deletion logic here for each external ID
      const index = this.observation.refOFWAProcess.items.findIndex(item => item.externalId === externalId);
      if (index !== -1) {
        this.removeChecklistItem(index);
      } else {
        console.warn(`External ID ${externalId} not found in observation items.`);
      }
    });
  }
  
  // removeMainChecklistItem(index: number) {
  //   if (index >= 0 && index < this.observation.refOFWAProcess.items.length) {
  //     // Log the external ID of the item before deletion
  //     const removedItem = this.observation.refOFWAProcess.items[index];
  //     console.log('Removing item with externalId:', removedItem.externalId);
  
  //     // Remove the item from the array
  //     this.observation.refOFWAProcess.items.splice(index, 1);
  //     console.log('Updated observation:', this.observation);
  
  //     // Construct the disEdge object with the externalId of the removed item
  //     const disEdge = {
  //       items: [{
  //         instanceType: "edge",
  //         externalId: `${this.observation.externalId}-${removedItem.externalId}`,
  //         space: this.observation.space
  //       }]
  //     };
  
  //     console.log('disEdge object:', disEdge);
      
  //     // Check if dataService and postData are available
  //     if (this.dataService && this.dataService.postData) {
  //       console.log('Calling deleteInstance API...');
  //       // Call postData to delete the instance
  //       this.dataService.postData(disEdge, `${this.dataService.NODE_API}/api/service/deleteInstance`).subscribe(
  //         (response) => {
  //           console.log('Item deleted successfully from the table', response);
  //         },
  //         (error) => {
  //           console.error('Error deleting item from the table', error);
  //         }
  //       );
  //     } else {
  //       console.error('DataService or postData method not found.');
  //     }
  //   } else {
  //     console.error('Invalid index for item removal:', index);
  //   }
  // }
  
  
  
  
  removeChecklistItem(index: number) {
    if (index >= 0 && index < this.observation && this.observation.refOFWAProcess.items.length) {
      // Log the external ID of the item before deletion
      const removedItem = this.observation.refOFWAProcess.items[index];
      console.log('Removing item with externalId:', removedItem.externalId);
  
      // Remove the item from the array
      this.observation.refOFWAProcess.items.splice(index, 1);
      console.log('Updated observation:', this.observation);
  
      // Construct the disEdge object with the externalId of the removed item
      const disEdge = {
        items: [{
          instanceType: "edge",
          externalId: `${this.observation.externalId}-${removedItem.externalId}`,
          space: this.observation.space
        }]
      };
  
      console.log('disEdge object:', disEdge);
      
      // Check if dataService and postData are available
      if (this.dataService && this.dataService.postData) {
        console.log('Calling deleteInstance API...');
        // Call postData to delete the instance
        this.dataService.postData(disEdge, `${this.dataService.NODE_API}/api/service/deleteInstance`).subscribe(
          (response) => {
            console.log('Item deleted successfully from the table', response);
          },
          (error) => {
            console.error('Error deleting item from the table', error);
          }
        );
      } else {
        console.error('DataService or postData method not found.');
      }
    } else {
      console.error('Invalid index for item removal:', index);
    }
  }

  
  questionFun(type,categoryId,item:any) {
    console.log("categoryId ----->",categoryId)
    console.log("item ----->",item)
    var _this = this;
    if (type === "SubProcess") {
        var primaryQuestion = _this.questionList.filter(e => e["refOFWASubProcess"]["externalId"] == categoryId);
        console.log(primaryQuestion)
        if (primaryQuestion.length == 0) {
          _this.loaderCount++;
          _this.dataService.postData({ "externalId": categoryId }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
            console.log(resData)
            _this.loaderCount--;
            var primaryQuestionList = (resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"]).filter(item => item.refOFWACategory === null);
            console.log(primaryQuestionList)
            _this.questionList = _.concat(_this.questionList, primaryQuestionList);
            
            // Add primary question to checklist
            if(primaryQuestionList.length > 0){
            _this.addPrimaryQuestion(item);
            _this.checklistPrimaryQuestion(item);}
            _this.cd.detectChanges();
          });
        } else {
          // Add primary question if already fetched
          _this.addPrimaryQuestion(item);
          _this.checklistPrimaryQuestion(item);
          _this.cd.detectChanges();
        }
      } else {
        console.log(_this.questionList)
        var myQuestions = _this.questionList.filter(e => e["refOFWACategory"] && e["refOFWACategory"]["externalId"] == categoryId);

    console.log(myQuestions)
    if (myQuestions.length == 0) {
      _this.loaderCount ++;
      _this.dataService.postData({ "externalId": categoryId }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
        _this.loaderCount --;
        var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
        _this.questionList = _.concat(_this.questionList, myQuestionList)
        if(type == "Category"){
          _this.addCategory(item);
          _this.checklistCategoryQuestion(item);
        }else{
          _this.checkListUpdate(item);
          _this.checklistQuestion(item);
        }
        
      })
    } else {
      if(type == "Category"){
        _this.addCategory(item);
        _this.checklistCategoryQuestion(item);
      }else{
        _this.checkListUpdate(item);
        _this.checklistQuestion(item);
      }
    }
    //_this.treeExpand.treeControl.expandAll();
  }}


  addPrimaryQuestion(item) {
    const _this = this;
    let primaryQuestionFormGroup;
    let primaryQuestionAns = null;
  console.log(this.observation,)
    // Fetch answers for the primary question (if applicable)
    if (this.observation) {
      primaryQuestionAns = this.observation.refOFWAChecklist.items.find(e => e.refOFWASubProcess && e.refOFWASubProcess.externalId === item.externalId);
    }
  
    if (primaryQuestionAns) {
      let ansVal;
      let isSafe = "";
  
      if (primaryQuestionAns.isSafe) isSafe = "safe";
      if (primaryQuestionAns.isUnSafe) isSafe = "unsafe";
      if (primaryQuestionAns.isNotObserved) isSafe = "notObserved";
  
      primaryQuestionFormGroup = new FormGroup({
        isSafe: new FormControl(isSafe),
        note: new FormControl({
          value: primaryQuestionAns.note || '',
          disabled: _this.isView 
            ? true 
            : primaryQuestionAns.note 
              ? false 
              : ansVal 
                ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") 
                : _this.defaultItem?.isNotes === "No"
        }),
        activity: new FormControl(primaryQuestionAns.activity),
        riskyAction: new FormControl(primaryQuestionAns.riskyAction),
        risk: new FormControl(primaryQuestionAns.risk),
        riskAgreement: new FormControl(primaryQuestionAns.riskAgreement),
        reasonForAction: new FormControl(primaryQuestionAns.reasonForAction),
        safeBehaviour: new FormControl(primaryQuestionAns.safeBehaviour),
        suggestedSolution: new FormControl(primaryQuestionAns.suggestedSolution)
      });
      
      if (_this.isView) primaryQuestionFormGroup.disable();
  
    } else {
      primaryQuestionFormGroup = new FormGroup({
        isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
        note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
        activity: new FormControl(''),
        riskyAction: new FormControl(''),
        risk: new FormControl(''),
        riskAgreement: new FormControl('No'),
        reasonForAction: new FormControl(''),
        safeBehaviour: new FormControl('Possible'),
        suggestedSolution: new FormControl('')
      });
    }
  
    const primaryObj = {
      name: item.name,
      bold: true,
      isPrimaryQuestion: true,
      id: item.externalId,
      children: [], // Initialize children as an empty array
      formGroup: primaryQuestionFormGroup,
      guidelineDocument: item.guidelineDocument ? item.guidelineDocument : undefined
    };
  
    _this.checkListArray.push(primaryObj);
    _this.updateBoldProperties();
    _this.expandAll();
    
    _this.cd.detectChanges();
  }
  checklistPrimaryQuestion(primaryQuestion: any) {
    const _this = this;
    console.log("Primary Question Index");
    console.log("checklistarray---> ", _this.checkListArray)
  
    // Find the index of the primary question in the checklist array
    const primaryQuestionIndex = _this.checkListArray.findIndex(e => e && e.id == primaryQuestion["externalId"]);
    console.log(primaryQuestionIndex);
  
    // if (primaryQuestionIndex != -1) {
    //   // Filter the questions related to the primary question (previously subProcess)
    //   const myQuestion = _this.questionList.filter(e => e["refOFWASubProcess"]["externalId"] == primaryQuestion["externalId"]);
    //   console.log(myQuestion);
  
    //   if (myQuestion.length > 0) {
    //     if (_this.checkListArray[primaryQuestionIndex]["children"].length === 0) {
    //       myQuestion.forEach((question) => {
    //         let primaryQuestionFormGroup;
    //         const questionExist = _this.checkListArray[primaryQuestionIndex]["children"].find(e => e.id == question["externalId"]);
  
    //         if (!questionExist) {
    //           // Handle existing answer logic if `observation` exists
    //           if (_this.observation) {
    //             const questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => 
    //               (e.refOFWAPrimaryQuestion && e.refOFWAPrimaryQuestion.externalId === primaryQuestion.externalId) && 
    //               (e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]
    //             );
    //             console.log(questionAns,primaryQuestion);
  
    //             if (questionAns) {
    //               let ansVal;
    //               let isSafe = "";
    //               if (questionAns.isSafe) {
    //                 isSafe = "safe";
    //                 ansVal = "safe";
    //               }
    //               if (questionAns.isUnSafe) {
    //                 isSafe = "unsafe";
    //                 ansVal = "notsafe";
    //               }
    //               if (questionAns.isNotObserved) {
    //                 isSafe = "notObserved";
    //                 ansVal = "notobserved";
    //               }
  
    //               primaryQuestionFormGroup = new FormGroup({
    //                 isSafe: new FormControl(isSafe),
    //                 isInjuryPotential: new FormControl({ value: questionAns.isInjuryPotential ? "Yes" : "No", disabled: true }),
    //                 note: new FormControl({ value: questionAns.note || '', disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
    //                 activity: new FormControl(questionAns.activity),
    //                 riskyAction: new FormControl(questionAns.riskyAction),
    //                 risk: new FormControl(questionAns.risk),
    //                 riskAgreement: new FormControl(questionAns.riskAgreement),
    //                 reasonForAction: new FormControl(questionAns.reasonForAction),
    //                 safeBehaviour: new FormControl(questionAns.safeBehaviour),
    //                 suggestedSolution: new FormControl(questionAns.suggestedSolution)
    //               });
    //             } else {
    //               // Create new form group with default values if there's no existing answer
    //               primaryQuestionFormGroup = new FormGroup({
    //                 isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
    //                 isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
    //                 note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
    //                 activity: new FormControl(''),
    //                 riskyAction: new FormControl(''),
    //                 risk: new FormControl(''),
    //                 riskAgreement: new FormControl('No'),
    //                 reasonForAction: new FormControl(''),
    //                 safeBehaviour: new FormControl('Possible'),
    //                 suggestedSolution: new FormControl('')
    //               });
    //             }
    //           } else {
    //             // No observation data, creating default form group
    //             primaryQuestionFormGroup = new FormGroup({
    //               isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
    //               isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
    //               note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
    //               activity: new FormControl(''),
    //               riskyAction: new FormControl(''),
    //               risk: new FormControl(''),
    //               riskAgreement: new FormControl('No'),
    //               reasonForAction: new FormControl(''),
    //               safeBehaviour: new FormControl('Possible'),
    //               suggestedSolution: new FormControl('')
    //             });
    //           }
  
    //           // Add the primary question to the checklist
    //           _this.checkListArray[primaryQuestionIndex]["children"].push({
    //             name: question["name"],
    //             question: question["description"],
    //             sequence: question["sequence"] ? question["sequence"] : question["name"],
    //             bold: false,
    //             id: question["externalId"],
    //             formGroup: primaryQuestionFormGroup
    //           });
  
    //           // Adjust bold property and sorting logic
    //           const tempArr = [];
    //           _this.checkListArray.forEach(ele => {
    //             if (ele.children.length > 0) {
    //               ele.children.forEach(ele2 => {
    //                 if (ele2.question) {
    //                   ele2.bold = false;
    //                   ele.bold = true;
    //                 }
    //               });
    //             } else {
    //               ele.bold = false;
    //             }
    //             tempArr.push(ele);
    //           });
    //           _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
    //           _this.expandAll();
    //         }
    //       });
    //     }
    //   }
    // }
    if (primaryQuestionIndex != -1) {
      // Filter the questions related to the primary question (previously subProcess)
      const myQuestion = _this.questionList.filter(e => e["refOFWASubProcess"]["externalId"] === primaryQuestion["externalId"]);
      console.log(myQuestion);
    
      if (myQuestion.length > 0) {
        if (_this.checkListArray[primaryQuestionIndex]["children"].length === 0) {
          myQuestion.forEach((question) => {
            let primaryQuestionFormGroup;
            const questionExist = _this.checkListArray[primaryQuestionIndex]["children"].find(e => e.id === question["externalId"]);
    
            if (!questionExist) {
              // Handle existing answer logic if `observation` exists
              if (_this.observation) {
                var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => 
                  (e.refOFWASubProcess && e.refOFWASubProcess.externalId === primaryQuestion.externalId) && 
                  (e.refOFWAQuestion && e.refOFWAQuestion.externalId === question["externalId"])
                );
                console.log(questionAns, primaryQuestion);
    
                if (questionAns) {
                  let ansVal = '';
                  let isSafe = '';
    
                  // Set the safety status based on the observation answer
                  if (questionAns.isSafe) {
                    isSafe = "safe";
                    ansVal = "safe";
                  }
                  if (questionAns.isUnSafe) {
                    isSafe = "unsafe";
                    ansVal = "notsafe";
                  }
                  if (questionAns.isNotObserved) {
                    isSafe = "notObserved";
                    ansVal = "notobserved";
                  }
    
                  // Create a form group with the observation answer data
                  primaryQuestionFormGroup = new FormGroup({
                    isSafe: new FormControl(isSafe),
                    isInjuryPotential: new FormControl({ value: questionAns.isInjuryPotential ? "Yes" : "No", disabled: true }), 
                    note: new FormControl({
                      value: questionAns.note || '',
                      disabled: _this.isView 
                        ? true 
                        : questionAns.note 
                          ? false 
                          : ansVal 
                            ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") 
                            : _this.defaultItem?.isNotes === "No"
                    }),                  
                    activity: new FormControl(questionAns.activity),
                    riskyAction: new FormControl(questionAns.riskyAction),
                    risk: new FormControl(questionAns.risk),
                    riskAgreement: new FormControl(questionAns.riskAgreement),
                    reasonForAction: new FormControl(questionAns.reasonForAction),
                    safeBehaviour: new FormControl(questionAns.safeBehaviour),
                    suggestedSolution: new FormControl(questionAns.suggestedSolution)
                  });
                  if (_this.isView) primaryQuestionFormGroup.disable();
                  _this.cd.detectChanges();
                } else {
                  console.log(_this.defaultItem)
                  // Create a new form group with default values if no observation answer is found
                  primaryQuestionFormGroup = new FormGroup({
                    isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
                    isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
                    note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                    activity: new FormControl(''),
                    riskyAction: new FormControl(''),
                    risk: new FormControl(''),
                    riskAgreement: new FormControl('No'),
                    reasonForAction: new FormControl(''),
                    safeBehaviour: new FormControl('Possible'),
                    suggestedSolution: new FormControl('')
                  });
                }
              } else {
                // Create a default form group when there's no observation data
                console.log(_this.defaultItem)
                primaryQuestionFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
                  isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
                  note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }
    
              // Add the primary question to the checklist array
              _this.checkListArray[primaryQuestionIndex]["children"].push({
                name: question["name"],
                question: question["description"],
                sequence: question["sequence"] ? question["sequence"] : question["name"],
                bold: false,
                id: question["externalId"],
                formGroup: primaryQuestionFormGroup,
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
              });
    
              // Adjust bold property and sorting logic
              const tempArr = [];
              _this.checkListArray.forEach(ele => {
                if (ele.children.length > 0) {
                  ele.children.forEach(ele2 => {
                    if (ele2.question) {
                      ele2.bold = false;
                      ele.bold = true;
                    }
                  });
                } else {
                  ele.bold = false;
                }
                tempArr.push(ele);
              });
              _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
              _this.expandAll();
            }
          });
        }
      }
    }
    
    _this.cd.detectChanges();
    
  }
  
  
  addCategory(item) {
    const _this = this;
    let subCategoryFormGroup;
    let subCategoryAns = null;
  
    if (this.observation) {
      subCategoryAns = this.observation.refOFWAChecklist.items.find(e => {
        const subCategoryMatch = !e.refOFWACategory || e.refOFWACategory.externalId === item.externalId;
        return subCategoryMatch && !e.refOFWAQuestion;
      });
    }
    if (subCategoryAns) {
      var ansVal;
      // Populate form group with existing data
      let isSafe = "";
      if (subCategoryAns.isSafe) {
        isSafe = "safe";
        ansVal = "safe";
      }
      if (subCategoryAns.isUnSafe) {
        isSafe = "unsafe";
        ansVal = "notsafe";
      } else if (subCategoryAns.isNotObserved) {
        isSafe = "notObserved";
        ansVal = "notobserved";
      }
      if(ansVal){
        _this.newConfigDetail[ansVal]["isNotes"]
      }else{
        _this.defaultItem?.isNotes === "No"
      }
      
      subCategoryFormGroup = new FormGroup({
        isSafe: new FormControl(isSafe),
        isInjuryPotential: new FormControl({ value: subCategoryAns.isInjuryPotential ? "Yes" : "No", disabled: true}),
        note: new FormControl({ value: subCategoryAns.note || '', disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
        activity: new FormControl(subCategoryAns.activity),
        riskyAction: new FormControl(subCategoryAns.riskyAction),
        risk: new FormControl(subCategoryAns.risk),
        riskAgreement: new FormControl(subCategoryAns.riskAgreement),
        reasonForAction: new FormControl(subCategoryAns.reasonForAction),
        safeBehaviour: new FormControl(subCategoryAns.safeBehaviour),
        suggestedSolution: new FormControl(subCategoryAns.suggestedSolution)
      });
  
      if (_this.isView) {
        subCategoryFormGroup.disable();
      }
    } else {
      // Create new form group with default values
      subCategoryFormGroup = new FormGroup({
        isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
        isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
        note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
        activity: new FormControl(''),
        riskyAction: new FormControl(''),
        risk: new FormControl(''),
        riskAgreement: new FormControl('No'),
        reasonForAction: new FormControl(''),
        safeBehaviour: new FormControl('Possible'),
        suggestedSolution: new FormControl('')
      });
    }
  
    const parentObj = {
      name: item.name,
      bold: true,
      isCategory: true,
      id: item.externalId,
      children: [], // Initialize children as an empty array
      formGroup: subCategoryFormGroup,
      guidelineDocument:item.guidelineDocument ? item.guidelineDocument: undefined
    };
  
    if(subCategoryAns && subCategoryAns.isUnSafe){
      _this.radioButton(parentObj)
    }
    _this.checkListArray.push(parentObj);
  
    // Update bold property based on children presence
    _this.updateBoldProperties();
  
    _this.expandAll();
  }

  checklistCategoryQuestion(category: any) {
    var _this = this;
    console.log("categoryIndex")
    console.log("checklistarray---> ", _this.checkListArray)
    
    var categoryIndex = _this.checkListArray.findIndex(e => e && e.id == category["externalId"]);
    console.log(categoryIndex)
   
    if (categoryIndex != -1) {
      console.log(_this.questionList, category)
      var myQuestion = _this.questionList.filter(e => e["refOFWACategory"] && e["refOFWACategory"]["externalId"] == category["externalId"]);
      console.log(myQuestion)
      if (myQuestion.length > 0 ) {
        if (_this.checkListArray[categoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var categoryFormGroup;
            var questionExist = _this.checkListArray[categoryIndex]["children"].find(e=>e.id ==question["externalId"])
            if(!questionExist){
            if (_this.observation) {
              console.log(_this.observation["refOFWAChecklist"]["items"],category,question )
              var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => ((e.refOFWACategory &&e.refOFWACategory.externalId) == category.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              if (questionAns) {
                var ansVal;
                var isSafe = "";
                if (questionAns.isSafe) {
                  isSafe = "safe";
                  ansVal = "safe";
                }
                if (questionAns.isUnSafe) {
                  isSafe = "unsafe";
                  ansVal = "notsafe";
                }
                if (questionAns.isNotObserved) {
                  isSafe = "notObserved";
                  ansVal = "notobserved";
                }
                var isInjuryPotential = "No";
                if (questionAns.isInjuryPotential) {
                  isInjuryPotential = "Yes"
                }
                
                if (ansVal.length > 0) {
                  _this.newConfigDetail[ansVal]["isNotes"]
                } else {
                  _this.defaultItem?.isNotes === "No"
                }
                categoryFormGroup = new FormGroup({
                  isSafe: new FormControl(isSafe),
                  // isInjuryPotential: new FormControl(isInjuryPotential),
                  isInjuryPotential:new FormControl({value:isInjuryPotential ? isInjuryPotential: _this.defaultItem.isInjuryPotential ,disabled: true }),
                  // note: new FormControl(questionAns.note),
                  note: new FormControl({ value: questionAns? questionAns.note  : "", disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
                  activity: new FormControl(questionAns.activity),
                  riskyAction: new FormControl(questionAns.riskyAction),
                  risk: new FormControl(questionAns.risk),
                  riskAgreement: new FormControl(questionAns.riskAgreement),
                  reasonForAction: new FormControl(questionAns.reasonForAction),
                  safeBehaviour: new FormControl(questionAns.safeBehaviour),
                  suggestedSolution: new FormControl(questionAns.suggestedSolution)
                });
                if (_this.isView) {
                  categoryFormGroup.disable();
                }
              } else {
                categoryFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                  isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }

            } else {
              categoryFormGroup = new FormGroup({
                isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                activity: new FormControl(''),
                riskyAction: new FormControl(''),
                risk: new FormControl(''),
                riskAgreement: new FormControl('No'),
                reasonForAction: new FormControl(''),
                safeBehaviour: new FormControl('Possible'),
                suggestedSolution: new FormControl('')
              });
            }


           
            _this.checkListArray[categoryIndex]["children"].push({
              name: question["name"],
              question: question["description"],
              sequence:question["sequence"] ? question["sequence"] : question["name"],
              bold: false,
              id: question["externalId"],
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
              formGroup: categoryFormGroup
            })

          }
          var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                console.log(ele2)
                if(ele2.question){
                  ele2.bold = false;
                  ele.bold = true;
                }
                // if(ele2.children.length > 0){
                //   ele2.bold = true
                // }
              });
            }else{
              ele.bold = false;
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
          console.log(_this.dataSource.data)
          })
          
          
        }

      }else{
        var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                console.log(ele2)
                if(ele2.children.length > 0){
                  ele2.bold = true
                }else{
                  ele2.bold = false;
                }
              });
            }else{
              ele.bold = false
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
      }
    }

  }
  
  updateBoldProperties() {
    var _this = this;
    // Iterate through checkListArray to update bold property based on children presence
    _this.checkListArray.forEach(ele => {
      ele.bold = ele.children && ele.children.length > 0;
    });
    console.log(_this.checkListArray)
  
    // Sort checkListArray by 'sequence' if applicable
    _this.dataSource.data = _.sortBy(_this.checkListArray, ['sequence']);
  }
  

  removeSubcategory(subCategory) {
    const _this = this;
    
    // Find the index of the parent item in checkListArray
    const parentIndex = _this.checkListArray.findIndex(e => e.id === subCategory.refOFWAProcess.externalId);
    
    if (parentIndex !== -1) {
      // Find the index of the subCategory item in children array
      const myIndex = _this.checkListArray[parentIndex].children.findIndex(e => e.id === subCategory.externalId);
      
      if (myIndex !== -1) {
        // Remove the subCategory item from children array
        _this.checkListArray[parentIndex].children.splice(myIndex, 1);
        
        // Log the removed item's externalId
        console.log('Removed item externalId:', subCategory.externalId);
        
        // Update dataSource.data if needed
        let tempArr = [];
        _this.checkListArray.forEach(ele => {
          if (ele.children.length > 0) {
            ele.children.forEach(ele2 => {
              if (ele2.children && ele2.children.length > 0) {
                ele2.bold = true;
              }
            });
          }else{
            ele.bold = false;
          }
          tempArr.push(ele);
        });
        _this.dataSource.data = tempArr;
        
        // Expand all items in tree control
        _this.expandAll();
        
        // Optionally log the updated checkListArray for verification
        console.log('Updated checkListArray:', _this.checkListArray);
  
        // Call removeChecklistItem for the subCategory item
        _this.removeChecklistItem(myIndex);
      } else {
        console.log('Subcategory with externalId', subCategory.externalId, 'not found in children array.');
      }
    } else {
      console.log('Parent item with externalId', subCategory.refOFWAProcess.externalId, 'not found in checkListArray.');
    }
  }

  checkListUpdate(subCategory: any) {
    const _this = this;
    console.log("checklist array", _this.checkListArray);
    const parentIndex = _this.checkListArray.findIndex(e => e && e.id === subCategory.refOFWAProcess.externalId);
    const myIndex = _this.checkListArray.findIndex(e => e && e.id === subCategory.externalId);
  
    if (subCategory.selected === true) {
      let subCategoryFormGroup;
      let subCateggoryAns = null;
  
      if (this.observation) {
    subCateggoryAns = this.observation.refOFWAChecklist.items.find(e => {
        const subCategoryMatch = e.refOFWASubCategory && e.refOFWASubCategory.externalId === subCategory.externalId;
        return subCategoryMatch && (!e.refOFWAQuestion || !e.refOFWAQuestion.externalId );
    });
}

  
      if (subCateggoryAns) {
        // Populate form group with existing data
        var ansVal;
        let isSafe = "";
        if (subCateggoryAns.isSafe) {
          isSafe = "safe";
          ansVal = "safe";
        }
        if (subCateggoryAns.isUnSafe) {
          isSafe = "unsafe";
          ansVal = "notsafe";
        } else if (subCateggoryAns.isNotObserved) {
          isSafe = "notObserved";
          ansVal = "notobserved";
        }
        if (ansVal) {
          _this.newConfigDetail[ansVal]["isNotes"]
        } else {
          _this.defaultItem?.isNotes === "No"
        }
        subCategoryFormGroup = new FormGroup({
          isSafe: new FormControl(isSafe),
          isInjuryPotential: new FormControl({ value: subCateggoryAns.isInjuryPotential ? "Yes" : "No", disabled: true }),
          note: new FormControl({ value: subCateggoryAns.note || '', disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No" }),
          activity: new FormControl(subCateggoryAns.activity),
          riskyAction: new FormControl(subCateggoryAns.riskyAction),
          risk: new FormControl(subCateggoryAns.risk),
          riskAgreement: new FormControl(subCateggoryAns.riskAgreement),
          reasonForAction: new FormControl(subCateggoryAns.reasonForAction),
          safeBehaviour: new FormControl(subCateggoryAns.safeBehaviour),
          suggestedSolution: new FormControl(subCateggoryAns.suggestedSolution)
        });
  
        if (_this.isView) {
          subCategoryFormGroup.disable();
        }
      } else {
        // Create a new form group with default values
        subCategoryFormGroup = new FormGroup({
          isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value : ""),
          isInjuryPotential: new FormControl({ value: _this.defaultItem ? _this.defaultItem.isInjuryPotential : "", disabled: true }),
          note: new FormControl({ value: '', disabled: _this.defaultItem && _this.defaultItem.isNotes === "No" }),
          activity: new FormControl(''),
          riskyAction: new FormControl(''),
          risk: new FormControl(''),
          riskAgreement: new FormControl('No'),
          reasonForAction: new FormControl(''),
          safeBehaviour: new FormControl('Possible'),
          suggestedSolution: new FormControl('')
        });
      }
  
      const nodeObj = {
        name: subCategory.name,
        bold: false, // Ensure subcategories are not bolded
        id: subCategory.externalId,
        sequence:subCategory.sequence,
        children: [],
        checkListId: subCateggoryAns ? subCateggoryAns.externalId : "",
        checkListSpace: subCateggoryAns ? subCateggoryAns.space : "",
        formGroup: subCategoryFormGroup,
        guidelineDocument:subCategory.guidelineDocument ? subCategory.guidelineDocument: undefined
      };

      // if(subCateggoryAns && subCateggoryAns.isUnSafe){
      //   _this.radioButton(nodeObj)
      // }
  
      // Check if myIndex is -1 and add the node to checkListArray
      if (myIndex === -1) {
        // If parentIndex is found, add to children of that category
        if (parentIndex !== -1) {
          _this.checkListArray[parentIndex].children.push(nodeObj);
          _this.checkListArray[parentIndex].children = _.sortBy(_this.checkListArray[parentIndex].children, ['sequence']);
          console.log(_this.checkListArray[parentIndex])
         if(_this.observation && _this.observation.refOFWAChecklist){
            var subCateggoryAnsN = _this.observation.refOFWAChecklist.items.find(e => {
              const subCategoryMatch = e.refOFWASubCategory && e.refOFWAQuestion  && e.refOFWASubCategory.externalId === subCategory.externalId;
              return subCategoryMatch 
          });
            if(subCateggoryAns && subCateggoryAns.isUnSafe && !subCateggoryAnsN){
              _this.radioButton(nodeObj)
            }
          }
        } else {
          // Add at the end of the array
        var myCategory  = _this.commonService.processList.find(e => e.externalId == subCategory.refOFWAProcess.externalId);

          _this.checkListArray.push({
            name: subCategory.refOFWAProcess.name,
            bold: true, // Ensure categories with children are bolded
            id: subCategory.refOFWAProcess.externalId,
            children: [nodeObj],
            guidelineDocument:myCategory.guidelineDocument ? myCategory.guidelineDocument: undefined
          });
        }
  
        // Sort checkListArray by id after adding new node
        _this.checkListArray.sort((a, b) => a.id.localeCompare(b.id));
  
        // Update dataSource.data after sorting
        _this.dataSource.data = _.sortBy(_this.checkListArray, ['id']);
      }
    }
  
    // Ensure UI updates
    _this.expandAll();
    _this.checklistQuestion(subCategory);
  }
  

//   onSelectChange(a:any){
//     var _this = this;
//     if (a.controls.isInjuryPotential.value === 'Yes') {
//       a.controls.note.enable()
//   } else if (a.controls.isInjuryPotential.value === 'No') {
//     a.controls.note.disable() 
//   }
//   if (_this.newConfigDetail.notobserved.isNotes===false  
//     || _this.newConfigDetail.notobserved.isNotes=="No" || _this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No" ||
//     _this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No")
//     a.controls.note.disable() 

// }


  onCheckboxChange(a:any) {
    var _this = this;
    // SAFE
    if (a.value.isSafe === 'safe') {
      if(_this.newConfigDetail.safe.isInjuryPotential!="null"){
        a.controls.isInjuryPotential.disable()
        if(_this.newConfigDetail.safe.isInjuryPotential===false || _this.newConfigDetail.safe.isInjuryPotential=="No")
          {
            a.controls.isInjuryPotential.setValue('No')
            if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
              a.controls.note.disable()
            }
            else a.controls.note.enable()
        }
        else
        {
          a.controls.isInjuryPotential.setValue('Yes')
          if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
            a.controls.note.disable()
          }
          else a.controls.note.enable()
        }
    }
    else
    {
      a.controls.isInjuryPotential.enable()
      a.controls.isInjuryPotential.setValue('')
      if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
  }
  // UNSAFE
  if (a.value.isSafe === 'unsafe') {
    if(_this.newConfigDetail.notsafe.isInjuryPotential!="null"){
      a.controls.isInjuryPotential.disable()
      if(_this.newConfigDetail.notsafe.isInjuryPotential===false || _this.newConfigDetail.notsafe.isInjuryPotential=="No")
        {
          a.controls.isInjuryPotential.setValue('No')
          if(_this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No"){
            a.controls.note.disable()
          }
          else a.controls.note.enable()
      }
      else
      {
        a.controls.isInjuryPotential.setValue('Yes')
        if(_this.newConfigDetail.notsafe.isNotes===false || _this.newConfigDetail.notsafe.isNotes=="No"){
          a.controls.note.disable()
        }
        else a.controls.note.enable()
      }
  }
  else
  {
    a.controls.isInjuryPotential.enable()
    a.controls.isInjuryPotential.setValue('')
    if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
      a.controls.note.disable()
    }
    else a.controls.note.enable()
  }
}
// NOT OBSERVED
if (a.value.isSafe === 'notObserved') {
  if(_this.newConfigDetail.notobserved.isInjuryPotential!="null"){
    a.controls.isInjuryPotential.disable()
    if(_this.newConfigDetail.notobserved.isInjuryPotential===false || _this.newConfigDetail.notobserved.isInjuryPotential=="No")
      {
        a.controls.isInjuryPotential.setValue('No')
        if(_this.newConfigDetail.notobserved.isNotes===false || _this.newConfigDetail.notobserved.isNotes=="No"){
          a.controls.note.disable()
        }
        else a.controls.note.enable()
    }
    else
    {
      a.controls.isInjuryPotential.setValue('Yes')
      if(_this.newConfigDetail.notobserved.isNotes===false || _this.newConfigDetail.notobserved.isNotes=="No"){
        a.controls.note.disable()
      }
      else a.controls.note.enable()
    }
}
else
{
  a.controls.isInjuryPotential.enable()
  a.controls.isInjuryPotential.setValue('')
  if(_this.newConfigDetail.safe.isNotes===false || _this.newConfigDetail.safe.isNotes=="No"){
    a.controls.note.disable()
  }
  else a.controls.note.enable()
}
}
  }

  checklistQuestion(subCategory: any) {
    var _this = this;
    var categoryIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    if (categoryIndex != -1) {
      var subCategoryIndex = _this.checkListArray[categoryIndex].children.findIndex(e => e["id"] == subCategory["externalId"]);
      var myQuestion = _this.questionList.filter(e => e["refOFWASubCategory"] && e["refOFWASubCategory"]["externalId"] == subCategory["externalId"]);
      if (myQuestion.length > 0 && subCategoryIndex != -1) {
        if (_this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var subCategoryFormGroup;
            var questionExist = _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].find(e=>e.id ==question["externalId"])
            if(!questionExist){
            if (_this.observation) {
              var questionAns = _this.observation["refOFWAChecklist"]["items"].find(e => (e.refOFWASubCategory.externalId == subCategory.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              if (questionAns) {
                var ansVal;
                var isSafe = "";
                if (questionAns.isSafe) {
                  isSafe = "safe";
                  ansVal = "safe";
                }
                if (questionAns.isUnSafe) {
                  isSafe = "unsafe";
                  ansVal = "notsafe";
                }
                if (questionAns.isNotObserved) {
                  isSafe = "notObserved";
                  ansVal = "notobserved";
                }
                var isInjuryPotential = "No";
                if (questionAns.isInjuryPotential) {
                  isInjuryPotential = "Yes"
                }
                if(ansVal.length>0){
                  _this.newConfigDetail[ansVal]["isNotes"]
                }else{
                  _this.defaultItem?.isNotes === "No"
                }
                subCategoryFormGroup = new FormGroup({
                  isSafe: new FormControl(isSafe),
                  // isInjuryPotential: new FormControl(isInjuryPotential),
                  isInjuryPotential:new FormControl({value:isInjuryPotential ? isInjuryPotential: _this.defaultItem.isInjuryPotential ,disabled: true }),
                  // note: new FormControl(questionAns.note),
                  note: new FormControl({ value: questionAns? questionAns.note  : "", disabled: ansVal ? (_this.newConfigDetail[ansVal]["isNotes"] == false || _this.newConfigDetail[ansVal]["isNotes"] == "No") : _this.defaultItem?.isNotes === "No"  }),
                  activity: new FormControl(questionAns.activity),
                  riskyAction: new FormControl(questionAns.riskyAction),
                  risk: new FormControl(questionAns.risk),
                  riskAgreement: new FormControl(questionAns.riskAgreement),
                  reasonForAction: new FormControl(questionAns.reasonForAction),
                  safeBehaviour: new FormControl(questionAns.safeBehaviour),
                  suggestedSolution: new FormControl(questionAns.suggestedSolution)
                });
                if (_this.isView) {
                  subCategoryFormGroup.disable();
                }
              } else {
                subCategoryFormGroup = new FormGroup({
                  isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                  isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                  activity: new FormControl(''),
                  riskyAction: new FormControl(''),
                  risk: new FormControl(''),
                  riskAgreement: new FormControl('No'),
                  reasonForAction: new FormControl(''),
                  safeBehaviour: new FormControl('Possible'),
                  suggestedSolution: new FormControl('')
                });
              }

            } else {
              subCategoryFormGroup = new FormGroup({
                isSafe: new FormControl(_this.defaultItem ? _this.defaultItem.value  : ""),
                isInjuryPotential: new FormControl({value:_this.defaultItem ? _this.defaultItem.isInjuryPotential  : "" ,disabled: true }),
          note: new FormControl({ value: '', disabled: (_this.defaultItem && _this.defaultItem.isNotes) === "No" }),
                activity: new FormControl(''),
                riskyAction: new FormControl(''),
                risk: new FormControl(''),
                riskAgreement: new FormControl('No'),
                reasonForAction: new FormControl(''),
                safeBehaviour: new FormControl('Possible'),
                suggestedSolution: new FormControl('')
              });
            }


           
            _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].push({
              name: question["name"],
              question: question["description"],
              bold: false,
              sequence:question["sequence"] ? question["sequence"] : question["name"],
              id: question["externalId"],
              checkListId: questionAns ? questionAns.externalId :"",
              checkListSpace: questionAns ? questionAns.space : "",
              formGroup: subCategoryFormGroup
            })

          }
          var tempArr = []
          this.checkListArray.forEach(ele =>{
            if(ele.children.length > 0){
              ele.children.forEach(ele2 => {
                if(ele2.children && ele2.children.length > 0){
                  ele2.bold = true
                }else{
                  ele2.bold = false
                }
              });
              // tempArr.push(ele)
            }else{
              ele.bold = false
            }
            tempArr.push(ele)
          })
          _this.dataSource.data = _.sortBy(tempArr, ['sequence']);
          _this.expandAll();
          })
          
          
        }

      }else{
        console.log(_this.checkListArray[categoryIndex].children[subCategoryIndex])
      }
    }

  }

  // test(): void {
  //   console.log("Triggering dialog...");
  
  //   const dialogConfig = new MatDialogConfig();
  //   dialogConfig.data = {
  //     process: this.process.name,
  //     site: this.subProcess.refSite.externalId,
  //     unit: this.unitControl.value,
  //     location: this.observedForm.get("locationObserve").value
  //   };
  //   dialogConfig.disableClose = true;
  //   dialogConfig.position = { top: "50px" };
  //   dialogConfig.panelClass = "react-table-modalbox";
  
  //   // Open the dialog
  //   const dialogRef = this.dialog.open(ActionPopupComponent, dialogConfig);
  
  //   // Handle the dialog's result
  //   dialogRef.afterClosed().subscribe(result => {
  //     console.log("Dialog result:", result);
  
  //     if (result) {
  //       const isEditMode = history.state.action === "Edit";
  //       const currentInjuryPotential = this.observedForm.get("isInjuryPotential").value;
  
  //       if (!isEditMode || 
  //           (this.observation && this.observation.isInjuryPotential === "Yes") ||
  //           (this.observation && !this.observation.isInjuryPotential)) {
  //         // For new observations or specific edit scenarios
  //         this.actionPopupResponse = result;
          
  //         // Set isInjuryPotential based on the action taken
  //         if (result.actionTaken) {
  //           this.observedForm.get("isInjuryPotential").setValue("Yes");
  //         } else {
  //           // Assuming 'No1' for minor corrections and 'No2' for significant upgrades
  //           this.observedForm.get("isInjuryPotential").setValue(
  //             result.minorCorrections ? "No1" : "No2"
  //           );
  //         }
  //       } else {
  //         // For edit mode where we don't want to change the existing value
  //         console.log("Keeping existing isInjuryPotential value:", currentInjuryPotential);
  //       }
  
  //       // Update the tooltip text
  //       const node = this.getNodeForCurrentSelection(); // You need to implement this method
  //       if (node) {
  //         node.tooltipText = this.getTooltipTextForValue(this.observedForm.get("isInjuryPotential").value);
  //       }
  //     } else {
  //       // Dialog was cancelled
  //       console.log("Dialog cancelled, resetting isInjuryPotential");
  //       this.observedForm.get("isInjuryPotential").setValue(null);
  //     }
  //   });
  // }
  
  // private getNodeForCurrentSelection(): any {
  //   // Implementation depends on how you're storing your nodes
  //   // This is just a placeholder
  //   return this.nodes.find(node => node.id === this.currentNodeId);
  // }
  tooltipText: string = '';
  
 // Remove the tooltipText property, as we'll store it in the node object

getSelectedOptionText(node: any): string {
  return node.tooltipText || '';
}

onSelectionChange(event: any, node: any): void {
  const selectedValue = event.value;
  console.log('Selected value:', selectedValue);
  node.tooltipText = this.getTooltipTextForValue(selectedValue);
  // if (selectedValue === 'No1' || selectedValue === 'No2') {
  //   this.test();
  // }
}

private getTooltipTextForValue(value: string): string {
  switch (value) {
    case 'Yes':
      return 'Correct issue on spot';
    case 'No1':
      return 'Minor corrections to Procedures';
    case 'No2':
      return 'Significant process or Training upgrades required';
    default:
      return '';
  }
}

  radioButton(node) {
    
    console.log(this.dataSource)
    console.log(node)
    console.log(this.tabsListArr)
    if (!node.question && !this.tabsListArr.find(e => e.id == node.id)) {
      this.tabsListArr.push(node)
    }
  }
  spliceFeedback(node){
    var myIndex = this.tabsListArr.findIndex(e=> e.id == node.id);
    if(myIndex!=-1){
      this.tabsListArr.splice(myIndex, 1);
    }
    // const removed = this.tabsListArr.splice(2, 2, "guava");
    // this.tabsListArr.push(node)
  }


  popupAnimation = trigger('popupAnimation', [
    state('hide', style({
      opacity: 0,
      transform: 'translateY(-50px)'
    })),
    state('show', style({
      opacity: 1,
      transform: 'translateY(0)'
    })),
    transition('hide <=> show', animate('300ms ease-in-out'))
  ]);

  submitClick() {
    var _this = this;
   
    let safecheck: string;
    let foundUnsafe = false;
    let foundNotobserved = false;
    safecheck='';
     console.log(_this.dataSource.data)
     var notesInvalid = false;
     _.each(_this.dataSource.data, function (eCatergory) {
      var notesInvalid1 = false;
      // Check if eCatergory has no children
      console.log(eCatergory)
      if (!eCatergory.children || (eCatergory.children && eCatergory.children.length <= 0)) {
    
      if (foundUnsafe) return; 
      console.log(eCatergory.formGroup.value.isSafe);
      if (eCatergory.formGroup.value.isSafe === "safe" && !foundNotobserved) {
        safecheck = "Safe";
      } else if (eCatergory.formGroup.value.isSafe === "unsafe") {
        safecheck = "Unsafe";
        foundUnsafe = true;
        return;
      } else if (eCatergory.formGroup.value.isSafe === "notObserved" || eCatergory.formGroup.value.isSafe === "") {
        safecheck = "";
        foundNotobserved = true;
      }}

      const notesControlCat = eCatergory?.formGroup?.controls.note;
      notesControlCat.clearValidators();
      notesControlCat.updateValueAndValidity();
      notesControlCat.markAllAsTouched();
      if ((!eCatergory.formGroup.value.note || eCatergory.formGroup.value.note.length == 0) && (!eCatergory.children || (eCatergory.children && eCatergory.children.length <= 0))) {
        if (_this.isSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "safe") {
          console.log("a1")
          notesInvalid1 = true;
        }
        if (_this.isNotSafeNotesMandatory && eCatergory.formGroup.value.isSafe == "unsafe") {
          console.log("a2")
          notesInvalid1 = true;
        }
        if (_this.isNotObservedNotesMandatory && eCatergory.formGroup.value.isSafe == "notObserved") {
          console.log("a3")
          notesInvalid1 = true;
        }
        if(notesInvalid1){
          notesInvalid = true
          notesControlCat.setValidators(Validators.required);
          notesControlCat.updateValueAndValidity();
          notesControlCat.markAllAsTouched();
        }
      }
      
    
      _.each(eCatergory.children, function (eSubCatergory) {
        var notesInvalid2 = false;
        const notesControl = eSubCatergory?.formGroup?.controls.note;
        notesControl.clearValidators();
        notesControl.updateValueAndValidity();
        notesControl.markAllAsTouched();
        if ((!eSubCatergory.formGroup.value.note || eSubCatergory.formGroup.value.note.length == 0) && (!eSubCatergory.children || (eSubCatergory.children && eSubCatergory.children.length <= 0))) {
          if (_this.isSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "safe") {
            console.log("a1")
            notesInvalid2 = true;
          }
          if (_this.isNotSafeNotesMandatory && eSubCatergory.formGroup.value.isSafe == "unsafe") {
            console.log("a2")
            notesInvalid2 = true;
          }
          if (_this.isNotObservedNotesMandatory && eSubCatergory.formGroup.value.isSafe == "notObserved") {
            console.log("a3")
            notesInvalid2 = true;
          }
          if(notesInvalid2){
            notesInvalid = true
            notesControl.setValidators(Validators.required);
            notesControl.updateValueAndValidity();
            notesControl.markAllAsTouched();
          }
        }
       
        // Check if eSubCatergory has no children
        console.log(eSubCatergory)
        if (!eSubCatergory.children || (eSubCatergory.children && eSubCatergory.children.length <= 0)) {
    
        if (foundUnsafe) return; 
        console.log(eSubCatergory.formGroup.value.isSafe);
        if (eSubCatergory.formGroup.value.isSafe === "safe" && !foundNotobserved) {
          safecheck = "Safe";
        } else if (eSubCatergory.formGroup.value.isSafe === "unsafe") {
          safecheck = "Unsafe";
          foundUnsafe = true;
          return;
        } else if (eSubCatergory.formGroup.value.isSafe === "notObserved" || eSubCatergory.formGroup.value.isSafe === "") {
          safecheck = "";
          foundNotobserved = true;
        }}
    
        _.each(eSubCatergory.children, function (eQuestion) {
          var notesInvalid3 = false;
          const notesControlQ = eQuestion?.formGroup?.controls.note;
          notesControlQ.clearValidators();
          notesControlQ.updateValueAndValidity();
          notesControlQ.markAllAsTouched();
          // Assuming eQuestion is the final level and doesn't have children
          if (!eQuestion.formGroup.value.note || eQuestion.formGroup.value.note.length == 0) {
            if (_this.isSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "safe") {
              console.log("b1")
              notesInvalid3 = true;
            }
            if (_this.isNotSafeNotesMandatory && eQuestion.formGroup.value.isSafe == "unsafe") {
              console.log("b2")
              notesInvalid3 = true;
            }
            if (_this.isNotObservedNotesMandatory && eQuestion.formGroup.value.isSafe == "notObserved") {
              console.log("b3")
              notesInvalid3 = true;
            }
            if(notesInvalid3){
              notesInvalid = true;
              notesControlQ.setValidators(Validators.required);
              notesControlQ.updateValueAndValidity();
              notesControlQ.markAllAsTouched();
            }
          }
          console.log(eQuestion)
          if (foundUnsafe) return; 
          console.log(eQuestion.formGroup.value.isSafe);
          if (eQuestion.formGroup.value.isSafe === "safe" && !foundNotobserved) {
            safecheck = "Safe";
          } else if (eQuestion.formGroup.value.isSafe === "unsafe") {
            safecheck = "Unsafe";
            foundUnsafe = true;
            return;
          } else if (eQuestion.formGroup.value.isSafe === "notObserved" || eQuestion.formGroup.value.isSafe === "") {
            safecheck = "";
            foundNotobserved = true;
          }
        });
      });
    });
    
    console.log('safecheck --->',safecheck)
    console.log('unitControl --->',_this.unitControl.value)
    console.log('this.observedForm --->',this.observedForm)
    console.log(this.selectedCategory, this.selectedSubCategory)
    if(this.observedForm.get('WBA').value == 'Celanese'){
      this.observedForm.get('nameOfCompany').clearValidators();
    }
    this.observedForm.get('nameOfCompany').updateValueAndValidity();
    if(this.observedForm.get('operationalLearning').value == 'No'){
      this.observedForm.get('operationalLearningDescription').clearValidators();
    }
    this.observedForm.get('operationalLearningDescription').updateValueAndValidity();

    if(this.observedForm.valid){
      var unitFind = _this.unitList.find(item => item.externalId == _this.unitControl.value)
    
      _this.loaderFlag = true;
      var observedObj = this.observedForm.value;
      var deptFind = _this.departmentList.find(item => item.externalId == observedObj["department"])
      var obDate = new Date(observedObj["datetime"]);
      const currentYear = obDate.getFullYear().toString()
      const currentMonth = ("0" + (obDate.getMonth() + 1)).slice(-2);
      const currentDay = ("0" + obDate.getDate()).slice(-2);
      var strDate = currentYear + "-" + currentMonth + "-" + currentDay;
      //06:15 AM
      const convertTime12to24 = (time12h) => {
        console.log("time12h >>> ",time12h)
        var tempDate = new Date(observedObj["datetime"]);
        if(time12h){
          var time12hArray =  time12h.split(':');
          var hours = time12hArray[0];
        // let [hours, minutes] = time12h.split(':');
        var timeMinute = time12hArray[1].split(" ");
        var minutes = timeMinute[0];
        if(timeMinute && timeMinute.length>1 && timeMinute[1] == "PM"){
          hours = parseInt(time12hArray[0]) + 12;
        }
        console.log(hours)
        tempDate.setHours(hours);
        tempDate.setMinutes(minutes);
      }
        return tempDate;
      }
      console.log(observedObj["workPermit"])
      var postObj = {
        
        "date": strDate.toString(),
        //"comments": observedObj["commentsObserve"],
        "isOperationalLearning": observedObj["operationalLearning"] == 'Yes' ? true : false,
        "operationalLearningDescription": observedObj["operationalLearningDescription"],
        "signature": observedObj["signature"],
        "result": observedObj["result"],
        "measure": observedObj["measure"],
        "discussionPoint": observedObj["discussionPoint"],
        // "shift": observedObj["shift"],
        "shortDescription": observedObj["shortDescription"],
        // "auditedBy": observedObj["auditedBy"],
        "companyName": observedObj["nameOfCompany"],
        "companyType": observedObj["WBA"],
        "activityAudited": observedObj["WABA"],
        "workPermitNumber": observedObj["workReleaseNumber"],
        "workPermitType": observedObj["workPermit"] ? observedObj["workPermit"] : [],
        //"floor": observedObj["floor"]+"",
//"urgency": _this.selectedUrgency,
        "isActive" :true,
        //"observationStatus": safecheck,
        "eventType": observedObj["eventType"],
        "eventDesccription": observedObj["eventDesccription"],
        "correctiveActionFlag": observedObj["correctiveActionFlag"],
        "isInjuryPotential": observedObj["isInjuryPotential"],
       // "descripeCorrectiveActionTaken": observedObj["descripeCorrectiveActionTaken"],
      }
      if(_this.observation){
        postObj["externalId"] = _this.observation["externalId"];
      }
      if(deptFind){
        postObj["refDeparment"] = {
          "space":deptFind["space"],
          "externalId":deptFind["externalId"]
        }
      }
  
      if(observedObj["startTime"] || observedObj["endTime"]){
      // 2023-09-11T21:45:00.000Z
      var startTimeGet = convertTime12to24(observedObj["startTime"]);
      var endTimeGet = convertTime12to24(observedObj["endTime"]);
  
      const currentstartMonth = ("0" + (startTimeGet?.getMonth() + 1)).slice(-2);
      const currentstartDay = ("0" + startTimeGet?.getDate()).slice(-2);
      const currentstartHour = ("0" + (startTimeGet?.getHours())).slice(-2);
      const currentstartMinute = ("0" + startTimeGet?.getMinutes()).slice(-2);
  
      const currentendMonth = ("0" + (endTimeGet?.getMonth() + 1)).slice(-2);
      const currentendDay = ("0" + endTimeGet?.getDate()).slice(-2);
      const currentendHour = ("0" + (endTimeGet?.getHours())).slice(-2);
      const currentendMinute = ("0" + endTimeGet?.getMinutes()).slice(-2);
      var startTime = startTimeGet?.getFullYear() + "-" + currentstartMonth + "-" + currentstartDay + "T" + currentstartHour + ":" + currentstartMinute + ":00.000Z";
      var endTime = endTimeGet?.getFullYear() + "-" + currentendMonth + "-" + currentendDay + "T" + currentendHour + ":" + currentendMinute + ":00.000Z";
  
        postObj["startTime"] = startTime;
        if(endTime!="NaN-aN-aNTaN:aN:00.000Z")  postObj["endTime"] = endTime;
      }
      console.log("postObj ----->",postObj)
      // "mediaFilePath": "",
      postObj["crafts"] = observedObj["crafts"]
      
      //postObj["observedCraft"] = observedObj["observedCraft"]
      
      var behalf = _this.behalfList.find(e=> e.externalId == observedObj["behalf"])
     if(behalf){
      postObj["observedOnBehalfOf"] = {
        "space":behalf["space"],
        "externalId":behalf["externalId"]
      }
     }
    //  if(_this.commonService["userInfo"] && _this.commonService["userInfo"]["externalId"]){
    //   postObj["performerAzureDirectoryUserID"] = {
    //     "space":_this.commonService["userInfo"]["space"],
    //     "externalId":_this.commonService["userInfo"]["externalId"]
    //   }
    //  }
     
    //   observedOnBehalfOf: User
    // performerAzureDirectoryUserID: User
  
      if (_this["selectedRegion"]) {
        postObj["refGeoRegion"] = {
          "space": _this["selectedRegion"].space,
          "externalId": _this["selectedRegion"].externalId
        }
      }
      if (_this["selectedCountry"]) {
        postObj["refCountry"] = {
          "space": _this["selectedCountry"].space,
          "externalId": _this["selectedCountry"].externalId
        }
      }
  
      if (_this["selectedSite"]) {
        postObj["refSite"] = {
          "space": _this["selectedSite"].space,
          "externalId": _this["selectedSite"].externalId
        }
      }
      if (_this["scheduleDetails"] && _this["scheduleDetails"]["refOFWASchedule"]) {
        postObj["refOFWASchedule"] = {
          "space": _this["scheduleDetails"]["refOFWASchedule"].space,
          "externalId": _this["scheduleDetails"]["refOFWASchedule"].externalId
        }
      }
      if (_this["scheduleDetails"]) {
        postObj["refOFWAScheduleDetail"] = {
          "space": _this["scheduleDetails"].space,
          "externalId": _this["scheduleDetails"].externalId
        }
      }
      // refOFWASchedule: OFWASchedule
      // refOFWAScheduleDetail: OFWAScheduleDetail
      if (unitFind) {
        postObj["refUnit"] = {
          "space": unitFind.space,
          "externalId": unitFind.externalId
        }
      }
      if (_this["selectedReportingLocation"]) {
        postObj["refReportingLocation"] = {
          "space": _this["selectedReportingLocation"].space,
          "externalId": _this["selectedReportingLocation"].externalId
        }
      }
      if (_this["selectedReportingLine"]) {
        postObj["refReportingLine"] = {
          "space": _this["selectedReportingLine"].space,
          "externalId": _this["selectedReportingLine"].externalId
        }
      }
  
      // if (_this["selectedFunctionalLocation"]) {
      //   postObj["refFunctionalLocation"] = {
      //     "space": _this["selectedFunctionalLocation"].space,
      //     "externalId": _this["selectedFunctionalLocation"].externalId
      //   }
      // }
      // if (_this["selectedEquipment"]) {
      //   postObj["refEquipment"] = {
      //     "space": _this["selectedEquipment"].space,
      //     "externalId": _this["selectedEquipment"].externalId
      //   }
      // }
      if (_this["selectedBusinessLine"]) {
        postObj["refBusinessSegment"] = {
          "space": _this["selectedBusinessLine"].space,
          "externalId": _this["selectedBusinessLine"].externalId
        }
      }

      if (_this.process) {
        postObj["refProcess"] = {
          "space": _this.process.space,
          "externalId": _this.process.externalId
        }
      }
      if (_this.corePrinciple) {
        postObj["refCorePrinciple"] = {
          "space": _this.corePrinciple.space,
          "externalId": _this.corePrinciple.externalId
        }
      } if (_this.subProcess) {
        postObj["refSubProcess"] = {
          "space": _this.subProcess.space,
          "externalId": _this.subProcess.externalId
        }
      }

      // if (_this.monthControl.value) {
      //   postObj["month"] = _this.monthControl.value;
      // }
      // if (_this.weekControl.value) {
      //   postObj["week"] = _this.weekControl.value+"";
      // }
  
      var observeObj = {
        "type": _this.commonService.configuration["typeAudit"],
        "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          postObj
        ]
      }
      console.log('observeObj',observeObj)
      _this.dataService.postData(observeObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
        if (data["items"].length > 0) {

          var logObj = {
            objectExternalId:data["items"][0].externalId,
            objectType:_this.commonService.configuration["typeAudit"],
            refUser:{
              "space": _this.dataService.userInfo.user.space,
              "externalId": _this.dataService.userInfo.user.externalId
            },
            logType:"Created",
            dateTime:new Date(),
            beforeJSON:postObj,
            afterJSON:postObj
          }
          if(_this.observation){
            logObj["beforeJSON"] = _this.observation;
            logObj["logType"] = "Edited";
          }
          var mainLog = {
            "type": _this.commonService.configuration["typeOFWALog"],
            "siteCode": _this.commonService.configuration["allSiteCode"],
            "unitCode": _this.commonService.configuration["allUnitCode"],
            "items": [
              logObj
            ]
          }
          _this.dataService.postData(mainLog, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(logdata => {
     
          })
           
          if (_this["scheduleDetails"]) {
            var postObjDetail = {
              externalId:_this["scheduleDetails"].externalId,
              status: "Completed",
              isEnable:true
            }
            var instanceAuditObj = {
              "type": _this.commonService.configuration["typeOFWAScheduleDetail"],
              "siteCode": _this["scheduleDetails"].space?_this["scheduleDetails"].space.split('-')[1]:"COR",
              "unitCode": _this.commonService.configuration["allUnitCode"],
              "items": [
                postObjDetail
              ]
              }
              console.log('instanceAuditObj',instanceAuditObj);
              _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
              
                var postObjDetail2 = {
                  externalId:_this["scheduleDetails"]["refOFWASchedule"].externalId,
                  status: "In progress",
                  isEnable:true
                }
                var instanceAuditObj2 = {
                  "type":  _this.commonService.configuration["typeSchedule"],
                  "siteCode": _this["scheduleDetails"]["refOFWASchedule"].space?_this["scheduleDetails"]["refOFWASchedule"].space.split('-')[1]:"COR",
                  "unitCode": _this.commonService.configuration["allUnitCode"],
                  "items": [
                    postObjDetail2
                  ]
                  }
                  console.log('instanceAuditObj2',instanceAuditObj2);
                  _this.dataService.postData(instanceAuditObj2, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
                 
                    _this.dataService.postData({scheduleId:_this["scheduleDetails"]["refOFWASchedule"].externalId}, _this.dataService.NODE_API + "/api/service/listScheduleDetail").subscribe(data3 => {

                      var listProcess = data3['data']['list' + _this.commonService.configuration["typeOFWAScheduleDetail"]]['items'];
                      var count = 0;
                      listProcess.forEach(element => {
                        if(element.status == "Completed"){
                          count++;
                        }
                      });
                      if(count == listProcess.length){
                        var postObjDetail4 = {
                          externalId:_this["scheduleDetails"]["refOFWASchedule"].externalId,
                          status: "Completed",
                          isEnable:true
                        }
                        var instanceAuditObj4 = {
                          "type":  _this.commonService.configuration["typeSchedule"],
                          "siteCode": _this["scheduleDetails"]["refOFWASchedule"].space?_this["scheduleDetails"]["refOFWASchedule"].space.split('-')[1]:"COR",
                          "unitCode": _this.commonService.configuration["allUnitCode"],
                          "items": [
                            postObjDetail4
                          ]
                          }
                          console.log('instanceAuditObj4',instanceAuditObj4);
                          _this.dataService.postData(instanceAuditObj4, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data4 => {
                          })        

                      }
                    })
                 
                  })
              })

          }
        console.log(data)
        // _this.postPrimaryChecklistArray(data["items"][0]);
        _this.postChecklistArray(data["items"][0]);
          if(_this.actionPopupResponse && observedObj["correctiveActionFlag"] && observedObj["correctiveActionFlag"] == "No"){
            _this.createAction(data["items"][0].externalId)
          }
          if(_this.actionPopupResponse && observedObj["isInjuryPotential"] && observedObj["isInjuryPotential"] == "NO"){
            _this.createAction(data["items"][0].externalId)
          }
          _this.assetEdgeCreation(observedObj, data["items"][0]);
          _this.obRes = data["items"][0]
          _this.saveImages(); 
          //_this.evidenceEdgeCreation(data["items"][0]);
         
         // this.commonService.triggerToast({ type: 'success', title: '', msg: 'Saved successfully' }); 
          _this.showSuccessPopup = true;
          console.log("pop--->",_this.showSuccessPopup)
          var notificationGroupUser = [];
          if(_this.notificationGroupEnable?.isEnabled && _this.notificationGroup?.groupName){
            if(_this.commonService.notificationGroupData){

              // Extract users' emails where externalId matches
              notificationGroupUser = _this.commonService.notificationGroupData.message
                .filter(item => item.externalId === _this.notificationGroup.groupName)
                .flatMap(item => item.users.map(user => user.email));

              console.log(notificationGroupUser); // Output: Array of email addresses

            }
            var instanceNotification = [
              {
                application: _this.commonService.applicationInfo.name,
                description: 'New/Updated Observation',
                users: notificationGroupUser,
                severity: _this.selectedUrgency,
                properties: [
                  {
                    name: 'Description',
                    value: observedObj["describeObserve"],
                    type: 'text',
                  },
                  {
                    name: 'Start',
                    value: new Date(
                      postObj["startTime"]
                    ).toDateString(),
                    type: 'text',
                  },
                  {
                    name: 'End',
                    value: new Date(
                      postObj["endTime"]
                    ).toDateString(),
                    type: 'text',
                  },
                ],
              },
            ];

            let notificationType = 'New Observation Created';
            _this.commonService.triggerToast({
              type: 'success',
              title: '',
              msg: _this.commonService.toasterLabelObject['toasterSavesuccess'],
            });
            _this.commonService.notification(
              instanceNotification,
              notificationType
            );
          }
          _this.loaderFlag = false; 
          _this.cd.detectChanges();
          // Navigate to observation list
        //  this.router.navigate(['observations/observation-list']);
        } else {
          _this.commonService.triggerToast({ type: 'error', title: '', msg: _this.labels['toasterFailed'] });
          //_this.showFailurePopup = true;
        }
        _this.loaderFlag = false; 
      }, error => {
        console.error('Error saving observation:', error);
        _this.commonService.triggerToast({ type: 'error', title: '', msg: _this.labels['toasterFailedtosaveobs'] });
        //_this.showFailurePopup = true;
        console.log("pop--->",this.showFailurePopup)
        _this.loaderFlag = true; 
      });
  
    } else {
      this.commonService.triggerToast({ type: 'error', title: '', msg: _this.labels['toasterPleasefillreqfields'] });
      //_this.showFailurePopup = true;
    }
  }


  checklistPrimaryQuestionArray: any = [];
  checklistSubQuestionArray: any = [];
  postPrimaryChecklistArray(fdmObj) {
    var _this = this;
  
    var questionArray = [];
    console.log("Primary Checklist array");
    console.log(_this.dataSource.data);
  
    _.each(_this.dataSource.data, function (ePrimaryQuestion) {
      var primaryQuestionId = ePrimaryQuestion.id;
      _this.checklistPrimaryQuestionArray.push(ePrimaryQuestion);
      var primaryQuestion = _this.processList.find(e => e.externalId == primaryQuestionId);
      if (!ePrimaryQuestion.children || ePrimaryQuestion.children.length == 0) {
        var questionAnswer = {};
        var questionId = ePrimaryQuestion.id;
        questionAnswer["refOFWASubProcess"] = {
          space: primaryQuestion["space"],
          externalId: primaryQuestion["externalId"]
        };
        
        var questionValue = ePrimaryQuestion.formGroup.value;
        if (ePrimaryQuestion.checkListId && ePrimaryQuestion.checkListId.length > 0) {
          questionAnswer["externalId"] = ePrimaryQuestion.checkListId;
        }
  
        var isInjuryPotential = questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes";
        console.log("I am here")
        questionAnswer["isSafe"] = questionValue.isSafe == "safe";
        questionAnswer["isUnSafe"] = questionValue.isSafe == "unsafe";
        questionAnswer["isNotObserved"] = questionValue.isSafe == "notObserved";
        questionAnswer["isInjuryPotential"] = isInjuryPotential;
        questionAnswer["note"] = questionValue.note;
        questionAnswer["activity"] = questionValue.activity;
        questionAnswer["riskyAction"] = questionValue.riskyAction;
        questionAnswer["risk"] = questionValue.risk;
        questionAnswer["riskAgreement"] = questionValue.riskAgreement;
        questionAnswer["reasonForAction"] = questionValue.reasonForAction;
        questionAnswer["safeBehaviour"] = questionValue.safeBehaviour;
        questionAnswer["suggestedSolution"] = questionValue.suggestedSolution;
  
        questionArray.push(questionAnswer);
      }
      _.each(ePrimaryQuestion.children, function (eSubQuestion) {
        var subQuestionId = eSubQuestion.id;
        var subQuestion = _this.processList.find(e => e.externalId == subQuestionId);
        if (subQuestion) {
          var subQuestionValue = eSubQuestion.formGroup.value;
          _this.checklistSubQuestionArray.push(subQuestion);
          
          var subQuestionAnswer = {};
          if (eSubQuestion.checkListId && eSubQuestion.checkListId.length > 0) {
            subQuestionAnswer["externalId"] = eSubQuestion.checkListId;
          }
          subQuestionAnswer["refOFWASubProcess"] = {
            space: primaryQuestion["space"],
            externalId: primaryQuestion["externalId"]
          };
          subQuestionAnswer["refOFWACategory"] = {
            space: subQuestion["space"],
            externalId: subQuestion["externalId"]
          };
  
          var isInjuryPotential = subQuestionValue.isInjuryPotential && subQuestionValue.isInjuryPotential == "Yes";
          subQuestionAnswer["isSafe"] = subQuestionValue.isSafe == "safe";
          subQuestionAnswer["isUnSafe"] = subQuestionValue.isSafe == "unsafe";
          subQuestionAnswer["isNotObserved"] = subQuestionValue.isSafe == "notObserved";
          subQuestionAnswer["isInjuryPotential"] = isInjuryPotential;
          subQuestionAnswer["note"] = subQuestionValue.note;
          subQuestionAnswer["activity"] = subQuestionValue.activity;
          subQuestionAnswer["riskyAction"] = subQuestionValue.riskyAction;
          subQuestionAnswer["risk"] = subQuestionValue.risk;
          subQuestionAnswer["riskAgreement"] = subQuestionValue.riskAgreement;
          subQuestionAnswer["reasonForAction"] = subQuestionValue.reasonForAction;
          subQuestionAnswer["safeBehaviour"] = subQuestionValue.safeBehaviour;
          subQuestionAnswer["suggestedSolution"] = subQuestionValue.suggestedSolution;
  
          questionArray.push(subQuestionAnswer);
  
          _.each(eSubQuestion.children, function (eQuestion) {
            var questionAnswer = {};
            var questionId = eQuestion.id;
            var question = _this.questionList.find(e => e.externalId == questionId);
            questionAnswer["refOFWASubProcess"] = {
              space: primaryQuestion["space"],
              externalId: primaryQuestion["externalId"]
            };
            questionAnswer["refOFWACategory"] = {
              space: subQuestion["space"],
              externalId: subQuestion["externalId"]
            };
            questionAnswer["refOFWAQuestion"] = {
              space: question["space"],
              externalId: question["externalId"]
            };
  
            var questionValue = eQuestion.formGroup.value;
            if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
              questionAnswer["externalId"] = eQuestion.checkListId;
            }
  
            var isInjuryPotential = questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes";
            questionAnswer["isSafe"] = questionValue.isSafe == "safe";
            questionAnswer["isUnSafe"] = questionValue.isSafe == "unsafe";
            questionAnswer["isNotObserved"] = questionValue.isSafe == "notObserved";
            questionAnswer["isInjuryPotential"] = isInjuryPotential;
            questionAnswer["note"] = questionValue.note;
  
            questionArray.push(questionAnswer);
          });
        } else {
          var eQuestion = eSubQuestion;
          var questionAnswer = {};
          var questionId = eQuestion.id;
          var question = _this.questionList.find(e => e.externalId == questionId);
          questionAnswer["refOFWASubProcess"] = {
            space: primaryQuestion["space"],
            externalId: primaryQuestion["externalId"]
          };
          questionAnswer["refOFWAQuestion"] = {
            space: question["space"],
            externalId: question["externalId"]
          };
  
          var questionValue = eQuestion.formGroup.value;
          if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
            questionAnswer["externalId"] = eQuestion.checkListId;
          }
  
          var isInjuryPotential = questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes";
          questionAnswer["isSafe"] = questionValue.isSafe == "safe";
          questionAnswer["isUnSafe"] = questionValue.isSafe == "unsafe";
          questionAnswer["isNotObserved"] = questionValue.isSafe == "notObserved";
          questionAnswer["isInjuryPotential"] = isInjuryPotential;
          questionAnswer["note"] = questionValue.note;
  
          questionArray.push(questionAnswer);
        }
      });
    });
  
    _this.processEdgeCreation(fdmObj);
    var postObjChecklist = {
      "type": _this.commonService.configuration["typeChecklist"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": questionArray
    };
  
    _this.dataService.postData(postObjChecklist, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        _this.checklistEdgeCreation(data["items"], fdmObj);
      }
      // this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    });
  }
  

  postChecklistArray(fdmObj) {
    var _this = this;

    var questionArray = []
    console.log("checklist array")
    console.log(_this.dataSource.data)
    _.each(_this.dataSource.data, function (eCatergory) {
      var catergoryId = eCatergory.id;
      _this.checklistCategoryArray.push(eCatergory);
      var category = _this.processList.find(e => e.externalId == catergoryId);
      if(!eCatergory.children || eCatergory.children.length==0){
        var questionAnswer = {};
        var questionId = eCatergory.id;
        questionAnswer["refOFWACategory"] = {
          space: category["space"],
          externalId: category["externalId"]
        }
        var questionValue = eCatergory.formGroup.value;
        if (eCatergory.checkListId && eCatergory.checkListId.length > 0) {
          questionAnswer["externalId"] = eCatergory.checkListId
        }
        var isInjuryPotential = false;
        if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
          isInjuryPotential = true;
        }

        questionAnswer["isSafe"] = false;
        questionAnswer["isUnSafe"] = false;
        questionAnswer["isNotObserved"] = false;
        if (questionValue.isSafe == "safe") {
          questionAnswer["isSafe"] = true;
        }
        if (questionValue.isSafe == "unsafe") {
          questionAnswer["isUnSafe"] = true;
        }
        if (questionValue.isSafe == "notObserved") {
          questionAnswer["isNotObserved"] = true;
        }
        questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eCatergory);
        questionAnswer["note"] = questionValue.note;
        questionAnswer["activity"] = questionValue.activity;
        questionAnswer["riskyAction"] = questionValue.riskyAction;
        questionAnswer["risk"] = questionValue.risk;
        questionAnswer["riskAgreement"] = questionValue.riskAgreement;
        questionAnswer["reasonForAction"] = questionValue.reasonForAction;
        questionAnswer["safeBehaviour"] = questionValue.safeBehaviour;
        questionAnswer["suggestedSolution"] = questionValue.suggestedSolution;

        questionArray.push(questionAnswer);
      }
      _.each(eCatergory.children, function (eSubCatergory) {
        var subCategoryId = eSubCatergory.id;
        var subCategory = _this.processList.find(e => e.externalId == subCategoryId);
        if(subCategory){
          var subCategoryValue = eSubCatergory.formGroup.value;
          _this.checklistSubCategoryArray.push(subCategory);
          var subCategoryAnswer = {};
          if (eSubCatergory.checkListId && eSubCatergory.checkListId.length > 0) {
            subCategoryAnswer["externalId"] = eSubCatergory.checkListId;
          }
          subCategoryAnswer["refOFWACategory"] = {
            space: category["space"],
            externalId: category["externalId"]
          }
          subCategoryAnswer["refOFWASubCategory"] = {
            space: subCategory["space"],
            externalId: subCategory["externalId"]
          }
  
          var isInjuryPotential = false;
          if (subCategoryValue.isInjuryPotential && subCategoryValue.isInjuryPotential == "Yes") {
            isInjuryPotential = true;
          }
  
          subCategoryAnswer["isSafe"] = false;
          subCategoryAnswer["isUnSafe"] = false
          subCategoryAnswer["isNotObserved"] = false
          if (subCategoryValue.isSafe == "safe") {
            subCategoryAnswer["isSafe"] = true;
          }
          if (subCategoryValue.isSafe == "unsafe") {
            subCategoryAnswer["isUnSafe"] = true;
          }
          if (subCategoryValue.isSafe == "notObserved") {
            subCategoryAnswer["isNotObserved"] = true;
          }

        subCategoryAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eSubCatergory);

          subCategoryAnswer["note"] = subCategoryValue.note;
          subCategoryAnswer["activity"] = subCategoryValue.activity;
          subCategoryAnswer["riskyAction"] = subCategoryValue.riskyAction;
          
          subCategoryAnswer["risk"] = subCategoryValue.risk;
          subCategoryAnswer["riskAgreement"] = subCategoryValue.riskAgreement;
          subCategoryAnswer["reasonForAction"] = subCategoryValue.reasonForAction;
          subCategoryAnswer["safeBehaviour"] = subCategoryValue.safeBehaviour;
          subCategoryAnswer["suggestedSolution"] = subCategoryValue.suggestedSolution;
  
          questionArray.push(subCategoryAnswer);
  
          _.each(eSubCatergory.children, function (eQuestion) {
            var questionAnswer = {};
            var questionId = eQuestion.id;
            var question = _this.questionList.find(e => e.externalId == questionId);
            questionAnswer["refOFWACategory"] = {
              space: category["space"],
              externalId: category["externalId"]
            }
            questionAnswer["refOFWASubCategory"] = {
              space: subCategory["space"],
              externalId: subCategory["externalId"]
            }
            questionAnswer["refOFWAQuestion"] = {
              space: question["space"],
              externalId: question["externalId"]
            }
            var questionValue = eQuestion.formGroup.value;
            if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
              questionAnswer["externalId"] = eQuestion.checkListId
            }
            var isInjuryPotential = false;
            if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
              isInjuryPotential = true;
            }
  
            questionAnswer["isSafe"] = false;
            questionAnswer["isUnSafe"] = false;
            questionAnswer["isNotObserved"] = false;
            if (questionValue.isSafe == "safe") {
              questionAnswer["isSafe"] = true;
            }
            if (questionValue.isSafe == "unsafe") {
              questionAnswer["isUnSafe"] = true;
            }
            if (questionValue.isSafe == "notObserved") {
              questionAnswer["isNotObserved"] = true;
            }

          questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eQuestion);

            questionAnswer["note"] = questionValue.note;
            questionArray.push(questionAnswer);
          })
        }else{
         // _this.checklistCategoryArray.push(eCatergory);
         var eQuestion = eSubCatergory;
          // _.each(eSubCatergory.children, function (eQuestion) {
            var questionAnswer = {};
            var questionId = eQuestion.id;
            var question = _this.questionList.find(e => e.externalId == questionId);
            console.log("TESTING POST",questionAnswer,category)
            if(category.processType=="Sub Process"){
              questionAnswer["refOFWASubProcess"] = {
                space: category["space"],
                externalId: category["externalId"]
              };
              questionAnswer["refOFWAQuestion"] = {
                space: question["space"],
                externalId: question["externalId"]
              };
            }else{
            questionAnswer["refOFWACategory"] = {
              space: category["space"],
              externalId: category["externalId"]
            }
            questionAnswer["refOFWAQuestion"] = {
              space: question["space"],
              externalId: question["externalId"]
            }}
            var questionValue = eQuestion.formGroup.value;
            console.log("TESTING POST",eQuestion)
            if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
              questionAnswer["externalId"] = eQuestion.checkListId
            }
            var isInjuryPotential = false;
            if (questionValue.isInjuryPotential && questionValue.isInjuryPotential == "Yes") {
              isInjuryPotential = true;
            }
  
            questionAnswer["isSafe"] = false;
            questionAnswer["isUnSafe"] = false;
            questionAnswer["isNotObserved"] = false;
            if (questionValue.isSafe == "safe") {
              questionAnswer["isSafe"] = true;
            }
            if (questionValue.isSafe == "unsafe") {
              questionAnswer["isUnSafe"] = true;
            }
            if (questionValue.isSafe == "notObserved") {
              questionAnswer["isNotObserved"] = true;
            }
            questionAnswer["isInjuryPotential"] = _this.getTrueInjuryPotential(eQuestion);;
            questionAnswer["note"] = questionValue.note;
            questionArray.push(questionAnswer);
          // })
        }
        
      })
    })

    _this.processEdgeCreation(fdmObj);
    var postObjChecklist = {
      "type": _this.commonService.configuration["typeChecklist"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": questionArray
    }
    _this.dataService.postData(postObjChecklist, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      if (data["items"].length > 0) {
        _this.checklistEdgeCreation(data["items"], fdmObj);
      }

      // this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    })

  }

    getTrueInjuryPotential(eSubCategory) {
    console.log(eSubCategory.formGroup.controls.isInjuryPotential.value)
    console.log(eSubCategory.formGroup.getRawValue().isInjuryPotential)
    return eSubCategory.formGroup.getRawValue().isInjuryPotential == "Yes";
  }

  checklistEdgeCreation(checkListObj, fdmObj) {
    var _this = this;
    var myCheckListArray = [];
    _.each(checkListObj, function (eCheckList) {
      var edgeObj = {
        "instanceType": "edge",
        "space": fdmObj["space"],
        "externalId": fdmObj["externalId"] + "-" + eCheckList["externalId"],
        "type": {
          "space": _this.commonService.configuration["DataModelSpace"],
          "externalId": _this.commonService.configuration["typeAudit"] + ".refOFWAChecklist"
        },
        "startNode": {
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"]
        },
        "endNode": {
          "space": eCheckList["space"],
          "externalId": eCheckList["externalId"]
        }
      }
      myCheckListArray.push(edgeObj);
    });

    _this.dataService.postData(myCheckListArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

    });
  }
  assetEdgeCreation(observedObj, fdmObj) {

    var _this = this;
    if(_this.editEquipment.length > 0){

       
      var disconnectNode=   {
        "items": [
        
         ]
      }
  
      _this.editEquipment.forEach(element => {
        var ob =   {
          "instanceType":"edge",
          "externalId": fdmObj["externalId"]+"-"+element.externalId, 
          "space": fdmObj["space"]
        }
        disconnectNode.items.push(ob)
      })
      console.log('disconnectNode',disconnectNode)
      _this.dataService.postData(disconnectNode, _this.dataService.NODE_API + "/api/service/deleteInstance").subscribe(disNode => {
        if (observedObj.assets && observedObj.assets.length > 0) {
          var myAssetArray = [];
          _.each(observedObj.assets, function (eAsset) {
            var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
            var edgeObj = {
              "instanceType": "edge",
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
              "type": {
                "space": _this.commonService.configuration["DataModelSpace"],
                "externalId": _this.commonService.configuration["typeAudit"] + ".refEquipment"
              },
              "startNode": {
                "space": fdmObj["space"],
                "externalId": fdmObj["externalId"]
              },
              "endNode": {
                "space": myAsset.space,
                "externalId": myAsset.externalId
              }
            }
            myAssetArray.push(edgeObj);
          })
        }
        _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
    
        });
      })
    }else{
      if (observedObj.assets && observedObj.assets.length > 0) {
        var myAssetArray = [];
        _.each(observedObj.assets, function (eAsset) {
          var myAsset = _this.assetsList.find(e => e.externalId == eAsset.externalId);
          var edgeObj = {
            "instanceType": "edge",
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"] + "-" + myAsset["externalId"],
            "type": {
              "space": _this.commonService.configuration["DataModelSpace"],
              "externalId": _this.commonService.configuration["typeAudit"] + ".refEquipment"
            },
            "startNode": {
              "space": fdmObj["space"],
              "externalId": fdmObj["externalId"]
            },
            "endNode": {
              "space": myAsset.space,
              "externalId": myAsset.externalId
            }
          }
          myAssetArray.push(edgeObj);
        })
      }
      _this.dataService.postData(myAssetArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
  
      });
    }
 
  }

  evidenceEdgeCreation(fdmObj) {
    var _this = this;
    var myEvidenceArray = [];
    if (_this.evidenceObj) {
      _.each(_this.evidenceObj, function (eEvidence) {
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eEvidence["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeAudit"] + ".evidenceDocument"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eEvidence["space"],
            "externalId": eEvidence["externalId"]
          }
        }
        myEvidenceArray.push(edgeObj);
      });

      _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
  }

  loadExistingImages() {
    if (this.observation.evidenceDocument && this.observation.evidenceDocument.items.length > 0) {
      this.images = []; // Clear existing images array
      this.observation.evidenceDocument.items.forEach(item => {
        const image = `${this.commonService.configuration["AzureAudience"]}/api/v1/projects/${this.commonService.configuration["Project"]}/documents/${item.evidenceDocumentId}/preview/image/pages/1`;
        this.dataService.getImage(image).subscribe(
          (resData: any) => {
            const objectURL = URL.createObjectURL(resData);
            item.image = this.sanitizer.bypassSecurityTrustUrl(objectURL);
            console.log('Existing Image loaded successfully:', item.image);
            this.images.push(item.image); // Add the image URL to images array
          },
          (error) => {
            console.error('Error loading existing image:', error);
          }
        );
      });
    }
    console.log('Final images array after loading existing images:', this.images);
  }
  
  // Function to upload new images and store them
  async uploadAndStoreNewImages($event: any) {
    this.loaderFlag = true;
  
    if ($event.target.files && $event.target.files.length) {
      const files = $event.target.files;
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        reader.onload = (e: any) => {
          const imageObjectURL = e.target.result;
          this.images.push(imageObjectURL); 
          console.log(`File ${i + 1}/${files.length} loaded:`, file.name);
        };
        reader.readAsDataURL(file);
        const fileContent = file;
        const buffer = await fileContent.arrayBuffer();
        const fileNameArray = file.name.split(".");
        var imgObj = { name: fileNameArray[0], mimeType: file.type, }
        if(this.dataSetImageId){
          imgObj["dataSetId"] = this.dataSetImageId
        }
        const fileupload: any = await this.client.files.upload(imgObj, buffer);
        console.log(`File ${i + 1}/${files.length} uploaded to server:`, fileupload);
        const myObj = {
          name: fileupload.name,
          mimetype: fileupload.mimeType,
          pathURL: fileupload.uploadUrl,
          pathStore: "CDF",
          evidenceDocumentId: fileupload.id + "",
          description: ""
        };
        if (this.observation && this.observation["evidenceDocument"] && this.observation["evidenceDocument"]["items"].length > 0) {
          myObj["externalId"] = this.observation["evidenceDocument"]["items"][0]["externalId"];
        }
        this.evidenceObj.push(myObj);
      }
    }
  
    this.loaderFlag = false;
    console.log('Final images array after uploading new images:', this.images);
  }

  processEdgeCreation(fdmObj) {
    var _this = this;
    var mySubCategoryArray = [];
    if (_this.checklistSubCategoryArray) {
      _.each(_this.checklistSubCategoryArray, function (eSubCatergory) {
        var edgeObj = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eSubCatergory["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeAudit"] + ".refOFWAProcess"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": eSubCatergory["space"],
            "externalId": eSubCatergory["externalId"]
          }
        }
        
        mySubCategoryArray.push(edgeObj);
      });

      _this.dataService.postData(mySubCategoryArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
    if (_this.checklistCategoryArray) {
      var myCategoryArray=[]
      _.each(_this.checklistCategoryArray, function (eCatergory) {

        var edgeObjNewSubCat = {
          "instanceType": "edge",
          "space": fdmObj["space"],
          "externalId": fdmObj["externalId"] + "-" + eCatergory["id"]?eCatergory["id"] : eCatergory["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeAudit"] + ".refCategory"
          },
          "startNode": {
            "space": fdmObj["space"],
            "externalId": fdmObj["externalId"]
          },
          "endNode": {
            "space": _this.process["space"],
            "externalId": eCatergory["id"]?eCatergory["id"] : eCatergory["externalId"]
          }
        }
        myCategoryArray.push(edgeObjNewSubCat);
      });

      _this.dataService.postData(myCategoryArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }
  }
  cancelClick() {
    this.observation = null;
    this.observedForm.reset();
    this.selectedCategory = null;
    this.selectedSubCategory = null;
    this.newItemEvent.emit({ "cancel": true });
  }


  closeSuccessPopup() {
    this.showSuccessPopup = false;
    console.log(this.process)
      this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
   
  }

  closeSuccessPopup2() {
    this.showFailurePopup= false;
    //this.router.navigate(['observations/observation-list']);
  }

  backClick() {
   
    this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
    this.observation = null;
    this.observedForm.reset();
    this.selectedCategory = null;
    this.selectedSubCategory = null;
    // this.dataSource.data = [];
    this.subCategoryList = null;
    this.categoryList = null;
    this.dataSource.disconnect();
   
    this.newItemEvent.emit({ "type": "Cancel","processType":"Observation" });
  }
  goPage(page) {
    this.router.navigate([page]);
  }
  feedBackClick() {
    if (this.feedBackEnable == true) {
      this.feedBackEnable = false
    } else {
      this.feedBackEnable = true
    }
  }

  logSelectedCraft(selectedCraft: string) {
    console.log('Selected Craft:', selectedCraft);
    // You can perform any additional actions with the selected value here
  }

  createAction(observationId) {
    var _this = this;
    _this.actionPopupResponse.postObj["items"][0]["eventMetadata"]["objectId"] = observationId;
    _this.dataService.postData(_this.actionPopupResponse.postObj, _this.dataService.NODE_API + '/api/service/createInstanceByProperties'
    ).subscribe((data) => {
      _this.commonService.notification(
        _this.actionPopupResponse.instanceNotification,
        _this.actionPopupResponse.notificationType
      );
    });
  }
  imageView(item) {
    console.log(item)
    var _this = this;
    if (item.guidelineDocument && item.guidelineDocument.length > 0) {
      var fileId = item.guidelineDocument[0]["id"];
      _this.loaderFlag = true;
      _this.dataService.getImage(_this.commonService.configuration["AzureAudience"] + "/api/v1/projects/" + _this.commonService.configuration["Project"] + "/documents/" + fileId + "/preview/image/pages/1").
        subscribe((resData: any) => {
          let objectURL = URL.createObjectURL(resData);
          _this.loaderFlag = false;
          _this.cd.detectChanges();
          window.open(objectURL, '_blank');
        });
    }
  }

  onDropdownChange(event: MatSelectChange) {
    console.log('Selected value:', event.value);
}

setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
}

