import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { formatDate } from '@angular/common';
import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatDateFormats } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { TokenService } from 'src/app/token.service';
import { CogniteClient } from '@cognite/sdk';
import { v1 as uuidv1 } from 'uuid';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { NgZone } from '@angular/core';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-create-action',
  templateUrl: './create-action.component.html',
  styleUrls: ['./create-action.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS},]
})
export class CreateActionComponent implements OnInit {
  siteControl: FormControl = new FormControl('');
  filteredSiteOptions: Observable<any[]>;

 
  unitControl: FormControl = new FormControl("");
  unitList = [];
  filteredUnitList = [];

  
  reportingLocationControl: FormControl = new FormControl("");
  reportingLocationList = [];
  filteredReportingLocationList = this.reportingLocationList.slice();

  filterFlag: string;
  selectedRegion: any;
  selectedCountry: any;
  selectedSite: any;

  loaderFlag: Boolean;

  searchControl: FormControl = new FormControl('');

  observedBy: FormControl = new FormControl('Williams');
  labels = {}
  startDateControl: FormControl = new FormControl(new Date());
  endDateControl: FormControl = new FormControl(new Date());
  tempLocation = [];

  createObservedForm: FormGroup;
  userList = [];
  filteredUserList = this.userList.slice();

  statusList = [
    {
      name: 'High',
      value: 'high',
    },
    {
      name: 'Medium',
      value: 'medium',
    },
    {
      name: 'Low',
      value: 'low',
    },
  ];
  rowData: any;

  appInfo: any;
  siteList = [];
  fl_searchVal: any;
  constructor(
    private router: Router,
    private tokenService: TokenService,
    private fb: FormBuilder,
    private dataService: DataService,
    private cdRef: ChangeDetectorRef,
    public commonService: CommonService,
    private translate: TranslateService,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone
  ) {

    this.labels = {
      'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
      'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
      'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'] || 'buttonCreatefieldwalk',
      'actionDesc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actionDesc'] || 'actionDesc',
      'createactionDescmessage': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionDescmessage'] || 'createactionDescmessage',
      'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
      'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
      'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
      'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
      'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
      'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
      'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
      'formcontrolsAssignedto': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAssignedto'] || 'formcontrolsAssignedto',
      'commonfilterChooseassigned': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassigned'] || 'commonfilterChooseassigned',
      'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
      'formcontrolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDuedate'] || 'formcontrolsDuedate',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
      'formcontrolsAddAttachment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddAttachment'] || 'formcontrolsAddAttachment',
      'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile'
    }
    var _this = this;
    _this.setDateFormat();
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
          'buttonCreatefieldwalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCreatefieldwalk'] || 'buttonCreatefieldwalk',
          'actionDesc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actionDesc'] || 'actionDesc',
          'createactionDescmessage': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'createactionDescmessage'] || 'createactionDescmessage',
          'priority': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'priority'] || 'priority',
          'high': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'high'] || 'high',
          'medium': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'medium'] || 'medium',
          'low': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'low'] || 'low',
          'unit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unit'] || 'unit',
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
          'formcontrolsAssignedto': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAssignedto'] || 'formcontrolsAssignedto',
          'commonfilterChooseassigned': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseassigned'] || 'commonfilterChooseassigned',
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
          'formcontrolsDuedate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsDuedate'] || 'formcontrolsDuedate',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
      'formcontrolsAddAttachment': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAddAttachment'] || 'formcontrolsAddAttachment',
      'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile'
        }
        
        console.log('commonService label', _this.labels)
        _this.cdRef.detectChanges();
      })
      // _this.cdRef.detectChanges();
    })

    _this.siteList = _this.commonService.siteList;

    this.rowData = history.state;
    console.log(' this.rowData', this.rowData);
    if (this.rowData.data == undefined || !this.rowData.pageFrom) {
      // _this.goPage('observations/observation-list')
      _this.goPage('/action/action-list');
    }
    this.createObservedForm = this.fb.group({
      observeDesc: ['', Validators.required],

      observedBy: ['', Validators.required],
      status: ['', Validators.required],
      startDate: [new Date(), Validators.required],
      endDate: [new Date(), Validators.required],
    });
    this.siteControl.setValue(this.dataService.siteId);
    if (this.rowData.action == 'Edit' || this.rowData.action == 'View') {
      var userFind = this.userList.find(
        (item) => item.externalId == this.rowData.data.assignedTo.externalId
      );
      console.log('userFind', userFind);

      this.createObservedForm = this.fb.group({
        observeDesc: [this.rowData.data.description, Validators.required],

        observedBy: [userFind, Validators.required],
        status: [this.rowData.data.priority, Validators.required],
        startDate: [
          new Date(this.rowData.data.assignmentDate),
          Validators.required,
        ],
        endDate: [new Date(this.rowData.data.dueDate), Validators.required],
      });
      //  this.rowData.data.reportingSite
      if (this.rowData.data.reportingSite) {
        this.siteControl.setValue(this.rowData.data.reportingSite.externalId);
      }
    }

    if (this.rowData.action == 'Create Action') {
      this.createObservedForm = this.fb.group({
        observeDesc: ['', Validators.required],

        observedBy: ['', Validators.required],
        status: ['', Validators.required],
        startDate: [new Date(), Validators.required],
        endDate: [new Date(), Validators.required],
      });
    }
  
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      var uItem = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      uItem.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.userList = uItem;
      _this.filteredUserList = uItem.slice();
    })

  

      var site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value)
      _this.selectedSite = site;
      console.log('_this.selectedSite ',_this.selectedSite)
      // _this.siteChanged()
      _this.selectedCountry = site["country"];
      if (site["country"] && site["country"]["parent"]) {
        _this.selectedRegion = site["country"]["parent"];
      }
      if (site && site["reportingLocations"]["items"].length > 0) {
        _this.reportingLocationList = _.orderBy(site["reportingLocations"]["items"], ['description'], ['asc']);
        _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
        _this.filteredReportingLocationList = _this.reportingLocationList.slice();
        _this.tempLocation =  _this.reportingLocationList.slice()
        console.log('reportingLocationList',_this.reportingLocationList)
      }
      if(_this.selectedSite){
        _this.commonService.siteList.filter(function (e) {
          if (_this.selectedSite.externalId.indexOf(e.externalId) > -1) {
            if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
              _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
              _this.unitList = _.uniqBy(_this.unitList, 'externalId');
              _this.filteredUnitList = _this.unitList.slice();
              console.log('unitList',_this.unitList)
            }
    
         
          }
          //return selectedSite.indexOf(e.externalId) > -1;
        });
      }
      // _this.commonService.siteChanges$.subscribe((sit: any) => {
      // console.log('sit',sit)
      // })
   
      _this.unitControl.valueChanges.subscribe(xArray => {
        console.log('xArray',xArray)
        var tempArray =_this.tempLocation.filter(item => item.reportingUnit?.externalId ==  xArray);
        console.log('tempArray',tempArray)
       
        _this.reportingLocationList =tempArray
        _this.filteredReportingLocationList = _this.reportingLocationList?.slice();
       // _this.cd.dete
  
       });

       if(_this.rowData.data){
        if(_this.rowData.data.refUnit){
          _this.unitControl.setValue(_this.rowData.data.refUnit.externalId)
        }
        if(_this.rowData.data.refReportingLocation){
          _this.reportingLocationControl.setValue(_this.rowData.data.refReportingLocation.externalId)
        }
       }
       _this.getInialTokel(this.tokenService.getToken());
  
  }
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";

      _this.filteredUnitList = _this.unitList.slice();

    }
  }
  async onFilterChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
   var filter:any = document.getElementsByClassName('mat-filter-input');
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.userList = [];
    _this.filteredUserList = [];

    _this.dataService
    .postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + '/api/service/listAllUser')
    .subscribe((resData: any) => {
      var uItem = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      uItem.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.userList = uItem;
      _this.filteredUserList = uItem.slice();
      _this.cdRef.detectChanges()
    //  _this.userList = _this.rcaUserList.filter(e => JSON.stringify(e).indexOf(event) != -1)
  
      })
      _this.cdRef.detectChanges()
  }
}
siteChanged() {
  var _this = this;
  console.log('    _this.selectedSite ')
  var selectedSite = _this.commonService.getSelectedValue(_this.selectedSite.externalId);
  _this.unitList = [];
  _this.filteredUnitList = _this.unitList.slice();


  if (selectedSite.length > 0) {

    var myCountries = [];
    var myRegions = [];
    _this.commonService.siteList.filter(function (e) {
      if (selectedSite.indexOf(e.externalId) > -1) {
        if (e["country"]) {

          myCountries.push(e["country"]["externalId"])
          if (e["country"]["parent"])
            myRegions.push(e["country"]["parent"]["externalId"])

        }
      }
      return selectedSite.indexOf(e.externalId) > -1;
    });
    _this.filterFlag = "Site";

  } else {
    selectedSite = _this.commonService.siteList.map(eItem => (eItem.externalId))
  }

  if (selectedSite.length > 0) {
    _this.commonService.siteList.filter(function (e) {
      if (selectedSite.indexOf(e.externalId) > -1) {
        if (e["reportingUnits"]["items"] && e["reportingUnits"]["items"].length > 0) {
          _this.unitList = _this.unitList.concat(_.orderBy(e["reportingUnits"]["items"], ['name'], ['asc']));
          _this.unitList = _.uniqBy(_this.unitList, 'externalId');
          _this.filteredUnitList = _this.unitList.slice();
        }

     
      }
      return selectedSite.indexOf(e.externalId) > -1;
    });
  } else {

    if (_this.commonService.siteList.length > 0) {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.unitList.slice();
    }


  }
}
  ngAfterViewInit(): void {
    var _this = this;
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        _this.appInfo = _this.commonService.applicationInfo;
        if (fiterType == 'Site') {
          _this.siteList = _this.commonService.siteList;
        }
      }
    });
    _this.setDateFormat();
  }

  goPage(page) {
    this.router.navigate([page]);
  }

  uploadFile($event) {
    console.log($event.target.files[0]); // outputs the first file
  }
  
  submitInner(assignId, fileId, siteInfo){
  var _this=this;
  var actionItemObj = {
    title: _this.createObservedForm.value.observeDesc,
    description: _this.createObservedForm.value.observeDesc,
    assignedToIds: [assignId],
    attachmentIds: [fileId],
    assignedTo: assignId,
    reportingUnitId:_this.unitControl.value,
    reportingLocationId:_this.reportingLocationControl.value,
    assignmentDate: formatDate(
      _this.createObservedForm.value.startDate,
      'yyyy-MM-dd',
      'en-US'
    ),
    dueDate: formatDate(
      _this.createObservedForm.value.endDate,
      'yyyy-MM-dd',
      'en-US'
    ),
    
    objectType: _this.rowData.pageFrom,
    objectId: _this.rowData.data.externalId,
    sourceId: _this.rowData.data.externalId,
    // sourceType: {
    //   "externalId":"AST-OFWA-OFWAEvent",
    //   "space":"AIM-COR-ALL-REF",
    //   "name":"OFWAEvent",
    //   "description":""
    // },
    sourceTypeId:"AST-OFWA-OFWAEvent",
    priority: _this.createObservedForm.value.status,
    createdById: _this.dataService.userInfo.externalId,
  };
  console.log('actionItemObj', actionItemObj);
  var mainObj = {
    application: {
      externalId: _this.appInfo.externalId,
      space: _this.appInfo.space,
    },
    reportingSite: {
      externalId: siteInfo.externalId,
      space: siteInfo.space,
    },
    eventMetadata: actionItemObj,
  };

  var instanceNotification = [
    {
      application: _this.commonService.applicationInfo.name,
      description: 'Action Notification',
      users: [_this.createObservedForm.value.observedBy.externalId],
      severity: _this.createObservedForm.value.status,
      properties: [
        {
          name: 'Title',
          value: _this.createObservedForm.value.observeDesc,
          type: 'text',
        },
        {
          name: 'Start',
          value: new Date(
            _this.createObservedForm.value.startDate
          ).toDateString(),
          type: 'text',
        },
        {
          name: 'End',
          value: new Date(
            _this.createObservedForm.value.endDate
          ).toDateString(),
          type: 'text',
        },
      ],
    },
  ];
  
  var postObj = {
    type: _this.commonService.configuration['typeActionItemEvent'],
    siteCode:_this.dataService.siteId.split('-')[1],
    unitCode: _this.commonService.configuration['allUnitCode'],
    items: [mainObj],
  };
  console.log('postObj', postObj);
  console.log('instanceNotification',instanceNotification)
  _this.dataService
    .postData(
      postObj,
      _this.dataService.NODE_API +
        '/api/service/createInstanceByProperties'
    )
    .subscribe((data) => {
      // var resVal:any = data
      // var res1 = resVal.items[0]

      // var myArray = [];
      // var edgeObj = {
      //   "instanceType": "edge",
      //   "space": _this.rowData.data.space,
      //   "externalId": _this.rowData.data.externalId + "-" + res1.externalId,
      //   "type": {
      //     "space": _this.commonService.configuration["DataModelSpace"],
      //     "externalId": _this.rowData.pageFrom+".refAction"
      //   },
      //   "startNode": {
      //     "space": _this.rowData.data.space,
      //     "externalId":_this.rowData.data.externalId
      //   },
      //   "endNode": {
      //     "space":  res1.space,
      //     "externalId":  res1.externalId,
      //   }
      // }
      // myArray.push(edgeObj)

      //  _this.dataService.postData(myArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
        let notificationType = 'Action Creation';

        _this.commonService.loaderFlag=false;
        _this.commonService.triggerToast({
          type: 'success',
          title: '',
          msg: _this.commonService.toasterLabelObject['toasterSavesuccess'],
        });
        _this.commonService.notification(
          instanceNotification,
          notificationType
        );
      //this.goPage('observations/audit-list')
        _this.goPage('/action/action-list');
      ///action/action-list
      //  });
    });
  }
  submitInnerNoAtt(assignId, fileId, siteInfo){
    var _this=this;
    var actionItemObj = {
      title: _this.createObservedForm.value.observeDesc,
      description: _this.createObservedForm.value.observeDesc,
      assignedToIds: [assignId],
      assignedTo: assignId,
      reportingUnitId:_this.unitControl.value,
      reportingLocationId:_this.reportingLocationControl.value,
      assignmentDate: formatDate(
        _this.createObservedForm.value.startDate,
        'yyyy-MM-dd',
        'en-US'
      ),
      dueDate: formatDate(
        _this.createObservedForm.value.endDate,
        'yyyy-MM-dd',
        'en-US'
      ),
      
      objectType: _this.rowData.pageFrom,
      objectId: _this.rowData.data.externalId,
      sourceId: _this.rowData.data.externalId,
      // sourceType: {
      //   "externalId":"AST-OFWA-OFWAEvent",
      //   "space":"AIM-COR-ALL-REF",
      //   "name":"OFWAEvent",
      //   "description":""
      // },
      sourceTypeId:"AST-OFWA-OFWAEvent",
      priority: _this.createObservedForm.value.status,
      createdById: _this.dataService.userInfo.externalId,
    };
    console.log('actionItemObj', actionItemObj);
    var mainObj = {
      application: {
        externalId: _this.appInfo.externalId,
        space: _this.appInfo.space,
      },
      reportingSite: {
        externalId: siteInfo.externalId,
        space: siteInfo.space,
      },
      eventMetadata: actionItemObj,
    };
  
    var instanceNotification = [
      {
        application: _this.commonService.applicationInfo.name,
        description: 'Action Notification',
        users: [_this.createObservedForm.value.observedBy.externalId],
        severity: _this.createObservedForm.value.status,
        properties: [
          {
            name: 'Title',
            value: _this.createObservedForm.value.observeDesc,
            type: 'text',
          },
          {
            name: 'Start',
            value: new Date(
              _this.createObservedForm.value.startDate
            ).toDateString(),
            type: 'text',
          },
          {
            name: 'End',
            value: new Date(
              _this.createObservedForm.value.endDate
            ).toDateString(),
            type: 'text',
          },
        ],
      },
    ];
    
    var postObj = {
      type: _this.commonService.configuration['typeActionItemEvent'],
      siteCode:_this.dataService.siteId.split('-')[1],
      unitCode: _this.commonService.configuration['allUnitCode'],
      items: [mainObj],
    };
    console.log('postObj', postObj);
    console.log('instanceNotification',instanceNotification)
    _this.dataService
      .postData(
        postObj,
        _this.dataService.NODE_API +
          '/api/service/createInstanceByProperties'
      )
      .subscribe((data) => {
        // var resVal:any = data
        // var res1 = resVal.items[0]
  
        // var myArray = [];
        // var edgeObj = {
        //   "instanceType": "edge",
        //   "space": _this.rowData.data.space,
        //   "externalId": _this.rowData.data.externalId + "-" + res1.externalId,
        //   "type": {
        //     "space": _this.commonService.configuration["DataModelSpace"],
        //     "externalId": _this.rowData.pageFrom+".refAction"
        //   },
        //   "startNode": {
        //     "space": _this.rowData.data.space,
        //     "externalId":_this.rowData.data.externalId
        //   },
        //   "endNode": {
        //     "space":  res1.space,
        //     "externalId":  res1.externalId,
        //   }
        // }
        // myArray.push(edgeObj)
  
        //  _this.dataService.postData(myArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {
          let notificationType = 'Action Creation';
  
          _this.commonService.loaderFlag=false;
          _this.commonService.triggerToast({
            type: 'success',
            title: '',
            msg: _this.commonService.toasterLabelObject['toasterSavesuccess'],
          });
          _this.commonService.notification(
            instanceNotification,
            notificationType
          );
        //this.goPage('observations/audit-list')
          _this.goPage('/action/action-list');
        ///action/action-list
        //  });
      });
    }
  submitClick() {
    var _this = this;

    console.log(this.createObservedForm);
    var siteInfo = this.siteList.find(
      (item) => item.externalId == this.siteControl.value
    );
    // var createUser = _this.userList.find(
    //   (item) => item.user.externalId == _this.commonService.userInfo.externalId
    // );
    // console.log('createUser', createUser);

    if (this.rowData.action == 'Create Action') {
      if (
        this.createObservedForm.valid &&
        this.rowData &&
        this.appInfo.externalId &&
        this.siteControl.value &&
        this.unitControl.value &&
        this.reportingLocationControl.value
      ) {
       if(10 < this.createObservedForm.value.observeDesc.length && this.createObservedForm.value.observeDesc.length < 200){      
          _this.commonService.loaderFlag=true;
          _this.dataService.postData({ email: this.createObservedForm.value.observedBy.externalId }, _this.dataService.NODE_API + "/api/service/listUserAzureAttribute").
          subscribe((resData: any) => {
                if (resData["data"] && resData["data"]["list" + _this.commonService.configuration["typeUserAzureAttribute"]]["items"].length > 0) {
                  var userItems = resData["data"]["list" + _this.commonService.configuration["typeUserAzureAttribute"]]["items"][0];
                  var assignId = userItems.externalId
                  var fileId =null
                  if (_this.selectedFile) {
                    _this.uploadImage(_this.selectedFile,function(fileRes){
                      console.log(fileRes,"I am in")
                      if(fileRes){
                        fileId = fileRes.externalId
                      }
                       // ["guidelineDocument"] = [fileRes.externalId];
                      // _this.addProcessApi(processType, result, refOFWAProcess);
                //     })
                // }
                  _this.submitInner(assignId, fileId, siteInfo)
                  })
                }
                else{
                  _this.submitInnerNoAtt(assignId, fileId, siteInfo)
                }
                  }else{
                _this.commonService.loaderFlag=false;
                _this.commonService.triggerToast({
                  type: 'error',
                  title: '',
                  msg: _this.commonService.toasterLabelObject['toasterUsernotfound'],
                });
              }
            })
        }else{
          _this.commonService.loaderFlag=false;
          _this.commonService.triggerToast({
            type: 'error',
            title: '',
            msg: _this.commonService.toasterLabelObject['toasterPleasefilldetails'],
          });
        }
      
     
     
      } else {
        _this.commonService.loaderFlag=false;
        _this.commonService.triggerToast({
          type: 'error',
          title: '',
          msg: _this.commonService.toasterLabelObject['toasterPleasefilldetails'],
        });
      }
    }

    // if(this.rowData.action == "Edit"){
    //   if (this.createObservedForm.valid &&   this.rowData && this.appInfo.externalId && this.siteControl.value) {
    //     console.log('appInfo',this.appInfo)
    //     console.log('createObservedForm',this.createObservedForm.value)

    //     var siteInfo = this.siteList.find(item => item.externalId == this.siteControl.value)

    //     var objValEdit= {
    //       "externalId" : this.rowData.data.externalId,
    //       "application": {
    //           "externalId":this.appInfo.externalId,
    //           "space": this.appInfo.space
    //       },

    //       "description":  this.createObservedForm.value.observeDesc,

    //        "assignedTo": {
    //         "externalId":this.createObservedForm.value.observedBy.externalId,
    //         "space": this.createObservedForm.value.observedBy.space
    //     },
    //       "assignmentDate": formatDate(this.createObservedForm.value.startDate,'yyyy-MM-dd',"en-US") ,
    //       "dueDate": formatDate( this.createObservedForm.value.endDate ,'yyyy-MM-dd',"en-US") ,

    //        "reportingSite":{
    //         "externalId":siteInfo.externalId,
    //         "space": siteInfo.space
    //     },

    //       "priority": this.createObservedForm.value.status

    //     }

    //     var instanceNotification = [
    //       {
    //         application: this.commonService.applicationInfo.name,
    //         description: 'Action Notification',
    //         users: [
    //           this.createObservedForm.value.observedBy.user.externalId,
    //         ],
    //         severity: this.createObservedForm.value.status,
    //         properties: [
    //           {
    //             name: 'Title',
    //             value: this.createObservedForm.value.observeDesc,
    //             type: 'text',
    //           },{
    //             name: 'Start',
    //             value: new Date(this.createObservedForm.value.startDate).toDateString(),
    //             type: 'text',
    //           },
    //           {
    //             name: 'End',
    //             value: new Date(this.createObservedForm.value.endDate).toDateString(),
    //             type: 'text',
    //           },
    //         ],
    //       },
    //     ];

    //       var postObjEdit = {
    //         "type": _this.commonService.configuration["typeAction"],
    //         "siteCode": _this.commonService.configuration["allSiteCode"],
    //         "unitCode": _this.commonService.configuration["allUnitCode"],
    //         "items": [
    //           objValEdit
    //         ]
    //       }
    //       console.log('postObj', postObjEdit)
    //       _this.dataService.postData(postObjEdit, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
    //         var resVal:any = data
    //         var res1 = resVal.items[0]
    //         let notificationType= 'Action Update'
    //         _this.commonService.triggerToast({ type: 'success', title: "", msg: "Successfully updated" });
    //         _this.commonService.notification(instanceNotification,notificationType);
    //         //this.goPage('observations/audit-list')
    //         _this.goPage('/action/action-list')

    //       })
    //   } else {
    //     this.commonService.triggerToast({ type: 'error', title: "", msg: "Please fill details" });

    //   }
    // }
  }

  goBack(){
    if(this.rowData.pageFrom == "Observation"){
      this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
    }
    else if(this.rowData.pageFrom == "Hazards"){
      this.router.navigate(['observations/list'], { state: { listType: "Hazards" } });
    }
    else if(this.rowData.pageFrom == "FieldWalk"){
      this.router.navigate(['observations/list'], { state: { listType: "FieldWalk" } });
    }else{
      this.goPage('/action/action-list')
    }
    
  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
  selectedFile: any;
  fileUrl: any;
  configureDataDialog: any;

  async onFileChange($event: any) {
    var _this = this;
    const fileInput = $event.target;
    const files = fileInput.files;
    console.log(files)
    if(files.length>0){
      this.selectedFile = files[0];
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.fileUrl = e.target.result;
      };
      reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
    }
  }

  imageView() {
    var _this = this;
    if (_this.selectedFile && _this.fileUrl) {
      console.log(_this.fileUrl)
      window.open(_this.fileUrl, '_blank');
  
    } else {
      var fileId = _this.configureDataDialog.guidelineDocument[0]["id"];
      _this.loaderFlag = true;
      _this.dataService.getImage(_this.commonService.configuration["AzureAudience"] + "/api/v1/projects/" + _this.commonService.configuration["Project"] + "/documents/" + fileId + "/preview/image/pages/1").
        subscribe((resData: any) => {
          let objectURL = URL.createObjectURL(resData);
          _this.loaderFlag = false;
          window.open(objectURL, '_blank');
        });
    }
  }

  dataSetImageId: any;  
  client: CogniteClient;

  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }
  getDataSetId() {
    var _this = this;
    var site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value)
    var objPost = { "items": [
        { "externalId": `${_this.commonService.configuration["AppCode"]}-${site.siteCode}-${_this.commonService.configuration["allUnitCode"]}-${_this.commonService.configuration["dataSpaceCode"]}`}
      ], "ignoreUnknownIds": false
    }
    _this.dataService.postData(objPost, _this.dataService.NODE_API + "/api/service/getDataSetId").subscribe((resData: any) => {
      if (resData && resData.items && resData.items.length > 0) {
        _this.dataSetImageId = resData.items[0].id
      }
    })
  }
  async uploadImage(res, cb) {
    
    this.getDataSetId();
    if(res){
      this.loaderFlag = true;
      const fileContent = res;
      const file = res;
      const buffer = await fileContent.arrayBuffer();
      const fileNameArray = file.name.split(".");
      var imgObj = { name: fileNameArray[0], mimeType: file.type,externalId: uuidv1()}
      if (this.dataSetImageId) {
        imgObj["dataSetId"] = this.dataSetImageId
      }
      const fileupload: any = await this.client.files.upload(imgObj, buffer);
      cb(fileupload)
    }else{
      cb(null)
    }
  }

}
