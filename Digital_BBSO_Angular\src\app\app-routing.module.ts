import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BrowserUtils } from '@azure/msal-browser';
import { QualityAssessmentComponent } from './observation-module/quality-assessment/quality-assessment.component';
import { generalinfoComponent } from './observation-module/general-info/general-infocomponent';
const routes: Routes = [
  { path: '', redirectTo: 'observations', pathMatch: 'full' },
  {
    path: 'observations',
    loadChildren: () => import('./observation-module/observation.module')
    .then (m => m.ObservationModule)
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./dashboard/dashboard.module')
    .then (m => m.DashboardModule)
  },
  {
    path: 'schedule-list',
    loadChildren: () => import('./schedule-module/scheduler.module')
    .then (m => m.SchedulerModule)
  },
  {
    path: 'noaccess',
    loadChildren: () => import('./user-no-access/userno-access.module')
    .then (m => m.UserNoAceessModule)
  },
  {
    path: 'configuration',
    loadChildren: () => import('./configuration-module/configuration.module')
    .then (m => m.ConfigurationModule)
  },
  {
    path: 'action',
    loadChildren: () => import('./actions-module/action.module')
    .then (m => m.ActionModule)
  },
  {
    path: 'alert',
    loadChildren: () => import('./alert-module/alert.module')
    .then (m => m.AlertModule)
  },
  {
    path: 'rule',
    loadChildren: () => import('./rule-module/rule.module')
    .then (m => m.RuleModule)
  },
  {
    path: 'workflow',
    loadChildren: () => import('./workflow-module/workflow.module')
    .then (m => m.WorkflowModule)
  },
  {
    path: 'infield',
    loadChildren: () => import('./observation-module/observation.module')
    .then (m => m.ObservationModule),
    canActivate: [],
    resolve: {
      externalRedirect: () => {
        window.open('https://cognite-infield.cogniteapp.com/observation', '_blank');
        return null;
      }
    }
  },
  {
    path: 'settings',
    loadChildren: () => import('./settings/settings.module')
    .then (m => m.SettingsModule)
  },
  {
    path:'vendor-audit',
    component:QualityAssessmentComponent
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      // Don't perform initial navigation in iframes or popups
      initialNavigation:
        !BrowserUtils.isInIframe() && !BrowserUtils.isInPopup()
          ? 'enabledNonBlocking'
          : 'disabled', // Set to enabledBlocking to use Angular Universal
    })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
