
.table-container.default{
    // background: rgba(174, 177, 194, 0.2);
    // border-radius: 4px;
    // padding: 8px;
    table {
        width: 100%;
        background: transparent;
        display: flex;
        flex-flow: column;
        .mat-header-row{
            display: flex;
            height: auto !important;
            .mat-header-cell{
                padding: 10px 5px 10px 20px;
                border: 0;
                display: flex;
                .header-cell-content {
                    letter-spacing: 0.22px;
                    color: #313131 !important;
                    font-weight: 400;
                    font-family: 'Roboto-Medium';
                    font-size: 14px;
                    line-height: 22px;
                    }
            }
        }
        
        .mat-row{
            display: flex;
            height: 52px!important;
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border-top: 1px solid #E0E0E0;
            &:nth-of-type(even){
                background: #F9F9F9;
            }
            &:last-child{
                border-bottom: 1px solid #E0E0E0;
            }
            
        .mat-cell{
            width: 11%;
            letter-spacing: 0.08px;
            color: #313131;
            font-size: 14px;
            display: flex;
            align-items: center;
            line-height: 18px;
            padding: 0px 15px 0px 20px;
            font-family: 'Roboto-regular';
            font-weight: 400;
            border: 0;
        }
        }
    }
}

.table-container .mat-icon-button {
    width: 15px;
    height: 15px;
    line-height: 15px;
    border-radius: 0;
    margin-left: 2px !important;
        .mat-icon.material-icons {
            width: 15px;
            height: 15px;
            line-height: 15px;
            font-size: 15px;
        }
}

  
//   .table-container {
//     height: 300px;
//     overflow: auto;
//   }

//   .mat-table {
//     overflow-x: scroll;
//   }

// .mat-cell,
// .mat-header-cell {
//   word-wrap: initial;
//   display: table-cell;
//   padding: 0px 10px;
//   line-break: unset;
//   width: 100%;
//   white-space: nowrap;
//   overflow: hidden;
//   vertical-align: middle;
// }
// .mat-row,
// .mat-header-row {
//   display: table-row;
// }


// .mat-row,
// .mat-header-row {
//     min-width: 800px;
// }