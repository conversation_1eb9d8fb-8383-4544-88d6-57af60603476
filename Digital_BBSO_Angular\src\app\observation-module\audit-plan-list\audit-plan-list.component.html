<div fxLayout="row" class="overflow-section">
  <div fxFlex="100">
    <div class="audit-plan-unit-section">
      <app-common-filter [siteControl]="siteControl"></app-common-filter>
      <div class="audit-head-icon">
        <div class="icon-bg-box" (click)="summaryClick()" matTooltip="Column summary">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-stroke-svg-icon"
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="settingClick()" matTooltip="Column selection">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
              d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
          </svg>
        </div>
      </div>
    </div>

    <div class="audit-section">
      <div class="filter-section">
        <commom-label [labelText]="'OBSERVATION.OBSERVATION_LIST.TITLE_AUDIT'" [tagName]="'h4'"
          [cstClassName]="'Suppliers Audit Plan'"></commom-label>

        <div class="audit-head-icon">
          <mat-form-field class="marginLeft-5 marginRight-5" appearance="outline">
            <mat-select [formControl]="auditTypeControl">
              <mat-option *ngFor="let item of auditType" value="{{item.name}}">{{item.name}}</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- <mat-form-field class="marginLeft-5 marginRight-5" appearance="outline">
            <mat-select [formControl]="supplierTypeControl">
              <mat-option *ngFor="let item of supplierType" value="{{item.name}}">{{item.name}}</mat-option>
            </mat-select>
          </mat-form-field> -->
        </div>

      </div>

      <div class="marginTop d-flex">
        <iframe id="iFrameAuditPlan" class="iFrameTable"  [src]="url| safe" title="description">
        </iframe>
        <!-- <table mat-table [dataSource]="dataSource" matTableResponsive class="audit-table">
    
             
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef>ID</th>
                  <td mat-cell *matCellDef="let element"> {{element.id}} </td>
                </ng-container>
           
                <ng-container matColumnDef="vendorNo">
                  <th mat-header-cell *matHeaderCellDef> Vendor No</th>
                  <td mat-cell *matCellDef="let element"> {{element.vendorNo}} </td>
                </ng-container>
              
            
                <ng-container matColumnDef="vendarName">
                  <th mat-header-cell *matHeaderCellDef> Vendar Name</th>
                  <td mat-cell *matCellDef="let element"> {{element.vendarName}} </td>
                </ng-container>

                <ng-container matColumnDef="vendorContactPerson">
                  <th mat-header-cell *matHeaderCellDef> Vendor Contact Person</th>
                  <td mat-cell *matCellDef="let element"> {{element.vendorContactPerson}} </td>
                </ng-container>
                <ng-container matColumnDef="email">
                    <th mat-header-cell *matHeaderCellDef>Email</th>
                    <td mat-cell *matCellDef="let element"> {{element.email}} </td>
                  </ng-container>
                  
                  <ng-container matColumnDef="region">
                    <th mat-header-cell *matHeaderCellDef>Region</th>
                    <td mat-cell *matCellDef="let element"> {{element.region}} </td>
                  </ng-container>
                  <ng-container matColumnDef="country">
                    <th mat-header-cell *matHeaderCellDef>Country</th>
                    <td mat-cell *matCellDef="let element"> {{element.country}} </td>
                  </ng-container>
                  
                  <ng-container matColumnDef="yearQ">
                    <th mat-header-cell *matHeaderCellDef>Year-Q</th>
                    <td mat-cell *matCellDef="let element"> {{element.yearQ}} </td>
                  </ng-container>
                     
                     <ng-container matColumnDef="auditor">
                        <th mat-header-cell *matHeaderCellDef>Auditor</th>
                        <td mat-cell *matCellDef="let element"> {{element.auditor}} </td>
                      </ng-container>
                         
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let element"> {{element.status}} </td>
                  </ng-container>
               
                 
                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef> Actions </th>
                    <td mat-cell *matCellDef="let element"> 
                        <div fxLayout="row" >
                            <div class="config-table-icon" style="margin-top: 8px" (click)="goSupplier()">
                                <mat-icon style="color: #4F4F4F;font-size: 18px;">near_me</mat-icon>
                            </div>
                            <div class="config-table-icon"  style="margin-top: 8px" (click)="goPage('observations/audit-plan-quality')" >
                                <mat-icon style="color: #4F4F4F;font-size: 18px;">remove_red_eye</mat-icon>
                            </div>
                         
                            <div class="config-table-icon"style="margin-top: 8px" (click)="goPage('observations/scorecard')">
                                
                                <mat-icon style="color: #4F4F4F;font-size: 18px;">help_outline</mat-icon>
                          
                           </div>
                            <div  class="config-table-icon"  style="margin-top: 8px" (click)="goPage('action/create-action')">
                                <mat-icon style="color: #4F4F4F;font-size: 18px;">add_circle</mat-icon>
                            </div>
                            <div  class="config-table-icon"  style="margin-top: 8px" (click)="goPage('observations/audit-summary')">
                                <mat-icon style="color: #4F4F4F;font-size: 18px;">text_snippet</mat-icon>
                            </div>
                            
                        
                        </div>
                     
                    </td>
                  </ng-container>
                
                <tr mat-header-row *matHeaderRowDef="displayedColumns2"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns2"></tr>
              </table>
               -->

      </div>
    </div>
  </div>
</div>