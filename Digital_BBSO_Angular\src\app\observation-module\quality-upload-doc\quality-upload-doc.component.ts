import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { FormControl } from '@angular/forms';
import { CogniteAuthentication, CogniteClient } from '@cognite/sdk';
import { TokenService } from 'src/app/services/token.service';
import { TranslateService } from '@ngx-translate/core';
import { MatStepper } from '@angular/material/stepper';
import { environment } from 'src/environments/environment';
import { LanguageService } from 'src/app/services/language.service';

@Component({
  selector: 'app-quality-upload-doc',
  templateUrl: './quality-upload-doc.component.html',
  styleUrls: ['./quality-upload-doc.component.scss']
})
export class QualityUploadDocComponent implements OnInit {

  client: CogniteClient;
  auditId: any;
  scheduleId: any;

  uploadDocArray = [];
  selectedSite: any;
  loaderFlag: boolean;
  labels = {}

  @Input() auditData: any;
  @Input() stepper: MatStepper;
  constructor(private tokenService: TokenService, private router: Router, private dataService: DataService, private commonService: CommonService, private translate:TranslateService,
    private languageService: LanguageService,
    private cd:ChangeDetectorRef, private ngZone:NgZone
  ) {
    this.labels = {
      'treeheaderDocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderDocument'] || 'treeheaderDocument',
      'treeheaderUploaddocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderUploaddocument'] || 'treeheaderUploaddocument',
      'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile',
      'treeheaderUploaded': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderUploaded'] || 'treeheaderUploaded',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
    }
    var _this = this;
    _this.loaderFlag = true;
    // if (!this.router.getCurrentNavigation().extras.state) {
    //   this.router.navigate(['observations/completed']);
    // }
   

    // this.auditId = this.router.getCurrentNavigation().extras.state["id"];
    // this.scheduleId = this.router.getCurrentNavigation().extras.state["scheduleId"];
    this.auditId = _this.dataService["auditData"]["externalId"];
    this.scheduleId = _this.dataService["auditData"]["refOFWASchedule"]["externalId"];
    this.getInialTokel(this.tokenService.getToken());
    console.log(_this.dataService["auditData"])
    var site = _this.commonService.siteList.find(e => e.externalId == _this.dataService["auditData"]["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
    _this.selectedSite = site;
    _this.dataService.postData({ "name": "EvidenceChecklist" }, _this.dataService.NODE_API + "/api/service/listCommonRefEnum").subscribe((resData: any) => {
      var checkList = resData["data"]["list" + _this.commonService.configuration["typeCommonRefEnum"]]["items"]
      // _this.dataService.postData({ schedules: this.scheduleId }, _this.dataService.NODE_API + "/api/service/listEvidenceChecklist").subscribe(data => {
      // var resData = data["data"]["list" + _this.commonService.configuration["typeEvidenceChecklist"]]["items"];
      console.log(_this.dataService["auditData"])
      var evidenceData = _this.dataService["auditData"]["evidenceDocument"]["items"];
      _this.loaderFlag = false;
      var myChecklist = _this.dataService.auditData["refOFWASchedule"]["evidenceChecklist"] && _this.dataService.auditData["refOFWASchedule"]["evidenceChecklist"].length>0 ? _this.dataService.auditData["refOFWASchedule"]["evidenceChecklist"] :checkList
      _.each(myChecklist, function (params: any) {
        var myValue = evidenceData.find(e => params == e.name);
        if (myValue) {
          var evidenceDocument = myValue;
          // if (myValue.evidenceDocument.items.length > 0) {
          //   evidenceDocument = myValue.evidenceDocument.items[0];
          // }
          _this.uploadDocArray.push({
            "externalId": myValue.externalId,
            "name": params,
            "upload": new FormControl(),
            "evidenceObj": [evidenceDocument]
          })
        } else {
          _this.uploadDocArray.push({
            "name": params,
            "upload": new FormControl()
          })
        }
      })
      // })
    })
  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'treeheaderDocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderDocument'] || 'treeheaderDocument',
          'treeheaderUploaddocument': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderUploaddocument'] || 'treeheaderUploaddocument',
          'chooseFile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseFile'] || 'chooseFile',
          'treeheaderUploaded': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderUploaded'] || 'treeheaderUploaded',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })

    if(!this.auditData){
      this.router.navigate(['observations/completed']);
    }
  }
  async getInialTokel(token: any) {
    var _this = this;
    const project = this.dataService.project
    const getToken = async () => {
      return token;
    };
    const appId = this.dataService.appId
    const baseUrl = this.dataService.baseUrl;
    _this.client = await new CogniteClient({
      appId,
      project,
      baseUrl,
      getToken
    });
    var clientAuthent = await _this.client.authenticate();
  }
  async uploadFile(item, $event) {

    var _this = this
    _this.loaderFlag = true;
    if ($event.target.files && $event.target.files.length) {
      const fileContent = $event.target.files[0];
      const buffer = await fileContent.arrayBuffer();
      const myArray = fileContent.name.split(".");
      var fileupload: any = await _this.client.
        files.upload(
          { name: myArray[0], mimeType: fileContent.type },
          buffer);
      var myObj = {
        "name": item.name,
        "mimetype": fileupload.mimeType,
        "pathURL": fileupload.uploadUrl,
        "pathStore": "CDF",
        "evidenceDocumentId": fileupload.id + "",
        "description": ""

      };
      var postObj = {
        "type": _this.commonService.configuration["typeEvidenceDocument"],
        "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
        "unitCode": _this.commonService.configuration["allUnitCode"],
        "items": [
          myObj
        ]
      }
      _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
        if (data["items"].length > 0) {
          item.evidenceObj = data["items"];
        }
        _this.loaderFlag = false;
        this.commonService.triggerToast({ type: 'success', title: "", msg: this.commonService.toasterLabelObject['toasterUploadsuccess'] });
      })
    }
  }
  submitClick() {
    let _this = this;
    _this.loaderFlag = true;

    let myEvidenceArray = [];
    console.log(_this.uploadDocArray);

    // Build evidence array
    _this.uploadDocArray.forEach((eEvidence) => {
      console.log(eEvidence["evidenceObj"]);
      if (eEvidence["evidenceObj"]) {
          eEvidence["evidenceObj"].forEach(evidence => {
              const edgeObj = {
                  "instanceType": "edge",
                  "space": _this.dataService["auditData"]["space"],
                  "externalId": `${_this.dataService["auditData"]["externalId"]}-${evidence["externalId"]}`,
                  "type": {
                      "space": _this.commonService.configuration["DataModelSpace"],
                      "externalId": `${_this.commonService.configuration["typeAudit"]}.evidenceDocument`
                  },
                  "startNode": {
                      "space": _this.dataService["auditData"]["space"],
                      "externalId": _this.dataService["auditData"]["externalId"]
                  },
                  "endNode": {
                      "space": evidence["space"],
                      "externalId": evidence["externalId"]
                  }
              };
              myEvidenceArray.push(edgeObj);
          });
      }
      
    });

    // First API call
    _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

        const form = JSON.parse(localStorage.getItem('form'));
        const audit = JSON.parse(localStorage.getItem('emailsend'));

        console.log("auditForm", audit);
        console.log("formData", form);

        // Prepare contact data
        const postContact = {
            "mobileNumber": form["telephone"],
            "product": form["product"],
            "corporateWebSite": form["website"],
            "jobTitle": form["jobTitle"],
            "externalId": audit["externalId"]
        };

        const observeObjContact = {
            "type": _this.commonService.configuration["typeAudit"],
            "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
            "unitCode": _this.commonService.configuration["allUnitCode"],
            "items": [postContact]
        };

        console.log("observeObjContact", observeObjContact);

        // Second API call for contact
        _this.dataService.postData(observeObjContact, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
            if (data["items"] && data["items"].length > 0) {

                const postAuditSummary = {
                    "productFamily": form["productFamily"],
                    "locationManufacturingSite": form["manufacturingSite"]
                };

                const observeObjAuditSummary = {
                    "type": _this.commonService.configuration["typeAuditSummary"],
                    "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
                    "unitCode": _this.commonService.configuration["allUnitCode"],
                    "items": [postAuditSummary]
                };

                // Third API call for audit summary
                _this.dataService.postData(observeObjAuditSummary, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe((data) => {


                  const form = JSON.parse(localStorage.getItem('form'));
                  const audit = JSON.parse(localStorage.getItem('emailsend'));

                  console.log("data", data);
                    const lastSummary = {
                        "externalId": audit["externalId"],
                        "refOFWAAuditSummary": {
                            "externalId": data["items"][0]["externalId"],
                            "space": data["items"][0]["space"]
                        },
                        "emailAddress": form["email"],
                    };

                    console.log("lastSummary", lastSummary);

                    const finalAuditSummaryObj = {
                        "type": _this.commonService.configuration["typeAudit"],
                        "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
                        "unitCode": _this.commonService.configuration["allUnitCode"],
                        "items": [lastSummary]
                    };

                    var instanceNotification = [
                      {
                        application: this.commonService.applicationInfo.name,
                        description: 'Audit Completed Notification',
                        users: [audit.refOFWASchedule.performerAzureDirectoryUserID.externalId],
                        severity: audit.refOFWASchedule?.priority || 'Low',
                        properties: [
                          {
                            name: 'Title',
                            value: audit.refOFWASchedule.title,
                            type: 'text',
                          },
                          {
                        name: 'Audit Number',
                        value: audit.refOFWASchedule.auditNumber,
                        type: 'text',
                      },
                      {
                        name: 'Audit Type',
                        value:audit.refSubProcess.name,
                        type: 'text',
                      },
                      {
                        name: 'Start',
                        value: new Date(audit.observationStartDate).toDateString(),
                        type: 'text',
                      },
                      {
                        name: 'End',
                        value: new Date(audit.observationEndDate).toDateString(),
                        type: 'text',
                      },
                      { name: 'Year', value: parseInt(audit.refOFWASchedule.year), type: 'number' },
                      {
                        name: 'Quarter',
                        value: audit.refOFWASchedule.quarter,
                        type: 'text',
                      },
                        ],
                      },
                    ];
                    // Fourth API call for final audit summary
                    _this.dataService.postData(finalAuditSummaryObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(() => {
                      let notificationType = 'Audit Completed';
                      
                      _this.commonService.notification(
                        instanceNotification,
                        notificationType
                      );
                        _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });

                        // Navigation logic after all API calls succeed
                        const scheduleCategory = _this.commonService.processList.find(e => e.externalId === _this.dataService.auditData["refOFWASchedule"]["refOFWACategory"]["externalId"])["refOFWAProcess"];
                        const routePath = (scheduleCategory && scheduleCategory.name === 'Internal Audit') ? 'observations/list' : 'observations/completed';
                        _this.router.navigate([routePath], { state: { id: _this.auditId, listType: "Audit" } });

                    }, error => {
                        console.error("Error during final audit summary API call", error);
                        _this.loaderFlag = false;
                    });
                }, error => {
                    console.error("Error during audit summary creation", error);
                    _this.loaderFlag = false;
                });
            }
        }, error => {
            console.error("Error during contact API call", error);
            _this.loaderFlag = false;
        });
    }, error => {
        console.error("Error during evidence API call", error);
        _this.loaderFlag = false;
    });
}

  // var postObj = {
    //   "type": _this.commonService.configuration["typeEvidenceChecklist"],
    //   "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
    //   "unitCode": _this.commonService.configuration["allUnitCode"],
    //   "items": []
    // }
    // _.each(this.uploadDocArray, function (eData) {
    //   var myObj = {
    //     "name": eData.name
    //   };
    //   if (eData.externalId && eData.externalId.length > 0) {
    //     myObj["externalId"] = eData.externalId;
    //   }

    //   myObj["refOFWACategory"] = {
    //     space: _this.dataService["auditData"]["refOFWASchedule"]["refOFWACategory"]["space"],
    //     externalId: _this.dataService["auditData"]["refOFWASchedule"]["refOFWACategory"]["externalId"],
    //   }
    //   myObj["refOFWASchedule"] = {
    //     space: _this.dataService["auditData"]["refOFWASchedule"]["space"],
    //     externalId: _this.dataService["auditData"]["refOFWASchedule"]["externalId"],
    //   }
    //   myObj["isDocumentUploaded"] = eData.evidenceObj ? true : false;
    //   postObj.items.push(myObj);
    // })
    // _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
    //   if (data["items"].length > 0) {
    //     _this.loaderFlag = false;
    //     _this.evidenceEdgeCreation(data["items"]);
    //   }
    //   _this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    //   _this.router.navigate(['observations/completed'], { state: { id: this.auditId} });

    // }) 
  backButton() {
    this.stepper.previous();
    // this.router.navigate(['/observations/quality-assessment'], { queryParams: { id: this.scheduleId } });
    // this.router.navigate(['observations/quality-assessment', { queryParams: {id:this.scheduleId}, queryParamsHandling: 'preserve' }]);
  }

  evidenceEdgeCreation(evidenceChecklist: []) {
    var _this = this;
    var myEvidenceArray = [];
    _.each(_this.uploadDocArray, function (eData, index) {
      if (eData.evidenceObj.length > 0) {
        var eEvidence = eData.evidenceObj[0];
        var eChecklist = evidenceChecklist[index];
        var edgeObj = {
          "instanceType": "edge",
          "space": eChecklist["space"],
          "externalId": eChecklist["externalId"] + "-" + eEvidence["externalId"],
          "type": {
            "space": _this.commonService.configuration["DataModelSpace"],
            "externalId": _this.commonService.configuration["typeEvidenceChecklist"] + ".evidenceDocument"
          },
          "startNode": {
            "space": eChecklist["space"],
            "externalId": eChecklist["externalId"]
          },
          "endNode": {
            "space": eEvidence["space"],
            "externalId": eEvidence["externalId"]
          }
        }
        myEvidenceArray.push(edgeObj);
      }
    });
    if (myEvidenceArray.length > 0) {
      _this.dataService.postData(myEvidenceArray, _this.dataService.NODE_API + "/api/service/createEdge").subscribe(data => {

      });
    }

  }

  goPage(page) {
    this.router.navigate([page]);
  }

  async uploadMultipleFiles(item, $event) {
    const _this = this;
    _this.loaderFlag = true;
  
    if ($event.target.files && $event.target.files.length) {
      const files = $event.target.files;
  
      for (let fileContent of files) {
        const buffer = await fileContent.arrayBuffer();
        const myArray = fileContent.name.split(".");
  
        // Upload file to the Cognite client
        const fileUpload: any = await _this.client.files.upload(
          { name: myArray[0], mimeType: fileContent.type },
          buffer
        );
  
        // Prepare the evidence object
        const evidenceObj = {
          name: item.name,
          mimetype: fileUpload.mimeType,
          pathURL: fileUpload.uploadUrl,
          pathStore: "CDF",
          evidenceDocumentId: fileUpload.id + "",
          description: ""
        };
  
        // Post the evidence object to the server
        const postObj = {
          type: _this.commonService.configuration["typeEvidenceDocument"],
          siteCode: _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
          unitCode: _this.commonService.configuration["allUnitCode"],
          items: [evidenceObj]
        };
  
        await _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
          if (data["items"].length > 0) {
            if (!item.evidenceObj) {
              item.evidenceObj = [];
            }
            item.evidenceObj.push(...data["items"]); // Append the new evidence objects
          }
          _this.loaderFlag = false;
        }, error => {
          console.error("Error during file upload", error);
          _this.loaderFlag = false;
        });
      }
    } else {
      _this.loaderFlag = false;
    }
  }
  
}
