<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
  <div fxFlex="100">
    <!-- <app-common-filter [siteControl]="siteControl">
      
    </app-common-filter> -->
    <div fxLayout="row auto" fxLayoutAlign="start center">
      <div fxLayout="row">
      <!-- <div fxFlex="100" style="display: flex;">
        <div class="treat-md" style="align-content: center;">
          <span class="semi-bold">
            <!-- {{ 'CONFIGURATION.UNIT_SITE.SITE' | translate }} -->
            <!--{{ labels['site'] }}
          </span>
        </div>
        <div>
          <mat-form-field appearance="outline" class="set-back-color">
            <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesite'] }}" [formControl]="siteControl"
              disableOptionCentering>
              <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="siteList"
                (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
              <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                {{item.description}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div> -->
      <div fxFlex="100" style="display: flex;" *ngIf="processFlag">
        <div class="treat-md" style="align-content: center;">
          <span class="semi-bold">
            <!-- {{ 'CONFIGURATION.UNIT_SITE.PROCESS' | translate }} -->
            {{ labels['process'] }}
          </span>
        </div>
        <div>
          <mat-form-field appearance="outline" class="set-back-color">
            <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChooseprocess'] }}"
              [formControl]="processControl" disableOptionCentering>
              <mat-select-filter [noResultsMessage]=" labels['commonfilterNoresults'] "
                [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'" [array]="processList"
                (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
              <mat-option *ngFor="let item of filteredProcessList" [value]="item.externalId">
                <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                <!-- {{ labels['cards'+item.name] }} -->
                <span *ngIf="item.name == 'Hazards'" >
                    {{ labels['cardsHazards'] }}
                  </span>
                  <span *ngIf="item.name == 'All'" >
                    {{ labels['all'] }}
                  </span>
                  <span *ngIf="item.name == 'Field Walk'" >
                    {{ labels['fieldWalk'] }}
                  </span>
                  <span *ngIf="item.name == 'Audit'" >
                    {{ labels['audit'] }}
                  </span>
                  <span *ngIf="item.name == 'Observation'" >
                    {{ labels['observation'] }}
                  </span>
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxFlex="100" style="display: flex;" *ngIf="processFlag">
        <div style="margin-left: 5px;">
          <mat-form-field appearance="outline" class="add_sub_drop">
            <input type="text" [formControl]="searchControl" class="input"
                placeholder="{{labels['commonfilterSearch'] }}" aria-span="Search" matInput>
        </mat-form-field>
        </div>
      </div>
    </div>
    </div>

    
    
    <app-main-observation *ngIf="corePrincipleFlag"  (newItemEvent)="getNextevent($event)"  
    [site]="siteSubject.asObservable()" [preFilled]="selectedCorePrinciple"></app-main-observation>

    <app-create-observation *ngIf="processFlag && selectedCorePrinciple" (newItemEvent)="getNextevent($event)"
      [corePrinciple]="selectedCorePrinciple" [process]="selectedProcess" [subProcess]="selectedSubProcess" 
      [search]="searchControl.value"></app-create-observation>

    <!-- <app-sub-observation *ngIf="subProcessFlag && selectedProcess" (newItemEvent)="getNextevent($event)" 
      [process]="selectedProcess" [corePrinciple]="selectedCorePrinciple" ></app-sub-observation> -->

    <app-behaviour-checklist *ngIf="checkListFlag && selectedSubProcess"  (newItemEvent)="getNextevent($event)"
    [process]="selectedProcess" [corePrinciple]="selectedCorePrinciple" 
    [subProcess]="selectedSubProcess" [observation]="observation" [scheduleDetails]="scheduleDetails" ></app-behaviour-checklist>
    <app-action-checklist *ngIf="checkListFlag2 && selectedSubProcess"  (newItemEvent)="getNextevent($event)"
  [process]="selectedProcess" [corePrinciple]="selectedCorePrinciple" 
  [subProcess]="selectedSubProcess" [observation]="observation" [scheduleDetails]="scheduleDetails"></app-action-checklist>
    <app-critical-checklist *ngIf="checkListFlag3 && selectedSubProcess"  (newItemEvent)="getNextevent($event)"
    [process]="selectedProcess" [corePrinciple]="selectedCorePrinciple" [id]="id"
    [subProcess]="selectedSubProcess" [observation]="observation" [scheduleDetails]="scheduleDetails"></app-critical-checklist>
    <app-procedure-audit-checklist *ngIf="checkListFlag4 && selectedSubProcess"  (newItemEvent)="getNextevent($event)"
    [process]="selectedProcess" [corePrinciple]="selectedCorePrinciple" [id]="id"
    [subProcess]="selectedSubProcess" [observation]="observation" [scheduleDetails]="scheduleDetails"></app-procedure-audit-checklist>


    <div fxLayout="row" class="clear"> </div>
  </div>

</div>
