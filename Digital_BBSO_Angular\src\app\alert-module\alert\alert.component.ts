import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';

@Component({
  selector: 'app-alert',
  templateUrl: './alert.component.html',
  styleUrls: ['./alert.component.scss']
})
export class AlertComponent implements OnInit {

  alertArray = [
    {
      type:"Observation",
      description:"To close the actions that has been raised for Water Pipe Corrosion",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"Overdue"
    },
    {
      type:"Observation",
      description:"Your scheduled observation has not been completed",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"Overdue"
    },
    {
      type:"Observation",
      description:"To close the actions that has been raised for Water Pipe Corrosion",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"In Progress"
    },
    {
      type:"Observation",
      description:"To close the actions that has been raised for Water Pipe Corrosion",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"Overdue"
    },
    {
      type:"Observation",
      description:"To close the actions that has been raised for Water Pipe Corrosion",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"Overdue"
    },
    {
      type:"Observation",
      description:"To close the actions that has been raised for Water Pipe Corrosion",
      assignedBy:"Kristin",
      dueOn:"Mar 22, 2023",
      application:"Threat Management",
      status:"Overdue"
    }
  ]
  displayedColumns: any = [];

  
  allColumns = [

    { key: 'type', displayName: "Type", name: "type", activeFlag: true, summary: false },
    { key: 'description', displayName: "Description", name: "description", activeFlag: true, summary: false },
    { key: 'assignedBy', displayName: "Assigned By", name: "assignedBy", activeFlag: true, summary: false },
    { key: 'dueOn', displayName: "Due On", name: "dueOn", activeFlag: true, summary: false },
     { key: 'status', displayName: "Status", name: "status", activeFlag: true, summary: false },
     { key: 'actions', displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  ];
  url: any ="";
  constructor(private router: Router, private dataService: DataService,private dialog: MatDialog,private tokenService:TokenService) {
    this.url =this.dataService.React_API+ "/alertList";
   
   }

  ngOnInit(): void {
    var _this = this;
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "FormConfig") {
        console.log(e.data.action)
        if (e.data.action == "FormView") {
        
          console.log('FormView-->')
        } else if (e.data.action == "FormEdit") {
        
          console.log('FormEdit-->')
        
      }
    };


  }
}
  ngAfterViewInit(): void {
    
    var _this=this;
    var iframe = document.getElementById('iFrameFormConfig');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FormConfig", "action": "Column", "data": this.displayedColumns }, '*');
  // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');

  }
  ngOnDestroy(): void {
    
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    console.log('_this.displayedColumns,',_this.displayedColumns,)
    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol',summaryCol)
   // this.columnSummarysubject.next(summaryCol);
   var iframe = document.getElementById('iFrameFormConfig');
   if (iframe == null) return;
   var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
   iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
   iWindow?.postMessage({ "type": "FormConfig", "action": "Summary", "data": summaryCol }, '*');
  }

}
