<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="audit-plan-unit-section schedular-tracker-section">
            <!-- <app-common-filter [siteControl]="siteControl"></app-common-filter> -->
            <div class="observation-list-filter-wrapper">
                <commom-label labelText="{{ labels['createscheduleTitle'] }}" [tagName]="'h4'"
                    [cstClassName]="'heading unit-heading'"></commom-label>
                <!-- <div class="observation-list-filter">
                    <common-lib-button *ngIf="ScheduleTracker && ScheduleTracker.featureAccessLevelCode !='NoAccess'"  [className]="'cst-btn marginLeft-5'" [text]="'BUTTON.TRACKER'"
                        (buttonAction)="goPage('schedule-list/schedule-tracker')"></common-lib-button>
                </div> -->
            </div>
            <!-- <mat-form-field appearance="outline" class="set-back-color">
                <mat-select (click)="filterClick()" placeholder="Choose site" [formControl]="siteControl"
                    disableOptionCentering>
                    <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="siteList"
                        (filteredReturn)="filteredSiteList =$event"></mat-select-filter>
                    <mat-option *ngFor="let item of filteredSiteList" [value]="item.externalId">
                        {{item.description}}
                    </mat-option>
                </mat-select>
            </mat-form-field> -->
            <div class="audit-head-icon">
                <div class="icon-bg-box schedular-tracker-back-icon" (click)="goPage('schedule-list')">
                    <mat-icon>arrow_back_ios</mat-icon>
                </div>
            </div>
        </div>
        <div class="ovservation-list-section" *ngIf="siteControl && siteControl.value">
            

            <div class="marginTop outerbox" fxLayout="column" >
                <div fxLayout="row" fxLayoutGap="10px" fxFlex="100">
                <div>
                    <div>
                        <!-- <span class="start-color">
                            &nbsp; *
                        </span> -->
                    </div>
                    <div>
                        <mat-form-field appearance="outline" >
                            <mat-label>{{ labels['corePrincipleTitle'] }}</mat-label>
                            <mat-select placeholder="{{ labels['commonfilterChoosecoreprinciple'] }}" [formControl]="corePrincipleControl" disableOptionCentering>
                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                    [array]="corePrincipleList"
                                    (filteredReturn)="filteredCorePrincipleList =$event"></mat-select-filter>
                                <mat-option *ngFor="let item of filteredCorePrincipleList" [value]="item.externalId">
                                    <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                                    {{ labels['cards'+item.name] }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                   
                </div>
                <div>
                    <div>
                        
                        <!-- <span class="start-color">
                            &nbsp; *
                        </span> -->
                    </div>
                    <div>
                        <mat-form-field appearance="outline" >
                            <mat-label>{{ labels['process'] }}</mat-label>
                            <mat-select placeholder="{{ labels['commonfilterChooseprocess'] }}" [formControl]="processControl" disableOptionCentering>
                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                    [array]="processList"
                                    (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
                                <mat-option *ngFor="let item of filteredProcessList" [value]="item.externalId">
                                    <span *ngIf="item.name != 'Field Walk' || item.name != 'Observation' || item.name != 'Audit' " >
                                        {{ labels['cards'+item.name] }}
                                    </span>
                                    <span *ngIf="item.name == 'Field Walk'" >
                                        {{ labels['fieldWalk'] }}
                                    </span>
                                    <span *ngIf="item.name == 'Observation'" >
                                        {{ labels['observation'] }}
                                    </span>
                                    <span *ngIf="item.name == 'Audit'" >
                                        {{ labels['audit'] }}
                                    </span>
                                    <!-- {{'OBSERVATION.MAIN.CARDS.'+item.name | translate }} -->
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                </div>
                <div>
                    <div>
                        
                        <!-- <span class="start-color">
                            &nbsp; *
                        </span> -->
                    </div>
                    <div>
                        <!-- <mat-form-field appearance="outline" class="schedule-form-width">

                            <mat-select value="Behaviour" formControlName="observationType">
                                <mat-option value="Behaviour">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.BEHAVIOUR' | translate }}</mat-option>
                                <mat-option value="Hazards">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.HAZARDS' | translate }}</mat-option>
                                <mat-option value="Incidents">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.INCIDENTS' | translate }}</mat-option>
                            </mat-select>
                        </mat-form-field> -->
                        <mat-form-field appearance="outline" >
                            <mat-label *ngIf="selectedProcess && selectedProcess.name == 'Observation'">{{ labels['observationType'] }}</mat-label>
                            <mat-label *ngIf="selectedProcess && selectedProcess.name == 'Audit'">{{ labels['formcontrolsAudittype'] }}</mat-label>
                            <mat-label *ngIf="selectedProcess && selectedProcess.name != 'Audit' && selectedProcess.name != 'Observation'">{{ labels['subProcess'] }}</mat-label>
                            <mat-label *ngIf="!selectedProcess " >{{ labels['subProcess'] }}</mat-label>
                            <mat-select placeholder="{{ labels['observationType'] }}" [formControl]="observationType" disableOptionCentering>
                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                    [array]="subProcessList"
                                    (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                    {{item.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>

                    </div>

                </div>

            </div>
                <!-- <div>
                    <div>
                        <span class="subheading-1 regular">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.PROCESS' | translate }}</span>
                        <span class="start-color">
                            &nbsp; *
                        </span>
                    </div>
                    <div>
                      

                        <mat-radio-group aria-label="Select an option" fxLayoutGap="15px" [formControl]="processRadio">
                            <mat-radio-button *ngIf="ScheduleCreateObservation && ScheduleCreateObservation.featureAccessLevelCode !='NoAccess'" value="Observation" name="process">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.OBSERVATION' | translate }}</mat-radio-button>
                            <mat-radio-button *ngIf="ScheduleCreateFieldWalk && ScheduleCreateFieldWalk.featureAccessLevelCode !='NoAccess'" value="Field Walk" name="process">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.FIELD_WALK' | translate }}</mat-radio-button>
                            <mat-radio-button *ngIf="ScheduleCreateAudit && ScheduleCreateAudit.featureAccessLevelCode !='NoAccess'"  value="Audit" name="process">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.AUDIT' | translate }}</mat-radio-button>
                        </mat-radio-group>
                    </div>

                </div> -->
               
                <div fxFlex="100">

                
                <form *ngIf="loadForm" [formGroup]="createSchedule">
                    <div *ngIf="radioForm==false && observationType.value">
                        <div class=" marginTop">
                            <div>
                                
                                <!-- <span class="start-color">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div fxLayout="column">

                                <mat-form-field appearance="outline" style="height: 90px;" class="schedule-form-width">
                                    <mat-label>{{ labels['title'] }}</mat-label>

                                    <textarea matInput formControlName="title" cdkTextareaAutosize
                                        cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>
                                <span style="font-size: 12px;">
                                    *{{ labels['createactionDescmessage'] }}
                                </span>
                            </div>

                        </div>
                        <div class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                           
                        </div>
                        <div  *ngIf="configDetail && configDetail.locationObserved.isEnabled" class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    
                                    <!-- <span class="start-color" *ngIf="configDetail && configDetail.locationObserved.isMandatory">
                                        &nbsp; *
                                    </span> -->
                                </div>
                                <div>
                                  
                                    <mat-form-field appearance="outline" class="schedule-form-width">
                                        <mat-label>{{ labels['locationObserved'] }}</mat-label>
                                        <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}" formControlName="locationObserver"
                                            disableOptionCentering>
                                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'"
                                                [array]="reportingLocationList"
                                                (filteredReturn)="filteredReportingLocationList =$event"></mat-select-filter>
                                            <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item.externalId">
                                                {{item.description}}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                </div>

                            </div>

                        </div>
                        <div class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    
                                    <!-- <span class="start-color">
                                        &nbsp; *
                                    </span> -->
                                </div>
                                <div>
                                    <!-- <mat-radio-group aria-label="Select an option" fxLayoutGap="15px" [formControl]="selectionControl"  (change)="onSelectionChange($event)">
                                        <mat-radio-button value="user">User</mat-radio-button>
                                        <mat-radio-button value="group">Group</mat-radio-button>
                                    </mat-radio-group> -->
                                </div>
                                <div>
                                    <!-- <mat-form-field appearance="outline" class="schedule-form-width">

                                        <mat-select value="Albert" formControlName="observer">
                                            <mat-option value="Albert">Albert</mat-option>
                                            <mat-option value="James">James</mat-option>
                                            <mat-option value="Michael">Michael</mat-option>
                                        </mat-select>
                                    </mat-form-field> -->
                                    <mat-form-field appearance="outline" class="schedule-form-width">
                                        <mat-label>{{ labels['observer'] }}</mat-label>
                                        <mat-select placeholder="{{ labels['commonfilterChoosebehalf'] }}" formControlName="observer" disableOptionCentering>
                                          <ng-container *ngIf="selectionControl.value === 'user'">
                                            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                               [array]="behalfList"
                                                               (filteredReturn)="onBehalfChange($event)">
                                            </mat-select-filter>
                                          </ng-container>
                                          <mat-option *ngFor="let item of selectionControl.value === 'user' ? filteredBehalfList : filteredGroupList" [value]="item.externalId">
                                            {{item.name}}
                                          </mat-option>
                                        </mat-select>
                                      </mat-form-field>
                                      
                                    

                                </div>

                            </div>

                        </div>
                        <div class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    <span class="body-2 regular" style="font-size: 14px !important;">{{ labels['occurrence'] }}</span>
                                    <span class="start-color">
                                        &nbsp; *
                                    </span>
                                </div>
                                <div class="schedule-form-width">
                                    <!-- [ngClass]="{'disabled': isView}" -->
                                    <commom-select-box [modeselect]="eventData && eventData.value"  [disable]="actionFlag =='View'||actionFlag =='Edit' " [ngClass]="{'disabled-select-box': actionFlag === 'View' || actionFlag === 'Edit'}" [options]="OccurrenceOptions" (selectedOption)="selectedOption($event)"
                                [className]="'dashboard-announcement-select form-full-width-drop-down'"></commom-select-box>
                                <div class="occurrence-section" *ngIf="occurrenceData && occurrenceData.view && eventData && eventData.value != '1'">
                                    <div class="occurrence-text text-display">
                                        <span>{{labels['occursevery'] }}</span> 
                                    <span *ngIf="occurrenceData.view == 'Daily' || (occurrenceData.view == 'Weekly' && occurrenceData.days.length == 7)" class="mlr-2">{{labels['day']  }}</span> 
                                    <span *ngIf="occurrenceData.view == 'Weekly' && occurrenceData.days.length < 7" class="mlr-2">
                                        <ng-container *ngFor="let day of occurrenceData.days; let ind =index">
                                            {{ labels[day.toLowerCase()] }}
                                            <span *ngIf="occurrenceData.days.length > 1 && ind == occurrenceData.days.length - 2">{{labels ['and']}}</span>
                                            <span *ngIf="occurrenceData.days.length > 2 && ind < occurrenceData.days.length - 2">,</span>
                                        </ng-container>
                                    </span> 
                                    <span *ngIf="occurrenceData.view == 'Monthly'">
                                        <span class="mlr-2">{{labels['month'] }}</span>
                                        <span *ngIf="occurrenceData.selectedItem == 'On day'" class="mlr-2">{{labels['onday'] }}</span>
                                        <span *ngIf="occurrenceData.selectedItem != 'On day'" class="mlr-2">{{ labels['on'] }}</span>
                                        <span *ngIf="occurrenceData.selectedItem == 'On day'" class="mlr-2">{{occurrenceData.dayCount}}</span>
                                        <span *ngIf="occurrenceData.datType && occurrenceData.selectedItem != 'On day'" class="mlr-2">{{occurrenceData.datType}}</span>
                                        <span *ngIf="occurrenceData.datType && occurrenceData.selectedItem != 'On day'" class="mlr-2">{{occurrenceData.day}}</span>
                                    </span>
                                    <span *ngIf="occurrenceData.startDate && occurrenceData.view != 'Quarterly'" class="mlr-2">{{ labels['starting'] }}</span>
                                    <span *ngIf="occurrenceData.startDate" class="mlr-2">{{occurrenceData.startDate}}</span>
                                    <span *ngIf="occurrenceData.endDate" class="mlr-2">{{labels['until'] }}</span>
                                    <span *ngIf="occurrenceData.endDate" class="mlr-2">{{occurrenceData.endDate}}</span>
                                    </div>
                                </div>
                                </div>

                            </div>
                            <!-- <commom-label [labelText]="'ACTION.CREATE_ACTION.FORM_CONTROLS.OCCURRENCE'" [tagName]="'label'" [cstClassName]="'form-label d-flex mt-5'"
                                [strict]="true"></commom-label> -->
                          
                        </div>

                       
                        <div *ngIf="this.eventData && this.eventData.value == 1" class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    
                                    <!-- <span class="start-color">
                                        &nbsp; *
                                    </span> -->
                                </div>
                                <div>

                                    <mat-form-field appearance="outline"
                                        class="set-back-color-action schedule-form-sub-width">
                                        <mat-label>{{ labels['startDate'] }}</mat-label>
                                        <input formControlName="datetime" autocomplete="off" matInput
                                            [matDatepicker]="releasedAtPicker2" (click)="releasedAtPicker2.open()">
                                        <mat-datepicker-toggle matSuffix [for]="releasedAtPicker2">
                                        </mat-datepicker-toggle>
                                        <mat-datepicker #releasedAtPicker2>
                                        </mat-datepicker>
                                    </mat-form-field>
                                </div>

                            </div>


                        </div>
                        <div *ngIf="this.eventData && this.eventData.value == 1" class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    
                                    <!-- <span class="start-color">
                                        &nbsp; *
                                    </span> -->
                                </div>
                                <div>

                                    <mat-form-field appearance="outline"
                                        class="set-back-color-action schedule-form-sub-width">
                                        <mat-label>{{ labels['formcontrolsDuedate'] }}</mat-label>
                                        <input formControlName="duetime" autocomplete="off" matInput
                                            [matDatepicker]="releasedAtPicker3" (click)="releasedAtPicker3.open()">
                                        <mat-datepicker-toggle matSuffix [for]="releasedAtPicker3">
                                        </mat-datepicker-toggle>
                                        <mat-datepicker #releasedAtPicker3>
                                        </mat-datepicker>
                                    </mat-form-field>
                                </div>

                            </div>


                        </div>

                        <!-- <div class=" marginTop" fxLayout="row wrap" fxLayoutGap="10px">
                            <div>
                                <div>
                                    <span class="subheading-1 regular">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.OBSERVATION_POINTS' | translate }}</span>
                                    <span class="start-color">
                                        &nbsp; *
                                    </span>
                                </div>
                                <div>
                                    <mat-form-field appearance="outline" class="schedule-form-sub-width">

                                        <mat-select formControlName="observePoints">
                                            <mat-option value="50">50</mat-option>
                                            <mat-option value="40">40</mat-option>
                                            <mat-option value="30">30</mat-option>
                                        </mat-select>
                                    </mat-form-field>

                                </div>

                            </div>

                        </div> -->
                        <div class="create-schedular-fotter">
                            <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                                (buttonAction)="goPage('schedule-list')"></common-lib-button>
                            <common-lib-button *ngIf="actionFlag!='View'" [className]="'cst-btn'"  text="{{ labels['buttonSave'] }}"
                                (buttonAction)="submitClick();"></common-lib-button>
                        </div>
                    </div>
                </form>
                <form [formGroup]="auditForm">
                    <div *ngIf="radioForm==true && processControl.value">
                        <div class="marginTop">
                            <div>
                                
                                <!-- <span class="start-color">
                                    &nbsp; *
                                </span> -->
                            </div>
                            <div>

                                <mat-form-field appearance="outline" style="height: 90px;" class="schedule-form-width">
                                    <mat-label>{{ labels['title'] }}</mat-label>

                                    <textarea matInput formControlName="title" cdkTextareaAutosize
                                        cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                                </mat-form-field>
                            </div>

                        </div>

                        <div class="marginTop">
                            <div fxLayout="row wrap" fxLayoutGap="20px">
                                <!-- <div>
                                    <div>
                                        <span class="subheading-1 regular">{{ 'SCHEDULE.CREATE_SCHEDULE.FORM_CONTROLS.CORE_PRINCIPLE' | translate }}</span>
                                        <span class="start-color">
                                            &nbsp; *
                                        </span>
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-select placeholder="{{ labels['commonfilterChoosecoreprinciple'] }}" formControlName="corePrincipleType" disableOptionCentering>
                                                <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                                    [array]="corePrincipleList"
                                                    (filteredReturn)="filteredCorePrincipleList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredCorePrincipleList" [value]="item.externalId">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>

                                </div> -->
                                <div *ngIf="false">
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['formcontrolsAudittype'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChooseaudittype'] }}" formControlName="auditType" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                                    [array]="subProcessList"
                                                    (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                </div>
                                <div>
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['category']}}</mat-label>
                                            <mat-select placeholder="{{ labels['chooseCategory'] }}" formControlName="category" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                                    [array]="categoryList"
                                                    (filteredReturn)="filteredCategoryList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredCategoryList" [value]="item.externalId">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div>
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <!-- <mat-form-field appearance="outline" >
                                            <mat-select placeholder="{{ 'COMMONFILTER.CHOOSE_AUDIT_TYPE' | translate }}" formControlName="auditType" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'value'"
                                                    [array]="subProcessList"
                                                    (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field> -->
                                        <mat-form-field appearance="outline">
                                            <mat-label>{{ labels['tablecolsAuditnumber'] }}</mat-label>
                                            <input matInput placeholder="{{ labels['tablecolsAuditnumber'] }}" formControlName="auditNumber"/>
                                          </mat-form-field>

                                    </div>
                                </div>
                                
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Internal Audit'">
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['formcontrolsFirsttimeaudit'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChoose'] }} " formControlName="isFirstTimeAudit" disableOptionCentering>
                                               
                                                <mat-option *ngFor="let item of yesOrNoList" [value]="item.name">
                                                    {{ labels['formcontrols'+item.name] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Internal Audit'">
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div >
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['formcontrolsLegalentity'] }}</mat-label>
                                            <input type="text"  placeholder="{{ labels['formcontrolsLegalentity'] }}"  formControlName="legalEntity" 
                                                aria-span="Legal Entity" matInput>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Internal Audit'">
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div >
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['formcontrolsSitecertificatenumber'] }}</mat-label>
                                            <input type="text"  placeholder="{{ labels['formcontrolsSitecertificatenumber'] }}"  formControlName="siteCertificateNumber"
                                                aria-span="Site Certificate Number" matInput>
                                        </mat-form-field>
                                    </div>
                                </div>

                            </div>
                            <div fxLayout="row wrap" fxLayoutGap="20px" style="margin-top: 10px;">
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['formcontrolsSuppliernumber'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChoosesupplier'] }}" formControlName="Supplier" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'number'"
                                                    [array]="SupplierList"
                                                    (filteredReturn)="onSupplierNumChange($event)" ></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredSupplierList" [value]="item.externalId">
                                                    {{item.number}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div >
                                        <mat-form-field appearance="outline" class="location-input">
                                            <mat-label>{{ labels['formcontrolsSuppliername'] }}</mat-label>
                                            <input [disabled]="true" type="text" placeholder="{{ labels['formcontrolsSuppliername'] }}" [value]="selectedSupplier?.name || ''" class="input" aria-span="Search" matInput>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div >
                                        <mat-form-field appearance="outline" class="location-input">
                                            <mat-label>{{ labels['formcontrolsEmail'] }}</mat-label>
                                            <input type="text" formControlName="email" placeholder="{{ labels['formcontrolsSupplieremail'] }}" class="input"
                                                aria-span="Search" matInput>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div>
                                        
                                        <mat-form-field appearance="outline" class="location-input">
                                            <mat-label>{{ labels['formcontrolsRegion'] }}</mat-label>
                                            <input type="text" [disabled]="true" placeholder="{{ labels['formcontrolsRegion'] }}" [value]="selectedSupplier?.address?.country?.name || ''" class="input" aria-span="Search" matInput>
                                        </mat-form-field>
                                        
                                    </div>

                                </div>
                                <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; 
                                        </span> -->
                                    </div>
                                    <div>
                                       
                                        <mat-form-field appearance="outline" class="location-input">
                                            <mat-label>{{ labels['formcontrolsCountry'] }}</mat-label>
                                            <input type="text" [disabled]="true" placeholder="{{ labels['formcontrolsCountry'] }}" [value]="selectedSupplier?.address?.country?.parent?.description || ''" class="input" aria-span="Search" matInput>

                                        </mat-form-field>

                                    </div>

                                </div>
                                </div>
                        </div>
                        <div class="marginTop">
                            <div fxLayout="row wrap" fxLayoutGap="20px">

                                
                               
                               
                                <div>
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['year'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChooseyear'] }}" formControlName="year" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="yearList"
                                                    (filteredReturn)="filteredYearList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredYearList" [value]="item.value">
                                                    {{item.value}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>


                                    </div>

                                </div>
                                <div>
                                    <div>
                                       
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['quarter'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChoosequarter'] }}" formControlName="quarter" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="quarterList"
                                                    (filteredReturn)="filteredQuarterList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredQuarterList" [value]="item.value">
                                                    {{item.value}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>
                                <div *ngIf="configDetail && configDetail.priority.isEnabled">
                                    <div>
                                       
                                        <!-- <span class="start-color" *ngIf="configDetail && configDetail.priority.isMandatory">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" >
                                            <mat-label>{{ labels['priority'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChoosepriority'] }}" formControlName="priority" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="priorityList"
                                                    (filteredReturn)="filteredPriorityList =$event"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredPriorityList" [value]="item.value">
                                                    {{labels[item.value.toLowerCase()] }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>

                                <!-- //start date -->
                                <div>
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline"
                                            class="set-back-color-action schedule-form-sub-width">
                                            <mat-label>{{ labels['startDate'] }}</mat-label>
                                            <input formControlName="startDate" autocomplete="off" matInput
                                                [matDatepicker]="picker">
                                            <mat-datepicker-toggle matSuffix [for]="picker">
                                            </mat-datepicker-toggle>
                                            <mat-datepicker #picker>
                                            </mat-datepicker>
                                        </mat-form-field>
                                    </div>
    
                                </div>
                                <!-- //end date -->
                                <div>
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline"
                                            class="set-back-color-action schedule-form-sub-width">
                                            <mat-label>{{ labels['endDate'] }}</mat-label>
                                            <input formControlName="endDate" autocomplete="off" matInput
                                                [matDatepicker]="picker2">
                                            <mat-datepicker-toggle matSuffix [for]="picker2">
                                            </mat-datepicker-toggle>
                                            <mat-datepicker #picker2>
                                            </mat-datepicker>
                                        </mat-form-field>
                                    </div>
    
                                </div>
                            </div>

                        </div>

                        <div class="marginTop">
                            <div fxLayout="row wrap" fxLayoutGap="20px">
                                <div>
                                    <div>
                                        
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <!-- <mat-form-field appearance="outline" class="schedule-form-width">

                                            <mat-select formControlName="leadAuditor">
                                                <mat-option value="U. Sahoo">U. Sahoo</mat-option>
                                                <mat-option value="Davis">Davis</mat-option>
                                                <mat-option value="Richard">Richard</mat-option>
                                            </mat-select>
                                        </mat-form-field> -->
                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-label>{{ labels['formcontrolsLeadauditor'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['formcontrolsChooseleadauditor'] }}" formControlName="leadAuditor" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="attendeeList"
                                                    (filteredReturn)="onAttendeesChange($event)"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredattendeeList" [value]="item.externalId">
                                                    {{item.name}} 
                                                   
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>

                                </div>


                            </div>

                        </div>

                        <div class="marginTop" *ngIf="selectedAuditType">
                            <div fxLayout="row wrap" fxLayoutGap="20px">
                                <div>
                                    <div>
                                        <!-- <span class="start-color">
                                            &nbsp; *
                                        </span> -->
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-label *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">{{ labels['formcontrolsProcurementppr'] }}</mat-label>
                                        <mat-label *ngIf="selectedAuditType && selectedAuditType.name=='Internal Audit'">{{ labels['formcontrolsSitemanager'] }}</mat-label>
                                        
                                            <mat-select placeholder="{{ labels['commonfilterChooseppr'] }}" formControlName="procurement" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="procurementList"
                                                    (filteredReturn)="onProcurementChangee($event)"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredprocurementList" [value]="item.externalId">
                                                    {{item.name}} 
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="marginTop" *ngIf="selectedAuditType && selectedAuditType.name=='Internal Audit'">
                            <div fxLayout="row wrap" fxLayoutGap="20px">
                                <div>
                                    <div>
                                        
                                        <span class="start-color">
                                        </span>
                                    </div>
                                    <div>
                                        <mat-form-field appearance="outline" class="behav-tree-select">
                                            <mat-label>{{ labels['formcontrolsCoauditor'] }}</mat-label>
                                            <mat-select placeholder="{{ labels['commonfilterChoosecoauditor'] }}" formControlName="coAuditor" disableOptionCentering>
                                                <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                                                    [array]="procurementList"
                                                    (filteredReturn)="onProcurementChangee($event)"></mat-select-filter>
                                                <mat-option *ngFor="let item of filteredprocurementList" [value]="item.externalId">
                                                    {{item.name}} 
                                                    
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>
                        <div *ngIf="selectedAuditType && selectedAuditType.name=='Supplier Audit'">
                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="blueLightBox marginTop padding-right_10  upload-section" *ngFor="let item of checkList">
                                <div fxFlex="50" class="paddingLeft-10">
                                    <mat-checkbox class="example-margin" style="font-size: 14px !important;" [formControl]="item.isSelected" [disabled]="isView">
                                        {{item.name}}
                                    </mat-checkbox>
                                </div>
                            </div>
                        </div>

                        <div class="create-schedular-fotter">
                            <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                                (buttonAction)="goPage('schedule-list')"></common-lib-button>
                            <common-lib-button *ngIf="actionFlag!='View'" [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                                (buttonAction)="auditSubmit();"></common-lib-button>
                        </div>
                    </div>


                </form>
            </div>


            </div>
        </div>


    </div>

</div>
