
<div class="create-observation">

    <div *ngIf="process.name === 'Observation'" >
        <commom-label labelText="{{ labels['observation'] }}" firstlabelText="{{ labels['commonCreatean'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading mb-50'"></commom-label>
    </div>
    <div *ngIf="process.name === 'Hazards'" >
        <commom-label labelText="{{ labels['behaviourchecklistCreateanhazards'] }}" [tagName]="'h4'" [cstClassName]="'heading unit-heading mb-50'"></commom-label>
    </div>

    <div  fxLayout="row" class="mb-20 justify-center">
        <div class="arrow-box arrow-right" >
            <div fxLayout="row" fxLayoutAlign="center center">
                <div fxFlex="15" >
                    <div>  
                          <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="30" height="30" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M9.707 19.121a.997.997 0 0 1-1.414 0l-5.646-5.647a1.5 1.5 0 0 1 0-2.121l.707-.707a1.5 1.5 0 0 1 2.121 0L9 14.171l9.525-9.525a1.5 1.5 0 0 1 2.121 0l.707.707a1.5 1.5 0 0 1 0 2.121z" fill="#fff" data-original="#000000"></path></g></svg>

                    </div>
                 </div>
                <div fxFlex="85"  fxLayout="column">
                    <span class="regular title">
                        <!-- {{ 'OBSERVATION.CREATE.CARD.TITLE' | translate }} abc -->
                          {{ labels['process'] }}
                    </span>
                    <span class="semi-regular-12" style="margin-top: 5px;">
                        <!-- {{ 'OBSERVATION.MAIN.CARDS.'+ process.name | translate }}  -->

                         <ng-container *ngIf="process.name !== 'Field Walk'; else fieldWalkTemplate">
                            {{ labels['cards' + process.name] }}
                        </ng-container>
                        <ng-template #fieldWalkTemplate>
                            <!-- Content to display when process.name is 'Field Walk' -->
                            <span>{{ labels['fieldWalk'] }}</span>
                        </ng-template>
                    </span>
                </div>

            </div>
        </div>
        <div class="arrow-box-leftside arrow-left" style="margin-left: 0px;" >
            <div fxLayout="row" fxLayoutAlign="center center">
                <div fxFlex="15" style="padding-left: 2px;">
                    <div *ngIf="selectedProcess">
                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="30" height="30" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M9.707 19.121a.997.997 0 0 1-1.414 0l-5.646-5.647a1.5 1.5 0 0 1 0-2.121l.707-.707a1.5 1.5 0 0 1 2.121 0L9 14.171l9.525-9.525a1.5 1.5 0 0 1 2.121 0l.707.707a1.5 1.5 0 0 1 0 2.121z" fill="#fff" data-original="#000000"></path></g></svg>
                   
                    </div>
                    <span *ngIf="!selectedProcess" class="semi-bold-28">2</span>
                </div>
                <div  fxFlex="85" fxLayout="column">
                    <span class="regular title">
                        <!-- {{'Observation type' | translate }} -->
                         {{ labels['observationType'] }}
                    </span>
                    <span *ngIf="!selectedProcess" class="semi-regular-12" style="margin-top: 5px;">
                        <!-- {{'Choose the Observation type' | translate }} -->
                         {{ labels['choosetheobservationtype'] }}
                    </span>
                    <span *ngIf="selectedProcess" class="semi-regular-12" style="margin-top: 5px;">
                        <!-- {{ selectedProcess.name | translate }} -->
                          {{ labels['cards'+selectedProcess.name] }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="create-observation-section"  *ngIf="processList.length > 0">
        <div fxLayout="row">
            <div fxFlex="100" class="d-flex flex-flow-wrap justify-center">
                <mat-card class="grid-stl" (click)="selectProcess(item)"
                    [ngClass]="(selectedProcess && selectedProcess.externalId) == item.externalId ? 'selectedItem' : ''" *ngFor="let item of processList">
                    <div class="grid-stl-ico">
                        <i [innerHTML]="item.iconImage ? item.iconImage : defaultIcon | safeHtml"></i>
                    </div>
                    <div class="grid-stl-heading">
                        <span class="subheading-2">{{ item.name }}  </span>
                    </div>
                </mat-card>
            </div>
        </div>
    </div>
    
    <div class="create-obervation-fotter" fxLayout="column" fxLayoutAlign="center center">
        <!-- <common-lib-button [className]="'cancel cst-btn'" [text]="'BUTTON.CANCEL'"
            (buttonAction)="cancelClick()"></common-lib-button> -->
            <div fxLayout="row"  fxLayoutGap="10px">
                <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonBack']}}"
            (buttonAction)="goBack()"></common-lib-button>
          
            </div>
           
    </div>


 

</div>
   
     
   
  
