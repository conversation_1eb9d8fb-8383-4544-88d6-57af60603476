// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production                : false,
  //Celanese
  NODE_API                  : "https://localhost:8100",
  React_API                 : "https://localhost:3900",
  UserAPI                   : "http://127.0.0.1:8000",
  Notification_Portal       : "https://app-dplantnotificationsportal-d-ussc-01.azurewebsites.net/notifications",

  project                   : "celanese-dev",
  appId                     : "OFWA-Dev",
  clientId                  : 'a5cadb9c-6b96-448f-8845-1c27d127d442',
  tenantId                  : '7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37',
  cluster                   : "az-eastus-1",
  modelId                   : 7575155737800092,
  revisionId                : 3624517118008353,

  redirectUri               : 'https://localhost:4900',
  regionId                  : "REG-19",
  countryId                 : "CTR-US",
  siteId                    : "STS-CLK",
  unitId                    : "UNT-CLKAAS",
  businessLineId            : "BUL-AC",
  locationId                : "",
  applicationId             : "APP-OFWA"
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
