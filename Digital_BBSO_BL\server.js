const express = require('express')
const cors = require('cors');
const bodyParser = require('body-parser')
const app = express();
const swaggerFile = require('./autojson/swagger-output.json')
const swaggerUI = require('swagger-ui-express');
const morgan = require('morgan');
const passport = require('passport');

const BearerStrategy = require('passport-azure-ad').BearerStrategy;
const superagent = require('superagent');
const constant = require('./api/constant/constant');
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0;

process.env.PORT = process.env.PORT               || 8100;

process.env.SOCKET_PORT = process.env.SOCKET_PORT || 3100;
process.env.NODE_ENV = process.env.NODE_ENV       || 'development';


process.env.CDF_ENTITY = process.env.CDF_ENTITY                           || "http://localhost:8091";
process.env.NODE_SERVICE = process.env.NODE_SERVICE                       || "http://localhost:8100";
process.env.UM_SERVICE = process.env.UM_SERVICE                           || "http://127.0.0.1:8000";     
process.env.TRANSLATION_SERVICE = process.env.TRANSLATION_SERVICE         || "http://127.0.0.1:8003/translation"

// process.env.CDF_ENTITY = process.env.CDF_ENTITY                           || "https://app-dplantbbsonodeentity-d-ussc-01.azurewebsites.net";
// process.env.NODE_SERVICE = process.env.NODE_SERVICE                       || "https://app-dplantbbsonodeservice-d-ussc-01.azurewebsites.net";
// process.env.UM_SERVICE = process.env.UM_SERVICE                           || "https://app-dplantusermgmtapi-d-ussc-01.azurewebsites.net";
// process.env.NOTIFICATION = process.env.NOTIFICATION                       || "https://app-dplantnotificationsapi-d-ussc-01.azurewebsites.net";
// process.env.TRANSLATION_SERVICE = process.env.TRANSLATION_SERVICE         || "https://app-dplantappstranslation-d-ussc-01.azurewebsites.net/translation"

process.env.RATE_LIMIT_RESET = process.env.RATE_LIMIT_RESET               || "5000";
process.env.RATE_LIMIT_MAX = process.env.RATE_LIMIT_MAX                   || "200";


process.env.AppID = process.env.AppID                                     || "OFWA-Dev";

process.env.AzureTenantID = process.env.AzureTenantID                     || "7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37";
process.env.AzureClientID = process.env.AzureClientID                     || "01c3a55a-e036-4030-854b-8c256966f6aa";
process.env.AzureAudience = process.env.AzureAudience                     || "https://az-eastus-1.cognitedata.com";
process.env.AzureAuthority = process.env.AzureAuthority                   || "login.microsoftonline.com";
process.env.AzureDiscovery = process.env.AzureDiscovery                   || ".well-known/openid-configuration";
process.env.AzureVersion = process.env.AzureVersion                       || "v2.0";
process.env.AzureValidateIssuer = process.env.AzureValidateIssuer         || true;
process.env.AzurePassReqToCallback = process.env.AzurePassReqToCallback   || false;
process.env.AzureLoggingLevel = process.env.AzureLoggingLevel             || "info";

app.use(cors());
app.use(express.json());

app.use(morgan('dev'));

app.use(passport.initialize());



// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: false }))

// parse application/json
// app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ limit: "50mb", extended: false, parameterLimit: 50000000 }))

// create application/x-www-form-urlencoded parser
var urlencodedParser = bodyParser.urlencoded({ extended: false })
// I also (most important) had to set up the limit @ nginx (webapp reverse proxy)

//  # Max upload size.
// client_max_body_size 50M;


app.use('/static', express.static('assets'));
// https://innoart.innoeops.com/api/attachment/downloadAttachment/organization-logo/6229f1ffede7cd481714fc3c.png
var options = {
  customCss: `.topbar-wrapper img {content:url('../../static/logo.png'); width:150px; height:auto;}
  .swagger-ui .topbar { background-color: #fff; }`,
  customSiteTitle: "Celanese",
  customfavIcon: "../../static/favicon.ico"
};
app.use('/api-docs', swaggerUI.serve, swaggerUI.setup(swaggerFile, options));



  
const authOptions = {
  identityMetadata: `https://${process.env.AzureAuthority}/${process.env.AzureTenantID }/${process.env.AzureVersion}/${process.env.AzureDiscovery}`,
 // issuer: `https://${config.metadata.authority}/${config.credentials.tenantID}/${config.metadata.version}`,
  clientID: process.env.AzureClientID,
  audience: process.env.AzureAudience,//"00000003-0000-0000-c000-000000000000" ,//"https://api.cognitedata.com",// "00000003-0000-0000-c000-000000000000", //config.credentials.audience,
  validateIssuer: process.env.AzureValidateIssuer,
  passReqToCallback: process.env.AzurePassReqToCallback,
  //loggingLevel: config.settings.loggingLevel,
  //loggingNoPII: false,
  issuer: `https://sts.windows.net/${process.env.AzureTenantID }/`,
  scope:['DATA.VIEW']// ['DATA.VIEW IDENTITY user_impersonation'] //scopesCognite //['user.read'],// [ 'openid',]
  //scope: EXPOSED_SCOPES
 // isB2C: false
};

const bearerStrategy = new BearerStrategy(authOptions, (token, done) => {
      // Send user info using the second argument
     // console.log('token',token)
      done(null, {}, token);
  }
);
passport.use(bearerStrategy);
app.get('/api',
    passport.authenticate('oauth-bearer', {session: false}),
    (req, res) => {
      // #swagger.ignore = true
       // console.log(' req.authInfo', req.authInfo)
       // console.log('Validated claims: ', req.authInfo);

        // Service relies on the name claim.  
        res.status(200).json(req.authInfo)
        // res.status(200).json({
        //     'name': req.authInfo['name'],
        //     'issued-by': req.authInfo['iss'],
        //     'issued-for': req.authInfo['aud'],
        //     'scope': req.authInfo['scp']
        // });
    }
);

app.get('/getjson', (req, res) => {
  // #swagger.ignore = true
   res.status(200).send(swaggerFile)
  
 });


 
const rateLimit = require('express-rate-limit')

const limiter = rateLimit({
	windowMs: parseInt(process.env.RATE_LIMIT_RESET), //15 * 60 * 1000, // 15 minutes
	max: parseInt(process.env.RATE_LIMIT_MAX), //100, // Limit each IP to 100 requests per `window` (here, per 15 minutes)
  message:{
    code: 429,
    status: 'error',
    data: {},
    message: "Too many requests"
},
	standardHeaders: false, // Return rate limit info in the `RateLimit-*` headers
	legacyHeaders: true, // Disable the `X-RateLimit-*` headers
})

// Apply the rate limiting middleware to all requests
app.use(limiter)

require('./routes')(app);
// require('./config/database');
// require('./scheduler');

process.env.EMAIL           = process.env.EMAIL                           || "<EMAIL>";
process.env.EMAIL_PASSWORD  = process.env.EMAIL_PASSWORD                  || "Openmicro2021$";

app.use((req, res, next) => {

  var time = Date.now();

  res.on('finish', function() {
      var clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
      var method = req.method;
      var path = req.baseUrl;
      var duration = Date.now() - time;

      // console.log({
      //     clientIp,
      //     elapsedTime: `${duration}ms`,
      //     hostname: req.headers.host,
      //     level: 'INFO',
      //     method,
      //     path,
      //     phase: process.env.NODE_ENV,
      //     reqBody: req.body,
      //     reqHeaders: req.headers,
      //     resBody: res.body,
      //     resHeaders: res.getHeaders(),
      //     status: res.statusCode
      // });
  });

  next();
});
getTypeConfig(function (response) {
  constant.configuration = response;
});
app.get('/typeConfig', (req, res) => {
  // #swagger.ignore = true
  /* 	#swagger.tags = ['Business']  */
  getTypeConfig(function (response) {
    res.status(200).json(response)
  });
});

function getTypeConfig(cb) {
  superagent
    .get(process.env.CDF_ENTITY + '/typeConfig')
    .set('Content-Type', 'application/json')
    .end((error, resp) => {
      if(error){
        console.log(error)
      }
      cb(resp ? resp.body : {})
    })
}
app.get('/', (req, res) => {
  // #swagger.ignore = true
  getTypeConfig(function (response) {
    constant.configuration = response
  });
  res.send('Node server working')
});

 
process
  .on('unhandledRejection', (reason, p) => {
    console.error('\x1b[31m%s\x1b[0m', 'Unhandled Rejection at Promise');
    console.error('\x1b[31m%s\x1b[0m',reason, p);
  })
  .on('uncaughtException', err => {
    console.error('\x1b[31m%s\x1b[0m', 'Uncaught Exception thrown');
    console.error('\x1b[31m%s\x1b[0m', err+'\n');
    // process.exit(1);
    console.log("caughtException" + err.stack);
  });

process.on('beforeExit', code => {
  // Can make asynchronous calls
  setTimeout(() => {
    console.log(`beforeExit Process will exit with code: ${code}`)
    const memory = process.memoryUsage();
    var usedMemory = (memory.heapUsed / 1024 / 1024 / 1024).toFixed(4) + 'GB';
    console.log("usedMemory " + usedMemory);
    process.exit(code)
  }, 100)
})

process.on('exit', code => {
  // Only synchronous calls
  const memory = process.memoryUsage();
  var usedMemory = (memory.heapUsed / 1024 / 1024 / 1024).toFixed(4) + 'GB';
  console.log("usedMemory " + usedMemory);
  console.log(`exit Process exited with code: ${code}`)
})

// const methodOverride = require('method-override')
// app.use(methodOverride())
app.use(clientErrorHandler)
app.use(errorHandler)
function clientErrorHandler(err, req, res, next) {
  console.log("clientErrorHandler")
  if (req.xhr) {
    // res.status(500).send({ error: 'Something failed!' })
    res.status(500).json({
      code: 500,
      status:'error',
      data: [],
      message: 'Error'
    });
  } else {
    next(err)
  }
}
function errorHandler (err, req, res, next) {
  console.log("errorHandler")
  if (res.headersSent) {
    return next(err)
  }
  // res.status(500)
  // res.render('error', { error: err })
  res.status(500).json({
    code: 500,
    status:'error',
    data: err,
    message: "Error"
  });
  
}

var http = require('http');
var https = require('https');
var fs = require('fs');
var privateKey  = fs.readFileSync('./ssl/key.pem', 'utf8');
var certificate = fs.readFileSync('./ssl/cert.pem', 'utf8');
var credentials = {key: privateKey, cert: certificate};
// your express configuration here


// var httpsServer = https.createServer(credentials, app);
// httpsServer.listen(process.env.PORT, '0.0.0.0', () => {
//   console.info('\x1b[36m%s\x1b[0m', '\nThe server is running on port           : ' + process.env.PORT);
//   console.info('\x1b[36m%s\x1b[0m', '\nHTTPS           ');
// });

var httpServer = http.createServer(app);
httpServer.listen(process.env.PORT, '0.0.0.0', () => {
  console.info('\x1b[36m%s\x1b[0m', '\nThe server is running on port           : ' + process.env.PORT);
 console.info('\x1b[36m%s\x1b[0m', '\nHTTP           ');
});
