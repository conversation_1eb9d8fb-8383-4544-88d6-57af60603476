describe('Home Page Test', () => {

    beforeEach(() => {
        // run these tests as if in a desktop
        // browser with a 720p monitor
       // cy.viewport(1280, 720)
      })
    it('Visits the initial project page', () => {
      //  cy.visit('/')
      cy.visit('/');
      cy.url().should('includes', 'observations');
    //  cy.visit('https://localhost:4900/observations')
     // cy.visit('http://localhost:4200/observations');

      cy.wait(2000);
      cy.get('#observe_box1').click();
      cy.wait(2000);
      cy.get('#ob_next_btn').click();
      //ob_next_btn
      cy.wait(2000);
      cy.get('#create_observe').click();
      //create_observe
      cy.wait(2000);
      cy.get('#create_next_btn').click();
      cy.wait(2000);
      cy.get('#sub_observe').click();
      
      cy.wait(2000);
      cy.get('#sub_next_btn').click();
      cy.url().should('includes', 'observations');
      //sub_obse_list
      cy.wait(2000);
      cy.get('#sub_obse_list').click();

      cy.wait(2000);
    
      cy.get('#location_observe').clear().type("utility 1");

      cy.wait(2000);
    //   cy.get('#obse_behalf').select('Plant')
 
      cy.get('mat-select[formControlName=behalf]')
      .click()
      .then(() => cy.get('mat-option').contains('Plant').click());

      cy.get('mat-select[formControlName=Crafts]')
      .click()
      .then(() => cy.get('mat-option').contains('Crafts').click());


      cy.get('input[formControlName=datetime]').click(); 
         //choose previous month
    cy.get('.mat-calendar-body-cell').first().click();
    cy.get('input[formControlName=startTime]').type("10:25 AM");
    cy.get('input[formControlName=endTime]').type("11:25 PM");
    cy.get('input[formControlName=assets]').type("Assets");
    cy.get('#contractor')
    .click()
    .then(() => cy.get('mat-option').contains('No').click());

    
    cy.get('#ob_textarea').clear().type("test 1");

    cy.wait(2000);
    cy.get('#ob_lastNext').click();
    cy.wait(2000);
    cy.url().should('includes', 'observation-list');

    })
  })
  