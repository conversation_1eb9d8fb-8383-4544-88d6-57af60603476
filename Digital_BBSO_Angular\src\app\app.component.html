


<section class="route-container" [ngClass]="docReact.width <= 768 && showMobileSideBar ?  'show-sidebar' : ''">
  <app-header></app-header>
  <app-sidebar></app-sidebar>
  <!-- <app-sub-header></app-sub-header> -->
 

<div *ngIf="showLoading" class="loader-container" fxLayout="column">
  <div class="fading-bars">
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
  </div>
   <div>
    <span class="loading-text semi-bold-12">Please wait, loading your User Access settings.</span>
   </div>
</div>
<!-- Lang<PERSON>e Fetch Loading -->
<div *ngIf="commonService.showLanguageLoading" class="loader-container" fxLayout="column">
  <div class="fading-bars">
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
  </div>
   <div>
    <span class="loading-text semi-bold-12">{{"Please wait, loading the Language Labels.." | translate }}</span>
   </div>
</div>

  <router-outlet *ngIf="!isIframe" style="margin: 5px;">
    <div *ngIf="commonService.loaderFlag" class="spinner-body">
      <mat-spinner class="spinner"></mat-spinner>
    </div>
  </router-outlet>
</section>

<div class="sideBar-overlay" *ngIf="(!expandCollapseMenu || showMobileSideBar)"></div>
<div  [style.visibility]="'hidden'">
  <iframe id="iFrameFieldWalk"  class="iFrameTable" [src]="url5| safe" title="description">
  </iframe>
</div>


