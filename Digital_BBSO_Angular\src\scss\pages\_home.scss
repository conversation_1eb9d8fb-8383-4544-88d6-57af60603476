@use "../abstract/mixins" as mixins;

.hero-area {
  padding: 8rem 20rem;
  display: flex;
  // flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;

  @include mixins.responsive(xs) {
    padding: 4rem 2rem;
    flex-direction: column;
  }

  @include mixins.responsive(sm) {
    padding: 4rem 2rem;
    flex-direction: column;
  }

  @include mixins.responsive(lg) {
    padding: 8rem 6rem;
    flex-direction: row;
  }

  @include mixins.responsive(xlg) {
    padding: 8rem 15rem;
    justify-content: space-between;
  }

  @include mixins.responsive(xxlg) {
    padding: 8rem 20rem;
    justify-content: space-between;
    // flex-wrap: nowrap;
  }

  img {
    width: 25rem;

    @include mixins.responsive(xlg) {
      width: 30rem;
    }

    @include mixins.responsive(xxlg) {
      width: 40rem;
    }
  }

  &--contents {
    max-width: 34rem;

    @include mixins.responsive(lg) {
      max-width: 25rem;
    }

    @include mixins.responsive(xxlg) {
      max-width: 40rem;
    }
  }

  &--title {
    font-size: 3rem;
    font-weight: 600;

    @include mixins.responsive(xs) {
      font-size: 2rem;
    }

    @include mixins.responsive(sm) {
      font-size: 1.5rem;
    }

    @include mixins.responsive(lg) {
      font-size: 2rem;
    }

    @include mixins.responsive(xlg) {
      font-size: 2.5rem;
    }

    @include mixins.responsive(xxlg) {
      font-size: 3rem;
    }

    & > span {
      font-size: 1.5rem;

      @include mixins.responsive(xxlg) {
        font-size: 3rem;
      }
    }
  }

  &--desc {
    margin-top: 2rem;
    font-size: 1.2rem;
    line-height: 1.8;
    color: gray;

    @include mixins.responsive(xs) {
      font-size: 0.9rem;
    }

    @include mixins.responsive(sm) {
      font-size: 0.9rem;
    }

    @include mixins.responsive(lg) {
      font-size: 1rem;
    }

    @include mixins.responsive(xxlg) {
      font-size: 1.2rem;
    }
  }
}
