import { ChangeDetectorRef, Component, EventEmitter, NgZone, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { Subject } from 'rxjs';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import * as _ from 'lodash';
import { TemplateRef, ViewChild } from '@angular/core';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-audit-list',
  templateUrl: './audit-list.component.html',
  styleUrls: ['./audit-list.component.scss'],
  providers:[ { provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS },]
})
export class AuditListComponent implements OnInit {
 
  siteControl: FormControl = new FormControl("");
  @ViewChild('filterDialog') filterDialog!: TemplateRef<any>;
  locationObserve: FormControl = new FormControl('');
  behalf: FormControl = new FormControl('');
  unitList = [];
  filteredUnitList = [];
  
  reportingLocationList: any;
  filteredReportingLocationList: any;
  tempLocation: any;
  behalfList: any;
  filteredBehalfList: any;
  OperationalLearning: boolean;
  fl_searchVal: any;
  siteList = [];
  filteredSiteList = [];
  site: any;
  filterFlag: string;
  apply: boolean=true;
  createdByList: { name: string; externalIds: string[]; }[];
  filteredCreatedByList: any;
  createdBy: FormControl = new FormControl("");
 
  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;
 
  searchControl: FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();
 
  processControl: FormControl = new FormControl("Observation");
 
  labels = {};

  auditTypeControl: FormControl = new FormControl("");
  auditTypeList = []
  subauditTypeControl: FormControl = new FormControl("");
  subauditTypeList=[]
 
  coreTypeControl: FormControl = new FormControl("");
  subProcessList: any[];
  filteredSubProcessList: any;
  subProcessControl: FormControl = new FormControl("");
  coreTypeList:any = []
 
  observeType: FormControl = new FormControl("");
 
  filteredObserveTypeOptions: Observable<any[]>
 
  // category: FormControl = new FormControl("");
  // filteredCategoryOptions: Observable<any[]>
 
  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });
 
  startDateControl: FormControl = new FormControl("");
  endDateControl: FormControl = new FormControl("");
 
  url: string = "";
  displayedColumns: any = [];
  allColumns = [
 
    { name: 'externalId', key2:'Id', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name:'refUnit',key2:'Unit',displayName:"Unit",key:"unit",activeFlag:false,summary:false},
    { name: 'corePrinciple', key2:'CorePrinciple',displayName: "Core Principle", key: "corePrinciple", activeFlag: true, summary: false },
    { name: 'subProcess', key2:'SubProcess',displayName: "Sub Process", key: "subProcess", activeFlag: true, summary: false },
    { name: 'refReportingLocation', key2:'Location',displayName: "Location", key: "location", activeFlag: false, summary: false },
    { name: 'shift', key2:'Shift',displayName: "Shift", key: "shift", activeFlag: false, summary: false },
    { name: 'date', key2:'Date',displayName: "Date", key: "date", activeFlag: false, summary: false },
    { name: 'observedOnBehalfOf',key2:'Observedby', displayName: "Observed By", key: "observedBy", activeFlag: false, summary: false },
    { name: 'title', key2:'Title',displayName: "Title", key: "title", activeFlag: true, summary: false },
    { name: 'year', key2:'Year',displayName: "Year", key: "year", activeFlag: true, summary: false },
    { name: 'month', key2:'Month',displayName: "Month", key: "month", activeFlag: false, summary: false },
    { name: 'quarter', key2:'Quarter',displayName: "Quarter", key: "quarter", activeFlag: true, summary: false },
    { name: 'priority', key2:'Priority',displayName: "Priority", key: "priority", activeFlag: true, summary: false },
    { key: 'operationalLearning', key2:'OperationalLearning',displayName: "Operational Learning", name: "isOperationalLearning", activeFlag: true, summary: false },
    { key: 'operationalLearningDescription', key2:'OperationalLearningDescription',displayName: "Describe the Operational Learning opportunities you found", name: "operationalLearningDescription", activeFlag: false, summary: false },
    { name: 'observationStartDate', key2:'StartDate',displayName: "Start Date", key: "startDate", activeFlag: true, summary: false },
    { name: 'observationEndDate', key2:'EndDate',displayName: "End Date", key: "endDate", activeFlag: true, summary: false },
    { name: 'workReleaseNumber', key2:'workReleaseNumber',displayName: "Work Release Number", key: "workReleaseNumber", activeFlag: false, summary: false },
    { name: 'status', key2:'Status',displayName: "Status", key: "status", activeFlag: true, summary: false },
    { name: 'createdTime', key2:'Createdon',displayName: "Created On", key: "createdOn", activeFlag: true, summary: false },
    { name: 'createdBy', key2:'Createdby',displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: 'lastUpdatedTime', key2:'Updatedon',displayName: "Updated On", key: "updatedOn", activeFlag: false, summary: false },
    { name: 'modifiedBy', key2:'Updatedby',displayName: "Updated By", key: "updatedBy", activeFlag: false, summary: false },
    { name: 'actions', key2:'Actions',displayName: "Actions", key: "actions", activeFlag: true, summary: false },
 
  ];

  allColumnsBackup= _.cloneDeep(this.allColumns)
  initialDataFlag = 0;
  loaderFlag: boolean;
  auditType: any;
  userAccessMenu: any;
  createAudit_obj: any;
  subProcessType: any;
  displayColumnactiveKeys: string[];
  operationalLearningControl = new FormControl('All');
  typeList: any;
  
  listType: any = "Audit";
  enabledFields: string[];
  columnOrder: any[];
  openFilterDialog() {
    this.dialog.open(this.filterDialog, {
      disableClose: true, // Prevent closing on outside click
    });
  }
 
  constructor(private router: Router, private commonService: CommonService, private tokenService: TokenService, private dataService: DataService, private dialog: MatDialog, private toastr: ToastrService,public translationService: TranslationService, private languageService: LanguageService, public ngZone: NgZone, private changeDetector: ChangeDetectorRef) {
    this.url = this.dataService.React_API + "/audit";
    console.log('commonService label', this.commonService.labelObject)
    console.log('commonService label', this.commonService.selectedLanguage)
    window?.postMessage({
      type: 'Language',
      action: 'Language',
      LanguageCode: `${this.commonService.selectedLanguage}`,
      idToken: this.tokenService.getIDToken(),
      labels: this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()],
    }, '*');

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'],
        'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'],
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'],
        'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'],
        'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'],
        'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'],
        'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'],
        'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'],
        'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'],
        'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
        'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'],
        'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'],
        'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'],
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'],
        'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'],
        'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'],
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'],
        'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'],
        'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'],
        'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'],
        'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'],
        'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'],
        'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'],
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'],
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'],
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'],
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
        'formcontrolsAuditSubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAuditSubprocess'],
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
        'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
        'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',  
      'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
      'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters',
      'auditCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditCount'] || 'auditCount',
      'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
      }
    }
    this.metricsbyperson = this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.metricsbyperson);
 
  }
 
 
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'],
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'],
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'],
          'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'],
          'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'],
          'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'],
          'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'],
          'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'],
          'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'],
          'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'],
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'],
          'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'],
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'],
          'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'],
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'],
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'],
          'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'],
          'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'],
          'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'],
          'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'],
          'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'],
          'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'],
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'],
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'],
          'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'],
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'],
          'formcontrolsAuditSubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAuditSubprocess'],
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
          'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
          'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
          'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
          'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
          'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
          'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
          'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
          'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
          'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',  
      'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
      'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters',
      'auditCount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'auditCount'] || 'auditCount',
      'subProcess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'subProcess'] || 'subProcess',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo)
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo.externalId)
     
     
      // if(_this.observation && _this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }

    })

    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if(_this.userAccessMenu){
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
      _this.loadProcessType();
    }
 
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
          _this.loadProcessType();
        }
      }
    })
 
    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      console.log("Site selected")
      setTimeout(function () {
        // _this.applyObservationFilter();
      }, 2000);
     
      _this.loadProcessType();
      _this.processLoad(() => { });
      _this.siteChanged();
    });

    if (_this.commonService.filterListFetched) {
      _this.filterInit('Site');
      _this.filterInit('Unit');
    }



    _this.auditTypeControl.valueChanges.subscribe(value => {
      var _this= this;
      _this.auditType = value["externalId"];
      console.log("auditType", _this.auditType)
      // _this.applyObservationFilter();
    });
    _this.loadProcessType()
  
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        _this.filterInit(fiterType);
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    
    
    _this.unitControl.valueChanges.subscribe(value => {
      console.log('Value:', value);
    
      _this.processLoad(() => { });
    
      let tempArray = [];
      
      // Check if value is an array or a single value
      if (Array.isArray(value)) {
        // If value is an array, filter for each element in the array
        tempArray = _this.tempLocation.filter(item => 
          value.includes(item.reportingUnit.externalId)
        );
      } else {
        // If value is a single value, filter normally
        tempArray = _this.tempLocation.filter(item => 
          item.reportingUnit.externalId === value
        );
      }
    
      console.log('tempArray:', tempArray);
    
      _this.reportingLocationList = tempArray;
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
    
      console.log('_this.reportingLocationList:', _this.reportingLocationList);
    });

    setTimeout(function () {
     //_this.applyObservationFilter();
    }, 2000);
    _this.coreTypeControl.valueChanges.subscribe(value => {
      var _this= this;
    _this.auditTypeList = _this.commonService.processList.filter(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == value["externalId"])
    });
    console.log("auditTypeList", _this.auditTypeList)
 
    _this.auditTypeControl.valueChanges.subscribe(value => {
      var _this= this;
      _this.auditType = value["externalId"];
      console.log("auditType", _this.auditType)
      _this.subauditTypeList = _this.commonService.processList.filter(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == value["externalId"])
      console.log("subauditTypeList", _this.subauditTypeList)
      
    var childrenProcess = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.typeList);
    })
    _this.subProcessList = childrenProcess.filter(e => e.isActive != false);
    _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
    _this.filteredSubProcessList = _this.subProcessList.slice();
      
      // _this.applyObservationFilter();
    });

    _this.subauditTypeControl.valueChanges.subscribe((value: any) => {
      // value =value.externalId
      // console.log(value)
      if(value!=null){
        _this.apply=false
        value =value.externalId
        _this.dataService.postData({ "externalId": value }, _this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
        
          var processConfig;
        if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
          processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
        }else{
          processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.auditTypeControl.value.name)
        }
        var myColumn = [];
          Object.entries(processConfig.columnConfig).forEach(([key, value]) => {
            value["key"] = key;
            myColumn.push(value);
          })
          _this.columnOrder = myColumn.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
          // if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
          //   var processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
    if(processConfig.columnConfig!=null){
      _this.apply=true
      _this.enabledFields = Object?.keys(processConfig?.columnConfig).filter(key => 
        typeof processConfig.columnConfig[key] === 'object' && processConfig.columnConfig[key].isEnabled
      ).map(key => processConfig.columnConfig[key].field);
      // _this.apply=true
      console.log(_this.enabledFields);
    }
            
    
          // }
        })
      }
        else{
          _this.apply=true
          console.log(this.allColumnsBackup)
          console.log(this.allColumnsBackup.filter(column => column.activeFlag).map(column => column.key2));
          this.enabledFields=this.allColumnsBackup.filter(column => column.activeFlag).map(column => column.key2)
        }

    })
 
 
    _this.subauditTypeControl.valueChanges.subscribe(value => {
      _this.subProcessType = value["externalId"];
      
      // _this.applyObservationFilter();
    });
 
 
 
 
 
    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);
 
 
 
    window.onmessage = function (e) {
      if (e.data && e.data.type === "Audit" && e.data.action) {
        switch (e.data.action) {
          case "AuditView":
            if (e.data.data.refSubProcess && e.data.data.refSubProcess.name === "Layered Process Audit") {
              _this.router.navigate(['observations/observation'], {
                state: {
                  externalId: e.data.data.externalId,
                  pageFrom: "Audit List",
                  action: "View"
                }
              });
            }else if (e.data.data.refSubProcess && e.data.data.refSubProcess.name === "Life Critical Audit") {
              _this.router.navigate(['observations/observation'], {
                state: {
                  externalId: e.data.data.externalId,
                  pageFrom: "Audit List2",
                  action: "View"
                }
              });
            }else if (e.data.data.refSubProcess && e.data.data.refSubProcess.name === "Procedure Audit") {
              _this.router.navigate(['observations/observation'], {
                state: {
                  externalId: e.data.data.externalId,
                  pageFrom: "Audit List3",
                  action: "View"
                }
              });
            }
             else {
              _this.router.navigate(['observations/audit-plan-quality'], {
                state: { externalId: e.data.data.externalId }
              });
            }
            break;
            case "EMailSend":
              const dataToSend = e.data.data;
              const jsonString = JSON.stringify(dataToSend);
            localStorage.setItem('emailsend', jsonString); // Serialize the data
            console.log('VendorUrl: ', _this.commonService.configuration["VendorUrl"]);
              window.open(
                _this.commonService.configuration["VendorUrl"] + "/observations/general-info?id="+e.data.data.externalId,
                "_blank"
              );
              // window.open(
              //   "http://localhost:4900" + "/observations/general-info"
              // );

              var instanceNotification = [
                {
                  application: _this.commonService.applicationInfo.name,
                  description: 'Supplier Notification',
                  externalUsers: [dataToSend.refOFWASchedule.email],
                  severity: dataToSend.refOFWASchedule.priority,
                  properties: [
                    {
                      name: 'Email',
                      value: dataToSend.refOFWASchedule.performerAzureDirectoryUserID.externalId,
                      type: 'text',
                    },
                    {
                      name: 'Start',
                      value: new Date(dataToSend.refOFWASchedule.observationStartDate).toDateString(),
                      type: 'text',
                    },
                    {
                      name: 'AuditNumber',
                      value: dataToSend.refOFWASchedule.auditNumber,
                      type: 'text',
                    },
                    {
                      name: 'End',
                      value: new Date(dataToSend.refOFWASchedule.observationEndDate).toDateString(),
                      type: 'text',
                    },
                    {
                      name: 'link',
                      value: _this.commonService.configuration["VendorUrl"] + "/observations/general-info?id=" + dataToSend.externalId, //audit.externalId,
                      type: 'text',
                    },
                  ],
                },
              ];
              let notificationType = 'Supplier Audit Notification';
              _this.commonService.notification(instanceNotification, notificationType);

            break;
          case "AuditEdit":
            if (e.data.data.refSubProcess && e.data.data.refSubProcess.name === "Life Critical Audit") {
              _this.router.navigate(['observations/observation'], {
                state: {
                  externalId: e.data.data.externalId,
                  pageFrom: "Audit List2"
                }
              });
            }
            else if (e.data.data.refSubProcess && e.data.data.refSubProcess.name === "Procedure Audit") {
              _this.router.navigate(['observations/observation'], {
                state: {
                  externalId: e.data.data.externalId,
                  pageFrom: "Audit List3"
                }
              });
            }
            else{
            _this.router.navigate(['observations/observation'], {
              state: {
                externalId: e.data.data.externalId,
                pageFrom: "Audit List"
              }
            });}
            break;
          case "Loading":
            _this.commonService.loaderFlag = e.data.data;;
            break;
          case "ScoreCard":
            _this.router.navigate(['observations/scorecard'], {
              state: { externalId: e.data.data.externalId }
            });
            break;
          case "Create":
            _this.router.navigate(['action/create-action'], {
              state: {
                data: e.data.data,
                pageFrom: _this.commonService.configuration["typeAudit"],
                action: "Create Action"
              }
            });
            break;
          case "AuditSummary":
            _this.router.navigate(['observations/audit-summary'], {
              state: { externalId: e.data.data.externalId }
            });
            break;
        }
      }
    };
 
    var iframe = document.getElementById('iFrameAudit');
    console.log('iframe', iframe);
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    console.log('iWindow', iWindow);
    iWindow?.postMessage(
      { 
        type: 'AuthToken', "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
        data: this.tokenService.getToken(),
        idToken: _this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: _this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
 
    var _this = this;
 
    this.endDateControl.valueChanges.subscribe(async range => {
      console.log(this.endDateControl.value);
      // _this.applyObservationFilter();
    });
 
 
 
  }

  
  filterInit(fiterType) {
    var _this = this;

    if (fiterType == 'Site') {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.commonService.siteList.slice();
      console.log('_this.siteList', _this.siteList);
      _this.commonService.observeListSubject.next(true);
      _this.setDateFormat();
    }
    if (fiterType == 'Unit') {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.commonService.unitList.slice();
    }
    //  _this.userData = _this.commonService.userInfo
  }

  onSelectOperationalLearning(value: boolean | string) {
    var _this = this
    if (value !== null) {
      console.log(value)
      if (value == "All"){
        _this.OperationalLearning = null
      }
      else{
        _this.OperationalLearning = !!value
      }
      // this.applyObservationFilter();
    }}


  clearFilters() {
    this.unitControl.reset();
    this.behalf.reset();
    this.locationObserve.reset();
    this.coreTypeControl.reset();
    this.subauditTypeControl.reset();
    this.auditTypeControl.reset();
    this.subProcessControl.reset();
    this.operationalLearningControl.setValue('All');
    this.startDateControl.reset();
    this.endDateControl.reset();
    this.applyObservationFilter();
    this.dialog.closeAll();
  }

  applyFilters() {
    // Logic to apply filters based on the selected values
    console.log('Applied filters:', {
      unit: this.unitControl.value,
      behalf: this.behalf.value,
      auditType: this.auditTypeControl.value,
      subauditType: this.subauditTypeControl.value,
      location: this.locationObserve.value,
      coreType: this.coreTypeControl.value,
      operationalLearning: this.operationalLearningControl.value
    });
    this.applyObservationFilter()
    if(this.enabledFields && this.enabledFields.length>0){
      this.updateActiveFlags(this.allColumns, this.enabledFields);
    }
    this.dialog.closeAll();
  }

  async onBehalfChange(item: any) {
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.behalfList = [];
    _this.filteredBehalfList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.behalfList.forEach((element)=>{
          element.name =  element.firstName+' '+element.lastName
        })
        _this.filteredBehalfList = _this.behalfList.slice();
      
  
      })
      _this.changeDetector.detectChanges()
      
  }
}

 

siteChanged() {
  var _this = this;
  var selectedSite = _this.commonService.getSelectedValue(
    _this.siteControl.value
  );
  _this.unitList = [];
  _this.filteredUnitList = _this.unitList.slice();

  if (selectedSite.length > 0) {
    var myCountries = [];
    var myRegions = [];
    _this.commonService.siteList.filter(function (e) {
      if (selectedSite.indexOf(e.externalId) > -1) {
        if (e['country']) {
          myCountries.push(e['country']['externalId']);
          if (e['country']['parent'])
            myRegions.push(e['country']['parent']['externalId']);
        }
      }
      return selectedSite.indexOf(e.externalId) > -1;
    });
    _this.filterFlag = 'Site';
  } else {
    selectedSite = _this.siteList.map((eItem) => eItem.externalId);
  }

  if (selectedSite.length > 0) {
    _this.commonService.siteList.filter(function (e) {
      if (selectedSite.indexOf(e.externalId) > -1) {
        if (
          e['reportingUnits']['items'] &&
          e['reportingUnits']['items'].length > 0
        ) {
          _this.unitList = _this.unitList.concat(
            _.orderBy(e['reportingUnits']['items'], ['name'], ['asc'])
          );
          _this.unitList = _.uniqBy(_this.unitList, 'externalId');
          _this.filteredUnitList = _this.unitList.slice();
        }
      }
      return selectedSite.indexOf(e.externalId) > -1;
    });
  } else {
    if (_this.siteList.length > 0) {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.unitList.slice();
    }
  }
}
  getUserMenuConfig(){
    var _this = this

    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
      setTimeout(()=>{
        _this.siteChanged();
      },1000)
    
    }

    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
 
      if(_this.commonService.menuFeatureUserIn.length>0){
 
      _this.createAudit_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditSendAudit);
      var AuditSendAudit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditSendAudit);
      var AuditView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditViewAudit);
      var AuditEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditEditAudit);
      var AuditSummary = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditSummary);
      var AuditScoreCard = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditScoreCard);
      var AuditCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditCreateAction);
      var iframe = document.getElementById('iFrameAudit');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      setTimeout(() => {
        console.log('Inside timeout getUserMenuConfig', iWindow, iframe);
        iWindow?.postMessage({
          type: 'Language',
          action: 'Language',
          LanguageCode: `${this.commonService.selectedLanguage}`,
          idToken: _this.tokenService.getIDToken(),
          labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
        }, '*');
      }, 1000)
      iWindow?.postMessage({ 
        "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
        "data": _this.tokenService.getToken(), 
        "idToken": _this.tokenService.getIDToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      }, '*');
      iWindow?.postMessage({ "type": "Audit", "action": "AccessMenu", "data": {
        AuditSendAudit:AuditSendAudit,
        AuditView:AuditView,
        AuditEdit:AuditEdit,
        AuditSummary:AuditSummary,
        AuditScoreCard :AuditScoreCard,
        AuditCreateAction:AuditCreateAction
      } }, '*');
       
      }else{
        _this.createAudit_obj = {};
        var iframe = document.getElementById('iFrameAudit');
        if (iframe == null) return;
        var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
           "data": _this.tokenService.getToken() }, '*');
        iWindow?.postMessage({ "type": "Audit", "action": "AccessMenu", "data": {
          AuditSendAudit:{},
          AuditView:{},
          AuditEdit:{},
          AuditSummary:{},
          AuditScoreCard :{},
          AuditCreateAction:{}
        } }, '*');
      }
   
  }
  loadProcessType(){
    var _this= this;
    console.log(_this.siteControl.value)
    _this.coreTypeList = _this.commonService.processList.filter(e => e.processType=="Core Principles" && e.refSite.externalId == _this.siteControl.value)
    console.log("coretypelist---->",_this.coreTypeList)
  }
 
  scheduleSendToVedor(audit) {
 
   
 
    // _this.dataService.postData({ auditId: audit.externalId }, _this.dataService.NODE_API + "/api/service/scheduleSendToVedor").subscribe(data => {
    //   _this.commonService.triggerToast({ type: 'success', title: "", msg: "Quality Assessment Questionnaire has been send to the supplier" });
    //   // if (data["items"].length > 0) {
    //   //   _this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    //   _this.loaderFlag = false;
    //   // } else {
    //   //   _this.commonService.triggerToast({ type: 'error', title: "", msg: "Failed" });
    //   // }
    // })
  }
 
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    if (_this.site && _this.site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(_this.site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      console.log('reportingLocationList',_this.reportingLocationList)
    }
    cb();
  }
 
 
  goSupplier() {
    //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
    this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier', {
      timeOut: 3000,
    });
  }
  ngAfterViewInit(): void {
 
    console.log(this.displayedColumns)
    var _this = this;
    this.setDateFormat();
    // _this.loaderFlag = false;
    var iframe = document.getElementById('iFrameAudit');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
       "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "Audit", "action": "Column", "data": _this.displayedColumns }, '*');
    iWindow?.postMessage({
      type: 'Language',
      action: 'Language',
      LanguageCode: `${this.commonService.selectedLanguage}`,
      idToken: _this.tokenService.getIDToken(),
      labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
    }, '*');
    _this.getUserMenuConfig();
 
    var frame = document.getElementsByTagName('iframe');
    var frameWindow = frame[0].contentWindow;
    console.log('frameWindow line 438: ', frameWindow);
 
    frameWindow?.postMessage({
      type: 'Language',
      action: 'Language',
      LanguageCode: `${this.commonService.selectedLanguage}`,
      idToken: _this.tokenService.getIDToken(),
      labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
    }, '*');
 
    console.log('after postmessage', frameWindow);
 
    _this.getUserMenuConfig();
  }
 
  logSelection(event: any): void {
    console.log('User selected:', event.value);
  }
 
  applyObservationFilter() {
    var _this = this;
    _this.commonService.loaderFlag = true;
    setTimeout(function () {
      
      var iframe = document.getElementById('iFrameAudit');
      console.log('iframe==>', iframe);
      if (iframe == null) {
        return;
      }
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ 
        "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
        "data": _this.tokenService.getToken(),
        "columnOrder": _this.columnOrder,
        "idToken": _this.tokenService.getIDToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      }, '*');
 
      var startD;
      var endD;
      _this.getUserMenuConfig();
 
      if (_this.endDateControl.value) {
        var myStartDate = new Date(_this.startDateControl.value);
        startD = myStartDate.getFullYear() + "-" + (("0" + (myStartDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myStartDate.getDate())).slice(-2));
        var myEndDate = new Date(_this.endDateControl.value);
        endD = myEndDate.getFullYear() + "-" + (("0" + (myEndDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myEndDate.getDate())).slice(-2));
      }
 
      console.log(startD, endD);
      console.log(_this.siteControl.value);
 
      var sites = [];
      if (_this.siteControl.value) {
        sites = [_this.siteControl.value];
      }
 console.log(_this.subauditTypeControl.value)
      // Get the selected subprocess name
      var selectedSubProcess = _this.subauditTypeControl.value ? _this.subauditTypeControl.value.name : '';
      // Send the data along with the selected subprocess name
      iWindow?.postMessage({
        "type": "Audit",
        "action": "Filter",
        "subCategory": "","behalf": this.behalf?.value, "location": this.locationObserve?.value,
        "auditType": _this.auditTypeControl?.value?.externalId,"createdBy": this.createdBy?.value,
        "units":this.unitControl?.value,
        "SubProcessType":  _this.subauditTypeControl?.value?.externalId,"OperLearning": _this.OperationalLearning ,
        "subProcessName": selectedSubProcess,
        "date": { start: startD, end: endD },
        "sites": sites,
        "LanguageCode": `${_this.commonService.selectedLanguage}`,
        "idToken": _this.tokenService.getIDToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      }, '*');
 
      console.log("applyFilter");
      if(selectedSubProcess=="Layered Process Audit" || selectedSubProcess=="Life Critical Audit"){
        _this.displayColumnactiveKeys = ['Id','Unit','Location','workReleaseNumber','Shift' ,'Date', 'Observedby','Createdby', 'Updatedby','Actions'];
        _this.displayColumnactiveKeys = _this.enabledFields?.filter(element => _this.displayColumnactiveKeys.includes(element));
      _this.updateActiveFlags(_this.allColumns, _this.displayColumnactiveKeys);
    }
      else{
        _this.displayColumnactiveKeys = ['Id','Title','Year','Quarter' ,'Priority', 'StartDate','EndDate','Status','workReleaseNumber', 'Operational Learning', 'Createdon','Createdby','Actions'];
        _this.displayColumnactiveKeys = _this.enabledFields?.filter(element => _this.displayColumnactiveKeys.includes(element));
      _this.updateActiveFlags(_this.allColumns, _this.displayColumnactiveKeys);
      }
      iWindow?.postMessage({
        type: 'Audit',
        action: 'Language',
        LanguageCode: `${_this.commonService.selectedLanguage}`,
        idToken: _this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      }, '*');
 
      console.log('applyFilter');
      _this.commonService.loaderFlag = false;
      _this.getUserMenuConfig();
      _this.changeDetector.detectChanges();
    }, 1500);
  }
 
  updateActiveFlags(columns: any[], activeKeys: string[]) {
    var _this=this;
    if(columns?.length>0 && activeKeys?.length>0){
      columns.forEach(column => {
        column.activeFlag = activeKeys.includes(column.key2);
      });
      // Call your function with the updated array    
      _this.setColumn(columns);
    }
    
  }
 
  closeDialog() {
    this.dialog.closeAll();
  }
  
  ngOnDestroy(): void {
 
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
 
    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    _this.allColumns = [];
    _this.displayedColumns = [];
    var i = 0;
    console.log(selColumn)
    selColumn.forEach(element => {
      i++;
      _this.allColumns.push(element);
      if (element.activeFlag) {
        _this.displayedColumns.push(element.name);
      }
 
 
    });
console.log(_this.displayedColumns)
    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
 
 
  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });
 
  }
  setSummary() {
 
    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol', summaryCol)
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameAudit');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
     iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
      "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "Audit", "action": "Summary", "data": summaryCol }, '*');
    _this.getUserMenuConfig();
  }
 
  processSelected(process) {
  }
 
  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {
 
  }
  categorySelected(cate) {
 
  }
 
  createAudit() {
    var _this = this;
    console.log("kkkkkkk")
    // observations
    var processList = _this.commonService.processList.filter(e => {
      return e.processType == "Process" && e.name == "Audit" && e.isConfigurable && e.refSite["externalId"] == _this.siteControl.value;
    })
    _this.router.navigateByUrl('observations/observation', {
      state: {
        "observation": processList[0],
        "pageFrom": "Adit List",
        "action": "Edit"
      }
    });
  }
  
  
  searchQuery: string = '';
  searchTerm = '';
  isPopupOpen = false;
  metricsbyperson: any;
  filteredItems: { name: string, count: number, externalIds: string[] }[] = [];
  items = [
   
  ];

  fetchItems(): void {
    this.loaderFlag = true;

    let startD = '';
    let endD = '';
    if (this.startDateControl.value) {
        const myStartDate = new Date(this.startDateControl.value);
        startD = `${myStartDate.getFullYear()}-${("0" + (myStartDate.getMonth() + 1)).slice(-2)}-${("0" + myStartDate.getDate()).slice(-2)}`;
    }
    if (this.endDateControl.value) {
        const myEndDate = new Date(this.endDateControl.value);
        endD = `${myEndDate.getFullYear()}-${("0" + (myEndDate.getMonth() + 1)).slice(-2)}-${("0" + myEndDate.getDate()).slice(-2)}`;
    }

    const requestBody = {
        limit: 1000,
        sites: [this.siteControl.value],
        listType: this.listType ,
        isActive: true,
        startDate: startD,
        endDate: endD
    };

    this.dataService.postData(requestBody, this.dataService.NODE_API + "/api/service/listAudit").subscribe((response: any) => {
        console.log("response---->", response);

        if (response && response.data) {
            const observationList = response['data']['list' + this.commonService.configuration['typeAudit']]['items'];
            console.log("Raw AuditList:", observationList);

            const nameExternalIdMap = new Map<string, { count: number, externalIds: string[] }>();
            const projectMap = new Map<string, { name: string, externalIds: string[] }>();
            const workOrderMap = new Map<string, { name: string, externalIds: string[] }>();
            const createdByMap = new Map<string, { name: string, externalIds: string[] }>();

            observationList.forEach((item: any) => {
              // const projectName = item.projectName;
              // const workOrderNumber = item.workOrderNumber;
                const displayName = item.createdBy ? item.createdBy : 'N/A';
                const externalId = item.externalId;
                const createdBy = item.createdBy;

              //   if (projectName) {
              //     if (!projectMap.has(projectName)) {
              //         projectMap.set(projectName, { name: projectName, externalIds: [] });
              //     }
              //     projectMap.get(projectName)!.externalIds.push(externalId);
              // }
      
              // if (workOrderNumber) {
              //     if (!workOrderMap.has(workOrderNumber)) {
              //         workOrderMap.set(workOrderNumber, { name: workOrderNumber, externalIds: [] });
              //     }
              //     workOrderMap.get(workOrderNumber)!.externalIds.push(externalId);
              // }
              
                    // Handle createdBy data
      if (createdBy) {
        if (!createdByMap.has(createdBy)) {
            createdByMap.set(createdBy, { name: createdBy, externalIds: [] });
        }
        createdByMap.get(createdBy)!.externalIds.push(externalId);
    }


                if (!nameExternalIdMap.has(displayName)) {
                    nameExternalIdMap.set(displayName, { count: 0, externalIds: [] });
                }

                const entry = nameExternalIdMap.get(displayName)!;
                entry.externalIds.push(externalId);
                entry.count = entry.externalIds.length; // Update count to reflect all occurrences
            });

    this.createdByList = Array.from(createdByMap.values());
    this.filteredCreatedByList = this.createdByList;

            this.items = Array.from(nameExternalIdMap.entries()).map(([name, { count, externalIds }]) => {
                return { name, count, externalIds };
            });

            // Log detailed information
            this.items.forEach(item => {
                console.log(`DisplayName: ${item.name}, Count: ${item.count}, ExternalIds: ${item.externalIds.join(', ')}`);
            });

          console.log("All items:", this.items);
          this.filterItems();
          this.changeDetector.detectChanges(); 
          this.loaderFlag = false;
      } else {
          console.error("Response structure is not as expected");
          this.loaderFlag = false;
      }



        
    });
}

  resetSearch(): void {
    this.searchQuery = ''; 
    this.searchTerm = '';
    this.items = [];  
  }

  cancelClick() {
    this.resetSearch()
    this.isPopupOpen = !this.isPopupOpen;
  }
  popupClick() {
    this.isPopupOpen = !this.isPopupOpen;
    if (this.isPopupOpen) {
      this.fetchItems();
    }
  }
  onSearchTermChange(): void {
    console.log("Search term changed:", this.searchTerm);
    this.filterItems();
  }
    filterItems(): void {
    console.log("Filtering with searchTerm:", this.searchTerm);
    this.filteredItems = this.items.filter(item => {
      const matches = item.name?.toLowerCase().includes(this.searchTerm.toLowerCase());
      if (matches) {
        console.log("Item matches:", item);
      }
      return matches;
    });
    console.log("Filtered items:", this.filteredItems);
    this.changeDetector.detectChanges();  // Ensure Angular detects changes
  }



setDateFormat() {
    console.log('this.commonService.dateFormat observaation list')
    console.log(this.commonService.dateFormat)
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
}
 
