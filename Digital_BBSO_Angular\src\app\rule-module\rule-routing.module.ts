import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CreateRuleComponent } from './create-rule/create-rule.component';
import { RuleListComponent } from './rule-list/rule-list.component';



const routes: Routes = [
    { path: '', redirectTo: 'rule-list', pathMatch: 'full' },
    {
        path: 'rule-list',
        component: RuleListComponent
    },
    {
        path: 'create-rule', 
        component: CreateRuleComponent
    },

];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class RuleRoutingModule { }