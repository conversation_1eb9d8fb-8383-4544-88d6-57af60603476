import { AfterViewInit, ChangeDetectorRef, Component, NgZ<PERSON>, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { environment } from 'src/environments/environment';
import { LanguageService } from 'src/app/services/language.service';

@Component({
  selector: 'app-action-list',
  templateUrl: './action-list.component.html',
  styleUrls: ['./action-list.component.scss'],
})
export class ActionListComponent implements OnInit, AfterViewInit, OnDestroy {
  siteControl: FormControl = new FormControl('');
  filteredSiteOptions: Observable<any[]>;
  labels = {}

  unitControl: FormControl = new FormControl('');
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl('');

  startDateControl: FormControl = new FormControl();

  processControl: FormControl = new FormControl('');
  loaderFlag: boolean;
  process = [
    {
      value: 'All',
      name: 'All',
    },
    {
      value: 'Observation',
      name: 'Observation',
    },
    {
      value: 'FieldWalk',
      name: 'FieldWalk',
    },
    {
      value: 'OFWAAudit',
      name: 'Audit',
    },
  ];

  observationControl: FormControl = new FormControl('Behaviour');
  observation = [
    {
      name: 'Behaviour',
    },
    {
      name: 'Hazards',
    },
    {
      name: 'Incidents',
    },
  ];

  categoryControl: FormControl = new FormControl('PPE');
  category = [
    {
      name: 'PPE',
    },
    {
      name: 'Tools & Equipment',
    },
    {
      name: 'Work Environment',
    },
  ];

  assignedControl: FormControl = new FormControl('James');
  assignedToArr = [
    {
      name: 'James',
    },
    {
      name: 'Michael',
    },
    {
      name: 'David',
    },
  ];

  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });
  displayedColumns2: string[] = [
    'id',
    'observationType',
    'category',
    'subCategory',
    'recommendations',
    'assignedTo',
    'dueDate',
    'actions',
  ];

  displayedColumns: any = [];

  allColumns = [
    {
      name: 'id',
      displayName: 'Id',
      key: 'id',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'objectType',
      displayName: 'Process',
      key: 'process',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'description',
      displayName: 'Description',
      key: 'description',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'applicationName',
      displayName: 'Application Name',
      key: 'tablecolsApplicationname',
      activeFlag: false,
      summary: false,
    },
    {
      name: 'assignedToName',
      displayName: 'Assigned To',
      key: 'tablecolsAssignedto',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'site',
      displayName: 'Site',
      key: 'site',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'unit',
      displayName: 'Unit',
      key: 'unit',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'reportingLocation',
      displayName: 'Reporting Location',
      key: 'tablecolsReportinglocation',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'assignmentDate',
      displayName: 'Assigned Date',
      key: 'tablecolsAssigneddate',
      activeFlag: true,
      summary: false,
    },
    {
      name: 'dueDate',
      displayName: 'Due Date',
      key: 'tablecolsDuedate',
      activeFlag: true,
      summary: false,
    },
    // {
    //   name: 'objectType',
    //   displayName: 'Object Type',
    //   key: 'Objecttype',
    //   activeFlag: true,
    //   summary: false,
    // },
    // {
    //   name: 'objectExternalId',
    //   displayName: 'Object Id',
    //   key: 'Objectid',
    //   activeFlag: true,
    //   summary: false,
    // },
    {
      name: 'priority',
      displayName: 'Priority',
      key: 'priority',
      activeFlag: true,
      summary: false,
    },
    //  { key: 'actions', displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  ];

  // "applicationName":"left",
  //       "assignedTo":"left",
  //       "site":"left",
  // "description",
  // "assignmentDate",
  // "dueDate",
  // "objectType",
  // "objectExternalId",
  // "priority",
  url: any = '';
  userAccessMenu: any;
  constructor(
    private router: Router,
    private commonService: CommonService,
    private dataService: DataService,
    private tokenService: TokenService,
    private dialog: MatDialog,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone,
    private changeDetector: ChangeDetectorRef
  ) {
    //  this.url =this.dataService.React_API+ "/actionList";
    this.allColumns.forEach((item) => {
      if (item.activeFlag == true) {
        this.displayedColumns.push(item.name);
      }
    });

    console.log(' this.displayedColumns', this.displayedColumns);
    this.url = this.dataService.React_API + '/actionList';

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
        'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
        'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
        'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
        'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
      }
    }

  }

  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'actions': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'actions'] || 'actions',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
          'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == 'ActionList') {
        console.log(e.data.action);
        if (e.data.action == 'FormView') {
          console.log('FormView-->');
          console.log('e.data', e.data);
          _this.router.navigateByUrl('action/create-action', {
            state: {
              data: e.data.data,
              pageFrom: e.data.data.objectType,
              action: 'View',
            },
          });
        }
        else if ( e.data.action == "Loading") {
          _this.loaderFlag = false
        } 
        else if (e.data.action == 'FormEdit') {
          console.log('FormEdit-->');
          console.log('e.data', e.data);
          _this.router.navigateByUrl('action/create-action', {
            state: {
              data: e.data.data,
              pageFrom: e.data.data.objectType,
              action: 'Edit',
            },
          });
        } else if (e.data.action == 'InitalSetup') {
          var iframe = document.getElementById('iFrameActionList');
          if (iframe == null) return;
          var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
          iWindow?.postMessage(
            { 
              type: 'AuthToken', 
              data: _this.tokenService.getToken(),
              idToken:_this.tokenService.getIDToken(),
              LanguageCode:_this.commonService.selectedLanguage.toUpperCase() 
            },
            '*'
          );
          iWindow?.postMessage(
            {
              type: 'Language',
              action: 'Language',
              LanguageCode: `${_this.commonService.selectedLanguage.toUpperCase()}`,
              idToken: _this.tokenService.getIDToken(),
              labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
            },
            '*'
          );
          iWindow?.postMessage(
            {
              type: 'ActionList',
              action: 'Column',
              data: _this.displayedColumns,
              LanguageCode: `${_this.commonService.selectedLanguage}`,
            },
            '*'
          );
          console.log('Inside ngOnInit after actionList');
          _this.getUserMenuConfig();
        }
      }
    };

    if (_this.commonService.siteList.length > 0) {
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == 'userAccess') {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu);
          _this.getUserMenuConfig();
        }
      }
    });
  }
  getUserMenuConfig() {
    var _this = this;

    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
    if (_this.commonService.menuFeatureUserIn.length > 0) {
      var ActionView = _this.commonService.menuFeatureUserIn.find(
        (item) => item.featureCode == _this.dataService.appMenuCode.actionView
      );
      var ActionEdit = _this.commonService.menuFeatureUserIn.find(
        (item) => item.featureCode == _this.dataService.appMenuCode.actionEdit
      );
      var iframe = document.getElementById('iFrameActionList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { 
          type: 'AuthToken', 
          data: _this.tokenService.getToken(),
          idToken:_this.tokenService.getIDToken(),
          LanguageCode:_this.commonService.selectedLanguage.toUpperCase()
        },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'ActionList',
          action: 'AccessMenu',
          data: { ActionView: ActionView, ActionEdit: ActionEdit },
        },
        '*'
      );
      iWindow?.postMessage({ "type": "ActionList", "action": "Filter", "data": {site:_this.dataService.siteId?_this.dataService.siteId:null}, }, '*');
    } else {
      var iframe = document.getElementById('iFrameActionList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { 
          type: 'AuthToken', 
          data: _this.tokenService.getToken(),
          idToken:_this.tokenService.getIDToken(),
          LanguageCode:_this.commonService.selectedLanguage.toUpperCase()
        },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'ActionList',
          action: 'AccessMenu',
          data: { ActionView: {}, ActionEdit: {} },
        },
        '*'
      );
      iWindow?.postMessage({ "type": "ActionList", "action": "Filter", "data": {site:_this.dataService.siteId?_this.dataService.siteId:null}, }, '*');
    }
  }

  ngAfterViewInit(): void {
    var _this = this;
    _this.loaderFlag = true;
    // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');
    // setTimeout(() => {
    //   _this.loaderFlag = true;
    //   var iframe = document.getElementById('iFrameActionList');
    //   if (iframe == null) return;
    //   var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    //   iWindow?.postMessage(
    //     {
    //       type: 'AuthToken',
    //       data: _this.tokenService.getToken(),
    //       LanguageCode: `${_this.commonService.selectedLanguage}`,
    //     },
    //     '*'
    //   );
    //   iWindow?.postMessage(
    //     {
    //       type: 'ActionList',
    //       action: 'Column',
    //       data: _this.displayedColumns,
    //       LanguageCode: `${_this.commonService.selectedLanguage}`,
    //     },
    //     '*'
    //   );
    //   // iWindow?.postMessage({ "type": "ActionList", "action": "Filter", "data": {site:_this.siteControl.value?_this.siteControl:null}, }, '*');
    //   _this.getUserMenuConfig();
    //   if (_this.dataService.siteId) {
    //     console.log('action', _this.dataService.siteId);
    //     _this.siteControl.setValue(_this.dataService.siteId);
    //   }
    // }, 100);
    _this.getUserMenuConfig();

    _this.siteControl.valueChanges.subscribe((siteVal) => {
      console.log('site action', siteVal);
      var _this = this;
      setTimeout(() => {
        var iframe = document.getElementById('iFrameActionList');
        if (iframe == null) return;
        var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        iWindow?.postMessage(
          { 
            type: 'AuthToken', 
            data: _this.tokenService.getToken(),
            idToken:_this.tokenService.getIDToken(),
            LanguageCode:_this.commonService.selectedLanguage.toUpperCase()
          },
          '*'
        );
        iWindow?.postMessage(
          { type: 'ActionList', action: 'Filter', data: { site: siteVal } },
          '*'
        );
    
      }, 100);
    });
    _this.processControl.valueChanges.subscribe((processVal) => {
      var _this = this;
      var iframe = document.getElementById('iFrameActionList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { 
          type: 'AuthToken', 
          data: _this.tokenService.getToken(),
          idToken:_this.tokenService.getIDToken(),
          LanguageCode:_this.commonService.selectedLanguage.toUpperCase()
        },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'ActionList',
          action: 'Filter',
          data: { objectType: processVal == 'All' ? null : processVal },
        },
        '*'
      );
    
    });
    // _this.processControl.setValue("All")
    // _this.filteredSiteOptions = _this.siteControl.valueChanges.pipe(
    //   startWith('',asyncScheduler),
    //   map(value => this.commonService._siteFilter(value || '')),
    // );

    // this.filteredUnitOptions = this.unitControl.valueChanges.pipe(
    //   startWith('',asyncScheduler),
    //   map(value => this.commonService._unitFilter(value || '')),
    // );
  }
  ngOnDestroy(): void {}
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result == 'object') {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach((element) => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }
    });

    console.log('_this.displayedColumns,', _this.displayedColumns);
    var iframe = document.getElementById('iFrameActionList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { 
        type: 'AuthToken', 
        data: _this.tokenService.getToken(),
        idToken:_this.tokenService.getIDToken(),
        LanguageCode:_this.commonService.selectedLanguage.toUpperCase() 
      },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'ActionList',
        action: 'Column',
        data: _this.displayedColumns,
        LanguageCode: `${_this.commonService.selectedLanguage}`,
      },
      '*'
    );
  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) {
        myColumns.push(eData);
      }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };
    dialogConfig.height = 'auto';
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe((result) => {
    //   if (typeof result == 'object') {
    //     this.setSummary();
    //   }
    // });
  }
  setSummary() {
    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          enable: 'Y',
          colorRange: eData['summaryColor'] ? eData['summaryColor'] : [],
        };
      }
    });
    console.log('summaryCol', summaryCol);
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameActionList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    // iWindow?.postMessage(
    //   { type: 'AuthToken', data: _this.tokenService.getToken() },
    //   '*'
    // );
    iWindow?.postMessage(
      { type: 'ActionList', action: 'Summary', data: summaryCol },
      '*'
    );
  
  }

  private _metafilter(value) {
    const filterValue: any = this._metanormalizeValue(value);
    return this.process.filter((street) =>
      this._metanormalizeValue(street.name).includes(filterValue)
    );
  }

  private _metanormalizeValue(value: any): any {
    return value.toLowerCase().replace(/\s/g, '');
  }

  processSelected(process) {}

  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {}
  categorySelected(cate) {}
  proceSelected(type) {}
}
