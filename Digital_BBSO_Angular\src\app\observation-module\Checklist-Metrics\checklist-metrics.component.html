
<div fxLayout="row" class="overflow-section">
    <div fxFlex="100">
        <div class="audit-plan-unit-section schedular-tracker-section" style="float: right;">
            <div class="audit-head-icon">
                <div class="icon-bg-box schedular-tracker-back-icon" (click)="goAudit()">
                    <mat-icon>arrow_back_ios</mat-icon>
                </div>
            </div>
        </div>
        <div  class="action-section-unit-section">
            <commom-label labelText="{{ labels['checklistTitle'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
        </div>
<div fxLayout="row wrap">
  <div fxFlex="100" fxLayoutAlign="start center">
    <div class="d-flex align-item-center mr-10">
      <!-- <span class="subheading-1 site-icon-text">{{
        'CONFIGURATION.FORM_CONTROLS.CORE_PRINCIPLE' | translate
        }}</span> -->
      <mat-form-field appearance="outline" class="set-back-color">
        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosecoreprinciple'] }}"
          [formControl]="corePrinciplesControl" disableOptionCentering>
          <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
            [array]="corePrinciplesList" (filteredReturn)="filteredCorePrinciplesList =$event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredCorePrinciplesList" [value]="item.externalId">
            {{ labels['cards'+item.name] }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="d-flex align-item-center mr-10">
      <!-- <span class="subheading-1 site-icon-text">{{ 'CONFIGURATION.UNIT_SITE.PROCESS' |
        translate }}</span> -->
      <mat-form-field appearance="outline" class="set-back-color">
        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChooseprocess'] }}"
          [formControl]="processControl" disableOptionCentering>
          <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
            [array]="processList" (filteredReturn)="filteredProcessList =$event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredProcessList" [value]="item.externalId">
            <span *ngIf="item.name != 'Field Walk' || item.name != 'Observation' || item.name != 'Audit'" >
              {{labels['cards'+item.name]}}
            </span>
            <span *ngIf="item.name == 'Field Walk'" >
              {{ labels['fieldWalk'] }}
            </span>
            <span *ngIf="item.name == 'Observation'" >
              {{labels['observation']}}
            </span>
            <span *ngIf="item.name == 'Audit'" >
              {{labels['audit']}}
            </span>
            <!-- {{ item.name != 'Field Walk' || item.name != 'Observation'? (labels['cards'+item.name]) : item.name == 'Field Walk' ? (labels['fieldWalk']) : (labels['observation']) }} -->
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="d-flex align-item-center mr-10">
      <!-- <span class="subheading-1 site-icon-text">{{ 'CONFIGURATION.UNIT_SITE.PROCESS' |
        translate }}</span> -->
        <mat-form-field appearance="outline" class="set-back-color">
          <mat-select (click)="filterClick()"
              placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
              [formControl]="subProcessControl" disableOptionCentering>
              <mat-select-filter [placeholder]="labels['commonfilterSearch']"
                  [displayMember]="'name'" [array]="subProcessList"
                  (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
              <mat-option *ngFor="let item of filteredSubProcessList"
                  [value]="item.externalId">
                  {{item.name }}
              </mat-option>
          </mat-select>
      </mat-form-field>
    </div>
  </div>
</div>
                            
      

       
  <br>

  <mat-dialog-content>
    <div class="overlay"></div>
    <div class="popup">
      <div class="popup-content">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

           <!-- Sub Category Name Column -->
          <ng-container matColumnDef="subCategoryName">
            <th mat-header-cell *matHeaderCellDef class="custom-header">{{labels['formcontrolsSubcategory'] }}</th>
            <td mat-cell *matCellDef="let element">{{element.subCategoryName}}</td>
          </ng-container>
        
          <!-- Category Name Column -->
          <ng-container matColumnDef="categoryName">
            <th mat-header-cell *matHeaderCellDef class="custom-header">{{ labels['category'] }}</th>
            <td mat-cell *matCellDef="let element">{{element.categoryName}}</td>
          </ng-container>
  
          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef class="custom-header">{{ labels['formcontrolsBehaviourchecklist'] }}</th>
            <td mat-cell *matCellDef="let element">{{element.description}}</td>
          </ng-container>
  
          <!-- Is Safe Count Column -->
          <ng-container matColumnDef="isSafeCount">
            <th mat-header-cell *matHeaderCellDef class="custom-header align-right">{{ labels['isSafe'] }}</th>
            <td mat-cell *matCellDef="let element" class="align-right">{{element.isSafeCount}}</td>
          </ng-container>
  
          <!-- Is UnSafe Count Column -->
          <ng-container matColumnDef="isUnSafeCount">
            <th mat-header-cell *matHeaderCellDef class="custom-header align-right">{{ labels['unSafe'] }}</th>
            <td mat-cell *matCellDef="let element" class="align-right">{{element.isUnSafeCount}}</td>
          </ng-container>
  
          <!-- Is Not Observed Count Column -->
          <ng-container matColumnDef="isNotObservedCount">
            <th mat-header-cell *matHeaderCellDef class="custom-header align-right">{{ labels['formcontrolsIsnotobserved'] }}</th>
            <td mat-cell *matCellDef="let element" class="align-right">{{element.isNotObservedCount}}</td>
          </ng-container>
  
          <!-- Header Row -->
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  
          <!-- Data Rows -->
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
  
        <!-- No Results Message -->
        <div *ngIf="dataSource.data.length === 0" class="no-results">
          {{ labels['commonfilterNoresults'] }}
        </div>
  
        <!-- Paginator -->
        <mat-paginator [pageSizeOptions]="[10, 50, 100, 500]" #paginator showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </mat-dialog-content>
  
  
          
          

          
          
    </div>
</div>
