import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import * as _ from 'lodash';
import { UserInterDailogComponent } from 'src/app/shared/user-integration-dialog/userInter-dailog.component';
import { ObservationSendMailComponent } from '../observation-send-mail/observation-send-mail.component';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { Subject } from 'rxjs';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import { MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import { TemplateRef, ViewChild } from '@angular/core';

export const DYNAMIC_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-observation-list',
  templateUrl: './observation-list.component.html',
  styleUrls: ['./observation-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers:[ { provide: MAT_DATE_FORMATS, useValue: DYNAMIC_DATE_FORMATS },]
})
export class ObservationListComponent implements OnInit {

  @ViewChild('filterDialog') filterDialog!: TemplateRef<any>;

  x: any = 60;
  y: any = 40;
  minSize: any = 100;

  showhide: boolean = false;
  expandFlag: boolean = false;
  errorSpinner: boolean = false;
  searchQuery: string = '';
  filteredItems: { name: string, count: number, externalIds: string[] }[] = [];
  searchTerm = '';

  // startDate = _this.commonService.labelObject[locale][key]
  labels = {};
  logList = [
  ]

  siteControl: FormControl = new FormControl("");

  operationalLearningControl = new FormControl('All');

  site: any;

  @Input() listType: any = "Observation";

  filterFlag = '';
  filteredSiteOptions: Observable<any[]>;
  siteList = [];
  filteredSiteList = [];

  filteredUnitOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl('');
  locationObserve: FormControl = new FormControl('');
  behalf: FormControl = new FormControl('');
  unitList = [];
  filteredUnitList = [];

  loaderFlag: boolean;

  selectedListType: string;

  searchControl: FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  processControl: FormControl = new FormControl("Observation");

  process = [
    {
      id: 1,
      name: "Observation",

    },
    {
      id: 2,
      name: "Field Walk",

    },
    {
      id: 3,
      name: "Audit",

    },


  ]
  filteredProcessOptions: Observable<any[]>


  observationControl: FormControl = new FormControl("Behaviour");
  observation = [
    {
      name: "Behaviour",
    },
    {
      name: "Hazards",
    },
    {
      name: "Incidents",
    },
  ]

  categoryControl: FormControl = new FormControl("PPE");
  category = [
    {
      name: "PPE"
    },
    {
      name: "Tools & Equipment"
    },
    {
      name: "Work Environment"
    },

  ]

  observeType: FormControl = new FormControl("");
  filteredObserveTypeOptions: Observable<any[]>
  
  coreTypeControl: FormControl = new FormControl("");
  subProcessControl: FormControl = new FormControl("");
  projectName: FormControl = new FormControl("");
  workOrderNumber: FormControl = new FormControl("");
  categoryOpt: FormControl = new FormControl("");
  subCategoryOpt: FormControl = new FormControl("");
  createdBy: FormControl = new FormControl("");
  status: FormControl = new FormControl("");
  
  coreTypeList:any = []
  auditTypeControl: FormControl = new FormControl("");

  // category: FormControl = new FormControl("");
  // filteredCategoryOptions: Observable<any[]>

  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });

  startDateControl: FormControl = new FormControl("");
  endDateControl: FormControl = new FormControl("");
  dateControl: FormControl = new FormControl("");
  displayedColumns: any = [];
  allColumns = [
    { key: 'id', key2:'Id', displayName: "Id", name: "externalId", activeFlag: true, summary: false },
    { key: 'date', key2:'Date', displayName: "Date", name: "date", activeFlag: true, summary: false },
    { key: 'week', key2:'Week', displayName: "Week", name: "week", activeFlag: false, summary: false },
    { key: 'craft', key2:'Craft', displayName: "Craft", name: "crafts", activeFlag: true, summary: false },
    { key: 'site', key2:'Site',displayName: "Site", name: "site", activeFlag: true, summary: false },
    { key: 'unit', key2:'Unit', displayName: "Unit", name: "unit", activeFlag: true, summary: false },
    { key: 'shortDescription', key2:'ShortDesc', displayName: "Short Description", name: "shortDescription", activeFlag: true, summary: false },
    { key: 'category',key2:'Category', displayName: "Category", name: "category", activeFlag: true, summary: false },
    { key: 'operationalLearning', key2:'OperationalLearning',displayName: "Operational Learning", name: "isOperationalLearning", activeFlag: true, summary: false },
    { key: 'art', key2:'Art', displayName: "Art", name: "art", activeFlag: false, summary: false },
    { key: 'cause' , key2:'Cause',displayName: "Cause", name: "cause", activeFlag: false, summary: false },
    { key: 'problem' , key2:'Problem',displayName: "Problem", name: "problem", activeFlag: false, summary: false },
    { key: 'solution' ,key2:'Solution', displayName: "Solution", name: "solution", activeFlag: false, summary: false },
    { key: 'measureActivity' , key2:'Measure',displayName: "Measure", name: "measure", activeFlag: false, summary: false },
    { key: 'operationalLearningDescription', key2:'OperationalLearningDescription',displayName: "Describe the Operational Learning opportunities you found", name: "operationalLearningDescription", activeFlag: false, summary: false },
    { key: 'subCategory', key2:'Subcategory',displayName: "Sub Category", name: "subCategory", activeFlag: true, summary: false },
    { key: 'location', key2:'Location',displayName: "Location Observed", name: "location", activeFlag: true, summary: false },
    { key: 'observationType', key2:'ObsType',displayName: "Observation Type", name: "observationType", activeFlag: true, summary: false },
    { key: 'status', key2:'Status',displayName: "Status", name: "observationStatus", activeFlag: true, summary: false },
    { key: 'workOrderNumber', key2:'WorkOrderNumber',displayName: "Work Order Number", name: "workOrderNumber", activeFlag: false, summary: false },
    { key: 'projectName', key2:'ProjectName',displayName: "Project Name", name: "projectName", activeFlag: false, summary: false },
    { key: 'description', key2:'Description',displayName: "Description", name: "description", activeFlag: false, summary: false },
    { key: 'createdOn', key2:'Createdon',displayName: "Created On", name: "createdTime", activeFlag: true, summary: false },
    { key: 'createdBy', key2:'Createdby',displayName: "Created By", name: "createdBy", activeFlag: true, summary: false },
    { key: 'updatedOn', key2:'Updatedon',displayName: "Updated On", name: "lastUpdatedTime", activeFlag: false, summary: false },
    { key: 'updatedBy', key2:'Updatedby',displayName: "Updated By", name: "modifiedBy", activeFlag: false, summary: false },
    { key: 'actions', key2:'Actions',displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  ];

  allColumnsBackup= _.cloneDeep(this.allColumns)
  isPopupOpen = false;
  items = [
   
  ];
  projectNameList: { name: string; externalIds: string[]; }[];
  workOrderNumberList: { name: string; externalIds: string[]; }[];
  filteredprojectNameList: any;
  filteredworkOrderNumberList: any;
  categoryList: { name: string; externalId: string[]; }[];
  subCategoryList: { name: string; externalId: string[]; }[];
  filteredCategoryList: any;
  filteredSubCategoryList: any;
  safeDisplayName: any;
  notsafeDisplayName: any;
  apply: boolean=true;
  createdByList: { name: string; externalIds: string[]; }[];
  statusList: { name: string; externalIds: string[]; }[];
  filteredCreatedByList: any;
  filteredStatusList: any;
  subProcessList: any[];
  filteredSubProcessList: any;
  columnOrder: any;

  openFilterDialog() {
    this.dialog.open(this.filterDialog, {
      disableClose: true, // Prevent closing on outside click
    });
  }
  

  url: string = "";
  initialDataFlag = 0;
  userAccessMenu;
  createOb_obj: any;
  createHa_obj: any;
  selectedProcess: any;
  enabledFields: string[];
  typeList: any;
  
  auditType: any;
  processConfig:any;
  OperationalLearning: boolean;
  metricsbyperson: any;
  reportingLocationList: any;
  filteredReportingLocationList: any;
  tempLocation: any;
  behalfList: any;
  filteredBehalfList: any;
  fl_searchVal: any;

  constructor(private cd: ChangeDetectorRef,private router: Router, private commonService: CommonService, private tokenService: TokenService, private dataService: DataService, private dialog: MatDialog, private toastr: ToastrService, private translate: TranslateService, public translationService: TranslationService, private languageService: LanguageService, public ngZone: NgZone, private changeDetector: ChangeDetectorRef) {
    this.url = this.dataService.React_API + "/observationList";
    this.setDateFormat();
    console.log('commonService label', this.commonService.labelObject)
    console.log('commonService label', this.commonService.selectedLanguage)
    

    window?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: this.commonService.labelObject[this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'formcontrolsEnddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnddate'] || 'formcontrolsEnddate',
        'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
        'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'],
        'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'] || 'checklistTitle',
        'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'] || 'reacttableMetricsbyperson',
        'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'] || 'formcontrolsSno',
        'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
        'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'] || 'formcontrolsHazcount',
        'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'] || 'formcontrolsObscount',
        'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
        'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
        'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
        'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
        'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
        'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'] || 'expand',
        'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'] || 'collapse',
        'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'] || 'logInfo',
        'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'] || 'tablecolsName',
        'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'] || 'type',
        'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
        'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
        'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate',
        'formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'],
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'],
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'chooseProjectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseProjectName'] || 'chooseProjectName',
        'chooseWorkOrderNumber':this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
        'chooseSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseSubCategory'] || 'chooseSubCategory',
        'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
        'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
      'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',
      'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
      'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters',
      }
    }

    this.metricsbyperson = this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.metricsbyperson);
  }
  deleteOb_obj: any;
  selectedSite: any;

  ngOnInit(): void {


    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'startDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'startDate'] || 'startDate',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'formcontrolsEnddate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsEnddate'] || 'formcontrolsEnddate',
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
          'commonfilterChooseunit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseunit'] || 'commonfilterChooseunit',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'],
          'reacttableMetricsbyperson': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableMetricsbyperson'] || 'reacttableMetricsbyperson',
          'formcontrolsSno': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSno'] || 'formcontrolsSno',
          'observedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observedBy'] || 'observedBy',
          'formcontrolsHazcount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsHazcount'] || 'formcontrolsHazcount',
          'formcontrolsObscount': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsObscount'] || 'formcontrolsObscount',
          'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'],
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'],
          'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
          'behaviourchecklistCreateanobervation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanobervation'] || 'behaviourchecklistCreateanobervation',
          'behaviourchecklistCreateanhazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'behaviourchecklistCreateanhazards'] || 'behaviourchecklistCreateanhazards',
          'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'] || 'expand',
          'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'] || 'collapse',
          'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'] || 'logInfo',
          'tablecolsName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tablecolsName'] || 'tablecolsName',
          'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'] || 'type',
          'dateAndTime': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'dateAndTime'] || 'dateAndTime',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'endDate': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'endDate'] || 'endDate','formcontrolsAudittype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsAudittype'],
        'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'],
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'operationalLearning': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'operationalLearning'] || 'operationalLearning',
        'locationObserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'locationObserved'] || 'locationObserved',
        'commonfilterChooselocation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooselocation'] || 'commonfilterChooselocation',
        'commonfilterChoosebehalf': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosebehalf'] || 'commonfilterChoosebehalf',
        'buttonApply': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonApply'] || 'buttonApply',
        'buttonClear': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonClear'] || 'buttonClear',
        'filter': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filter'] || 'filter',
        'workOrderNumber': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'workOrderNumber'] || 'workOrderNumber',
        'projectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'projectName'] || 'projectName',
        'chooseProjectName': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseProjectName'] || 'chooseProjectName',
        'chooseWorkOrderNumber':this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseWorkOrderNumber'] || 'chooseWorkOrderNumber',
        'chooseCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCategory'] || 'chooseCategory',
        'chooseSubCategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseSubCategory'] || 'chooseSubCategory',
        'chooseStatus': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseStatus'] || 'chooseStatus',
        'chooseCreatedBy': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'chooseCreatedBy'] || 'chooseCreatedBy',
        'formcontrolsYes': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsYes'] || 'formcontrolsYes',
      'formcontrolsNo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsNo'] || 'formcontrolsNo',
      'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',        
      'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
      'filters': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'filters'] || 'filters'
      }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
    
    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
      _this.behalfList.forEach((element)=>{
        element.name =  element.firstName+' '+element.lastName
      })
      _this.filteredBehalfList = _this.behalfList.slice();
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo)
      console.log('_this.commonService.userInfo.externalId',_this.dataService.userInfo.externalId)
     
     
      // if(_this.observation && _this.observation.observedOnBehalfOf){
      //   _this.observedForm.get("behalf").setValue(_this.observation.observedOnBehalfOf["externalId"]);
      // }

    })

    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
    //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
    if(_this.userAccessMenu){
      _this.getUserMenuConfig();
    }
  
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }

    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.siteControl.setValue(_this.dataService.siteId);
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    
    if (_this.commonService.filterListFetched) {
      _this.filterInit('Site');
      _this.filterInit('Unit');
    }



    _this.auditTypeControl.valueChanges.subscribe(value => {
      var _this= this;
      _this.auditType = value["externalId"];
      console.log("auditType", _this.auditType)
      // _this.applyObservationFilter();
    });
    _this.loadProcessType()
  
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        _this.filterInit(fiterType);
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.processLoad(() => { });
      _this.siteChanged();
    });


    
    _this.unitControl.valueChanges.subscribe(value => {
      console.log('Value:', value);
    
      _this.processLoad(() => { });
    
      let tempArray = [];
      
      // Check if value is an array or a single value
      if (Array.isArray(value)) {
        // If value is an array, filter for each element in the array
        tempArray = _this.tempLocation.filter(item => 
          value.includes(item.reportingUnit.externalId)
        );
      } else {
        // If value is a single value, filter normally
        tempArray = _this.tempLocation.filter(item => 
          item.reportingUnit.externalId === value
        );
      }
    
      console.log('tempArray:', tempArray);
    
      _this.reportingLocationList = tempArray;
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
    
      console.log('_this.reportingLocationList:', _this.reportingLocationList);
    });
    

    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);
    

    // setTimeout(function () {
    //   console.log("applied")
    //   _this.applyObservationFilter();
    // }, 1500);

    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "ObservationList") {
        if (e.data.action == "FormView") {
          _this.router.navigateByUrl('observations/observation', {
            state: { "externalId": e.data.data.externalId,
                    "pageFrom":"Observation List",
                    "action":"View" }
          });
        } else if (e.data.action == "FormDelete") {
          _this.deleteObservation(e.data.data.externalId,e.data.data.refSite.siteCode)
          // _this.router.navigateByUrl('observations', {
          //   state: { "externalId": e.data.data.externalId,
          //           "pageFrom":"Observation List",
          //           "action":"Delete" }
          // });
        }else if ( e.data.action == "FormEdit") {
          _this.router.navigateByUrl('observations/observation', {
            state: { "externalId": e.data.data.externalId,
                    "pageFrom":"Observation List",
                    "action":"Edit" }
          });
        }  else if ( e.data.action == "LangError") {
          _this.applyObservationFilter(); 
        }
        else if ( e.data.action == "Loading") {
          console.log("e.data.data",e.data.data)
              _this.commonService.loaderFlag = e.data.data;
        } 
        else if ( e.data.action == "observationHistory") {
       
        _this.showhide = true;
        console.log('observationHistory',_this.showhide);
        _this.getLogList(e.data.data.externalId);
        _this.cd.detectChanges();
       } else if( e.data.action == "sendMail"){
        //sendMail
       console.log('sendMail',e.data.data);
       _this.sendMailUser(e.data.data);
       }
         else if (e.data.action == "createAction") {
         // _this.goPage('action/create-action')
          _this.router.navigateByUrl('action/create-action', {
            state: { "data": e.data.data,
                    "pageFrom":e.data.processType,
                    "action":"Create Action" }
          });

        }
        else if (e.data.action == "Infield") {
          window.open('https://cognite-infield.cogniteapp.com/observations', '_blank', 'noopener, noreferrer')
        }
      };

    }

    var _this = this;

    this.endDateControl.valueChanges.subscribe(async range => {
      // _this.applyObservationFilter();
    });



    this.filteredProcessOptions = this.processControl.valueChanges.pipe(
      startWith('', asyncScheduler),
      map(value => this._metafilter(value || '')),
    );

    this.fetchItems();

    _this.coreTypeControl.valueChanges.subscribe(value => {
      var _this= this;

    _this.typeList = _this.commonService.processList.filter(e => (e.refOFWAProcess && e.refOFWAProcess.externalId) == value["externalId"])
    _this.typeList = (_this.typeList.filter(e => e.name==_this.listType))[0].externalId
    console.log("typeList", _this.typeList)
      var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.typeList);
      })
      _this.subProcessList = childrenProcess.filter(e => e.isActive != false);
      _this.subProcessList.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));
      _this.filteredSubProcessList = _this.subProcessList.slice();
    });
    _this.subProcessControl.valueChanges.subscribe((value: any) => {
      if(value!=null){
        _this.apply=false
        _this.dataService.postData({ "externalId": value }, this.dataService.NODE_API + "/api/service/listProcessConfigurationByProcess").subscribe((resData: any) => {
          var processConfig;
          if (resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
            processConfig = resData["data"]["list" + _this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
          }else{
            processConfig = _this.commonService.defaultConfigList.find(e => e.refProcess == _this.auditTypeControl.value.name)
          }
          var myColumn = [];
          Object.entries(processConfig.columnConfig).forEach(([key, value]) => {
            value["key"] = key;
            myColumn.push(value);
          })
          _this.columnOrder = myColumn.sort((a, b) => (a.sequence ? a.sequence : 0) - (b.sequence ? b.sequence : 0));

          // if (resData["data"]["list" + this.commonService.configuration["typeProcessConfiguration"]]["items"].length > 0) {
            // var processConfig = resData["data"]["list" + this.commonService.configuration["typeProcessConfiguration"]]["items"][0]
    if(processConfig.columnConfig!=null){
      this.enabledFields = Object?.keys(processConfig?.columnConfig).filter(key => 
        typeof processConfig.columnConfig[key] === 'object' && processConfig.columnConfig[key].isEnabled
      ).map(key => processConfig.columnConfig[key].field);
    }

    if(processConfig.configDetail!=null){
      _this.safeDisplayName=processConfig.configDetail.safe.displayName
      _this.notsafeDisplayName=processConfig.configDetail.notsafe.displayName
      _this.apply=true
      if (this.safeDisplayName && this.notsafeDisplayName) {
        this.filteredStatusList = this.statusList.map(status => {
            if (status.name === "Safe") {
                return { ...status, name2: this.safeDisplayName };
            } else if (status.name === "Unsafe") {
                return { ...status, name2: this.notsafeDisplayName };
            }
            return status;
        });
    }
    else{
      this.filteredStatusList = this.statusList.map(status => {
        if (status.name === "Safe") {
            return { ...status, name2: this.statusList[0].name };
        } else if (status.name === "Unsafe") {
            return { ...status, name2: this.statusList[1].name };
        }
        return status;
    });
    }
    }
            
            // _this.applyObservationFilter();
          });
          }
        // })
      // }
        else{
          _this.apply=true
          this.enabledFields=this.allColumnsBackup.filter(column => column.activeFlag).map(column => column.key2)
        }

    })
    
  // this.applyObservationFilter();

  }

  // ngAfterViewInit(): void {

  // }


  resetSearch(): void {
    this.searchQuery = ''; 
    this.searchTerm = '';
    this.items = [];  
  }

  loadProcessType(){
    var _this= this;
    console.log(_this.siteControl.value)
    _this.coreTypeList = _this.commonService.processList.filter(e => e.processType=="Core Principles" && e.refSite.externalId == _this.siteControl.value)
    console.log("coretypelist---->",_this.coreTypeList)
  }
  
cancelClick() {
  this.resetSearch()
  this.isPopupOpen = !this.isPopupOpen;
}

  popupClick() {
    this.isPopupOpen = !this.isPopupOpen;
    if (this.isPopupOpen) {
      this.fetchItems();
    }
  }

  clearFilters() {
    this.unitControl.reset();
    this.behalf.reset();
    this.locationObserve.reset();
    this.coreTypeControl.reset();
    this.projectName.reset();
    this.workOrderNumber.reset();
    this.subCategoryOpt.reset();
    this.categoryOpt.reset();
    this.createdBy.reset();
    this.status.reset();
    this.subProcessControl.reset();
    this.operationalLearningControl.setValue('All');
    this.startDateControl.reset();
    this.endDateControl.reset();
    this.applyObservationFilter();
    if(this.enabledFields && this.enabledFields.length>0){
      this.updateActiveFlags(this.allColumns, this.enabledFields);
    }
    this.dialog.closeAll();
  }

  closeDialog() {
    this.dialog.closeAll();
  }
  applyFilters() {
    // Logic to apply filters based on the selected values
    console.log('Applied filters:', {
      unit: this.unitControl.value,
      behalf: this.behalf.value,
      location: this.locationObserve.value,
      coreType: this.coreTypeControl.value,
      projectName: this.projectName.value,
      workOrderNumber: this.workOrderNumber.value,
      subCategory: this.subCategoryOpt.value,
      category: this.categoryOpt.value,
      createdBy: this.createdBy.value,
      status: this.status.value,
      operationalLearning: this.operationalLearningControl.value,
    });
    if(this.enabledFields && this.enabledFields.length>0){
      this.updateActiveFlags(this.allColumns, this.enabledFields);
    }
    
    this.applyObservationFilter()
    this.dialog.closeAll();
  }

  async onBehalfChange(item: any) {
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.behalfList = [];
    _this.filteredBehalfList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.behalfList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.behalfList.forEach((element)=>{
          element.name =  element.firstName+' '+element.lastName
        })
        _this.filteredBehalfList = _this.behalfList.slice();
      
  
      })
      _this.cd.detectChanges()
      
  }
}

  checklistclick(){
    this.router.navigateByUrl('observations/checklist-metrics');
    console.log('User selected list type:', this.selectedListType);
    this.commonService.setData(this.listType);

  }
  

  selectListType(type: string) {
    this.selectedListType = this.listType;
  }



  fetchItems(): void {
    this.commonService.loaderFlag = true;

    let startD = '';
    let endD = '';
    if (this.startDateControl.value) {
        const myStartDate = new Date(this.startDateControl.value);
        startD = `${myStartDate.getFullYear()}-${("0" + (myStartDate.getMonth() + 1)).slice(-2)}-${("0" + myStartDate.getDate()).slice(-2)}`;
    }
    if (this.endDateControl.value) {
        const myEndDate = new Date(this.endDateControl.value);
        endD = `${myEndDate.getFullYear()}-${("0" + (myEndDate.getMonth() + 1)).slice(-2)}-${("0" + myEndDate.getDate()).slice(-2)}`;
    }

    const requestBody = {
        limit: 1000,
        sites: [this.siteControl.value],
        listType: this.listType ,
        isActive: true,
        startDate: startD,
        endDate: endD
    };

    this.dataService.postData(requestBody, this.dataService.NODE_API + "/api/service/listObservation").subscribe((response: any) => {
        console.log("response---->", response);

        if (response && response.data) {
            const observationList = response['data']['list' + this.commonService.configuration['typeObservation']]['items'];
            console.log("Raw observationList:", observationList);

            const nameExternalIdMap = new Map<string, { count: number, externalIds: string[] }>();
            const projectMap = new Map<string, { name: string, externalIds: string[] }>();
            const workOrderMap = new Map<string, { name: string, externalIds: string[] }>();
            const categoryMap = new Map<string, { name: string, externalId: string[] }>();
            const subCategoryMap = new Map<string, { name: string, externalId: string[] }>();
            const createdByMap = new Map<string, { name: string, externalIds: string[] }>();
            const statusMap = new Map<string, { name: string, externalIds: string[] }>();

            observationList.forEach((item: any) => {
              console.log(item)
              const projectName = item.projectName;
              const workOrderNumber = item.workOrderNumber;
                const displayName = item.observedOnBehalfOf ? item.observedOnBehalfOf.displayName : 'N/A';
                const externalId = item.externalId;
                const createdBy = item.createdBy;
                const status = item.observationStatus;

                if (projectName) {
                  if (!projectMap.has(projectName)) {
                      projectMap.set(projectName, { name: projectName, externalIds: [] });
                  }
                  projectMap.get(projectName)!.externalIds.push(externalId);
              }
      
              if (workOrderNumber) {
                  if (!workOrderMap.has(workOrderNumber)) {
                      workOrderMap.set(workOrderNumber, { name: workOrderNumber, externalIds: [] });
                  }
                  workOrderMap.get(workOrderNumber)!.externalIds.push(externalId);
              }

                      // Handle createdBy data
        if (createdBy) {
          if (!createdByMap.has(createdBy)) {
              createdByMap.set(createdBy, { name: createdBy, externalIds: [] });
          }
          createdByMap.get(createdBy)!.externalIds.push(externalId);
      }

      // Handle status data
      if (status) {
          if (!statusMap.has(status)) {
              statusMap.set(status, { name: status, externalIds: [] });
          }
          statusMap.get(status)!.externalIds.push(externalId);
      }

              // Handle refCategory data
        if (item.refCategory && item.refCategory.items && item.refCategory.items.length > 0) {
          item.refCategory.items.forEach((category: any) => {
              const categoryName = category.name;
              const categoryExternalId = category.externalId;

              if (categoryName) {
                  if (!categoryMap.has(categoryName)) {
                      categoryMap.set(categoryName, { name: categoryName, externalId: categoryExternalId });
                  }
                  // categoryMap.get(categoryName)!.externalIds.push(categoryExternalId);
              }
          });
      }

      // Handle refSubCategory data
      if (item.refOFWAProcess && item.refOFWAProcess.items && item.refOFWAProcess.items.length > 0) {
          item.refOFWAProcess.items.forEach((subCategory: any) => {
              const subCategoryName = subCategory.name;
              console.log(subCategory.name)
              const subCategoryExternalId = subCategory.externalId;

              if (subCategoryName) {
                  if (!subCategoryMap.has(subCategoryName)) {
                      subCategoryMap.set(subCategoryName, { name: subCategoryName, externalId: subCategoryExternalId });
                  }
                  // subCategoryMap.get(subCategoryName)!.externalIds.push(subCategoryExternalId);
              }
          });
      }


                if (!nameExternalIdMap.has(displayName)) {
                    nameExternalIdMap.set(displayName, { count: 0, externalIds: [] });
                }

                const entry = nameExternalIdMap.get(displayName)!;
                entry.externalIds.push(externalId);
                entry.count = entry.externalIds.length; // Update count to reflect all occurrences
            });


    this.projectNameList = Array.from(projectMap.values());
    this.workOrderNumberList = Array.from(workOrderMap.values());
    this.categoryList = Array.from(categoryMap.values());
    this.subCategoryList = Array.from(subCategoryMap.values());
    this.createdByList = Array.from(createdByMap.values());
    this.statusList = Array.from(statusMap.values());
    this.statusList.sort((a, b) => {
      if (a.name === "Safe") return -1;
      if (b.name === "Safe") return 1;
      if (a.name === "Unsafe") return -1;
      if (b.name === "Unsafe") return 1;
      return 0;
  });

    this.filteredprojectNameList = this.projectNameList;
    this.filteredworkOrderNumberList = this.workOrderNumberList;
    this.filteredCategoryList = this.categoryList;
    this.filteredSubCategoryList = this.subCategoryList;
    this.filteredCreatedByList = this.createdByList;
    console.log(this.statusList,this.safeDisplayName, this.notsafeDisplayName);
    if (this.safeDisplayName && this.notsafeDisplayName) {
      this.filteredStatusList = this.statusList.map(status => {
          if (status.name === "Safe") {
              return { ...status, name2: this.safeDisplayName };
          } else if (status.name === "Unsafe") {
              return { ...status, name2: this.notsafeDisplayName };
          }
          return status;
      });
  } else {
    this.filteredStatusList = this.statusList.map(status => {
      console.log(status)
      if (status.name === "Safe") {
          return { ...status, name2: "Safe" };
      } else if (status.name === "Unsafe") {
          return { ...status, name2: "Unsafe" };
      }
      return status;
  });
  }
  

            this.items = Array.from(nameExternalIdMap.entries()).map(([name, { count, externalIds }]) => {
                return { name, count, externalIds };
            });

            // Log detailed information
            this.items.forEach(item => {
                console.log(`DisplayName: ${item.name}, Count: ${item.count}, ExternalIds: ${item.externalIds.join(', ')}`);
            });

          console.log("All items:", this.items);
          this.filterItems();
          this.cd.detectChanges(); 
          this.commonService.loaderFlag = false;
      } else {
          console.error("Response structure is not as expected");
          this.commonService.loaderFlag = false;
      }



        
    });
}
onStatusChange(selectedValue: string): void {
  if (selectedValue === this.safeDisplayName) {
      this.status.setValue("Safe");
  } else if (selectedValue === this.notsafeDisplayName) {
      this.status.setValue("Unsafe");
  } else {
      this.status.setValue(selectedValue);
  }
}


  filterItems(): void {
    console.log("Filtering with searchTerm:", this.searchTerm);
    this.filteredItems = this.items.filter(item => {
      const matches = item.name?.toLowerCase().includes(this.searchTerm.toLowerCase());
      if (matches) {
        console.log("Item matches:", item);
      }
      return matches;
    });
    console.log("Filtered items:", this.filteredItems);
    this.cd.detectChanges();  // Ensure Angular detects changes
  }

  onSearchTermChange(): void {
    console.log("Search term changed:", this.searchTerm);
    this.filterItems();
  }
  
  
  


  sendMailUser(obData){
    var _this =this;
    
    const dialogRef =  _this.dialog.open(ObservationSendMailComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'mail-dialog', data: obData
   });
   dialogRef.afterClosed().subscribe(result => {
   })


  }
  getLogList(objectId){
    var _this =this;
    _this.logList =[]
    _this.dataService.postData({"objectId":objectId}, _this.dataService.NODE_API + "/api/service/listOFWALog")
    .subscribe(data => {
      var listLog = data['data']['list' + _this.commonService.configuration["typeOFWALog"]]['items'];
      var temp = []
      listLog.forEach(element => {
        var obj =    {
          externalId:element.objectExternalId,
          logType:element.logType,
          name:element.refUser?element.refUser.firstName+' '+element.refUser.lastName:'',
          panelOpenState:false,
          createdTime:element.dateTime,
       
        }
        temp.push(obj)
       
      });
      _this.logList = temp
      _this.errorSpinner = true;
      _this.cd.detectChanges();
  
                     
    })

  }
  closeClick(){
    var _this =this;
    _this.showhide = false;
  }
  expandClick() {
    if (this.expandFlag) {
      this.expandFlag = false;
      this.x = 60;
      this.y = 40;

    
    } else {
      this.expandFlag = true;
      this.x = 0;
      this.y = 100;


    }

  }
  getUserMenuConfig(){
    var _this = this
 
    
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
      setTimeout(()=>{
        _this.siteChanged();
      },1000)
    
    }
      if(_this.commonService.menuFeatureUserIn.length>0){
        // var homeMenu = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.homeMenu);
        // if( !homeMenu || (homeMenu && homeMenu.featureAccessLevelCode == "NoAccess")){
        // _this.router.navigateByUrl('noaccess/no-access', {})

        // }
      _this.createOb_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.createObservation);
      _this.createHa_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardCreateAction);
      
        var obView;
        var obEdit;
        var obdelete;
        var ob3Dview;
        var obCreateAction;
        var obHistory;
        var obShare;
     
      if(_this.listType == "Observation"){
        obView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView);
        obEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationEdit);
        obdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationDelete);
        ob3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView3d);
        obCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationCreateAction);
        obHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationHistory);
        obShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationShare);
        
      }else{
        obView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardView);
        obEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardEdit);
        obdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardDelete);
        // var ob3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView3d);
        obCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardCreateAction);
        obHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardHistory);
        obShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationShare);
        
      }
      
      
      var iframe = document.getElementById('iFrameObservationList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ 
        "type": "AuthToken", "user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(),
        "data": _this.tokenService.getToken(),
        "idToken":_this.tokenService.getIDToken(),
        "LanguageCode":_this.commonService.selectedLanguage.toUpperCase(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      }, '*');
      iWindow?.postMessage(
        {
          type: 'Language',
          action: 'Language',
          LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
          idToken: this.tokenService.getIDToken(),
          labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
        },
        '*'
      );
      iWindow?.postMessage({ "type": "ObservationList", "action": "AccessMenu", "data": {obView:obView,obEdit:obEdit,ob3Dview:ob3Dview,obShare:obShare,obCreateAction:obCreateAction, obdelete:obdelete,obHistory:obHistory} }, '*');
        
      }else{
        _this.createOb_obj =  {}
        _this.createHa_obj = {}
        var iframe = document.getElementById('iFrameObservationList');
        if (iframe == null) return;
        var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        iWindow?.postMessage({ 
          "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
          "data": _this.tokenService.getToken(),
          "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
          "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
        }, '*');
        iWindow?.postMessage({ "type": "ObservationList", "action": "AccessMenu", "data": {obView:{},obEdit:{},ob3Dview:{},obShare:{},obCreateAction:{}, obdelete:{},obHistory:{}} }, '*');
    
      }
    
  }
  updateActiveFlags(columns: any[], activeKeys: string[]) {
    var _this=this;
    columns.forEach(column => {
      column.activeFlag = activeKeys.includes(column.key2);
    });
    console.log(columns)
    // Call your function with the updated array    
    _this.setColumn(columns);
  }

  deleteObservation(id:string, sitecode:string) {
    var _this = this
    console.log(_this.observation)

    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {
        title:'createactionAreyousure',
       }
   });
   dialogRef.afterClosed().subscribe(result => {
    console.log('result',result)
      if(result == 'YES'){
        _this.commonService.loaderFlag = true;
        var site = _this.commonService.siteList.find(e => e.externalId == _this.observation["refSite"])
        _this.selectedSite = site;
var softdata={
"type": _this.commonService.configuration["typeObservation"],
      "siteCode": sitecode,
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        {
          "externalId": id,
          "isActive" : false,
        }
      ]
}
console.log(softdata)
  _this.dataService.postData(softdata, _this.dataService.NODE_API + "/api/service/createInstanceByProperties")
  .subscribe(data => {
    // console.log(data)
    this.toastr.success('', _this.translate.instant('TOASTER.OBS_DELETE'), {
      timeOut: 3000,
    });
    this.applyObservationFilter();
    _this.commonService.loaderFlag = false;

  })
        _this.cd.detectChanges();
      }
  });

  

  }
  a:number=0
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    console.log(_this.site)
    if (_this.site && _this.site["reportingLocations"]["items"].length > 0) {
      _this.reportingLocationList = _.orderBy(_this.site["reportingLocations"]["items"], ['description'], ['asc']);
      _this.reportingLocationList = _.uniqBy(_this.reportingLocationList, 'externalId');
      _this.filteredReportingLocationList = _this.reportingLocationList.slice();
      _this.tempLocation =  _this.reportingLocationList.slice()
      console.log('reportingLocationList',_this.reportingLocationList)
    }
    _this.cd.detectChanges();
    if(_this.a==0){
      _this.applyObservationFilter();
    _this.a++
    }
    cb();
  }


  goSupplier() {
    var _this = this;
    //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
    this.toastr.success('', _this.translate.instant('TOASTER.QUALITY_ASSESSMENT'), {
      timeOut: 3000,
    });
  }
  ngAfterViewInit(): void {

    var _this = this;
  
  
    _this.setDateFormat();
      var iframe = document.getElementById('iFrameObservationList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ 
        "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
        "data": _this.tokenService.getToken(),
        "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
        "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
      }, '*');
      iWindow?.postMessage({ "type": "ObservationList", "action": "Column", "data": _this.displayedColumns }, '*');
      _this.getUserMenuConfig()
      // _this.loaderFlag = true;
 

  }

  applyObservationFilter() {
    var _this = this;
    _this.commonService.loaderFlag = true;
    console.log("HIIIII")
    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var startD;
    var endD;
    iWindow?.postMessage({ 
      "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
      "data": _this.tokenService.getToken(),"columnOrder": _this.columnOrder,
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
    }, '*');
    if (_this.endDateControl.value) {
      var myStartDate = new Date(this.startDateControl.value);
      startD = myStartDate.getFullYear() + "-" + (("0" + (myStartDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myStartDate.getDate())).slice(-2));
      var myEndDate = new Date(this.endDateControl.value);
      endD = myEndDate.getFullYear() + "-" + (("0" + (myEndDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myEndDate.getDate())).slice(-2));

    }
    var sites = []
    var units = []
    if (this.siteControl.value) {
      sites = [this.siteControl.value]
    }
    if (this.unitControl.value) {
      units = this.unitControl.value
    }

    iWindow?.postMessage({ "type": "ObservationList", "action": "Filter",
      "projectName": this.projectName?.value,"createdBy": this.createdBy?.value, "status": this.status?.value,
      "workOrderNumber": this.workOrderNumber?.value,"behalf": this.behalf?.value, "safeDisplayName":this.safeDisplayName, "notsafeDisplayName":this.notsafeDisplayName,
      "category":this.categoryOpt?.value,"subCategory":this.subCategoryOpt?.value, "subProcess":this.subProcessControl?.value,  "location": this.locationObserve?.value, "operLearning": _this.OperationalLearning ,"processId": _this.typeList,"listType":_this.listType, "date": { start: startD, end: endD }, sites: sites,units:units, LanguageCode: `${this.commonService.selectedLanguage}`, }, '*');
    _this.getUserMenuConfig();
    _this.cd.detectChanges();
    console.log(this.safeDisplayName,this.notsafeDisplayName)
  }
  ngOnDestroy(): void {

  }

  onSelectOperationalLearning(value: boolean | string) {
    var _this = this
    if (value !== null) {
      console.log(value)
      if (value == "All"){
        _this.OperationalLearning = null
      }
      else{
        _this.OperationalLearning = !!value
      }
      // this.applyObservationFilter();
    }}
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});

    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setColumn(result);
    //   }
    // });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);


  }
  setDateFormat() {
    DYNAMIC_DATE_FORMATS.display.dateInput = this.commonService.dateFormat.customFormat;
    DYNAMIC_DATE_FORMATS.parse.dateInput = this.commonService.dateFormat.customFormat;
  }
  filterInit(fiterType) {
    var _this = this;

    if (fiterType == 'Site') {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.commonService.siteList.slice();
      console.log('_this.siteList', _this.siteList);
      _this.commonService.observeListSubject.next(true);
      _this.setDateFormat();
    }
    if (fiterType == 'Unit') {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.commonService.unitList.slice();
    }
    //  _this.userData = _this.commonService.userInfo
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]['value'] = '';

      _this.filteredSiteList = _this.siteList.slice();
      _this.filteredUnitList = _this.unitList.slice();
     
    }
  }
  unitChanged() {
    var _this = this;

    var selectedUnit = _this.commonService.getSelectedValue(
      _this.unitControl.value
    );
    if (selectedUnit.length > 0) {
      let result = Array.isArray(selectedUnit);
      console.log('selectedUnit', selectedUnit);
      if (result == true) {
        var mySite = [];
        var finalSite = _this.commonService.siteList.filter(function (e) {
          var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
            if (selectedUnit.indexOf(f.externalId) > -1) {
              mySite.push(e['externalId']);
              return true;
            }
          });
          return siteFiltered.length > 0;
        });
        console.log('mySite', mySite);

        _this.filterFlag = 'Unit';
        _this.siteControl.setValue(mySite);
      } else {
        var mySit;

        var finalSite = _this.commonService.siteList.filter(function (e) {
          var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
            if (f.externalId == selectedUnit) {
              console.log('f.externalId', f.externalId);
              mySit = e.externalId;
              return true;
            }
          });
          return siteFiltered.length > 0;
        });
        console.log('mySite', mySite);

        _this.filterFlag = 'Unit';
        _this.siteControl.setValue(mySit);
      }
    } else {
      selectedUnit = _this.unitList.map((eItem) => eItem.externalId);
    }
  }
  siteChanged() {
    var _this = this;
    var selectedSite = _this.commonService.getSelectedValue(
      _this.siteControl.value
    );
    _this.unitList = [];
    _this.filteredUnitList = _this.unitList.slice();

    if (selectedSite.length > 0) {
      var myCountries = [];
      var myRegions = [];
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (e['country']) {
            myCountries.push(e['country']['externalId']);
            if (e['country']['parent'])
              myRegions.push(e['country']['parent']['externalId']);
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
      _this.filterFlag = 'Site';
    } else {
      selectedSite = _this.siteList.map((eItem) => eItem.externalId);
    }

    if (selectedSite.length > 0) {
      _this.commonService.siteList.filter(function (e) {
        if (selectedSite.indexOf(e.externalId) > -1) {
          if (
            e['reportingUnits']['items'] &&
            e['reportingUnits']['items'].length > 0
          ) {
            _this.unitList = _this.unitList.concat(
              _.orderBy(e['reportingUnits']['items'], ['name'], ['asc'])
            );
            _this.unitList = _.uniqBy(_this.unitList, 'externalId');
            _this.filteredUnitList = _this.unitList.slice();
          }
        }
        return selectedSite.indexOf(e.externalId) > -1;
      });
    } else {
      if (_this.siteList.length > 0) {
        _this.unitList = _this.commonService.unitList;
        _this.filteredUnitList = _this.unitList.slice();
      }
    }
  }

  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });

    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     console.log('result', result);
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
      "data": _this.tokenService.getToken(), 
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase() }, '*');
      // 
    iWindow?.postMessage({ "type": "ObservationList", "action": "Summary", "data": summaryCol }, '*');
    _this.getUserMenuConfig();
  }

  excelExport(){
    var _this = this
    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ 
      "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(),
      "data": _this.tokenService.getToken(),
      "labels": _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()],
      "LanguageCode":_this.commonService.selectedLanguage.toUpperCase()
    }, '*');
    iWindow?.postMessage({ "type": "ObservationList", "action": "Excel", "data": {} }, '*');

  }



  private _metafilter(value) {
    const filterValue: any = this._metanormalizeValue(value);
    return this.process.filter((street) => this._metanormalizeValue(street.name).includes(filterValue));
  }

  private _metanormalizeValue(value: any): any {
    return value.toLowerCase().replace(/\s/g, '');
  }

  processSelected(process) {
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {

  }
  categorySelected(cate) {

  }

  // createObservation() {
  //   var _this = this;
  //   console.log("kkkkkkk")
  //   // observations
  //   var processList = _this.commonService.processList.filter(e => {
  //     return e.processType == "Process" && e.name == "Observation" && e.refSite["externalId"] == _this.siteControl.value;
  //   })
  //   _this.router.navigateByUrl('observations/observation', {
  //     state: {
  //       "observation": processList[0],
  //       "pageFrom": "Observation List",
  //       "action": "Edit"
  //     }
  //   });
  // }
  createObservation() {
    var _this = this;
    _this.router.navigate(["observations/observation"]);
  }
}
