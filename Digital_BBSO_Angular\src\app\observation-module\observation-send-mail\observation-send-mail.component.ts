import { ChangeDetectorRef, Component, Inject, OnInit, NgZone } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-observation-send-mail',
  templateUrl: './observation-send-mail.component.html'
})
export class ObservationSendMailComponent implements OnInit {

  attendeeControl: FormControl = new FormControl("");
  externalAttendeeControl: FormControl = new FormControl("");
  attendeeList = [];
  filteredattendeeList = this.attendeeList.slice();
  selectedAttendee: any = [];
  externalAttendee: any = [];
  fl_searchVal: any;
  labels = {};
  observationDetail: any;
  constructor(
    public dialogRef: MatDialogRef<ObservationSendMailComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public router: Router,
    private dataService: DataService,
    private commonService: CommonService,
    private cdRef: ChangeDetectorRef,
    private translate: TranslateService,
    private languageService: LanguageService, public ngZone: NgZone,
    private changeDetector: ChangeDetectorRef
  ) { 
    this.observationDetail = data
    this.labels = {
      'observationSendmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationSendmail'] || 'observationSendmail',
      'commonfilterUsers': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterUsers'] || 'commonfilterUsers',
      'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
      'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
      'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
    }
  }

  ngOnInit(): void {
    var _this =this

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'observationSendmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationSendmail'] || 'observationSendmail',
          'commonfilterUsers': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterUsers'] || 'commonfilterUsers',
          'commonfilterChooseattendee': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseattendee'] || 'commonfilterChooseattendee',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSubmit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSubmit'] || 'buttonSubmit',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

    _this.dataService.postData({}, _this.dataService.NODE_API + "/api/service/listAllUser").
    subscribe((resData: any) => {
      var tempAttend = resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"]
      tempAttend.forEach(element => {
        element.fullName = element.firstName+' '+element.lastName
      });
      _this.attendeeList = tempAttend;
      _this.filteredattendeeList = _this.attendeeList.slice();
    })
    _this.attendeeControl.valueChanges.subscribe(value => {
      console.log('value',value)
       function push(array, item) {
        if (!array.find((ele) => ele.externalId === item.externalId)) {
          array.push(item);
        }
      }
      push(_this.selectedAttendee, value); 
      
    })

  }
  getUserMail(){
  
    var _this = this;
    function validateEmail(email) {
      // Regular expression for validating an email
      const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return re.test(String(email).toLowerCase());
    }
  
  // Example usage
  const email = this.externalAttendeeControl.value;
  if (validateEmail(email)) {
    var obj = {
      externalId:this.externalAttendeeControl.value,
      fullName:this.externalAttendeeControl.value
    }
    const isDuplicate = this.externalAttendee.some(item => item.externalId === obj.externalId);

    if (isDuplicate) {
      this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterDontenterduplicatemail'] });
        
    } else {
      this.externalAttendee.push(obj);
      this.externalAttendeeControl.reset();
       
    }
  
  } else {
    this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterFillvalidemail'] });
 
  }
  
  }
  onEnterPress(event: Event) {
    const keyboardEvent = event as KeyboardEvent;
    if (keyboardEvent.key === 'Enter') {
      this.getUserMail()
    }
  }

  removeSelectedUser(item){
    const index = this.selectedAttendee.findIndex(prop => prop.externalId === item.externalId)
    this.selectedAttendee.splice(index,1)
  }
  removeexternalSelectedUser(item){
    const index = this.externalAttendee.findIndex(prop => prop.externalId === item.externalId)
    this.externalAttendee.splice(index,1)
  }
  async onAttendeeChange(item: any) {
    console.log('item  -->>>>>>>',item)
    var _this = this;
    
   var filter:any = document.getElementsByClassName('mat-filter-input');
   
   if(filter.length > 0){
    _this.fl_searchVal = filter[0]["value"]
    _this.attendeeList = [];
    _this.filteredattendeeList = [];
      _this.dataService.postData({searchUser:_this.fl_searchVal}, _this.dataService.NODE_API + "/api/service/listAllUser").
      subscribe((resData: any) => {
    console.log('resData',resData)
        _this.attendeeList =  resData["data"]["search" + _this.commonService.configuration["typeUser"]]["items"];
        _this.attendeeList.forEach((element)=>{
          element.fullName =  element.firstName+' '+element.lastName
        })
        _this.filteredattendeeList = _this.attendeeList.slice();
      
  
      })
      _this.cdRef.detectChanges()
      
  }
  }

   
  cancelClick(): void {
    this.dialogRef.close('NO');
  }
  submitClick(): void {
  
   var _this =this
 
  //Site, Unit, Location, Date, Craft, Department, Short Description, Describe the Observation, and Urgency.
  if(this.selectedAttendee.length > 0){



    var instanceNotification = [
      {
        application: this.commonService.applicationInfo.name,
        description: 'Observation Details',
        users: [],
        severity: this.observationDetail.urgency?this.observationDetail.urgency:"Medium",
        properties:[
          {
            "name": "Site",
            "value":this.observationDetail.refSite && this.observationDetail.refSite.description?this.observationDetail.refSite.description:"",
            "type": "text"
          },
          {
            "name": "Unit",
            "value":this.observationDetail.refUnit && this.observationDetail.refUnit.description?this.observationDetail.refUnit.description:"",
            "type": "text"
          },
          {
            "name": "Location",
            "value": this.observationDetail.refReportingLocation && this.observationDetail.refReportingLocation.description?this.observationDetail.refReportingLocation.description:"",
            "type": "text"
          },
          {
            "name": "Date",
            "value": this.observationDetail.date?this.observationDetail.date:"",
            "type": "text"
          },
          {
            "name": "Craft",
            "value": this.observationDetail.crafts?this.observationDetail.crafts:"",
            "type": "text"
          },
          {
            "name": "Department",
            "value": this.observationDetail.refDeparment && this.observationDetail.refDeparment.description?this.observationDetail.refDeparment.description:"",
            "type": "text"
          },
          {
            "name": "Short Description",
            "value": this.observationDetail.shortDescription?this.observationDetail.shortDescription:"",
            "type": "text"
          },
          {
            "name": "Describe the Observation",
            "value": this.observationDetail.description?this.observationDetail.description:"",
            "type": "text"
          },
      ]
      
        }
    ];

    this.selectedAttendee.forEach((ele)=>{
      instanceNotification[0].users.push(ele.externalId)
    })
 
   console.log('instanceNotification',instanceNotification)
  
     let notificationType = 'Observation Details';
     _this.commonService.notification(
      instanceNotification,
      notificationType
    );
    
    var logObj = {
      objectExternalId:this.observationDetail.externalId,
      objectType:_this.commonService.configuration["typeObservation"],
      refUser:{
        "space": _this.dataService.userInfo.user.space,
        "externalId": _this.dataService.userInfo.user.externalId
      },
      logType:"Email",
      dateTime:new Date(),
      beforeJSON:this.observationDetail,
      afterJSON:instanceNotification[0]
    }
   
    var mainLog = {
      "type": _this.commonService.configuration["typeOFWALog"],
      "siteCode": _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        logObj
      ]
    }
    _this.dataService.postData(mainLog, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(logdata => {
 
      _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterEmailsent'] });
      _this.dialogRef.close('YES');
    })
  }else{
    _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
  }
  
  }
}
