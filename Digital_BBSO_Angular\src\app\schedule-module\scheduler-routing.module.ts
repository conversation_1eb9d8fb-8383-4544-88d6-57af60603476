import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ScheduleListComponent } from './schedule-list/schedule-list.component';
import { ScheduleTrackerComponent } from './schedule-tracker/schedule-tracker.component';
import { CreateScheduleComponent } from './create-schedule/create-schedule.component';
import { ScheduleDetailsComponent } from './schedule-details/schedule-details.component';


const routes: Routes = [
  
    {
        path: '',
        component: ScheduleListComponent
    },
    {
        path: 'schedule-tracker', component: ScheduleTrackerComponent
    },
    {
        path: 'create-schedule', component: CreateScheduleComponent
    },
    {
      path: 'schedule-details', component: ScheduleDetailsComponent
  },

];

@NgModule({
  imports: [
    RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SchedulerRoutingModule { }