import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';

@Component({
  selector: 'app-config-list',
  templateUrl: './config-list.component.html',
  styleUrls: ['./config-list.component.scss'],
})
export class ConfigListComponent implements OnInit {
  searchControl: FormControl = new FormControl('');
  siteControl: FormControl = new FormControl('');
  siteList = [];
  filteredSiteList = [];

  processControl: FormControl = new FormControl();
  processList = [];
  filteredProcessList = [];

  corePrinciplesControl: FormControl = new FormControl();
  corePrinciplesList = [];
  filteredCorePrinciplesList = [];

  filteredProcessOptions: Observable<any[]>;
  process = [
    {
      id: 1,
      name: 'Observation',
    },
    {
      id: 2,
      name: 'Field Walk',
    },
    {
      id: 3,
      name: 'Audit',
    },
  ];

  displayedColumns: any = [];

  allColumns = [
    {
      key: 'site',
      displayName: 'Site',
      name: 'site',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'corePrinciple',
      displayName: 'Core Principle',
      name: 'corePrinciple',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'process',
      displayName: 'Process',
      name: 'process',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'subProcess',
      displayName: 'Sub Process',
      name: 'subProcess',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'category',
      displayName: 'Category',
      name: 'category',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'subCategory',
      displayName: 'Sub Category',
      name: 'subCategory',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'createdTime',
      displayName: 'Created On',
      name: 'createdTime',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'createdBy',
      displayName: 'Created By',
      name: 'createdBy',
      activeFlag: true,
      summary: false,
    },
    {
      key: 'lastUpdatedTime',
      displayName: 'Updated On',
      name: 'lastUpdatedTime',
      activeFlag: false,
      summary: false,
    },
    {
      key: 'modifiedBy',
      displayName: 'Updated By',
      name: 'modifiedBy',
      activeFlag: false,
      summary: false,
    },
    {
      key: 'actions',
      displayName: 'Actions',
      name: 'actions',
      activeFlag: true,
      summary: false,
    },
  ];
  url: any = '';
  loaderFlag: boolean;
  userAccessMenu: any;
  constructor(
    private router: Router,
    private tokenService: TokenService,
    private dataService: DataService,
    private dialog: MatDialog,
    private commonService: CommonService
  ) {
    console.log(tokenService.getToken());
    this.url = this.dataService.React_API + '/configuration';
    console.log(history.state);
    // this.observeHistory = history.state
  }

  initialDataFlag = 0;
  ngOnInit(): void {
    var _this = this;
    // _this.loaderFlag = true;
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();

      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.loadCorePrinciplesList();
    }
    if (_this.initialDataFlag > 1) {
      setTimeout(function () {
        _this.siteControl.setValue(_this.dataService.siteId);
        _this.applyFilter();
      }, 1000);
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == 'Process') {
          _this.initialDataFlag = _this.initialDataFlag + 1;

          _this.loadCorePrinciplesList();
        }
        if (fiterType == 'userAccess') {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu);
          _this.getUserMenuConfig();
        }
        if (fiterType == 'Site') {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1;
          setTimeout(function () {
            _this.siteControl.setValue(_this.dataService.siteId);
          }, 1000);
        }
      }
    });
    _this.processControl.valueChanges.subscribe((value: any) => {
      _this.applyFilter();
    });
    _this.corePrinciplesControl.valueChanges.subscribe((value: any) => {
      var _this = this;
    var processList = _this.commonService.processList.filter((e) => {
      return (
        e.processType == 'Process' &&
        e.refOFWAProcess.externalId == value
      );
    });
    _this.processList = processList;
    _this.filteredProcessList = _this.processList.slice();
    });
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == 'Configuration') {
        console.log(e.data);
        if (e.data.action == 'FormView') {
          _this.router.navigateByUrl('configuration', {
            state: {},
          });
        } else if (e.data.action == 'FormEdit') {
          _this.router.navigateByUrl('configuration', {
            state: {},
          });
        } else if (e.data.action == 'addQuestion') {
          _this.router.navigateByUrl('configuration/question-list', {
            state: e.data.data,
          });
        }
      }
    };

    _this.siteControl.valueChanges.subscribe((site: any) => {
      _this.dataService.siteId = site;
      _this.commonService.getProcessConfiguration(
        _this.siteControl.value,
        function (data) {
          _this.loadCorePrinciplesList();
          _this.applyFilter();
        }
      );
    });
  }
  getUserMenuConfig() {
    var _this = this;

    if (_this.commonService.menuFeatureUserIn.length > 0) {
      var configurationQuestionList =
        _this.commonService.menuFeatureUserIn.find(
          (item) =>
            item.featureCode ==
            _this.dataService.appMenuCode.configurationQuestionList
        );

      setTimeout(function () {
        var iframe = document.getElementById('iFrameConfigurationList');
        if (iframe == null) return;
        var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        iWindow?.postMessage(
          { type: 'AuthToken', data: _this.tokenService.getToken() },
          '*'
        );
        iWindow?.postMessage(
          {
            type: 'Configuration',
            action: 'Column',
            data: _this.displayedColumns,
          },
          '*'
        );
        iWindow?.postMessage(
          {
            type: 'Configuration',
            action: 'AccessMenu',
            data: { configurationQuestionList: configurationQuestionList },
          },
          '*'
        );
      }, 100);
    } else {
      var iframe = document.getElementById('iFrameConfigurationList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { type: 'AuthToken', data: _this.tokenService.getToken() },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'Configuration',
          action: 'Column',
          data: _this.displayedColumns,
        },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'Configuration',
          action: 'AccessMenu',
          data: { configurationQuestionList: {} },
        },
        '*'
      );
    }
  }

  loadCorePrinciplesList() {
    var _this = this;
    var corePrinciplesList = _this.commonService.processList.filter((e) => {
      return (
        e.processType == 'Core Principles' &&
        e.refSite['externalId'] == _this.siteControl.value
      );
    });
    _this.corePrinciplesList = corePrinciplesList;
    _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
  }

  ngAfterViewInit(): void {}
  ngOnDestroy(): void {}

  applyFilter() {
    var _this = this;
    var iframe = document.getElementById('iFrameConfigurationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var process = [];
    if (_this.processControl.value) {
      process = [_this.processControl.value];
    }
    iWindow?.postMessage(
      { type: 'AuthToken', data: _this.tokenService.getToken() },
      '*'
    );
    iWindow?.postMessage(
      {
        type: 'Configuration',
        action: 'Filter',
        site: [_this.siteControl.value],
        process: process,
        LanguageCode: `${this.commonService.selectedLanguage}`,
      },
      '*'
    );
    setTimeout(function () {
      _this.loaderFlag = false;
      _this.defaultColumn();
    }, 1000);
    _this.getUserMenuConfig();
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result == 'object') {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach((element) => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }
    });

    console.log('_this.displayedColumns,', _this.displayedColumns);
    _this.updateColumn();
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);
    _this.getUserMenuConfig();
  }
  defaultColumn() {
    var _this = this;
    this.displayedColumns = [];
    var i = 0;
    _this.allColumns.forEach((element) => {
      i++;
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }
    });
    _this.updateColumn();
  }
  updateColumn() {
    var _this = this;
    setTimeout(function () {
      var iframe = document.getElementById('iFrameConfigurationList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage(
        { type: 'AuthToken', data: _this.tokenService.getToken() },
        '*'
      );
      iWindow?.postMessage(
        {
          type: 'Configuration',
          action: 'Column',
          data: _this.displayedColumns,
        },
        '*'
      );
      _this.getUserMenuConfig();
    }, 100);
  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) {
        myColumns.push(eData);
      }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: '150px', right: '10px' };
    dialogConfig.height = 'auto';
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe((result) => {
    //   if (typeof result == 'object') {
    //     this.setSummary();
    //   }
    // });
  }
  setSummary() {
    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          enable: 'Y',
          colorRange: eData['summaryColor'] ? eData['summaryColor'] : [],
        };
      }
    });
    console.log('summaryCol', summaryCol);
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameConfigurationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage(
      { type: 'AuthToken', data: _this.tokenService.getToken() },
      '*'
    );
    iWindow?.postMessage(
      { type: 'Configuration', action: 'Summary', data: summaryCol },
      '*'
    );
    _this.getUserMenuConfig();
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]['value'] = '';
      _this.filteredSiteList = _this.siteList.slice();
    }
  }

  private _metanormalizeValue(value: any): any {
    return value.toLowerCase().replace(/\s/g, '');
  }

  goPage(page) {
    this.router.navigate([page]);
  }

  processSelected(process) {
    console.log('process', process);
    if (process == 'Observation') {
    } else {
    }
  }
}
