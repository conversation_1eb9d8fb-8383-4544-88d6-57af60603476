import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { ParentToChildService } from 'src/app/broadcast/parent-to-child.service';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { AddCraftComponent } from '../../configuration-module/add-craft/add-craft.component';
import _ from "lodash";
import { TokenService } from 'src/app/token.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {provide: MAT_DATE_FORMATS, useValue: MAT_MOMENT_DATE_FORMATS},
  ],
})
export class ListComponent implements OnInit {



  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  selectedIndexBinding = 0;
  searchControl: FormControl = new FormControl("");

  siteControl: FormControl = new FormControl("");
  siteList = [];
  filteredSiteList = [];
  labels = {}

  ifSite = false;

  processConfig: any;
  loaderFlag: boolean;
  userAccessMenu: any;
  configurationList: any;

  craftList: any = [];
  craftExternalId: any;
  settingData: any;
  subMenuList: any[];
  selectedTab: string = "Observation";
  constructor(
    private dataService: DataService,
    public dialog: MatDialog,
    private commonService: CommonService,
    private parentchildService: ParentToChildService,
    private router: Router,
    private tokenService: TokenService,
    private _adapter: DateAdapter<any>,
    @Inject(MAT_DATE_LOCALE) private _locale: string,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService,
    private languageService: LanguageService,
    private ngZone: NgZone
  ) {

    this.labels = {
      'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
      'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
      'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
      'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
      'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
      'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
    }

    var _this = this;
    _this.loaderFlag = true;

    
  }

  initialDataFlag = 0;
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'observationlistList': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationlistList'] || 'observationlistList',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'cardsFieldwalks': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsFieldwalks'] || 'cardsFieldwalks',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })

    console.log(_this.tabGroup)
    _this.ifSite = true;
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.loaderFlag = false;
      console.log("LoaderFalse1")
      console.log(_this.commonService.userIntegrationMenu)
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if (_this.userAccessMenu) {
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId)
      _this.ifSite = true;
      _this.loaderFlag = false;
      console.log("LoaderFalse2")
      _this.getDateFormat(_this.dataService.siteId);
    }
    
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Core Principles") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
          

        }
        if (fiterType == "userAccess") {
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>', _this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.loaderFlag = false;
          console.log("LoaderFalse3")
        }
        console.log(_this.initialDataFlag)
        if (_this.initialDataFlag > 0) {
          _this.siteControl.setValue(_this.dataService.siteId);
          _this.getDateFormat(_this.dataService.siteId);
        }
      }
    })


    this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.craftExternalId = undefined;
      _this.ifSite = true;
      _this.getDateFormat(value);
      _this.getUserMenuConfig();
    });

  }
  configList: any = [];
  ngAfterViewInit(): void {
    var _this = this;
    console.log(history.state)
 
   function myFunction() {
      _this.cd.detectChanges();
    }
    setInterval(myFunction, 3000);
  }
  

getDateFormat(site){
  var _this = this;
  _this.dataService.postData({ "sites": site }, _this.dataService.NODE_API + "/api/service/listSetting").subscribe((resData: any) => {
    var listSetting = resData["data"]["list" + _this.commonService.configuration["typeSetting"]]["items"];
    console.log('List component listSetting', listSetting)
    if (listSetting.length > 0) {
      var settingData = listSetting[0];
        var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == settingData["dateFormat"])
        console.log('List component dateFormat', dateFormat)
        _this._locale = dateFormat.local;
        _this.commonService.dateFormat = dateFormat;
        _this._adapter.setLocale(_this._locale);
        _this.cd.detectChanges();
      }else{
      var dateFormat = _this.commonService.dateFormatArray.find(e => e.value == _this.commonService.configuration["dateFormat"])
      if (dateFormat && dateFormat.local) {
        _this.commonService.dateFormat = dateFormat;
        this._locale = dateFormat.local;
      } 
      _this._adapter.setLocale(_this._locale);
      _this.cd.detectChanges();
    }
  });
}

  selectTab(event:MatTabChangeEvent) {
    var _this = this;

    console.log(event)
    _this.selectedTab = _this.subMenuList[event.index];
  }


  getUserMenuConfig() {
    var _this = this

    if (_this.siteControl.value != _this.dataService.siteId) {
      _this.siteControl.setValue(_this.dataService.siteId)
    }
    if (_this.commonService.menuFeatureUserIn.length > 0) {
      _this.configurationList = {}
      _this.configurationList["observation"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observation);
      _this.configurationList["fieldWalk"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalk);
      _this.configurationList["auditMenu"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.auditMenu);
      _this.configurationList["hazards"] = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazards);
      console.log("_this.commonService.userIntegrationMenu")
      var myArray:any = [];
      if(_this.configurationList["observation"]){
        myArray.push("Observation");
      }
      if(_this.configurationList["fieldWalk"]){
        myArray.push("FieldWalk");
      }
      if(_this.configurationList["auditMenu"]){
        myArray.push("Audit");
      }
      if(_this.configurationList["hazards"]){
        myArray.push("Hazards");
      }
      _this.subMenuList = myArray;
     
      console.log("history.state")
      console.log(history.state)
      if(history.state.listType && myArray.findIndex(e => e == history.state.listType)> -1){
        _this.selectedIndexBinding = myArray.findIndex(e => e == history.state.listType)
        _this.selectedTab = _this.subMenuList[myArray.findIndex(e => e == history.state.listType)];
      }else{
        _this.selectedTab =  _this.subMenuList[0];
      }


    } else {
      _this.configurationList = {}
    }

  }

  goPage(page) {
    this.router.navigate([page]);
  }


  formConfig() {
    var _this = this;

    this.router.navigateByUrl('configuration/form-list', {
      state: {}
    });

  }



  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredSiteList = _this.siteList.slice();
    }
  }


}

