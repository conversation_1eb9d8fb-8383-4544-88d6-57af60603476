// export default App;
import React, { useRef, useEffect, useState, useMemo } from "react";
import { Runtime, Inspector } from "@observablehq/runtime";
import notebook from "../assets/.innoart-table/my-table";
import { useSearchParams } from 'react-router-dom';
import { html } from "htl";
import Asset_JSON from '../assets/data/cognite_data.json';
import Popup from "reactjs-popup";
import format from 'date-fns/format'
import axios from "axios";
import { CogniteClient } from "@cognite/sdk";
import { PublicClientApplication } from "@azure/msal-browser";
import Pagination from "./pagination/Pagination";
import * as Constants from '../Constant'

let main;
let limitSet = 10;
let firstPageIndex = 0;
let currentPageNumber = 1;
let listData = [];
let pageInfo = [];
let initFlag;
let allListData = [];
let colSummary = {


};
let displayedColumns = [
  
    "type",
    "description",
    "assignedBy",
    "dueOn",
    "status",
    "actions"

]
var paginationCursor = [];
let site;
let unit;
let search;
let token;
function AlertList() {
  const viewofSelectionRef = useRef();
  const [currentPage, setCurrentPage] = useState(1);
  const [dataCount, setDataCount] = useState(0);
  const [limit, setLimitCount] = useState(10);
  const [id, setId] = React.useState("5");
  window.onmessage = function (e) {
    if (e.data.type && e.data.type == "AuthToken") {
      token = e.data.data;
    }
    if (e.data.type && e.data.type == "FormConfig") {
      if (e.data.action == "Column") {
        displayedColumns = e.data.data;
        colFun();
      } else if (e.data.action == "Filter") {
        site = e.data.site;
        unit = e.data.unit;
        search = e.data.search;
        getData(e.data.site, e.data.unit, e.data.search);
      } else
        if (e.data.action == "Summary") {
          colSummary = e.data.data;
          colFun();
        } else
          if (e.data.action == "PageRows") {

            setCurrentPage(1);
            setLimitCount(parseInt(e.data.data))
            limitSet = parseInt(e.data.data);
            paginationCursor = [];
            getData(e.data.site, e.data.unit, e.data.search);


          }

    }

  };


  const [searchParams, setSearchParams] = useSearchParams();
  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value);
    setId(e.target.value)
    limitSet = e.target.value;
    
    console.log('limitSet',limitSet)
    filterData();
    // setLimit(parseInt(e.data.data))
    // limitSet = parseInt(e.data.data);
    // filterData();
  }
  function action1(x, i) {
    return html`<div style=" display: flex;
    flex-direction: row;align-item-center;">
    <div id="${i}" title="View" style="height:18px;margin-right:8px;cursor: pointer;" onClick=${() => allThreats(i)} >
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>               
    </div>
    <div id="${i}" title="Edit" style="height:18px;margin-right:8px;cursor: pointer;" onClick=${() => dataView(i)} >
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 32 32" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="m28.91 4.417-11 24a1 1 0 0 1-1.907-.334l-.93-11.157-11.156-.93a1 1 0 0 1-.334-1.906l24-11a1 1 0 0 1 1.326 1.326z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>                
      </div>
      </div>
      `
  }

  function dataView(event) {
    window.parent.postMessage({  "type": "FormConfig", "action": "FormEdit", "data": event }, "*");
  }
  function allThreats(event) {
    window.parent.postMessage({ "type": "FormConfig", "action": "FormView", "data": event }, "*");
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine("configuration", {
      columns: displayedColumns,

      header: {
       
    "type":"Type",
    "description":"Description",
    "assignedBy":"Assigned By",
    "dueOn":"Due On",
    "status":"Status",
    "actions":"Actions"
    
      },
      headerSummary: colSummary,
      format: {
        createdAt: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        lastUpdatedTime: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        id: x => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i)
      },

      align: {
        "type":"left",
        "description":"left",
        "assignedBy":"left",
        "dueOn":"left",
        "status":"left",
        "actions":"left"
      },
      rows: 25,
      width: {
        "type":200,
        "description":200,
        "assignedBy":200,
        "dueOn":200,
        "status":200,
        "actions":200
      

      },
      maxWidth: "100vw",
      layout: "auto",
    });
  }



  function pushObj(item) {
    if (!paginationCursor.find(({ id }) => id === item.id)) {
      paginationCursor.push(item);
    }
  }


  async function getData() {

        var  dataSource =  [
            {
              type:"Observation",
              description:"To close the actions that has been raised for Water Pipe Corrosion",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"Overdue",
              actions:''
            },
            {
              type:"Observation",
              description:"Your scheduled observation has not been completed",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"Overdue",
              actions:''
            },
            {
              type:"Observation",
              description:"To close the actions that has been raised for Water Pipe Corrosion",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"In Progress",
              actions:''
            },
            {
              type:"Observation",
              description:"To close the actions that has been raised for Water Pipe Corrosion",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"Overdue",
              actions:''
            },
            {
              type:"Observation",
              description:"To close the actions that has been raised for Water Pipe Corrosion",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"Overdue",
              actions:''
            },
            {
              type:"Observation",
              description:"To close the actions that has been raised for Water Pipe Corrosion",
              assignedBy:"Kristin",
              dueOn:"Mar 22, 2023",
              status:"Overdue",
              actions:''
            }
          ]
          listData = dataSource;
          setCurrentPage(1);
          initFlag = true
          filterData();

  }

  function filterData() {
    console.log('react filterData');
    console.log('listData', listData)
  
    var currentList = [];
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter(obj => {
          return JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !== -1;
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(firstPageIndex, (firstPageIndex + limitSet));
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, (firstPageIndex + limitSet));
      }
  
    }
   
    if (initFlag) {
      main.redefine("data", currentList);
      colFun();
    }
  }

  useEffect(() => {



    const runtime = new Runtime();
    main = runtime.module(notebook, name => {
      if (name === "viewof selection1") return new Inspector(viewofSelectionRef.current);
      if (name === "selection") {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage({ "type": "Assets", "action": "Select", "data": [], "selected": value }, "*");
          },
          // rejected(error) { console.error(error); }
        };
      }
    });
    setDataCount(1);
    colFun();
    getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose();
  }, []);

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit;
    const lastPageIndex = firstPageIndex + limit;
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage]);



  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className="tableBottom">
        <div>
          
        </div>
      <Pagination
        className="pagination-bar"
       // assetsType='assets_cognite'
        currentPage={currentPage}
        totalCount={dataCount}
        pageSize={limit}
        onPageChange={page => setCurrentPage(page)}
      />
      <div className="numberRows">
        <span className="numRowsText">
        Rows per page: &nbsp;
        </span>
       <select onChange={(e) => rowDroDownChange(e)}>
     
        <option >10</option>
        <option >20</option>
        <option >50</option>
        <option >100</option>
       </select>
      </div>


      </div>

    </>
  );
}


export default AlertList;