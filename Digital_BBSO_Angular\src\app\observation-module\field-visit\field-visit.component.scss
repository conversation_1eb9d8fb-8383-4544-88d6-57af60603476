
.subheading-1 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .uploadPhotoBox {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
  }
  
  .image-container {
    position: relative;
    height: 100px;
    width: 100px;
  }
  
  .proofImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .add-photo-container {
    height: 100px;
    width: 100px;
    cursor: pointer;
  }
  
  .add-photo-container img {
    border: 1px dashed #ccc;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 5px;
  }
  
  .remove-btn, .view-btn {
    position: absolute;
    top: 5px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
  
  .remove-btn {
    right: 5px;
    background: red;
    color: white;
  }
  
  #close{
    position: absolute;
    right: -2px;
    font-size: 12px;
    top: 3px;
    
  }
  
  .view-btn {
    right: 30px;
    background: #037f16;
    color: white;
  }
  
  .remove-btn:hover, .view-btn:hover {
    opacity: 1;
  }
  
  .image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.9);
  }
  
  .image-modal.show {
    display: block;
  }
  
  .modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    max-height: 80%;
    object-fit: contain;
  }
  
  .close-modal {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
  }
  
  .uploadPhotoBox.multiple-rows {
    max-height: unset;
  }
  
  
  .image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.9);
    cursor: pointer;
  }
  
  .image-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-content {
    display: block;
    width: auto;
    height: auto;
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    cursor: default;
  }
  
  .close-modal {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
  }
  
  .close-modal:hover {
    color: #bbb;
  }
  .view-btn {
    right: 30px;
    background: #037f16;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .view-btn svg {
    width: 14px;
    height: 14px;
  }
  
  .view-icon path {
    fill: #ffffff; 
  }
  .fullscreen-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(18, 17, 17, 0.9);
  }
  
  .modal-content {
    max-width: 90%;
    max-height: 90%;
  }
  
  .close-modal {
    position: absolute;
    top: 60px;
    right: 35px;
    color: #f7f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    background: none; 
    border: none; 
    padding: 0;   
  }
  
  
  .close-modal:hover {
    color: #f00; 
  }
  
  
  .save-btn {
    margin-left: 10px;
    margin-top: 10px;
  }
  
  
  .selected_border {
    border: 2px solid #3498db; 
    border-radius: 5px; 
    
  }
  
  
  .video-container {
    position: relative;
    display: inline-block;
    margin: 5px;
    width: 100px; /* Adjust width as needed */
    height: 100px; /* Adjust height as needed */
    overflow: hidden;
  }
  
  .proofVideo {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the video fits within the container */
    display: block;
  }
  

  @media (min-width: 1920px) and (max-height: 1000px) {

  
    .uploadPhotoBox {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 10px;
      overflow-y: auto;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
      max-height: 300px;
    }
  
    .proofImage {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
  
    .add-photo-container {
      height: 100px;
      width: 100px;
      cursor: pointer;
    }
  
    .add-photo-container img {
      border: 1px dashed #ccc;
      padding: 10px;
      box-sizing: border-box;
      border-radius: 5px;
    }
  
    .remove-btn, .view-btn {
      position: absolute;
      top: 5px;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      font-size: 12px;
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }
  
  
    .remove-btn .cancel-icon {
      fill: white; 
    }
  
    .remove-btn {
      right: 5px;
      background: red;
      color: white;
    }
  
    .view-btn {
      right: 30px;
      background: #037f16;
      color: white;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .view-btn svg {
      width: 14px;
      height: 14px;
    }
  
    .view-btn svg path {
      fill: #ffffff;
    }
  
    .remove-btn:hover, .view-btn:hover {
      opacity: 1;
    }
  
    .image-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0,0,0,0.9);
      cursor: pointer;
    }
  
    .image-modal.show {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .modal-content {
      display: block;
      width: auto;
      height: auto;
      max-width: 90%;
      max-height: 90%;
      object-fit: contain;
      cursor: default;
    }
  
    .close-modal {
      position: absolute;
      top: 60px;
      right: 35px;
      color: #f1f1f1;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      z-index: 1001;
    }
  
    .close-modal:hover {
      color: #bbb;
    }
  
    .close-modal-inside {
      position: absolute;
      top: 15px;
      right: 35px;
      color: #f1f1f1;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      z-index: 1001;
    }
  
    .close-modal-inside:hover {
      color: #bbb;
    }
  }
  
  //This media query is for portrait mode screens in tablet 
  
  @media (min-width: 1000px) and (max-width: 1920px) and (orientation: portrait) {
    
    .marginTop .uploadPhotoBox {  
      width: 80%;
      margin: 0 auto;
      margin-top: 10px;
    }
    .proofImage {
      width: 150px; 
      height: auto; 
    }
  
    .uploadPhotoBox {
      grid-template-columns: repeat(auto-fill, minmax(130px, 1fr)); 
      gap: 15px;
    }
  
    .add-photo-container { 
      width: 100px; 
      height: 100px; 
    }
  
    .add-photo-container img {
      width: 100%; 
      height: 100%; 
      object-fit: contain; 
    }
  
  
    .remove-btn {
      right: -40px;
      background: red;
      color: white;
    }
  
    .view-btn {
      right: -20px;
      background: #037f16;
      color: white;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
   
  }