<div *ngIf="loaderFlag" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<form [formGroup]="summaryForm">
    <div fxLayout="row" class="overflow-section">
        <div fxFlex="100">
            <div class="audit-plan-unit-section schedular-tracker-section"  style="float: right;">
               
                <div class="audit-head-icon">
                    <div class="icon-bg-box schedular-tracker-back-icon" (click)="goAudit()">
                        <mat-icon>arrow_back_ios</mat-icon>
                    </div>
                </div>
            </div>
            

            <div class="action-section-unit-section">
                <commom-label labelText="{{ labels['auditSummary'] }}" [tagName]="'h4'"
                    [cstClassName]="'Audit Summary'"></commom-label>
            </div>

            <div class="summary-section mt-20">
                <div class="marginBottom">
                    <span class="subheading-1 bold observe-color">
                        {{ labels['tableheaderGeneralinformation'] }}
                    </span>
                </div>

                <div *ngIf="vendorInfo" fxLayout="row" fxLayout.sm="column" fxLayout.xs="column" fxLayout.md="column"
                    fxLayoutGap="30px">
                    <div fxFlex="50">
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingCompanyname'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{vendorInfo.address.name}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingHqstreetaddress'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{vendorInfo.address.streetAddress1}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingCity'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{vendorInfo.address.city}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingState'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ audits.refGeoRegion.name }}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingPostalcode'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{vendorInfo.address.postalCode}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingCountry'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ vendorInfo.address?.country?.name ? vendorInfo.address.country.name : "" }}

                                </span>
                            </div>

                        </div>

                    </div>
                    <div fxFlex="50">
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingTelephone'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.mobileNumber}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingMaincontactname'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.contactName}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingMobile'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{vendorInfo.address.PhoneNumber}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingJobtitle'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.jobTitle}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingEmail'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.refOFWASchedule.email}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingCorporatewebsite']}}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.corporateWebSite}}
                                </span>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="marginBottom marginTop">
                    <span class="subheading-1 bold observe-color">
                        {{ labels['tableheaderProductrelatedinformation'] }}
                    </span>
                </div>

                <div fxLayout="row" fxLayout.sm="column" fxLayout.xs="column" fxLayout.md="column" fxLayoutGap="30px"
                    *ngIf="productInfo && vendorInfo">
                    <div fxFlex="50">
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingProductfamily'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <span class="caption" >
                                {{productInfo.productFamily}}
                            </span>
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="productFamily" class="input" placeholder=""
                                        matInput>
                                </mat-form-field>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingLocationmanufacturingsite'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <span class="caption" >
                                {{productInfo.locationManufacuringSite}}
                            </span>
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="locationManufacturingSite" class="input"
                                        placeholder="" matInput>
                                </mat-form-field>
                            </div>

                        </div>
                        
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingState'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ audits.refGeoRegion.name }}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingCountry'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ vendorInfo.address ? vendorInfo.address.country?.name : '' }}
                                </span>
                            </div>

                        </div>

                    </div>
                    <div fxFlex="50">
                        <!-- <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingProduct'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="product" class="input" placeholder="" matInput>
                                </mat-form-field>
                            </div>

                        </div> -->
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingProduct'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.product}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingMaincontactname'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.contactName}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingTelephone'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.mobileNumber}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingJobtitle'] }}
                                </span>
                            </div>
                            <div class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{audits.jobTitle}}
                                </span>
                            </div>

                        </div>
                    </div>

                </div>


                <div class="marginBottom marginTop">
                    <span class="subheading-1 bold observe-color">
                        {{ labels['tableheaderAuditrelatedinformation'] }}
                    </span>
                </div>

                <div fxLayout="row" fxLayoutGap="30px" *ngIf="auditInfo">
                    <div fxFlex="100">
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingAuditreason'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <!-- <span class="caption">
                                    {{auditInfo.auditReason}}
                                </span> -->
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="reason" class="input" placeholder="" matInput>
                                </mat-form-field>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingAuditdate'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10 " fxLayoutAlign="start center">
                                <span class="caption">
                                    {{auditInfo.auditDate | date: 'MM/dd/yyyy'}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingAuditor'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{auditInfo.auditor}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingDepartment'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <!-- <span class="caption">
                                    {{auditInfo.participants}}
                                </span> -->
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="participant" class="input" placeholder=""
                                        matInput>
                                </mat-form-field>
                            </div>

                        </div>


                    </div>


                </div>

                <div class="marginBottom marginTop">
                    <span class="subheading-1 bold observe-color">
                        {{ labels['auditSummary']?.toUpperCase()}}
                    </span>
                </div>

                <div fxLayout="row" fxLayoutGap="30px" *ngIf="auditSummary">
                    <div fxFlex="100">

                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingConclusion'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <!-- <span class="caption" >
                          {{auditSummary.consulation}}
                            </span> -->
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="conclusion" class="input" placeholder=""
                                        matInput>
                                </mat-form-field>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingPathforward'] }}
                                </span>
                            </div>
                            <div fxFlex="80" class="blueLightBoxSummary paddingLeft-10 background-none" fxLayoutAlign="start center">
                                <!-- <span class="caption" >
                                {{auditSummary.pathForward}}
                           </span> -->
                                <mat-form-field appearance="outline" class="width100">
                                    <input type="text" formControlName="pathForward" class="input" placeholder=""
                                        matInput>
                                </mat-form-field>
                            </div>

                        </div>


                    </div>


                </div>

                <div class="marginBottom marginTop">
                    <span class="subheading-1 bold observe-color">
                        {{ labels['tableheaderAuditresult'] }}
                    </span>
                </div>

                <div fxLayout="row" fxLayoutGap="30px" *ngIf="auditResult">
                    <div fxFlex="100">
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingNeedimprovement']}}
                                </span>
                            </div>
                            <div fxFlex="20" class="BoxSummaryRose paddingLeft-10" fxLayoutAlign="center center">
                                <span class="caption bold">
                                    {{auditResult.needImprovement}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingAcceptable']}}
                                </span>
                            </div>
                            <div fxFlex="20" class="BoxSummaryYellow paddingLeft-10" fxLayoutAlign="center center">
                                <span class="caption bold">
                                    {{auditResult.acceptable}}
                                </span>
                            </div>

                        </div>
                        <div fxLayout="row" class="marginTop-5" fxLayoutGap="7px">
                            <div fxFlex="20" class="listHeadBoxSummary paddingLeft-10" fxLayoutAlign="start center">
                                <span class="caption">
                                    {{ labels['tableheadingExcellent'] }}
                                </span>
                            </div>
                            <div fxFlex="20" class="BoxSummaryGreen paddingLeft-10" fxLayoutAlign="center center">
                                <span class="caption bold">
                                    {{auditResult.excellent}}
                                </span>
                            </div>

                        </div>

                    </div>


                </div>

                <div fxLayout="row wrap" fxLayoutGap="100px">
                    <div>
                        <div class="marginBottom marginTop">
                            <span class="subheading-1 regular">{{ labels['signature'] }}</span>

                        </div>
                        <div>
                            <mat-form-field appearance="outline" style="width: 273px;height: 90px;">
                                <textarea matInput formControlName="auditorSignature" cdkTextareaAutosize
                                    cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                            </mat-form-field>

                        </div>
                        <div class="marginBottom marginTop">

                            <span class="subheading-1 regular observe-color">{{
                                labels['formcontrolsLeadauditor'] }}</span>

                        </div>
                    </div>
                    <div>
                        <div class="marginBottom marginTop">
                            <span class="subheading-1 regular">{{ labels['signature'] }}</span>

                        </div>
                        <div>
                            <mat-form-field appearance="outline" style="width: 273px;height: 90px;">

                                <textarea matInput formControlName="managerSignature" cdkTextareaAutosize
                                    cdkAutosizeMinRows="4" cdkAutosizeMaxRows="5"></textarea>
                            </mat-form-field>

                        </div>
                        <div class="marginBottom marginTop">

                            <span class="subheading-1 regular  observe-color">{{
                                labels['formcontrolsManagerorganization'] }}</span>

                        </div>
                    </div>

                </div>

                <div class="btn-section marginTop-20">
                    <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                        (buttonAction)="submit()"></common-lib-button>
                </div>
            </div>
        </div>
    </div>
</form>
