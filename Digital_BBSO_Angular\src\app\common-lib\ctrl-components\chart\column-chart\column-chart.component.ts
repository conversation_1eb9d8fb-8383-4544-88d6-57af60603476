
import { ViewportRuler } from "@angular/cdk/overlay";
import { ChangeDetectionStrategy, Component, Input, OnInit, ViewChild } from "@angular/core";
import {
  ApexAxisChartSeries, ApexChart, ApexDataLabels, ApexFill, ApexLegend,
  ApexPlotOptions, ApexStroke, ApexTooltip, ApexXAxis, ApexYAxis, ChartComponent
} from "ng-apexcharts";



@Component({
  selector: 'common-column-chart',
  templateUrl: './column-chart.component.html',
  styleUrls: ['./column-chart.component.scss'],
  changeDetection:ChangeDetectionStrategy.OnPush,
})

export class ColumnChartComponent implements OnInit {
  @Input() series: ApexAxisChartSeries = [];
  @Input() plotOptions: ApexPlotOptions;
  @Input() dataLabels: ApexDataLabels;
  @Input() chartOption: ApexChart;
  @Input() stroke: ApexStroke;
  @Input() xaxis: ApexXAxis;
  @Input() yaxis: ApexYAxis;
  @Input() fill: ApexFill;
  @Input() tooltip: ApexTooltip;
  @Input() legend: ApexLegend;
  docReact: any;
  constructor(private viewportRuler: ViewportRuler) {
    this.docReact = this.viewportRuler.getViewportRect();

  }

  ngOnInit(): void {
    this.chartOption.height = this.chartOption.height && this.docReact.height >= 900 ? this.chartOption.height : this.docReact.height <= 750 ? 300 : 300;
  }



}