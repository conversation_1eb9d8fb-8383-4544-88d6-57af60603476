// export default App;
import React, { useRef, useEffect, useState, useMemo } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let allListData = []
let initFlag
let colSummary = {}
let displayedColumns = [
  'name',
  'role',
  'jan',
  'feb',
  'mar',
  'apr',
  'may',
  'jun',
  'jul',
  'aug',
  'sep',
  'nov',
  'dec',
  'total',
  'complete',
  'point',
]
var paginationCursor = []
let site
let unit
let search
let token
let userAccessMenu;
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
function ScheduleList() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')
  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value

    console.log('limitSet', limitSet)
    filterData()
    // setLimit(parseInt(e.data.data))
    // limitSet = parseInt(e.data.data);
    // filterData();
  }
  

  const [searchParams, setSearchParams] = useSearchParams()

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })
    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        token = e.data.data
      }
      if (e.data.type && e.data.type == 'FormConfig') {
        if (e.data.action == 'Column') {
          displayedColumns = e.data.data
          colFun()
        } else if (e.data.action == 'Filter') {
          console.log('Schedule Filter', e.data)
          site = e.data.site
          unit = e.data.unit
          search = e.data.search

          setting()
          getData(e.data.site, e.data.unit, e.data.search)
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          colFun()
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu scheduleList', userAccessMenu)
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData(e.data.site, e.data.unit, e.data.search)
        }
      }
    }
    setDataCount(1)
    colFun()
    // getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose()
  }, [])

  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    return html`<div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
      ${userAccessMenu &&
      userAccessMenu.ScheduleView &&
      userAccessMenu.ScheduleView.featureAccessLevelCode
        ? html`${userAccessMenu.ScheduleView.featureAccessLevelCode ==
          'ViewAccess'
            ? html`<div
                id="${i}"
                title="View"
                style="height:18px;margin-right:8px;cursor: pointer;"
                onClick=${() => allThreats(cInd)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="20"
                  height="20"
                  x="0"
                  y="0"
                  viewBox="0 0 488.85 488.85"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                  class=""
                >
                  <g>
                    <path
                      d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
      ${userAccessMenu &&
      userAccessMenu.ScheduleEdit &&
      userAccessMenu.ScheduleEdit.featureAccessLevelCode
        ? html`${userAccessMenu.ScheduleEdit.featureAccessLevelCode ==
          'EditAccess'
            ? html` <div
                id="${i}"
                title="Edit"
                style="height:18px;margin-right:8px;cursor: pointer;"
                onClick=${() => dataView(cInd)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
    </div> `
  }

  function dataView(event) {
    window.parent.postMessage(
      { type: 'FormConfig', action: 'FormEdit', data: event },
      '*'
    )
  }
  function allThreats(event) {
    window.parent.postMessage(
      { type: 'FormConfig', action: 'FormView', data: event },
      '*'
    )
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine('configuration', {
      columns: displayedColumns,

      header: {
        name: 'Name',
        role: 'Role',
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'May',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        nov: 'Nov',
        dec: 'Dec',
        total: 'Total',
        complete: 'Complete',
        point: 'Point',
      },
      headerSummary: colSummary,
      format: {
        createdAt: (x) => format(new Date(x),  dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x),  dateFormat+" "+timeFormat),
        id: (x) => {
          return x.toString()
        },
        actions: (x, i) =>  {
          console.log(x)
          if(x.status == "Disabled"){
            return html``;
          }else{
            return action1(x, i);
          }
        },
      },

      align: {
        name: 'left',
        role: 'left',
        jan: 'right',
        feb: 'right',
        mar: 'right',
        apr: 'right',
        may: 'right',
        jun: 'right',
        jul: 'right',
        aug: 'right',
        sep: 'right',
        nov: 'right',
        dec: 'right',
        total: 'right',
        complete: 'right',
        point: 'right',
      },
      rows: 25,
      width: {
        name: 200,
        role: 200,
        jan: 200,
        feb: 200,
        mar: 200,
        apr: 200,
        may: 200,
        jun: 200,
        jul: 200,
        aug: 200,
        sep: 200,
        nov: 200,
        dec: 200,
        total: 200,
        complete: 200,
        point: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  function pushObj(item) {
    if (!paginationCursor.find(({ id }) => id === item.id)) {
      paginationCursor.push(item)
    }
  }

  async function getData() {
    var dataSource = [
      {
        name: 'Albert',
        role: 'Craftsperson',
        jan: '2',
        feb: '2',
        mar: '2',
        apr: '2',
        may: '2',
        jun: '2',
        jul: '2',
        aug: '2',
        sep: '2',
        nov: '2',
        dec: '2',
        total: '24',
        complete: '2',
        point: '100',
      },
      {
        name: 'James',
        role: 'Unit Leader',
        jan: '3',
        feb: '3',
        mar: '3',
        apr: '3',
        may: '3',
        jun: '3',
        jul: '3',
        aug: '3',
        sep: '3',
        nov: '3',
        dec: '3',
        total: '36',
        complete: '3',
        point: '150',
      },
      {
        name: 'Michael',
        role: 'Daily Execution Leader',
        jan: '1',
        feb: '1',
        mar: '1',
        apr: '1',
        may: '1',
        jun: '1',
        jul: '1',
        aug: '1',
        sep: '1',
        nov: '1',
        dec: '1',
        total: '12',
        complete: '1',
        point: '50',
      },
      {
        name: 'Richard',
        role: 'Craftsperson',
        jan: '2',
        feb: '2',
        mar: '2',
        apr: '2',
        may: '2',
        jun: '2',
        jul: '2',
        aug: '2',
        sep: '2',
        nov: '2',
        dec: '2',
        total: '24',
        complete: '2',
        point: '100',
      },
      {
        name: 'Charles',
        role: 'Daily Execution Leader',
        jan: '4',
        feb: '4',
        mar: '4',
        apr: '4',
        may: '4',
        jun: '4',
        jul: '4',
        aug: '4',
        sep: '4',
        nov: '4',
        dec: '4',
        total: '48',
        complete: '4',
        point: '200',
      },
    ]
    listData = dataSource
    setCurrentPage(1)
    initFlag = true
    filterData()
  }

  function filterData() {
    console.log('react filterData')
    console.log('listData', listData)

    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }

function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }
  
  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
         // assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>Rows per page: &nbsp;</span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
            <option>100</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default ScheduleList
