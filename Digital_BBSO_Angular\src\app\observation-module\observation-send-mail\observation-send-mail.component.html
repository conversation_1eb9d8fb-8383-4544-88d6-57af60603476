<div class="confirmation-dialog-section">
 
        <div class="behaviour-checklist-section">
            <commom-label  labelText="{{ labels['observationSendmail'] }}" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
        




            <div fxLayout="column" fxLayout.md="column" fxLayout.sm="column" fxLayout.xs="column">
                <div class="marginTop" fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start end"  >
                    <div >
                        <div>
                            <span class="subheading-1 regular">
                                {{ labels['commonfilterUsers'] }}
                            </span>
                            <!-- <span class="start-color" >
                                &nbsp; *
                            </span> -->
                        </div>
                        <div>

                            <mat-form-field appearance="outline" class="behav-tree-select">
                                <mat-select placeholder="{{ labels['commonfilterChooseattendee']}}"  [formControl]="attendeeControl" disableOptionCentering>
                                    <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] " [placeholder]="labels['commonfilterSearch']" [displayMember]="'fullName'"
                                        [array]="attendeeList"
                                        (filteredReturn)="onAttendeeChange($event)"
                                        ></mat-select-filter>
                                    <mat-option *ngFor="let item of filteredattendeeList" [value]="item">
                                        {{item.fullName}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>

                    </div>
                    <div *ngFor="let item of selectedAttendee">
                        <div class="chip" >
                            <span class="chip-name">{{item.fullName}}</span>
                            <span class="chip-close" (click)="removeSelectedUser(item)">×</span>
                        </div>

                    </div>
                   


                </div>
                <!-- <div class="marginTop" fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start end"  >
                    <div >
                        <div>
                            <span class="subheading-1 regular">
                                {{ 'External E-mail' | translate }}
                            </span>
                          
                        </div>
                        <div fxLayout="row"  fxLayoutAlign="start center" >
                            <mat-form-field appearance="outline" style="width: 300px;" class="behav-tree-select">
                                <input type="text" class="input"  [formControl]="externalAttendeeControl" placeholder="" (keydown.enter)="onEnterPress($event)"
                                    aria-span="short description" matInput>
                            </mat-form-field>
                            <div>
                                <div (click)="getUserMail()">
                                    <mat-icon class="userMailIcon" >add</mat-icon>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div *ngFor="let item of externalAttendee">
                        <div class="chip" >
                            <span class="chip-name">{{item.fullName}}</span>
                            <span class="chip-close" (click)="removeexternalSelectedUser(item)">×</span>
                        </div>

                    </div>
                   


                </div> -->

            </div>
          
        </div>
 
    <div class="confirmation-fotter marginTop">
        <br>
        <br>
        <div class="behaviour-checklist-fotter" fxLayout="row" >
            <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                (buttonAction)="cancelClick()"></common-lib-button>
         
            <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSubmit'] }}"
                (buttonAction)="submitClick()"></common-lib-button>
        </div>
    </div>
</div>
