// export default App;
import React, { useRef, useEffect, useState, useMemo } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import { translate } from '@celanese/celanese-sdk'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let pageInfo = []
let initFlag
let allListData = []
let colSummary = {}

let displayedColumns = [
  'site',
  'corePrinciple',
  'process',
  'subProcess',
  'category',
  'subCategory',
  'createdTime',
  'createdBy',
  'lastUpdatedTime',
  'modifiedBy',
  'actions',
]

let headersKeyVal = {
  site: 'Site',
  corePrinciple: 'Core Principle',
  process: 'Process',
  subProcess: 'Sub Process',
  category: 'Category',
  subCategory: 'Sub Category',
  createdTime: 'Created On',
  createdBy: 'Created By',
  lastUpdatedTime: 'Updated On',
  modifiedBy: 'Updated By',
  actions: 'Actions',
}

var paginationCursor = []
let site
let process
let search
let token
let userAccessMenu
function Configuration() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')

  const [selectedLanguage, setSelectedLanguage] = useState('en')

  const [t, i18n] = useTranslation('global')

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    i18n.changeLanguage(newValue)
    console.log('Selected Language: ', selectedLanguage)
    console.log('i18n.language: ', i18n)
  }

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
        }
      }
    })
    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        token = e.data.data
      }
      if (e.data.type && e.data.type == 'Configuration') {
        if (e.data.action == 'Column') {
          displayedColumns = e.data.data
          colFun()
        } else if (e.data.action == 'Filter') {
          console.log('filter language: ', e.data)
          site = e.data.site
          process = e.data.process
          // language
          handleLanguageChange(e.data.LanguageCode)
          headersKeyVal = {
            site: t('CONFIG_LIST.SITE'),
            process: t('CONFIG_LIST.PROCESS'),
            corePrinciple: t('CONFIG_LIST.CORE_PRINCIPLE'),
            subProcess: t('CONFIG_LIST.SUB_PROCESS'),
            category: t('CONFIG_LIST.CATEGORY'),
            subCategory: t('CONFIG_LIST.SUB_CATEGORY'),
            createdTime: t('CONFIG_LIST.CREATED_ON'),
            createdBy: t('CONFIG_LIST.CREATED_BY'),
            lastUpdatedTime: t('CONFIG_LIST.UPDATED_ON'),
            modifiedBy: t('CONFIG_LIST.UPDATED_BY'),
            actions: t('CONFIG_LIST.ACTIONS'),
          }
          console.log('OBSList headersKeyVal', headersKeyVal)
          colFun()

          getData()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          colFun()
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu scheduleList', userAccessMenu)
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        console.log('Language', e.data)
        handleLanguageChange(e.data.LanguageCode)

        headersKeyVal = {
          site: t('CONFIG_LIST.SITE'),
          process: t('CONFIG_LIST.PROCESS'),
          corePrinciple: t('CONFIG_LIST.CORE_PRINCIPLE'),
          subProcess: t('CONFIG_LIST.SUB_PROCESS'),
          category: t('CONFIG_LIST.CATEGORY'),
          subCategory: t('CONFIG_LIST.SUB_CATEGORY'),
          createdTime: t('CONFIG_LIST.CREATED_ON'),
          createdBy: t('CONFIG_LIST.CREATED_BY'),
          lastUpdatedTime: t('CONFIG_LIST.UPDATED_ON'),
          modifiedBy: t('CONFIG_LIST.UPDATED_BY'),
          actions: t('CONFIG_LIST.ACTIONS'),
        }

        console.log('OBSLIst headersKeyVal', headersKeyVal)
        colFun()
        getData()
      }
    }
    setDataCount(1)
    colFun()
    return () => runtime.dispose()
  }, [])

  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value

    console.log('limitSet', limitSet)
    filterData()
    // setLimit(parseInt(e.data.data))
    // limitSet = parseInt(e.data.data);
    // filterData();
  }

  const [searchParams, setSearchParams] = useSearchParams()

  {
    /* <div  title="View" style="height:18px;margin-right:18px;cursor: pointer;" >
    <svg  id="${i}"  onClick=${questionList}  xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>               
    </div>
    <div title="Edit" style="height:18px;margin-right:18px;cursor: pointer;" >
      <svg  id="${i}"  onClick=${questionList}  xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"  width="15" height="15" x="0" y="0" viewBox="0 0 348.882 348.882" style="enable-background:new 0 0 512 512" xml:space="preserve"><g><path d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z" fill="#1A2254" data-original="#000000" class=""></path><path d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>
                       
      </div> */
  }

  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)

    return html`<div
      style=" display: flex;
    flex-direction: row;"
    >
      ${userAccessMenu &&
      userAccessMenu.configurationQuestionList &&
      userAccessMenu.configurationQuestionList.featureAccessLevelCode
        ? html`${userAccessMenu.configurationQuestionList
            .featureAccessLevelCode == 'ViewAccess' ||
          userAccessMenu.configurationQuestionList.featureAccessLevelCode ==
            'EditAccess'
            ? html`<div
                title=${t('ICONS.QUESTION')}
                style="height:18px;margin-right:18px;cursor: pointer;"
              >
                <svg
                  id="${i}"
                  onClick=${() => questionList(cInd)}
                  xmlns="http://www.w3.org/2000/svg"
                  width="13"
                  height="13"
                  viewBox="0 0 12 12.003"
                >
                  <path
                    id="Icon_awesome-wpforms"
                    data-name="Icon awesome-wpforms"
                    d="M12,3.407V13.1a1.144,1.144,0,0,1-1.157,1.157H1.157A1.148,1.148,0,0,1,0,13.093V3.407A1.144,1.144,0,0,1,1.157,2.25h9.688A1.143,1.143,0,0,1,12,3.407Zm-1,9.686V3.407a.159.159,0,0,0-.155-.155H10.6l-2.954,2L6,3.913,4.361,5.25l-2.954-2H1.157A.159.159,0,0,0,1,3.4v9.688a.159.159,0,0,0,.155.155h9.688A.155.155,0,0,0,11,13.093ZM4.023,6.375v.991H2.054V6.375Zm0,1.993v1H2.054v-1Zm.3-3.946L5.767,3.252H2.593L4.321,4.422ZM9.946,6.375v.991H4.7V6.375Zm0,1.993v1H4.7v-1ZM7.679,4.422,9.407,3.252H6.236L7.679,4.422Zm2.266,5.944v1H7.283v-1H9.946Z"
                    transform="translate(0 -2.25)"
                    fill="#1A2254"
                  />
                </svg>
              </div>`
            : ``}`
        : ``}
    </div> `
  }

  function dataView(event) {
    window.parent.postMessage(
      { type: 'Configuration', action: 'FormEdit', data: event },
      '*'
    )
  }
  function allThreats(event) {
    window.parent.postMessage(
      { type: 'Configuration', action: 'FormView', data: event },
      '*'
    )
  }

  function questionList(event) {
    console.log(event)
    window.parent.postMessage(
      {
        type: 'Configuration',
        action: 'addQuestion',
        data: listData[parseInt(event)],
      },
      '*'
    )
  }

  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine('configuration', {
      columns: displayedColumns,

      header: headersKeyVal,
      headerSummary: colSummary,
      format: {
        createdTime: (x) => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        lastUpdatedTime: (x) => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        id: (x) => {
          return x.toString()
        },
        actions: (x, i) => action1(x, i),
      },

      align: {
        site: 'left',
        process: 'left',
        corePrinciple: 'left',
        subProcess: 'left',
        category: 'left',
        subCategory: 'left',
        createdTime: 'left',
        createdBy: 'left',
        lastUpdatedTime: 'left',
        modifiedBy: 'left',
        actions: 'left',
      },
      rows: 25,
      width: {
        site: 200,
        process: 200,
        corePrinciple: 200,
        subProcess: 200,
        category: 200,
        subCategory: 200,
        createdOn: 200,
        createdBy: 200,
        updatedOn: 200,
        updatedBy: 200,
        actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  async function getData() {
    fetch(Constants.NODE_API + '/api/service/listSubCategory', {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + token,
        Accept: 'application/json',
        'Content-type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify({
        sites: site,
        process: process,
      }),
    })
      .then((res) => res.json())
      .then((result) => {
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   console.log('temp before', temp[1].childNodes[0].nodeValue)
        //   temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //   console.log('temp after', temp[1].childNodes[0].nodeValue)
        // }
        var listProcess =
          result['data']['list' + Constants.typeProcess]['items']
        listData = []
        listProcess.forEach((element) => {
          element['actions'] = ''
          element['site'] = element['refSite']
            ? element['refSite']['description']
            : ''
          element['subCategory'] = element['name']
          if (element['refOFWAProcess']) {
            var category = element['refOFWAProcess']
            element['category'] = category['name']
            if (category['refOFWAProcess']) {
              var subProcess = category['refOFWAProcess']
              element['subProcess'] = subProcess['name']
              if (subProcess['refOFWAProcess']) {
                var process = subProcess['refOFWAProcess']
                element['process'] = process['name']
                if (process['refOFWAProcess']) {
                  var corePrinciple = process['refOFWAProcess']
                  element['corePrinciple'] = corePrinciple['name']
                }
              }
            }
          }
          listData.push(element)
        })

        setCurrentPage(1)
        initFlag = true
        filterData()
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  function filterData() {
    console.log('react filterData')
    console.log('listData', listData)

    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      colFun()
    }
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
      <div ref={viewofSelectionRef} />
      <div className='tableBottom'>
        <div></div>
        <Pagination
          className='pagination-bar'
        //  assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
            <option>100</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default Configuration
