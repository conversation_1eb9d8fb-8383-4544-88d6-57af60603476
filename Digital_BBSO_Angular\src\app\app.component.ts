import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';

import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { MsalService, MsalBroadcastService, MSAL_GUARD_CONFIG, MsalGuardConfiguration } from '@azure/msal-angular';
import { AccountInfo, AuthenticationResult, EventMessage, InteractionType, PopupRequest, RedirectRequest, EventType } from '@azure/msal-browser';
import { Subject, Subscription } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { error } from 'jquery';
import { v1 as uuidv1 } from 'uuid';
import { ViewportRuler } from '@angular/cdk/scrolling';
import { ParentToChildService } from './broadcast/parent-to-child.service';
import { CommonService } from './services/common.service';
import { DataService } from './services/data.service';
import { TokenService } from './services/token.service';
import { Location } from '@angular/common';
import { CookieService } from 'ngx-cookie-service';
import { environment } from 'src/environments/environment';
import { TranslationService } from './services/TranslationService/translation.services';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {

  title = 'My Microsoft Login- Example';
  assets: any;
  assetsById: any;
  timeseries: any;
  events: any;
  files: any;
  sequences: any;
  models3D: any;
  currentItem: any = 'tele';
  a = 0;
  b = 0;

  showLoader: boolean = false;

  isIframe = false;
  //   isIframe = false;
  loginDisplay = false;
  docReact: any;
  showMobileSideBar: boolean = false;
  private readonly _destroying$ = new Subject<void>();

  expandCollapseActionBroadcastSubscription: Subscription;
  showMobileSideBarSubscription: Subscription;
  expandCollapseMenu: boolean = true;
  url5: string = "";
  showLoading:boolean = false;
  showLanguageLoading: boolean = false;
  constructor(
    private location: Location,
    public commonService: CommonService,
    private tokenService: TokenService,
    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private authService: MsalService,
    private msalBroadcastService: MsalBroadcastService,
    public dataService: DataService, private router: Router,
    private viewportRuler: ViewportRuler,
    private parentToChildService: ParentToChildService,
    private cookieService: CookieService,
    public translationService: TranslationService) {
    //this.login();
    this.url5= this.dataService.React_API;
    this.docReact = this.viewportRuler.getViewportRect();

    if (this.authService.instance.getAllAccounts().length > 0) {
      var account = this.authService.instance.getAllAccounts()[0];
      this.authService.instance.setActiveAccount(account);
    }
    this.cookieService.deleteAll();
  }

  subscribeBroadcast(): void {
    this.expandCollapseActionBroadcastSubscription = this.parentToChildService.expandCollapseActionBroadcast$.subscribe(state => {
      this.expandCollapseMenu = state;
    })

    this.showMobileSideBarSubscription = this.parentToChildService.showMobileSideBar$.subscribe(state => {
      this.showMobileSideBar = state;
    })
  }


  ngOnInit(): void {
    var _this = this;




    this.isIframe = window !== window.parent && !window.opener;

    this.subscribeBroadcast();
    this.commonService.getDefaultConfig();
    console.log(uuidv1())
    _this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => msg.eventType === EventType.ACQUIRE_TOKEN_SUCCESS),
        takeUntil(this._destroying$)
      )
      .subscribe((result: EventMessage) => {
        if (this.authService.instance.getAllAccounts().length > 0) {
          var account = this.authService.instance.getAllAccounts()[0];
          this.authService.instance.setActiveAccount(account);
        }
      })
    _this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => msg.eventType === EventType.LOGIN_SUCCESS),
        takeUntil(this._destroying$)
      )
      .subscribe((result: EventMessage) => {
        console.log(EventType.LOGIN_SUCCESS, result)
        if (this.authService.instance.getAllAccounts().length > 0) {
          var account = this.authService.instance.getAllAccounts()[0];
          this.authService.instance.setActiveAccount(account);
        }
        _this.saveAzureAccount();

      })

    _this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => msg.eventType === EventType.HANDLE_REDIRECT_END),
        takeUntil(this._destroying$)
      )
      .subscribe((result: EventMessage) => {
        console.log(EventType.HANDLE_REDIRECT_END, result)
        _this.authService.acquireTokenSilent({
          scopes: _this.dataService.scopes,
        }).subscribe((response) => {
          console.log(response)
          // _this.commonService.getGeoRegion(function(){

          // })
          _this.dataService.getData("https://graph.microsoft.com/v1.0/me").subscribe(
            (res: any) => {
              localStorage.setItem("user", JSON.stringify(res));
              console.log('res["email"]',res)
              console.log('res["email"]',res["userPrincipalName"])
            _this.commonService.getUser(res["userPrincipalName"], function (dataUser) {
              console.log(dataUser)
              _this.dataService.userInfo = dataUser;
            })
              this.dataService.getData(this.dataService.NODE_API + "/typeConfig").subscribe(
                (res: any) => {
                  _this.commonService.configuration = res;
             
                  _this.commonService.getlistUser(response.account.username, function (data) {
                  })
                  _this.showLoading = true
                  _this.translationService.getLabels(
                    'EN',
                    function(data) {
                      console.log('Inside getLabels callback')
                      _this.commonService.getIntegrationUser(response.account.username, function (data) {
                        console.log('data user callback',data, _this.commonService.selectedLanguage.toUpperCase())
                        _this.showLoading = false
                        _this.commonService.showLanguageLoading = true
                        console.log('Inside EN getLabels callback', _this.commonService.showLanguageLoading)
                        console.log('EN Lang labels', _this.commonService.labelObject)
                        // window?.postMessage(
                        //   {
                        //     type: 'Language',
                        //     action: 'Language',
                        //     LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
                        //     labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
                        //   },
                        //   '*'
                        // );
                        
                        _this.commonService.updateLabelObject(_this.commonService.selectedLanguage.toUpperCase())
                        
                        // Favourite Language API Call
                        _this.translationService.getLabels( 
                          _this.commonService.selectedLanguage.toUpperCase(), // Favourite Language
                          function(data){
                            const locale = _this.commonService.selectedLanguage.toUpperCase();
                            console.log(_this.commonService.selectedLanguage.toUpperCase())
                            console.log('Inside selected Language getLabels callback', _this.commonService.selectedLanguage.toUpperCase(), _this.commonService.showLanguageLoading)
                            console.log('Selected Lang labels', _this.commonService.labelObject)
                            
                            _this.commonService.updateLabelObject(_this.commonService.selectedLanguage.toUpperCase())
                            setTimeout(() => {
                              _this.commonService.showLanguageLoading = false
                              console.log('Inside EN getLabels callback showLangLoading: ',_this.commonService.showLanguageLoading)
                            }, 4000)
                          }
                        )
                        _this.a=2
                        _this.b=_this.translationService.availableLanguages.length
                      //   _this.translationService.availableLanguages.forEach(async (lang) => {
                      //     if (lang.code !== _this.commonService.selectedLanguage.toUpperCase()) {
                      //       _this.translationService.getLabels(
                      //         lang.code,
                      //         function(data){
                      //           _this.a++
                      //           if(_this.a == _this.b){
                      //             _this.commonService.showLanguageLoading = false
                      //           }
                      //         }
                      //       )
                      //     }
                      //   }
                      // )
    
                        if(data == null){
                          _this.commonService.getIntegrationUser( response.account.username,function (data) {
                          })
                         }
                         _this.commonService.getObservationList();
                      })
                    }
                  )
                  // _this.commonService.showLanguageLoading = false
                  // _this.commonService.getIntegrationUserTest(response.account.username, function (data) {
                  // })
                  
                  _this.commonService.getApplication();

                  _this.commonService.getGeoRegion();

                  _this.commonService.getCountry([], function (data) {
                  })
                  _this.commonService.getSiteList();
                  _this.commonService.getUnitList1([], function (data) {
                  })
                  _this.commonService.getBusinessLine([], function (data) {
                  })
                  // _this.commonService.getReportingLocation();
                  _this.commonService.getProcessConfiguration(_this.dataService.siteId, function (data) {

                  });
                });


              localStorage.setItem('dataSourceTkn', response.accessToken);
              _this.tokenService.saveToken(response.accessToken);
              _this.tokenService.saveRefreshToken(response.accessToken);
              localStorage.setItem('dataSourceIDTkn', response.idToken);
              _this.tokenService.saveIDToken(response.idToken);
              _this.tokenService.saveIDRefreshToken(response.idToken);

              // _this.dataService.labels = _this.translationService.labelsTemp
              // console.log('labelObject',_this.dataService.labels)

              if (_this.router.url == "/login") {
                _this.goHome();
              }
            });
        }, (error) => {
          console.log("error", error)
          _this.login();

        });


        _this.saveAzureAccount();
      })

    if (this.authService.instance.getAllAccounts().length > 0) {
      var account = this.authService.instance.getAllAccounts()[0];
      this.authService.instance.setActiveAccount(account);
    }

  }

  goHome() {
    var _this = this;
    _this.router.navigate(["observations/observation"]);
  }

  saveAzureAccount() {
    var _this = this;
    if (_this.authService.instance.getActiveAccount()) {
      var account = this.authService.instance.getAllAccounts()[0];
      this.authService.instance.setActiveAccount(account);
      _this.authService.acquireTokenSilent({
        scopes: _this.dataService.scopes,
      }).subscribe((response) => {
        localStorage.setItem('dataSourceTkn', response.accessToken);
        _this.tokenService.saveToken(response.accessToken);
        _this.tokenService.saveRefreshToken(response.accessToken);
        localStorage.setItem('dataSourceIDTkn', response.idToken);
        _this.tokenService.saveIDToken(response.idToken);
        _this.tokenService.saveIDRefreshToken(response.idToken);

        if (_this.router.url == "/login") {
          _this.goHome();
        }
      });
    } else {
      _this.login()
    }
  }

  isLoggedIn(): boolean {
    return this.authService.instance.getActiveAccount() != null
  }

  login() {
    if (this.msalGuardConfig.interactionType === InteractionType.Popup) {
      if (this.msalGuardConfig.authRequest) {
        this.authService.loginPopup({ ...this.msalGuardConfig.authRequest } as PopupRequest)
          .subscribe((response: AuthenticationResult) => {
            this.authService.instance.setActiveAccount(response.account);
          });
      } else {
        this.authService.loginPopup()
          .subscribe((response: AuthenticationResult) => {
            this.authService.instance.setActiveAccount(response.account);
          });
      }
    } else {
      if (this.msalGuardConfig.authRequest) {
        this.authService.loginRedirect({ ...this.msalGuardConfig.authRequest } as RedirectRequest);
      } else {
        this.authService.loginRedirect();
      }
    }
  }
  logout() {
    if (this.msalGuardConfig.interactionType === InteractionType.Popup) {
      this.authService.logoutPopup({
        postLogoutRedirectUri: "/",
        mainWindowRedirectUri: "/"
      });
    } else {
      this.authService.logoutRedirect({
        postLogoutRedirectUri: "/",
      });
    }
  }

  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
    this.expandCollapseActionBroadcastSubscription && this.expandCollapseActionBroadcastSubscription.unsubscribe();
    this.showMobileSideBarSubscription && this.showMobileSideBarSubscription.unsubscribe();
  }

}
