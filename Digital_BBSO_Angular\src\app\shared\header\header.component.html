<div fxLayout="row" class="header">
  <!-- <div *ngIf="showLanguageLoading" class="language-loader-container" fxLayout="column">
    <div class="flanguage-fading-bars">
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
    </div>
     <div>
      <span class="loading-text semi-bold-12">{{"Please wait, loading the Language Labels. (Header Component) " | translate }}</span>
     </div>
  </div> -->
  <div fxFlex="100">
    <mat-toolbar class="mat-elevation-z3 header-bg">
      <mat-toolbar-row>
        <div class="header-heading" [fxFlex]="docReact.width > 990 ? '50' : '10'" fxLayoutAlign="start center">
            <h4 class="header-color head-font" *ngIf="docReact.width > 990" style="font-size: 24px !important;">
               <!-- {{ 'TITLE' | translate }} -->
               <!-- {{ labels['title'] }} -->
                 {{applicationName}}
            </h4>
            <h4 class="header-color head-font mob-head" *ngIf="docReact.width <= 990" style="font-size: 24px !important;">
              <!-- <svg xmlns="http://www.w3.org/2000/svg" width="3.849" height="17.914" viewBox="0 0 3.849 17.914" (click)="showSideBar(true)" *ngIf="docReact.width <= 768">
                <g id="Group_948" data-name="Group 948" transform="translate(-41 -8.111)">
                  <path id="Path_501" data-name="Path 501" d="M44.207,8.729a1.86,1.86,0,1,0,0,2.566A1.6,1.6,0,0,0,44.207,8.729Z" transform="translate(0 0)" fill="#1a2254"/>
                  <path id="Path_502" data-name="Path 502" d="M44.207,43.244a1.906,1.906,0,1,0,0,2.78A1.826,1.826,0,0,0,44.207,43.244Z" transform="translate(0 -27.245)" fill="#1a2254"/>
                  <path id="Path_503" data-name="Path 503" d="M44.207,74.729a1.86,1.86,0,1,0,0,2.566A1.6,1.6,0,0,0,44.207,74.729Z" transform="translate(0 -51.887)" fill="#1a2254"/>
                </g>
              </svg> -->
              <!-- {{ 'MOB_TITLE' | translate }} -->
                <!-- {{ labels['mobtitle'] }} -->
                {{applicationName}}
           </h4>
        </div>

        <div class="profile-section" [fxFlex]="docReact.width > 990 ? '50' : '90'" fxLayoutAlign="end center">
      
          <div *ngIf="!vendorFlag" class="notification-section bt-icon-section">
            <button mat-icon-button (click)="notificationWebsite()">
              <svg id="header_button_icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <rect id="Rectangle_19353" data-name="Rectangle 19353" width="24" height="24" fill="rgba(255,255,255,0)"/>
                <path id="notifications_FILL0_wght400_GRAD0_opsz48" d="M160-865.4v-1.288h1.8v-6.569a5.183,5.183,0,0,1,1.063-3.21,4.582,4.582,0,0,1,2.8-1.771v-.623a1.055,1.055,0,0,1,.354-.816,1.217,1.217,0,0,1,.848-.322,1.217,1.217,0,0,1,.848.322,1.055,1.055,0,0,1,.354.816v.623a4.626,4.626,0,0,1,2.812,1.771,5.149,5.149,0,0,1,1.073,3.21v6.569h1.782v1.288ZM166.87-871.735Zm0,8.91a1.666,1.666,0,0,1-1.2-.5,1.636,1.636,0,0,1-.515-1.213h3.435a1.653,1.653,0,0,1-.5,1.213A1.654,1.654,0,0,1,166.87-862.825Zm-3.778-3.864h7.578v-6.569a3.721,3.721,0,0,0-1.095-2.705,3.614,3.614,0,0,0-2.684-1.116,3.649,3.649,0,0,0-2.694,1.116,3.7,3.7,0,0,0-1.106,2.705Z" transform="translate(-155 883)" fill="#4e7389"/>
              </svg><span *ngIf="unreadNotification > 0" class="unread-notification">{{unreadNotification}}</span>
            </button>
          </div>
<!-- 
          <div *ngIf="!vendorFlag" class="help-section bt-icon-section">
            <button mat-icon-button>
              <svg id="header_button_icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <rect id="Rectangle_19353" data-name="Rectangle 19353" width="24" height="24" fill="rgba(255,255,255,0)"/>
                <path id="question_mark_FILL0_wght400_GRAD0_opsz48" d="M277.773-827.744a6.534,6.534,0,0,1,.4-2.523,5.861,5.861,0,0,1,1.406-1.73,10.227,10.227,0,0,0,1.55-1.694,2.881,2.881,0,0,0,.541-1.67,2.445,2.445,0,0,0-.721-1.8,2.729,2.729,0,0,0-2.019-.721,2.524,2.524,0,0,0-1.923.709,4.767,4.767,0,0,0-.985,1.478L274-836.587A5.489,5.489,0,0,1,275.79-839a4.9,4.9,0,0,1,3.136-1,4.928,4.928,0,0,1,3.7,1.334,4.436,4.436,0,0,1,1.3,3.208,4.424,4.424,0,0,1-.493,2.091,7.942,7.942,0,0,1-1.574,1.971,6.15,6.15,0,0,0-1.418,1.73,5.88,5.88,0,0,0-.264,1.923Zm1.154,6.008a1.621,1.621,0,0,1-1.19-.493,1.621,1.621,0,0,1-.493-1.19,1.621,1.621,0,0,1,.493-1.19,1.621,1.621,0,0,1,1.19-.493,1.621,1.621,0,0,1,1.19.493,1.621,1.621,0,0,1,.493,1.19,1.621,1.621,0,0,1-.493,1.19A1.621,1.621,0,0,1,278.927-821.735Z" transform="translate(-267 843)" fill="#4e7389"/>
              </svg>
            </button>
          </div>

          <div *ngIf="!vendorFlag" class="search-section bt-icon-section">
            <button mat-icon-button>
              <svg id="header_button_icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <rect id="Rectangle_19353" data-name="Rectangle 19353" width="24" height="24" fill="rgba(255,255,255,0)"/>
                <path id="search" d="M126.84,125.814l-4.832-4.832a5.577,5.577,0,1,0-1.027,1.027l4.832,4.83a.153.153,0,0,0,.216,0l.811-.809A.153.153,0,0,0,126.84,125.814Zm-6.311-5.286a4.168,4.168,0,1,1,1.221-2.947A4.145,4.145,0,0,1,120.529,120.529Z" transform="translate(-107 -107)" fill="#4e7389"/>
              </svg>
            </button>
          </div>
           -->
           <!-- Language Drop Down -->
           <div class="language-section bt-icon-section1">
            <div (click)="languageDropdown()" matTooltip="{{ labels['menuChooselang'] }}">
              <button mat-icon-button>
                <span class="lan-ico">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14.13" height="14.13" viewBox="0 0 15.13 15.13">
                    <path id="public_FILL0_wght400_GRAD0_opsz48"
                      d="M87.565-864.87a7.368,7.368,0,0,1-2.95-.6,7.642,7.642,0,0,1-2.4-1.617,7.638,7.638,0,0,1-1.617-2.4,7.366,7.366,0,0,1-.6-2.95,7.367,7.367,0,0,1,.6-2.95,7.639,7.639,0,0,1,1.617-2.4,7.641,7.641,0,0,1,2.4-1.617,7.366,7.366,0,0,1,2.95-.6,7.366,7.366,0,0,1,2.95.6,7.641,7.641,0,0,1,2.4,1.617,7.639,7.639,0,0,1,1.617,2.4,7.366,7.366,0,0,1,.6,2.95,7.366,7.366,0,0,1-.6,2.95,7.638,7.638,0,0,1-1.617,2.4,7.642,7.642,0,0,1-2.4,1.617A7.367,7.367,0,0,1,87.565-864.87Zm-.813-1.154v-1.551a1.459,1.459,0,0,1-1.116-.492,1.646,1.646,0,0,1-.454-1.154v-.832L81.3-873.929a5.314,5.314,0,0,0-.132.747,7.32,7.32,0,0,0-.038.747,6.33,6.33,0,0,0,1.6,4.293A6.1,6.1,0,0,0,86.752-866.024Zm5.56-2.043a6.247,6.247,0,0,0,.728-.964,6.742,6.742,0,0,0,.53-1.069,6.053,6.053,0,0,0,.322-1.144,6.807,6.807,0,0,0,.1-1.191,6.394,6.394,0,0,0-1.1-3.641,6.381,6.381,0,0,0-2.931-2.392v.34a1.646,1.646,0,0,1-.454,1.154,1.459,1.459,0,0,1-1.116.492H86.752v1.645a.654.654,0,0,1-.255.529.889.889,0,0,1-.577.208H84.35v1.664h4.879a.663.663,0,0,1,.53.246.852.852,0,0,1,.208.567v2.4h.813a1.534,1.534,0,0,1,.965.322A1.6,1.6,0,0,1,92.312-868.066Z"
                      transform="translate(-80 880)" fill="#4e7389" />
                  </svg>
                </span>
                <span class="location-text">{{selectedLanguage.displayLanguageCode}}</span>
              </button>
              <mat-menu #langSelect="matMenu">
                <button mat-menu-item *ngFor="let item of languageList" (click)="languageClick(item)">
                  <span class="location-text">{{item.name}}</span>
                </button>
              </mat-menu>
            </div>

            <mat-form-field  appearance="fill">
              <mat-select #languageId id="languageId" (selectionChange)="languageChange()" [(ngModel)]="selectedLanguage">
                <mat-option style="height: 40px;" *ngFor="let item of languageList" [value]="item">
                  <button mat-icon-button>
                  <span style="font-size: 16px;">
                    {{item.name}}
                  </span></button>
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div style="margin: 5px;"></div>
          <!-- <div class="loaction bt-icon-section">
            <span class="location-text">
              Clear Lake
            </span>
            <button mat-icon-button>
              <svg xmlns="http://www.w3.org/2000/svg" width="11.747" height="14.684" viewBox="0 0 11.747 14.684">
                <path id="location_on_FILL0_wght400_GRAD0_opsz48"
                  d="M165.875-872.842a1.234,1.234,0,0,0,.907-.378,1.242,1.242,0,0,0,.376-.909,1.233,1.233,0,0,0-.378-.907,1.242,1.242,0,0,0-.909-.376,1.234,1.234,0,0,0-.907.378,1.242,1.242,0,0,0-.376.909,1.234,1.234,0,0,0,.378.907A1.241,1.241,0,0,0,165.875-872.842Zm0,6.075a21.494,21.494,0,0,0,3.607-4.029,6.112,6.112,0,0,0,1.166-3.185,4.786,4.786,0,0,0-1.382-3.541,4.615,4.615,0,0,0-3.39-1.379,4.615,4.615,0,0,0-3.39,1.379,4.786,4.786,0,0,0-1.382,3.541,6.023,6.023,0,0,0,1.193,3.185A23.34,23.34,0,0,0,165.874-866.766Zm0,1.45a24.408,24.408,0,0,1-4.414-4.671A7.264,7.264,0,0,1,160-873.98a5.678,5.678,0,0,1,1.771-4.387,5.867,5.867,0,0,1,4.1-1.633,5.867,5.867,0,0,1,4.1,1.633,5.678,5.678,0,0,1,1.771,4.387,7.264,7.264,0,0,1-1.459,3.992A24.409,24.409,0,0,1,165.874-865.316ZM165.874-874.126Z"
                  transform="translate(-160 880)" fill="#4e7389" />
              </svg>
            </button>
          </div> -->
          <div class="loaction bt-icon-section" [matMenuTriggerFor]="menu">
            <span class="location-text" *ngIf="defaultSite">
              {{defaultSite.siteName }}
            </span>
            <button mat-icon-button>
              <svg xmlns="http://www.w3.org/2000/svg" width="11.747" height="14.684" viewBox="0 0 11.747 14.684">
                <path id="location_on_FILL0_wght400_GRAD0_opsz48"
                  d="M165.875-872.842a1.234,1.234,0,0,0,.907-.378,1.242,1.242,0,0,0,.376-.909,1.233,1.233,0,0,0-.378-.907,1.242,1.242,0,0,0-.909-.376,1.234,1.234,0,0,0-.907.378,1.242,1.242,0,0,0-.376.909,1.234,1.234,0,0,0,.378.907A1.241,1.241,0,0,0,165.875-872.842Zm0,6.075a21.494,21.494,0,0,0,3.607-4.029,6.112,6.112,0,0,0,1.166-3.185,4.786,4.786,0,0,0-1.382-3.541,4.615,4.615,0,0,0-3.39-1.379,4.615,4.615,0,0,0-3.39,1.379,4.786,4.786,0,0,0-1.382,3.541,6.023,6.023,0,0,0,1.193,3.185A23.34,23.34,0,0,0,165.874-866.766Zm0,1.45a24.408,24.408,0,0,1-4.414-4.671A7.264,7.264,0,0,1,160-873.98a5.678,5.678,0,0,1,1.771-4.387,5.867,5.867,0,0,1,4.1-1.633,5.867,5.867,0,0,1,4.1,1.633,5.678,5.678,0,0,1,1.771,4.387,7.264,7.264,0,0,1-1.459,3.992A24.409,24.409,0,0,1,165.874-865.316ZM165.874-874.126Z"
                  transform="translate(-160 880)" fill="#4e7389" />
              </svg>
            </button>
       
          <mat-menu #menu="matMenu">
          <button mat-menu-item style="height: 34px !important;" *ngFor="let item of userAsSite" (click)="clickUserSite(item)">
            <ng-container *ngIf="item.siteName === 'All Sites'; else test " >
              {{item.siteName }}
            </ng-container>
            <ng-template #test >
              {{item.siteName}}
            </ng-template>
            </button>
          </mat-menu>
          </div>
           <!-- User-details -->
           <div class="user-profile-section">
            <a class="user-profile"  [matMenuTriggerFor]="account">
              {{shortName}}
            </a>
            <mat-menu #account="matMenu">
              <div fxLayoutAlign="start center" style="margin-top:20px" class="logout-pop"
                (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
                {{userInfo ? userInfo.displayName : ""}}
              </div>
              <div *ngIf="userInfo" fxLayoutAlign="start center" class="logout-pop"
                (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
                {{userInfo.jobTitle ? userInfo.jobTitle : ""}}
              </div>
              <div fxLayoutAlign="start center" style="margin-bottom: 0px;" class="logout-pop"
                (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
                <div>
                  {{selectedLanguage.displayLanguageCode}}
                </div>
                <div>
                  <mat-icon style="cursor: pointer;margin-top: 4px;"
                    [matMenuTriggerFor]="languageSelect">keyboard_arrow_down</mat-icon>
                </div>
                <mat-menu #langSelect="matMenu">
                  <button mat-menu-item *ngFor="let item of languageList" (click)="languageClick(item)">
                    <span>{{item.name}} {{ " - "+ item.displayLanguageCode }}</span>
                  </button>
                </mat-menu>
              </div>
              <mat-menu #languageSelect="matMenu" (click)="$event.stopPropagation()"
                (keydown)="$event.stopPropagation()">
                <button mat-menu-item *ngFor="let item of languageList" (click)="languageClick(item)">
                  <span>{{item.name}} {{ " - "+ item.displayLanguageCode }}</span>
                </button>
              </mat-menu>
              <br>
              <div fxLayoutAlign="center center" style="border-top: 2px solid gray;" (click)="$event.stopPropagation()"
                (keydown)="$event.stopPropagation()">
                <button mat-menu-item (click)="logoutAccount()" style="width: auto;margin-top: 15px;">
                  {{ labels['menuSignout'] }}
                  <mat-icon>exit_to_app</mat-icon>
                </button>
              </div>
            </mat-menu>
          </div>
        </div>

        

      
      </mat-toolbar-row>
    </mat-toolbar>
  </div>
</div>
