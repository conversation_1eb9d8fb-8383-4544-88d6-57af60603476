<div class="overflow-section">
    <div fxFlex="100">
        <div class="workflow-section">
            <div class="action-section-unit-section">
                <commom-label [labelText]="'WORKFLOW.MAIN_WORKFLOW.TITLE'" [tagName]="'h4'"
                [cstClassName]="'heading unit-heading'"></commom-label>
                <common-lib-button [className]="'cst-btn'" [text]="'BUTTON.CREATE_WORKFLOW'" [icon]="'add'"
                (buttonAction)="goPage('workflow/create-workflow')"></common-lib-button>
            </div>

            <div class="workflow">
                <div class="shared-dash-card grid-stl" *ngFor="let item of workFlowArray;">
                    <div class="ico-section">
                        <!-- <mat-icon style="color: white;font-size: 33px;width: 40px;height: 40px;">account_tree</mat-icon> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="23.333" viewBox="0 0 30 23.333">
                            <path id="Icon_awesome-tachometer-alt" data-name="Icon awesome-tachometer-alt" d="M15,2.25A15,15,0,0,0,2.034,24.792a1.648,1.648,0,0,0,1.429.792H26.536a1.648,1.648,0,0,0,1.429-.792A15,15,0,0,0,15,2.25Zm0,3.333a1.645,1.645,0,0,1,1.579,1.232,2.807,2.807,0,0,0-.18.347L15.919,8.6A1.623,1.623,0,0,1,15,8.917a1.667,1.667,0,0,1,0-3.333Zm-10,15a1.667,1.667,0,1,1,1.667-1.667A1.667,1.667,0,0,1,5,20.583ZM7.5,12.25a1.667,1.667,0,1,1,1.667-1.667A1.667,1.667,0,0,1,7.5,12.25ZM20.353,8.479l-3.194,9.583a3.271,3.271,0,0,1,.713,4.188H12.129a3.3,3.3,0,0,1,2.657-4.979l3.195-9.584a1.25,1.25,0,0,1,2.371.791Zm.764,2.979.808-2.424a1.643,1.643,0,0,1,.576-.116,1.667,1.667,0,1,1,0,3.333,1.641,1.641,0,0,1-1.384-.793ZM25,20.583a1.667,1.667,0,1,1,1.667-1.667A1.667,1.667,0,0,1,25,20.583Z" transform="translate(0 -2.25)" fill="#fff"></path></svg>
                    </div>
                   
                
                    <div class="content-sec">
                        <commom-label [labelText]="item.name"
                        [tagName]="'h6'" [cstClassName]="'cst-sub-heading2 shared-dash-heading'"></commom-label>
                        <div class="createdBy">
                            <span>{{ 'WORKFLOW.MAIN_WORKFLOW.CARDS.CREATED_AT' | translate }}:</span> <span>{{item.id}}</span>
                        </div>
                        <div class="createdBy">
                            <span>{{ 'WORKFLOW.MAIN_WORKFLOW.CARDS.CREATED_BY' | translate }}:</span> <span>{{item.createdAt}}</span>
                        </div>
                        <div class="bot-fot">
                            <a>{{'BUTTON.VIEW' | translate}}</a>
                            <a>{{'BUTTON.ASSIGN' | translate}}</a>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
  </div>