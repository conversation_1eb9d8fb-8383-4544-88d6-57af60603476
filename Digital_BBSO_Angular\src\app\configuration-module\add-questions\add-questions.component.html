<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-section">
  <div fxFlex="100">
    <div class="question-list-section">
      <div class="action-section-unit-section">
        <commom-label labelText="{{ labels['addquestionTitle'] }}" [labelTextNoLan]="titleName" [tagName]="'h4'"
          [cstClassName]="'heading unit-heading'"></commom-label>
      </div>

      <div class="marginTop">
        <form [formGroup]="addQuestionForm">
          <div>
            <!-- formArrayName="addQues" *ngFor="let ques of addQuestionForm.get('addQues')['controls']; let i = index" [formGroupName]="i" -->
            <div>
              <div class="leftMargin_10">

                <div fxLayout="row wrap" fxLayoutAlign="start center">
                  <div>
                    <span class="subheading-1">
                      {{ labels['formcontrolsQuestion'] }}
                    </span>
                  </div>
                  <div class="marginTop" fxFlex="100">
                    <mat-form-field appearance="outline">
                      <input matInput formControlName="name" />
                    </mat-form-field>
                  </div>
                  <br>
                  <br>
                  <div>
                    <span class="subheading-1">
                      {{ labels['formcontrolsQuestiondescription'] }}
                    </span>
                  </div>
                  <div class="marginTop" fxFlex="100">
                    <mat-form-field appearance="outline" class="add-question-area">
                      <textarea matInput formControlName="description" cdkTextareaAutosize cdkAutosizeMinRows="4"
                        cdkAutosizeMaxRows="5"></textarea>
                    </mat-form-field>
                  </div>
                  <br>
                  <br>
                  <div *ngIf="false">
                    <span class="subheading-1">
                      {{ labels['formcontrolsSequence']}}
                    </span>
                  </div>
                  <div *ngIf="false" class="marginTop" fxFlex="100">
                    <mat-form-field appearance="outline">
                      <input type="number" matInput formControlName="sequence" />
                    </mat-form-field>
                  </div>
                </div>

              </div>

            </div>

          </div>

        </form>
      </div>

      <div class="btn-section mt-20">
        <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
          (buttonAction)="cancelClick()"></common-lib-button>
        <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}" (buttonAction)="saveQuestion();"
          *ngIf="formType != 'View'"></common-lib-button>
      </div>
    </div>

  </div>
</div>
