import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ActionListComponent } from './action-list/action-list.component';
import { CreateActionComponent } from './create-action/create-action.component';
import { actionchecklistComponent } from './action-checklist/action-checklist.component';


const routes: Routes = [
    { path: '', redirectTo: 'action-list', pathMatch: 'full' },
    {
        path: 'action-list',
        component: ActionListComponent
    },
    {
        path:'create-action',
        component:CreateActionComponent
    },
    {
        path:'action-checklist',
        component:actionchecklistComponent
    }

];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ActionRoutingModule { }