import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { ConfigurationRoutingModule } from "./configuration-routing.module";
import { ConfigurationComponent } from "./configuration/configuration.component";
import { FormConfigListComponent } from "./form-config-list/form-config-list.component";
import { ConfigListComponent } from "./config-list/config-list.component";
import { QuestionColumnConfigComponent } from "./question-column-config/question-column-config.component";
@NgModule({
    declarations: [
        ConfigurationComponent,
        FormConfigListComponent,
        ConfigListComponent,
        QuestionColumnConfigComponent
    ],
    imports: [
        CommonModule,
        ConfigurationRoutingModule,
        SharedModule,
    ],
    providers: [],
  })
  export class ConfigurationModule { }