import { FlatTreeControl } from '@angular/cdk/tree';
import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { MatStepper } from '@angular/material/stepper';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
interface ExampleFlatNode {
  expandable: boolean;
  bold: boolean;
  name: string;
  level: number;
}

@Component({
  selector: 'app-quality-assessment',
  templateUrl: './quality-assessment.component.html',
  styleUrls: ['./quality-assessment.component.scss']
})
export class QualityAssessmentComponent implements OnInit {



  labels = {}
  private _transformer = (node: any, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      bold: node.bold,
      id: node.id,
      question: node.question,
      level: level,
      type: node.type,
      formGroup: node.formGroup,
      checkListId: node.externalId,
      checkListSpace: node.space
    };
  };

  treeControl = new FlatTreeControl<any>(
    node => node.level,

    node => node.expandable,
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children,
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  hasChild = (_: number, node: any) => node.expandable;

  observedForm: FormGroup;
  checkListArray = [];
  questionList: any = [];
  selectedSubCategory: null;
  observation: any;
  id: any;
  refOFWACategory: any;
  selectedSite: any;
  loaderFlag: Boolean;
  @Input() auditId: any;
  @Input() stepper: MatStepper;
  @Output() dataEmitter: EventEmitter<string> = new EventEmitter<string>();
  constructor(private commonService: CommonService, private dataService: DataService, private route: ActivatedRoute, private fb: FormBuilder, private router: Router, private translate: TranslateService,
    private languageService:LanguageService,
    private cd:ChangeDetectorRef,
    private ngZone:NgZone
  ) {
    this.dataSource.data = [];
    this.labels = {
      'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
      'treeheaderSupplierselfcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderSupplierselfcomments'] || 'treeheaderSupplierselfcomments',
      'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
      'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
    }
  }

  ngOnInit(): void {

    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
          'treeheaderSupplierselfcomments': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'treeheaderSupplierselfcomments'] || 'treeheaderSupplierselfcomments',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
          'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      // _this.cd.detectChanges();
    })
    _this.loaderFlag = true;
    console.log("&&oninit")
    // 
    setTimeout(() => {
      _this.dataService.getData(_this.dataService.NODE_API + "/typeConfig").subscribe(
        (res: any) => {
          _this.commonService.configuration = res;
      // if (_this.router.getCurrentNavigation()) {
      //   console.log("firston--->",)
      //   _this.getScheduleData(_this.router.getCurrentNavigation().extras.state["id"]);
      //   _this.id = _this.router.getCurrentNavigation().extras.state["id"]
      // } else {
        // this.route.queryParamMap
        //   .subscribe((params) => {
        //     // var queryParamMap = { ...params.keys, ...params };
        //     var queryParamMap = params;
        //     if (queryParamMap.get("id")) {
        //       _this.getScheduleData(queryParamMap.get("id"));
        //       _this.id = queryParamMap.get("id");
        //     } else {
        //       _this.router.navigate(['observations/completed']);
        //     }
  
        //   });
        if(_this.auditId){
          _this.id = _this.auditId;
          _this.getScheduleData(_this.auditId);
        }else{
          _this.router.navigate(['observations/completed']);
        }
      }
    // }
  );
    },1000)
    


    _this.observedForm = this.fb.group({
      locationObserve: [""],
      behalf: [""],
      datetime: ["", Validators.required],
      startTime: ["", Validators.required],
      endTime: ["", Validators.required]
    });

    // this.dataSource.data =  [
    //   {
    //         name: 'Industrial Automotive',
    //         bold:true,
    //         id:1,
    //         background:false,
    //         children: [ 
    //           {
    //             name: 'QMS',
    //             bold:true,
    //             id:1,
    //             background:true,
    //             children: [ 
    //               {
    //                 name: 'EP-1',
    //                 question:'Written quality policy',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-2',
    //                 question:'Management roles and responsibilities',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-3',
    //                 question:'Raw material acceptance and control',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-4',
    //                 question:'Written operating procedures and control points',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //             ]
    //           },
    //           {
    //             name: 'Raw Material Receipt & Storage',
    //             bold:true,
    //             id:1,
    //             background:true,
    //             children: [ 
    //               {
    //                 name: 'EP-1',
    //                 question:'Suitable visual standards or color checks are performed on raw materials (if color shades are relevant)?',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-2',
    //                 question:'Confirmation of certification from raw material provider and compliance with the specification?',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-3',
    //                 question:'Accepted raw material is visibly identified/labeled as accepted and stored in designated locations?',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //               {
    //                 name: 'EP-4',
    //                 question:'Nonconforming raw material or product is promptly visibly identified, stored in a segregated location and dispositioned to prevent use?',
    //                 background:true,
    //                 bold:false,
    //                 id:1,
    //               },
    //             ]
    //           },
    //         ],

    //       },


    //     ];
  }

  getScheduleData(id) {
    var _this = this;
    console.log(">>>>>>>>>>>>>>>1")
    _this.dataService.postData({ externalId: id }, _this.dataService.NODE_API + "/api/service/listAudit").subscribe(data => {
      console.log(data)
      console.log( _this.commonService.configuration["typeAudit"])
      
      if (data["data"]["list" +_this.commonService.configuration["typeAudit"]]["items"].length>0) {
        console.log(">>>>>>>>>>>>>>>2")
       
        var resData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
        console.log( resData)
        _this.dataService.auditData = resData;
        _this.dataService.postData({ schedules: [_this.dataService.auditData["refOFWASchedule"]["externalId"]] }, _this.dataService.NODE_API + "/api/service/listChecklist").subscribe(data1 => {
          console.log(">>>>>>>>>>>>>>>3")
          _this.observation = data1["data"]["list" + _this.commonService.configuration["typeChecklist"]]["items"];
          _this.commonService.getProcessConfiguration(resData["refSite"]["externalId"], function (data2) {
            console.log(">>>>>>>>>>>>>>>4")
            _this.refOFWACategory = resData["refOFWASchedule"].refOFWACategory["externalId"];
            var processList = _this.commonService.processList.filter(e => {
              return (e.refOFWAProcess && e.refOFWAProcess.externalId) == resData["refOFWASchedule"].refOFWACategory["externalId"];
            })
            var site = _this.commonService.siteList.find(e => e.externalId == resData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
            _this.selectedSite = site;
            _this.dataService.postData({ "externalId": _this.refOFWACategory }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
              console.log(">>>>>>>>>>>>>>>5")
              var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
              _this.questionList = _.concat(_this.questionList, myQuestionList)
              _this.loadQuestion(processList)
            })


          });
        })
      } else {
        _this.loaderFlag = false;
        this.router.navigate(['observations/completed']);
      }
    })
  }
  goPage(page) {
    this.router.navigate([page]);
  }

  loadSiteProcess() {
    var _this = this;

  }

  loadQuestion(processList) {
    var _this = this;
    _.each(processList, function (eData, index1) {
      if (index1 == processList.length - 1) {
        _this.selectSubCategory(eData, true);
        // _this.loaderFlag = false;
      } else {
        _this.selectSubCategory(eData, false);
      }
    })
  }
  async processListQuestion(processList, cb) {
    // const files = await getFilePaths();
    var _this = this;
    await Promise.all(processList.map(async (item) => {
      var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == item["refOFWAProcess"]["externalId"]);
      if (myQuestions.length == 0) {
        await _this.dataService.postData({ "externalId": item["refOFWAProcess"]["externalId"] }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
          var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
          var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == item["refOFWAProcess"]["externalId"]);
          if (myQuestions.length == 0) {
            _this.questionList = _.concat(_this.questionList, myQuestionList)
          }
          // _this.checkListUpdate(item);
        })
      } else {
        // _this.checkListUpdate(item);
      }
    }));
    cb()
  }


  async selectSubCategory(item, isFinal) {
    var _this = this;
    // if (item["selected"]) {
    //   item["selected"] = false;
    //   _this.selectedSubCategory = null;
    //   _this.removeSubcategory(item);
    // } else {
    //   item["selected"] = true;
    // }
    item["selected"] = true;

    if (!_this.checkListArray.find(e => e.id == item["refOFWAProcess"]["externalId"])) {
      var categoryFormGroup = new FormGroup({
        comment: new FormControl('')
      });
      var parentObj = {
        name: item["refOFWAProcess"]["name"],
        bold: true,
        id: item["refOFWAProcess"]["externalId"],
        children: [],
        formGroup: categoryFormGroup
      }
      _this.checkListArray.push(parentObj);
      // _this.dataSource.data.push(parentObj);
      var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == item["refOFWAProcess"]["externalId"]);
      if (myQuestions.length == 0) {
        await _this.dataService.postData({ "externalId": item["refOFWAProcess"]["externalId"] }, _this.dataService.NODE_API + "/api/service/listQuestionBankByCategory").subscribe((resData: any) => {
          var myQuestionList = resData["data"]["list" + _this.commonService.configuration["typeOFWAQuestion"]]["items"];
          var myQuestions = _this.questionList.filter(e => e["refOFWACategory"]["externalId"] == item["refOFWAProcess"]["externalId"]);
          if (myQuestions.length == 0) {
            _this.questionList = _.concat(_this.questionList, myQuestionList)
          }
          _this.checkListUpdate(item, isFinal);
          // _this.checklistQuestion(item);
        })
      } else {
        _this.checkListUpdate(item, isFinal);
        // _this.checklistQuestion(item);
      }
    } else {
      _this.checkListUpdate(item, isFinal);
    }
  }

  tempFun(dd) {
  }

  onCancel() {
    this.stepper.previous();
    // this.router.navigate(['observations/general-info']);
  }

  removeSubcategory(subCategory) {
    var _this = this;
    var parentIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    if (parentIndex != -1) {
      var myIndex = _this.checkListArray[parentIndex].children.findIndex(e => e.id == subCategory.externalId);
      _this.checkListArray[parentIndex].children.splice(myIndex, 1);
      _this.dataSource.data = this.checkListArray;
    }

  }

  checkListUpdate(subCategory: any, isFinal) {
    var _this = this;
    var parentIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    var myIndex = _this.checkListArray.findIndex(e => e.id == subCategory.externalId);
    if (myIndex == -1 && subCategory.selected == true) {
      var subCategoryFormGroup;
      if (_this.observation && _this.observation.length > 0) {
        var subCateggoryAns = _this.observation.find(e => e.refOFWASubCategory.externalId == subCategory.externalId && !e.refOFWAQuestion);
        if (subCateggoryAns) {
          subCategoryFormGroup = new FormGroup({
            comment: new FormControl(subCateggoryAns.comment)
          });
        } else {
          subCategoryFormGroup = new FormGroup({
            comment: new FormControl('')
          });
        }
      } else {
        subCategoryFormGroup = new FormGroup({
          comment: new FormControl('')
        });
      }

      _this.checkListArray[parentIndex].children.push({
        name: subCategory.name,
        bold: false,
        type: "SubCategory",
        id: subCategory.externalId,
        checkListId: subCateggoryAns ? subCateggoryAns.externalId : "",
        checkListSpace: subCateggoryAns ? subCateggoryAns.space : "",
        children: [],
        formGroup: subCategoryFormGroup
      })
      _this.checklistQuestion(subCategory, isFinal);
    } else {
      _this.checklistQuestion(subCategory, isFinal);
    }
    // _this.checklistQuestion(subCategory);
    // this.dataSource.data = this.checkListArray;
  }

  checklistQuestion(subCategory: any, isFinal) {
    var _this = this;
    var categoryIndex = _this.checkListArray.findIndex(e => e.id == subCategory["refOFWAProcess"]["externalId"]);
    if (categoryIndex != -1) {
      var subCategoryIndex = _this.checkListArray[categoryIndex].children.findIndex(e => e["id"] == subCategory["externalId"]);
      var myQuestion = _this.questionList.filter(e => e["refOFWASubCategory"] && e["refOFWASubCategory"]["externalId"] == subCategory["externalId"]);
      if (myQuestion.length > 0 && subCategoryIndex != -1) {
        if (_this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].length == 0) {
          myQuestion.forEach((question) => {
            var subCategoryFormGroup;
            if (_this.observation && _this.observation.length > 0) {
              var questionAns = _this.observation.find(e => (e.refOFWASubCategory && e.refOFWASubCategory.externalId == subCategory.externalId) && ((e.refOFWAQuestion && e.refOFWAQuestion.externalId) == question["externalId"]));
              if (questionAns) {
                subCategoryFormGroup = new FormGroup({
                  comment: new FormControl(questionAns.comment)
                });
              } else {
                subCategoryFormGroup = new FormGroup({
                  comment: new FormControl('')
                });
              }

            } else {
              subCategoryFormGroup = new FormGroup({
                comment: new FormControl('')
              });
            }



            _this.checkListArray[categoryIndex].children[subCategoryIndex]["children"].push({
              name: question["name"],
              question: question["description"],
              type: "Question",
              bold: false,
              id: question["externalId"],
              formGroup: subCategoryFormGroup,
              checkListId: questionAns ? questionAns.externalId : "",
              checkListSpace: questionAns ? questionAns.space : "",
            })

          })
          _this.dataSource.data = this.checkListArray;
          if (isFinal) {
            _this.loaderFlag = false;
          }
          // _this.dataSource.data = this.checkListArray;
        } else {
          _this.dataSource.data = this.checkListArray;
          if (isFinal) {
            _this.loaderFlag = false;
          }
        }

      } else {
        _this.dataSource.data = this.checkListArray;
        if (isFinal) {
          _this.loaderFlag = false;
        }
      }
    } else {
      _this.dataSource.data = this.checkListArray;
      if (isFinal) {
        _this.loaderFlag = false;
      }
    }

  }


  nextClick() {
    var _this = this;
    _this.loaderFlag = true;
    _this.postChecklistArray();

  }

  postChecklistArray() {
    var _this = this;
    // refQuestionBank: QuestionBank
    // refOFWACategory: Process
    // refOFWASubCategory: Process
    // satisfactory: String
    // ofi: String
    // unSatisfactory: String
    // comment: String
    // note: String
    // auditorSignature: String
    // managerSignature: String
    var questionArray = []
    _.each(this.dataSource.data, function (eCatergory) {
      var catergoryId = eCatergory.id;
      var category = _this.commonService.processList.find(e => e.externalId == catergoryId);
      _.each(eCatergory.children, function (eSubCatergory) {

        var subCategoryId = eSubCatergory.id;
        var subCategory = _this.commonService.processList.find(e => e.externalId == subCategoryId);
        // var subCategoryValue = eSubCatergory.formGroup.value;
        // var subCategoryAnswer = {};
        // if(eSubCatergory.checkListId && eSubCatergory.checkListId.length>0){
        //   subCategoryAnswer["externalId"] = eSubCatergory.checkListId;
        // }
        // subCategoryAnswer["refOFWACategory"] = {
        //   space: category["space"],
        //   externalId: category["externalId"]
        // }
        // subCategoryAnswer["refOFWASubCategory"] = {
        //   space: subCategory["space"],
        //   externalId: subCategory["externalId"]
        // }
        // subCategoryAnswer["refOFWASchedule"] = {
        //   space: _this.dataService.auditData["refOFWASchedule"]["space"],
        //   externalId: _this.dataService.auditData["refOFWASchedule"]["externalId"]
        // }

        // subCategoryAnswer["comment"] = subCategoryValue.comment;
        // questionArray.push(subCategoryAnswer);

        _.each(eSubCatergory.children, function (eQuestion) {
          var questionAnswer = {};

          var questionId = eQuestion.id;
          var question = _this.questionList.find(e => e.externalId == questionId);
          if (eQuestion.checkListId && eQuestion.checkListId.length > 0) {
            questionAnswer["externalId"] = eQuestion.checkListId
          }
          questionAnswer["refOFWACategory"] = {
            space: category["space"],
            externalId: category["externalId"]
          }
          questionAnswer["refOFWASubCategory"] = {
            space: subCategory["space"],
            externalId: subCategory["externalId"]
          }
          questionAnswer["refOFWAQuestion"] = {
            space: question["space"],
            externalId: question["externalId"]
          }
          questionAnswer["refOFWASchedule"] = {
            space: _this.dataService.auditData["refOFWASchedule"]["space"],
            externalId: _this.dataService.auditData["refOFWASchedule"]["externalId"]
          }
          var questionValue = eQuestion.formGroup.value;


          questionAnswer["comment"] = questionValue.comment;
          questionArray.push(questionAnswer);
        })
      })
    })

    var postObjChecklist = {
      "type": _this.commonService.configuration["typeChecklist"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": questionArray
    }
    _this.dataService.postData(postObjChecklist, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      _this.loaderFlag = false;
      if (data["items"].length > 0) {
        _this.stateUpdate();
        _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
        // _this.router.navigate(['observations/upload-doc'], { state: { id: this.id,scheduleId:_this.dataService.auditData["refOFWASchedule"]["externalId"], refOFWACategory: _this.refOFWACategory } });
        _this.stepper.next();
        _this.dataEmitter.emit("upload-doc");
      } else {
        _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterSomethingwentwrong'] });
      }
    })
  }

  stateUpdate() {
    var _this = this;
    var postAuditObj = {
      "externalId": _this.dataService.auditData.externalId,
      "status": "In progress"
    }

    var instanceAuditObj = {
      "type": _this.commonService.configuration["typeAudit"],
      "siteCode": _this["selectedSite"] ? _this["selectedSite"].siteCode : _this.commonService.configuration["allSiteCode"],
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        postAuditObj
      ]
    }
    _this.dataService.postData(instanceAuditObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
      // _this.commonService.triggerToast({ type: 'success', title: "", msg: "Created successfully" });
      // this.goPage('schedule-list');
    });
  }

}


