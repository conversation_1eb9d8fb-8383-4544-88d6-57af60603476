import { FormArray, FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import _ from "lodash";
import * as Joi from 'joi';

export class ValidationService {
    static getValidatorErrorMessage(validatorName: string, validatorValue?: any) {
        let config = {
            'required': 'Required',
            'minlength': `Minimum length ${validatorValue.requiredLength}`,
            'maxlength': `Maximum length ${validatorValue.requiredLength}`,
            'invalidEmailAddress': 'Invalid email address',
            'invalidMobileNumber': 'Check your phone number',
            'invalidAlphaNumeric': 'Invalid alpha numeric',
            'invalidSpecialChar': 'No Special characters match'
        };
        return config[validatorName];
    }
    static isNotEmpty(control) {
        let checkPat = /\S/;
        let checkedValue = control.value;
        const schema = Joi.string().required();
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    }else{
        return { flag: false, message: this.getValidatorErrorMessage('required', control) };
    }
        // if (checkPat.test(checkedValue)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('required', control) };
        // }
    }
    static minLengthValid(control) {
        let checkedValue = control.value;
        const schema = Joi.string().min(control.requiredLength);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('minlength', control) };
    }
        // if (control.requiredLength <= checkedValue.length) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('minlength', control) };
        // }
    }
    static maxLengthValid(control) {
        let checkedValue = control.value;
        const schema = Joi.string().max(control.requiredLength);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('maxlength', control) };
    }
        // if (control.requiredLength >= checkedValue.length) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('maxlength', control) };
        // }
    }
    static isMobileValid(control) {
        let checkPat = /^[0-9]{10}$/;
        let checkedValue = control.value;
        const schema = Joi.string().regex(/^[0-9]{10}$/);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidMobileNumber', control) };
    }
        // if (checkedValue.match(checkPat)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidMobileNumber', control) };
        // }
    }
    static isMultiMobileValid(control) {
        if(!control.value){
            control.value = "";
        }
        var mobileArr = control.value.split(',');
        var retArray = [];
        _.each(mobileArr, function (eData) {
            var filData = _.filter(control.data,function(fData){
                return fData == eData;
            });
            if(filData.length>0){
                retArray.push({ flag: true, message: "" });
            }else{
                retArray.push(ValidationService.isMobileValid({ value: eData }));
            }
            
        });
        return retArray;
    }
    static isEmailValid(control) {
        let checkedValue = control.value;
        const schema = Joi.string().regex(/[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidEmailAddress', control) };
    }
        // if (checkedValue.match(/[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidEmailAddress', control) };
        // }
    }
    static isMultiEmailValid(control) {
        
        if(!control.value){
            control.value = "";
        }
        var emailArr = control.value.split(',');
        var retArray = [];
        _.each(emailArr, function (eData) {
            if(eData.length > 0){
            var filData = _.filter(control.data,function(fData){
                return fData == eData;
            });
            if(filData.length>0){
                retArray.push({ flag: true, message: "" });
            }else{
                retArray.push(ValidationService.isEmailValid({ value: eData }));
            }
        }
        });
        return retArray;
    }
    static isAlphaNumeric(control) {
        let checkedValue = control.value;
        const schema = Joi.string().alphanum();
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidAlphaNumeric', control) };
    }
        // if (control.value.match(/^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9]+)$/)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidAlphaNumeric', control) };
        // }
    }
    static isNumeric(control) {
        let checkedValue = control.value;
    const schema = Joi.string().regex(/^\d+$/);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidNumeric', control) };
    }
        // if (control.value.match(/^\d+$/)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidNumeric', control) };
        // }
    }
    static isAlpha(control) {
        let checkedValue = control.value;
    const schema = Joi.string().regex(/^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9]+)$/);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidAlpha', control) };
    }
        // if (control.value.match(/^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9]+)$/)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidAlpha', control) };
        // }
    }
    static isSpecialChar(control) {
        let checkedValue = control.value;
    const schema = Joi.string().regex(/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/);
    const result = schema.validate(checkedValue);
    const { value, error} = result; 
    const valid = error == null; 
    if (valid) { 
    // if (control.value.match(/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/)) {
        return { flag: true, message: "" };
    } else {
        return { flag: false, message: this.getValidatorErrorMessage('invalidSpecialChar', control) };
    }
        // if (control.value.match(/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/)) {
        //     return { flag: true, message: "" };
        // } else {
        //     return { flag: false, message: this.getValidatorErrorMessage('invalidSpecialChar', control) };
        // }
    }
}