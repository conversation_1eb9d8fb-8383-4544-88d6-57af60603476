{"PAGINATION": {"ROWS_PER_PAGE": "Rijen per pagina", "PREV": "V<PERSON>g", "NEXT": "Volgende"}, "AUDIT": {"ID": "<PERSON> kaart", "TITLE": "Titel", "YEAR": "Jaar", "QUARTER": "Kwart<PERSON>", "PRIORITY": "Prioriteit", "START_DATE": "Begin datum", "END_DATE": "Einddatum", "STATUS": "Toestand", "CREATED_ON": "Gemaakt op", "CREATED_BY": "Gemaakt door", "UPDATED_ON": "Bijgewerkt aan", "UPDATED_BY": "Bijgewerkt door", "ACTIONS": "Acties", "SHIFT": "<PERSON><PERSON><PERSON><PERSON>", "UNIT": "<PERSON><PERSON><PERSON><PERSON>", "REFERENCE_LOCATION": "Locatie", "OBS_ON_BEHALF_OF": "Waargenomen door", "DATE": "Datum", "MONTH": "<PERSON><PERSON>"}, "ACTION_LIST": {"ID": "<PERSON> kaart", "DESC": "Beschrijving", "APP_NAME": "<PERSON><PERSON>", "ASSIGNED_TO": "Toegewezen aan", "SITE": "Locatie", "UNIT": "<PERSON><PERSON><PERSON><PERSON>", "REPORTING_LOCATION": "<PERSON><PERSON><PERSON> melden", "ASSIGN_DATE": "Toegewezen datum", "DUE_DATE": "Deadline", "OBJ_TYPE": "Object type", "OBJ_ID": "Object-ID", "PRIORITY": "Prioriteit", "ACTIONS": "Acties"}, "FIELD_WALK": {"ID": "<PERSON> kaart", "OBS_DATE": "Observatiedatum", "SITE": "Locatie", "CREATED_ON": "Gemaakt op", "CREATED_BY": "Gemaakt door", "UPDATED_ON": "Bijgewerkt aan", "UPDATED_BY": "Bijgewerkt door", "ACTIONS": "Acties"}, "OBS_LIST": {"ID": "<PERSON> kaart", "OBS_DATE": "Observatiedatum", "CRAFT": "Ambacht", "SITE": "Locatie", "DESC": "Beschrijving", "CREATED_ON": "Gemaakt op", "CREATED_BY": "Gemaakt door", "UPDATED_ON": "Bijgewerkt aan", "UPDATED_BY": "Bijgewerkt door", "ACTIONS": "Acties", "WEEK": "Week"}, "AUDIT_PLAN": {"ID": "<PERSON> kaart", "TITLE": "Titel", "AUDIT_NUMBER": "Auditnummer", "YEAR": "Jaar", "QUARTER": "Kwart<PERSON>", "PRIORITY": "Prioriteit", "STATUS": "Toestand", "CREATED_ON": "<PERSON><PERSON><PERSON><PERSON>", "CREATED_BY": "Gemaakt door", "UPDATED_ON": "Bijgewerkt aan", "UPDATED_BY": "Bijgewerkt door", "ACTIONS": "Acties", "OCCURRENCE": "<PERSON><PERSON><PERSON>", "OBS": "<PERSON><PERSON><PERSON>mer"}, "CONFIG_LIST": {"SITE": "Locatie", "PROCESS": "Proces", "CORE_PRINCIPLE": "Kern<PERSON><PERSON><PERSON><PERSON>", "SUB_PROCESS": "Subproces", "CATEGORY": "Categorie", "SUB_CATEGORY": "Subcategorie", "CREATED_ON": "Gemaakt op", "CREATED_BY": "Gemaakt door", "UPDATED_ON": "Bijgewerkt aan", "UPDATED_BY": "Bijgewerkt door", "ACTIONS": "Acties"}, "QUESTION_LIST": {"ID": "<PERSON> kaart", "QUESTION": "Vraag", "DESC": "Beschrijving", "CREATED_AT": "Gemaakt op", "CREATED_BY": "Gemaakt door", "DATE": "Datum", "ACTIONS": "Acties", "UNIT": "<PERSON><PERSON><PERSON><PERSON>", "OBS_TYPE": "Observatietype", "CATEGORY": "Categorie", "SUB_CATEGORY": "Subcategorie"}, "TABLE": {"NO_RESULTS": "<PERSON><PERSON> resultaten"}, "ICONS": {"VIEW": "Weergave", "EDIT": "Bewerking", "ASSETS": "Activa", "CREATE_ACTION": "<PERSON><PERSON>", "QUESTION": "Vraag", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "SEND": "<PERSON><PERSON><PERSON><PERSON>", "SCORE_CARD": "Score kaart", "AUDIT_SUMMARY": "Auditsamenvatting", "VIEW_360": "360 weergave", "VIEW_THREAT": "Bedreigingsweergave", "SCH_DETAILS": "Schemadetails", "DISABLE": "Uitzetten", "SEND_ACTION": "Verzenden naar actie", "OBS_HISTORY": "Observatie Geschiedenis", "CREATE_OBS": "Observatie cre<PERSON>", "DELETE": "Verwijderen", "SEND_MAIL": "<PERSON><PERSON>"}}