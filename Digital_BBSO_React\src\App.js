// import React, { Component } from 'react';
// import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
// import Home from './components/Incident';

import ReactDOM from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { translate,getMessages, useLocale } from "@celanese/celanese-sdk"

import Alert from './components/Alert';
import Assets from './components/Assets';
import ObservationList from './components/ObservationList';
import FormConfig from './components/FormConfigList';
import AuditPlan from './components/AuditPlan';
import RuleList from './components/RuleList'
import Configuration from './components/Configuration';
import QuestionList from './components/QuestionList';
import ScheduleList from './components/ScheduleList';
import ScheduleTracker from './components/ScheduleTracker'
import AlertList from './components/AlertList';
import AuditorManagementList from './components/AuditorManagementList';
import ActionList from './components/ActionList'
import FieldWalk from "./components/FieldWalk";
import Audit from "./components/Audit";
import ScheduleDetails from "./components/ScheduleDetails"
import { useEffect, useState } from "react";
import { availableLanguages } from "./utils/getLabels";
import CompExAsset from "./components/compExAsset";


let idToken;

export default function App() {

  const [messages, setMessages] = useState([]);

  const getIdTokenFromMsal = (token) => {      
    return Promise.resolve(token);
  };
  
  const getAuthToken = () => getIdTokenFromMsal(idToken);



  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Alert />}>
          
        </Route>
        {/* <Route index element={<Home />} /> */}
          {/* <Route path="alert" element={<Alert />} /> */}
          {/* <Route path="incident" element={<Incident />} /> */}
          {/* <Route path="threatassets" element={<ThreatAssets />} /> */}
          <Route path="assets" element={<Assets />} />
          <Route path="formConfig" element={<FormConfig />} />
          <Route path="observationList" element={<ObservationList />} />
          <Route path="audit" element={<Audit />} />
          <Route path="auditPlan" element={<AuditPlan />} />
          <Route path="ruleList" element={<RuleList />} />
          <Route path="configuration" element={<Configuration />} />
          <Route path="questionList" element={<QuestionList />} />
          <Route path="scheduleList" element={<ScheduleList />} />
          <Route path="scheduleTracker" element={<ScheduleTracker />} />
          <Route path="alertList" element={<AlertList />} />
          <Route path="auditor-management" element={<AuditorManagementList />} />
          <Route path="actionList" element={<ActionList />} />
          <Route path="fieldWalk" element={<FieldWalk />} />
          <Route path="scheduleDetails" element={<ScheduleDetails />} />
          <Route path="compEx-asset" element={<CompExAsset />} />
          
          
          
      
          <Route path="*" element={<Alert />} />
      </Routes>
    </BrowserRouter>
  );
}

localStorage.clear();
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
