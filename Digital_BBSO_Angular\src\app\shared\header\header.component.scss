.example-sidenav-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.example-sidenav {
  padding: 20px;
}


.language-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  // top: ;
}

.language-fading-bars {
  display: flex;
  justify-content: space-between;
  width: 80px;
}

.bar {
  width: 10px;
  height: 40px;
  background-color: #083d5b;
  animation: fade 1s infinite;
}

.bar:nth-child(1) {
  animation-delay: 0s;
}

.bar:nth-child(2) {
  animation-delay: 0.2s;
}

.bar:nth-child(3) {
  animation-delay: 0.4s;
}

.bar:nth-child(4) {
  animation-delay: 0.6s;
}

.bar:nth-child(5) {
  animation-delay: 0.8s;
}

::ng-deep .header{
  .mat-toolbar-multiple-rows{
    min-height: 48px !important;
    box-shadow: 0px 2px 3px 0px #00000029;
   .mat-toolbar-row{
     padding: 0 44px;
     height: 48px !important;
   }
 }
}
.unread-notification {
  position: absolute;
  top: -7px;
  right: -4px;
  background-color: rgb(8 61 91); /* Add your desired background color */
  color: white; /* Add your desired text color */
  border-radius: 50%; /* Make sure it's a perfect circle */
  width: 17px; /* Adjust the size as needed */
  height: 19px; /* Equal to width to ensure a circular shape */
  text-align: center;
  line-height: 20px; /* Center the text vertically */
}
.spinner-simple{
  width: 35px;
}