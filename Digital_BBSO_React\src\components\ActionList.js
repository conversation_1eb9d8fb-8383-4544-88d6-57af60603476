// export default App;
import React, { useRef, useEffect, useState, useMemo, useContext } from 'react'
import { Runtime, Inspector } from '@observablehq/runtime'
import notebook from '../assets/.innoart-table/my-table'
import { useSearchParams } from 'react-router-dom'
import { html } from 'htl'
import Asset_JSON from '../assets/data/cognite_data.json'
import Popup from 'reactjs-popup'
import format from 'date-fns/format'
import axios from 'axios'
import { CogniteClient } from '@cognite/sdk'
import { PublicClientApplication } from '@azure/msal-browser'
import Pagination from './pagination/Pagination'
import * as Constants from '../Constant'
import { useTranslation } from 'react-i18next'
import { translate, DynamicTranslationArea, TranslationContext, TranslationContextProvider } from '@celanese/celanese-sdk'

let main
let limitSet = 10
let firstPageIndex = 0
let currentPageNumber = 1
let listData = []
let initFlag
let pageInfo = []
let allListData = []
let colSummary = {}
let displayedColumns = [
  'id',
  'description',
  'assignedToName',
  'site',
  'unit',
  'reportingLocation',
  'assignmentDate',
  'dueDate',
  'objectType',
  'objectExternalId',
  'priority',
 // 'actions',
]

let headersKeyVal = {
  id: 'ID',
  description: 'Description',
  applicationName: 'Application Name',
  assignedToName: 'Assigned To',
  site: 'Site',
  unit: 'Unit',
  reportingLocation: 'Reporting Location',
  assignmentDate: 'Assign Date',
  dueDate: 'Due Date',
  objectType: 'Process',
  objectExternalId: 'Object Id',
  priority: 'Priority',
 // actions: 'Actions',
}

var paginationCursor = []
let site
let unit
let search
var applicationId = Constants.applicationId
let objectType
let token
let userAccessMenu
let dateFormat = "MM/dd/yyyy";
let timeFormat = "hh:mm aa";
let idToken
function ActionList() {
  const viewofSelectionRef = useRef()
  const [currentPage, setCurrentPage] = useState(1)
  const [dataCount, setDataCount] = useState(0)
  const [limit, setLimitCount] = useState(10)
  const [id, setId] = React.useState('5')
  const [selectedLanguage, setSelectedLanguage] = useState('en')

  const [t, i18n] = useTranslation('global')
  const { locale, updateLocale } = useContext(TranslationContext)
  const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

    const getIdTokenFromMsal = (token) => {      
      return Promise.resolve(token);
    };
    
    const getAuthToken = () => getIdTokenFromMsal(idToken);

  const handleLanguageChange = (newValue) => {
    setSelectedLanguage(newValue)
    if(i18n){
      i18n.changeLanguage(newValue)
      console.log('Selected Language: ', selectedLanguage)
      console.log('i18n.language: ', i18n)
    }
 
  }

  useEffect(() => {
    const runtime = new Runtime()
    main = runtime.module(notebook, (name) => {
      if (name === 'viewof selection1')
        return new Inspector(viewofSelectionRef.current)
      if (name === 'selection') {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage(
              { type: 'Assets', action: 'Select', data: [], selected: value },
              '*'
            )
          },
          // rejected(error) { console.error(error); }
        }
      }
    })

    window.onmessage = function (e) {
      if (e.data.type && e.data.type == 'AuthToken') {
        token = e.data.data
        console.log('token', token)
        console.log('Auth token: ', e.data)
        idToken = e.data.idToken
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || shouldTranslateDynamic)
        // console.log('token',token)
        //  colFun();
        //  getData();
      }
      if (e.data.type && e.data.type == 'ActionList') {
        if (e.data.action == 'Column') {
          window.parent.postMessage(
            {
              type: 'ActionList',
              action: 'Loading',
              data: true,
            },
            '*'
          )
          console.log('e.data.data', e.data.data)
          console.log('column language', e.data)
          displayedColumns = e.data.data
          setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
        window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
        console.log('DynamicTranslation', e.data)

          // language translation
          handleLanguageChange(e.data.LanguageCode)
          headersKeyVal = {
            id: translate('stLabel.id'),
            description: translate('stLabel.description'),
            applicationName: translate('stLabel.tablecolsApplicationname'),
            assignedToName: translate('stLabel.tablecolsAssignedto'),
            site: translate('stLabel.site'),
            unit: translate('stLabel.unit'),
            reportingLocation: translate('stLabel.tablecolsReportinglocation'),
            assignmentDate: translate('stLabel.tablecolsAssigneddate'),
            dueDate: translate('stLabel.tablecolsDuedate'),
            objectType: translate('stLabel.process'),
            objectExternalId: translate('stLabel.tablecolsObjectid'),
            priority: translate('stLabel.priority'),
          //  actions: t('ACTION_LIST.ACTIONS'),
          }
          console.log('ActionList headersKeyVal', headersKeyVal)
          //  console.log('token',token)
          colFun()
         // getData()
        } else if (e.data.action == 'Filter') {
          console.log('filter: ', e.data)
          site = e.data.data.site
          console.log('site', site)
          objectType = e.data.data.objectType
          unit = e.data.data.unit
          search = e.data.data.search
          setting();
          getData()
        } else if (e.data.action == 'Summary') {
          colSummary = e.data.data
          colFun()
          // getData();
        } else if (e.data.action == 'AccessMenu') {
          userAccessMenu = e.data.data
          console.log('userAccessMenu', userAccessMenu)
          colFun()
        } else if (e.data.action == 'PageRows') {
          setCurrentPage(1)
          setLimitCount(parseInt(e.data.data))
          limitSet = parseInt(e.data.data)
          paginationCursor = []
          getData()
        }
      }
      if (e.data.action == 'Language') {
        console.log('Language', e.data)
         // remove prev language data
         const prevLang = localStorage.getItem('LocaleData')
         localStorage.removeItem('LocaleData')
         localStorage.removeItem('APP-OFWATranslationData'+prevLang)
         localStorage.setItem('LocaleData', e.data.LanguageCode)
         localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, JSON.stringify(e.data.labels))
        handleLanguageChange(e.data.LanguageCode)
        updateLocale(e.data.LanguageCode)

        headersKeyVal = {
          id: translate('stLabel.id'),
          description: translate('stLabel.description'),
          applicationName: translate('stLabel.tablecolsApplicationname'),
          assignedToName: translate('stLabel.tablecolsAssignedto'),
          site: translate('stLabel.site'),
          unit: translate('stLabel.unit'),
          reportingLocation: translate('stLabel.tablecolsReportinglocation'),
          assignmentDate: translate('stLabel.tablecolsAssigneddate'),
          dueDate: translate('stLabel.tablecolsDuedate'),
          objectType: translate('stLabel.process'),
          objectExternalId: translate('stLabel.tablecolsObjectid'),
          priority: translate('stLabel.priority'),
        //  actions: t('ACTION_LIST.ACTIONS'),
        }

        console.log('ActionList headersKeyVal', headersKeyVal)
        colFun()
       // getData()
      }

      if (e.data.action == 'DynamicTranslation') {
        console.log('App.js DynamicTranslation', e.data)
        idToken = e.data.idToken
        // getAuthToken()
        setShouldTranslateDynamic(e.data.shouldTranslateDynamic || false)
        window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(e.data.shouldTranslateDynamic || false))
        console.log('DynamicTranslation', e.data)
        // localStorage.setItem('LocaleData', e.data.LanguageCode)
        // localStorage.setItem('APP-OFWATranslationData'+e.data.LanguageCode, e.data.labels)
      }
    }

    setDataCount(1)
    colFun()
    //   filterData()
    //getData();
    // main.redefine("data", Asset_JSON);
    window.parent.postMessage(
      { type: 'ActionList', action: 'InitalSetup', data: { initalCall: true } },
      '*'
    )

    return () => runtime.dispose()
  }, [])

  const [searchParams, setSearchParams] = useSearchParams()

  // function action1(x, i) {
  //   return html`<div style=" display: flex;
  //   flex-direction: row;align-item-center;">
  //   <div id="${i}"  onClick=${() => allThreats(i)} title="View" style="height:18px;margin-right:8px;cursor: pointer;" >
  //   <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20" x="0" y="0" viewBox="0 0 488.85 488.85" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>
  //   </div>
  //   <div id="${i}" title="Edit" style="height:18px;margin-right:8px;cursor: pointer;" onClick=${() => dataView(i)} >
  //     <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"  width="15" height="15" x="0" y="0" viewBox="0 0 348.882 348.882" style="enable-background:new 0 0 512 512" xml:space="preserve"><g><path d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z" fill="#1A2254" data-original="#000000" class=""></path><path d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z" fill="#1A2254" data-original="#000000" class=""></path></g></svg>

  //     </div>
  //     </div>
  //     `
  // }

  function openNewTab(externalId) {
    const url = Constants.AIM_URL+"/action-item/details/"+externalId;
    window.open(url, '_blank'); // Open URL in a new tab
  }
  function action1(x, i) {
    var cInd = (currentPage - 1) * limitSet + i
    console.log('cInd', cInd)
    return html`<div
      style=" display: flex;
    flex-direction: row;align-item-center;"
    >
      ${userAccessMenu &&
      userAccessMenu.ActionView &&
      userAccessMenu.ActionView.featureAccessLevelCode
        ? html`${userAccessMenu &&
          userAccessMenu.ActionView.featureAccessLevelCode == 'ViewAccess'
            ? html`<div
                onClick=${() => allThreats(cInd)}
                title=${translate('stLabel.buttonView')}
                style="height:18px;margin-right:12px;cursor: pointer;"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="20"
                  height="20"
                  x="0"
                  y="0"
                  viewBox="0 0 488.85 488.85"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                  class=""
                >
                  <g>
                    <path
                      d="M244.425 98.725c-93.4 0-178.1 51.1-240.6 134.1-5.1 6.8-5.1 16.3 0 23.1 62.5 83.1 147.2 134.2 240.6 134.2s178.1-51.1 240.6-134.1c5.1-6.8 5.1-16.3 0-23.1-62.5-83.1-147.2-134.2-240.6-134.2zm6.7 248.3c-62 3.9-113.2-47.2-109.3-109.3 3.2-51.2 44.7-92.7 95.9-95.9 62-3.9 113.2 47.2 109.3 109.3-3.3 51.1-44.8 92.6-95.9 95.9zm-3.1-47.4c-33.4 2.1-61-25.4-58.8-58.8 1.7-27.6 24.1-49.9 51.7-51.7 33.4-2.1 61 25.4 58.8 58.8-1.8 27.7-24.2 50-51.7 51.7z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
      ${userAccessMenu &&
      userAccessMenu.ActionEdit &&
      userAccessMenu.ActionEdit.featureAccessLevelCode
        ? html`${userAccessMenu.ActionEdit.featureAccessLevelCode ==
          'EditAccess'
            ? html` <div
                onClick=${() => dataView(cInd)}
                title=${translate('stLabel.edit')}
                style="height:18px;margin-right:12px;cursor: pointer;"
                }
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xmlns:svgjs="http://svgjs.com/svgjs"
                  width="15"
                  height="15"
                  x="0"
                  y="0"
                  viewBox="0 0 348.882 348.882"
                  style="enable-background:new 0 0 512 512"
                  xml:space="preserve"
                >
                  <g>
                    <path
                      d="m333.988 11.758-.42-.383A43.363 43.363 0 0 0 304.258 0a43.579 43.579 0 0 0-32.104 14.153L116.803 184.231a14.993 14.993 0 0 0-3.154 5.37l-18.267 54.762c-2.112 6.331-1.052 13.333 2.835 18.729 3.918 5.438 10.23 8.685 16.886 8.685h.001c2.879 0 5.693-.592 8.362-1.76l52.89-23.138a14.985 14.985 0 0 0 5.063-3.626L336.771 73.176c16.166-17.697 14.919-45.247-2.783-61.418zM130.381 234.247l10.719-32.134.904-.99 20.316 18.556-.904.99-31.035 13.578zm184.24-181.304L182.553 197.53l-20.316-18.556L294.305 34.386c2.583-2.828 6.118-4.386 9.954-4.386 3.365 0 6.588 1.252 9.082 3.53l.419.383c5.484 5.009 5.87 13.546.861 19.03z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                    <path
                      d="M303.85 138.388c-8.284 0-15 6.716-15 15v127.347c0 21.034-17.113 38.147-38.147 38.147H68.904c-21.035 0-38.147-17.113-38.147-38.147V100.413c0-21.034 17.113-38.147 38.147-38.147h131.587c8.284 0 15-6.716 15-15s-6.716-15-15-15H68.904C31.327 32.266.757 62.837.757 100.413v180.321c0 37.576 30.571 68.147 68.147 68.147h181.798c37.576 0 68.147-30.571 68.147-68.147V153.388c.001-8.284-6.715-15-14.999-15z"
                      fill="#1A2254"
                      data-original="#000000"
                      class=""
                    ></path>
                  </g>
                </svg>
              </div>`
            : ``}`
        : ``}
    </div> `
  }

  function dataView(index) {
    window.parent.postMessage(
      {
        type: 'ActionList',
        action: 'FormEdit',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }
  function allThreats(index) {
    window.parent.postMessage(
      {
        type: 'ActionList',
        action: 'FormView',
        data: listData[parseInt(index)],
      },
      '*'
    )
  }

  function colFun() {
    console.log('displayedColumns', displayedColumns)
     const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine('configuration', {
      columns: displayedColumns,

      header: headersKeyVal,
      headerSummary: colSummary,
      format: {
        createdTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        lastUpdatedTime: (x) => format(new Date(x), dateFormat+" "+timeFormat),
        dueDate: (x) => format(new Date(x), dateFormat),
        assignmentDate: (x) => format(new Date(x), dateFormat),
        site: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        unit: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        location: (x) => {
          return html`
            <span class="no-translate">
              ${x}
            </span>
        `},
        id: (x) => {
          return html`<div style="cursor: pointer;" onClick=${() => openNewTab(x)}>
            ${x.toString()}
          </div>`;
          // return x.toString()
        },
       // actions: (x, i) => action1(x, i),
      },

      align: {
        id: 'left',
        description: 'left',
        applicationName: 'left',
        assignedToName: 'left',
        site: 'left',
        assignmentDate: 'left',
        dueDate: 'left',
        objectType: 'left',
        objectExternalId: 'left',
        priority: 'left',
       // actions: 'left',
      },
      rows: 25,
      width: {
        id: 200,
        description: 200,
        assignmentDate: 200,
        dueDate: 200,
        objectType: 200,
        objectExternalId: 200,
        priority: 200,
        //actions: 200,
      },
      maxWidth: '100vw',
      layout: 'auto',
    })
  }

  function pushObj(item) {
    if (!paginationCursor.find(({ id }) => id === item.id)) {
      paginationCursor.push(item)
    }
  }
  function rowDroDownChange(e) {
    //setId(setId(e.target.value))
    setLimitCount(e.target.value)
    setId(e.target.value)
    limitSet = e.target.value

    console.log('limitSet', limitSet)
    filterData()
    // setLimit(parseInt(e.data.data))
    // limitSet = parseInt(e.data.data);
    // filterData();
  }
  function getData() {
    //console.log('searchParams.get("token")',searchParams.get("token"))
    console.log('applicationId', applicationId)

    fetch(Constants.NODE_API + '/api/service/list' + Constants.typeAction, {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + token,
        Accept: 'application/json',
        'Content-type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify({
        reportingSite: site ? [site] : [],
        objectType: objectType ? objectType : null,
        // "startDate": startDate,
        // "endDate": endDate
        applicationId: applicationId,
      }),
    })
      .then((res) => res.json())
      .then((result) => {
        console.log('result', result)
        // if (result.items === undefined) {
        //   const temp = document.getElementsByTagName('td')
        //   console.log('temp before', temp[1].childNodes[0].nodeValue)
        //   temp[1].childNodes[0].nodeValue = t('TABLE.NO_RESULTS')
        //   console.log('temp after', temp[1].childNodes[0].nodeValue)
        // }
        var listProcess = result['data']['list' + Constants.typeAction]['items']
        console.log('listProcess', listProcess)
        listData = []

        listProcess.forEach((element) => {
          element.id = element.externalId
          element.applicationName = element.application
            ? element.application.name
            : ''
          element.assignedToName = element.assignedTo
            ? element.assignedTo.user
              ? element.assignedTo.user.firstName +
                ' ' +
                element.assignedTo.user.lastName
              : ''
            : ''
          element['actions'] = ''
          element['site'] = element['reportingSite']
            ? element['reportingSite']['description']
            : ''
          element['unit'] = element['reportingUnit']
            ? element['reportingUnit']['description']
            : ''
          element['reportingLocation'] = element['reportingLocation']
            ? element['reportingLocation']['description']
            : ''
          // element["crafts"] = element["crafts"] ;

          listData.push(element)
        })
        //  main.redefine("data", listData);
        window.parent.postMessage(
          {
            type: 'ActionList',
            action: 'Loading',
            data: true,
          },
          '*'
        )
        setCurrentPage(1)
        initFlag = true
        filterData()
        // empty state()
        colFun()
      })

    // setDataCount(dataSource.length);
    // main.redefine("data", dataSource);
    // colFun();
  }

  // async function getData() {

  //       var  dataSource =[
  //           {
  //             id: 'OBS042423',
  //             observationType: 'Behaviour',
  //             category: 'PPE',
  //             subCategory: 'Eye protection',
  //             recommendations: 'Observed that Michael is using Personal Protective Equipment (PPE) such as hard hats, gloves, and safety glasses',
  //             assignedTo: 'Michael',
  //             dueDate: '04/24/2023',
  //             actions: ''
  //           },
  //           {
  //             id: 'OBS042423',
  //             observationType: 'Behaviour',
  //             category: 'PPE',
  //             subCategory: 'Eye protection',
  //             recommendations: 'Observed that Michael is using Personal Protective Equipment (PPE) such as hard hats, gloves, and safety glasses',
  //             assignedTo: 'Michael',
  //             dueDate: '04/24/2023',
  //             actions: ''
  //           },
  //           {
  //             id: 'OBS042423',
  //             observationType: 'Behaviour',
  //             category: 'PPE',
  //             subCategory: 'Eye protection',
  //             recommendations: 'Observed that Michael is using Personal Protective Equipment (PPE) such as hard hats, gloves, and safety glasses',
  //             assignedTo: 'Michael',
  //             dueDate: '04/24/2023',
  //             actions: ''
  //           },
  //           {
  //             id: 'OBS042423',
  //             observationType: 'Behaviour',
  //             category: 'PPE',
  //             subCategory: 'Eye protection',
  //             recommendations: 'Observed that Michael is using Personal Protective Equipment (PPE) such as hard hats, gloves, and safety glasses',
  //             assignedTo: 'Michael',
  //             dueDate: '04/24/2023',
  //             actions: ''
  //           },

  //         ];
  //       listData = dataSource;
  //     //  main.redefine("data", listData);
  //     setCurrentPage(1);
  //     initFlag = true
  //     filterData();
  //    // colFun();

  // }

  function setting() {
    fetch(
      Constants.NODE_API + '/api/service/listSetting',
      {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          Accept: 'application/json',
          'Content-type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          sites: site
        }),
      }
    )
      .then((res) => res.json())
      .then((result) => {
        if (result["data"] && result["data"]["list" + Constants.typeSetting]["items"].length > 0) {
          var settingData = result["data"]["list" + Constants.typeSetting]["items"][0];
          dateFormat = settingData.dateFormat;
          timeFormat = settingData.timeFormat;
        }
      })
  }
  function filterData() {
    console.log('react filterData')
    console.log('listData', listData)

    var currentList = []
    if (listData.length > 0) {
      if (search) {
        var searchList = listData.filter((obj) => {
          return (
            JSON.stringify(obj).toLowerCase().indexOf(search.toLowerCase()) !==
            -1
          )
        })
        setDataCount(searchList.length)
        currentList = searchList.slice(
          firstPageIndex,
          firstPageIndex + limitSet
        )
      } else {
        setDataCount(listData.length)
        currentList = listData.slice(firstPageIndex, firstPageIndex + limitSet)
      }
    }

    if (initFlag) {
      main.redefine('data', currentList)
      //  cL == [] state var => dom.eleTag('div').text('No Data Found')
      colFun()
    }
  }

  useMemo(() => {
    firstPageIndex = (currentPage - 1) * limit
    const lastPageIndex = firstPageIndex + limit
    // return Asset_JSON.slice(firstPageIndex, lastPageIndex);
    filterData()
  }, [currentPage])

  return (
    <>
    <DynamicTranslationArea
      getAuthToken={getAuthToken}
      dynamicTranslationLoadingState={{ dynamicTranslationLoading, setDynamicTranslationLoading }}
      shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
    >
      <TranslationContextProvider
        getAuthToken={getAuthToken}
      >
      <div ref={viewofSelectionRef} />
      </TranslationContextProvider>
      </DynamicTranslationArea>

      <div className='tableBottom no-translate'>
        <div></div>
        <Pagination
          className='pagination-bar'
       //   assetsType='assets_cognite'
          currentPage={currentPage}
          totalCount={dataCount}
          pageSize={limit}
          onPageChange={(page) => setCurrentPage(page)}
        />
        <div className='numberRows'>
          <span className='numRowsText'>
            {translate('stLabel.paginationRowsPerPage')}: &nbsp;
          </span>
          <select onChange={(e) => rowDroDownChange(e)}>
            <option>10</option>
            <option>20</option>
            <option>50</option>
            <option>100</option>
          </select>
        </div>
      </div>
    </>
  )
}

export default ActionList
