import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs';
import { retry } from 'rxjs';
import { AuthenticationResult, Configuration, InteractionType, PopupRequest, PublicClientApplication, RedirectRequest } from '@azure/msal-browser';
import { environment } from 'src/environments/environment';
import { MSAL_GUARD_CONFIG, MsalGuardConfiguration, MsalService } from '@azure/msal-angular';
import { TokenService } from './token.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class DataService {


  //local
  public NODE_API = environment.NODE_API//"https://localhost:8100";
  public React_API = environment.React_API//"https://localhost:3900";
  public applicationId = environment.applicationId
  public UserAPI = environment.UserAPI
  //dev celanese
  // public NODE_API   = "https://*************:8100";
  // public React_API  = "https://*************:3900";


  project = environment.project;
  appId = environment.appId;
  clientId = environment.clientId;
  tenantId = environment.tenantId;
  cluster = environment.cluster;
  modelId = environment.modelId;
  revisionId = environment.revisionId;

  siteId;
  unitId = environment.unitId;
  public startDate; 
  public endDate;

  limit = 1000
  year = 1;//month

  isTrendsTab = true;

  public baseUrl = `https://${this.cluster}.cognitedata.com`;
  public scopes = [
    `${this.baseUrl}/DATA.VIEW`,
    // `${baseUrl}/DATA.CHANGE`,
    //`${baseUrl}/IDENTITY`,
  ];
  public configuration: Configuration = {
    auth: {
      clientId: this.clientId,
      authority: `https://login.microsoftonline.com/${this.tenantId}`,
    },
  };
  public pca = new PublicClientApplication(this.configuration);

  public userInfo: any;
  public isGlobe = true;
  public Globe: any;

  public modelTypeList = [
    { "id": "globe", "name": "3D Globe" },
    { "id": "d3", "name": "Raster Globe" },
    { "id": "leaflet", "name": "2D View" },
  ]

  public myPoint: any = {
    "geometry": {
      "type": "Point",
      "coordinates": [
        -95.063163,
        29.625066
      ]
    },
    "type": "Feature",
    "properties": {
      "name": "Clear Lake"
    }
  }

  public isRefreshing = false;

  public isProgress = false;

  public auditData:any;

  public defaultIcon = `<svg id="Layer_1" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg"><path d="m152.875 138.5h-47c-4.418 0-8 3.582-8 8v47c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47c0-4.418-3.582-8-8-8zm-8 47h-31v-31h31zm52-38.999c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm0 46.998c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm-44 52.001h-47c-4.418 0-8 3.582-8 8v47c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47c0-4.418-3.582-8-8-8zm-8 47h-31v-31h31zm52-39c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm72.251 46.999c0 4.418-3.582 8-8 8h-56.251c-4.418 0-8-3.582-8-8s3.582-8 8-8h56.251c4.419 0 8 3.582 8 8zm-116.251 52h-47c-4.418 0-8 3.582-8 8v47.001c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47.001c0-4.418-3.582-8-8-8zm-8 47.001h-31v-31.001h31zm124.251-39c0 4.418-3.582 8-8 8h-56.251c-4.418 0-8-3.582-8-8s3.582-8 8-8h56.251c4.419 0 8 3.582 8 8zm-19.5 46.999c0 4.418-3.582 8-8 8h-36.751c-4.418 0-8-3.582-8-8s3.582-8 8-8h36.751c4.418 0 8 3.582 8 8zm223.299 38.318-75.8-131.291v-234.527c0-15.438-12.561-27.999-28-27.999h-69v-18c0-4.418-3.582-8-8-8h-38.149c-6.375-15.581-21.553-26-38.851-26s-32.476 10.419-38.851 26h-38.149c-4.418 0-8 3.582-8 8v18h-69c-15.439 0-28 12.561-28 27.999v368.001c0 15.439 12.561 28 28 28h171.976c.917 4.803 2.644 9.509 5.201 13.938 7.975 13.815 22.26 22.062 38.212 22.062h158.201c15.951 0 30.235-8.247 38.211-22.062 7.975-13.813 7.975-30.307-.001-44.121zm-326.8-403.817h35.949c3.696 0 6.911-2.531 7.777-6.124 2.822-11.703 13.215-19.876 25.274-19.876s22.452 8.173 25.274 19.876c.866 3.593 4.081 6.124 7.777 6.124h35.949v36h-138zm92.178 403.817c-2.6 4.504-4.337 9.294-5.242 14.183h-171.936c-6.617 0-12-5.383-12-12v-368.001c0-6.616 5.383-11.999 12-11.999h69v18c0 4.418 3.582 8 8 8h154c4.418 0 8-3.582 8-8v-18h69c6.617 0 12 5.383 12 11.999v214.821c-7.294-5.184-16.107-8.07-25.511-8.07-15.951 0-30.235 8.247-38.21 22.061zm220.766 36.121c-5.084 8.805-14.188 14.062-24.355 14.062h-158.2c-10.167 0-19.271-5.257-24.354-14.062s-5.083-19.316 0-28.121l79.101-137.007c5.083-8.805 14.188-14.061 24.354-14.061s19.271 5.256 24.354 14.061l79.101 137.007c5.082 8.805 5.082 19.317-.001 28.121zm-103.455-36.363c-11.091 0-20.114 9.023-20.114 20.114s9.023 20.114 20.114 20.114 20.115-9.023 20.115-20.114-9.024-20.114-20.115-20.114zm0 24.229c-2.269 0-4.114-1.846-4.114-4.114s1.846-4.114 4.114-4.114c2.269 0 4.115 1.846 4.115 4.114s-1.846 4.114-4.115 4.114zm-.002-29.855h.003c9.441-.001 17.103-7.392 17.443-16.825l2.39-66.195c.175-4.848-1.512-9.256-4.881-12.749-3.368-3.491-7.712-5.337-12.563-5.337h-4.781c-4.852 0-9.196 1.846-12.564 5.337-3.369 3.494-5.057 7.902-4.88 12.749l2.39 66.194c.34 9.437 8 16.826 17.443 16.826zm-3.437-84.662c.341-.353.556-.444 1.048-.444h4.781c.492 0 .707.092 1.047.444.341.354.425.572.408 1.063l-2.39 66.195c-.028.787-.668 1.403-1.456 1.403h-.001c-.787 0-1.426-.615-1.455-1.403l-2.39-66.196c-.017-.49.067-.708.408-1.062z"/></svg>`

  //configuration component
  public process= [
    {
      id:1,
      name:"Observation",
 
    },
    {
      id:2,
      name:"Field Walk",
  
    },
    {
      id:3,
      name:"Audit",
   
    },
    

  ] 

  public categoryList =[
    {
      id:1,
      name:"Stewardship"
    },
    {
      id:2,
      name:"Quality"
    },
    {
      id:3,
      name:"Reliability"
    },
  
  ]

  public   observationList = [
    {
      id:1,
      name:"Behaviour"
    },
    {
      id:2,
      name:"Hazards"
    },
    {
      id:3,
      name:"Incidents"
    },
  
  ]
  public   auditList = [
    {
      id:1,
      name:"Supplier"
    },
    {
      id:2,
      name:"Customer"
    },
  ]
 public categoriesList = [
    {
      id:1,
      name:"PPE"
    },
    {
      id:2,
      name:"Tool & Equipment"
    },
    {
      id:3,
      name:"Ergonomics"
    },
    {
      id:4,
      name:"Work Environment"
    },

  ]
  public  subCategoriesList = [
    {
      id:1,
      name:"Head"
    },
    {
      id:2,
      name:"Eye-Protection"
    },
    {
      id:3,
      name:"Hearing"
    },
    {
      id:4,
      name:"Body"
    },
    {
      id:5,
      name:"Hands and arms"
    },
    {
      id:6,
      name:"Feet and legs"
    },
    {
      id:7,
      name:"Quality of PPE"
    }
]

  //Dashboard Component
 
  public dashBoardList = [
    {
      name: "DASHBOARD.CARDS.BEHAVIOUR",
      value: "850",
      unit: ""
    },
    {
      name: "DASHBOARD.CARDS.HAZARD",
      value: "0",
      unit: "k"
    },
    {
      name: "DASHBOARD.CARDS.INCIDENT",
      value: "0",
      unit: "k"
    },
    // {
    //   name: "DASHBOARD.CARDS.AUDIT",
    //   value: "1200",
    //   unit: ""
    // },
    {
      name: "DASHBOARD.CARDS.ACTION",
      value: "600",
      unit: ""
    },

  ]
  public subObservation = [
    {
      id:1,
      name:"OBSERVATION.MAIN.CARDS.BEHAVIOUR",
      icon:`<svg height="512" viewBox="0 0 32 32" width="512" xmlns="http://www.w3.org/2000/svg">
       <g id="_x32_0"><path d="m19 23c-1.1025391 0-2-.8974609-2-2v-1.0673828c-.0087891-.0048828-.0175781-.0097656-.0273438-.0146484l-.9619141.5498047c-.3017578.1728516-.6445313.2636719-.9921875.2636719-.7089844 0-1.3710938-.3798828-1.7285156-.9912109l-2.0185547-3.4658203c-.2666016-.4589844-.3408203-1.0146484-.203125-1.5253906.1367188-.5146484.4697266-.9482422.9365234-1.2158203l.9316407-.5332033-.9306641-.5322266c-.4677734-.2685547-.8007813-.7021484-.9384766-1.2216797-.1367188-.5058594-.0625-1.0615234.203125-1.5195313l2.0185547-3.4648438c.359375-.6132813 1.0214844-.9931641 1.7294922-.9931641.3466797 0 .6894531.0908203.9912109.2626953l.9638672.5517578c.0087892-.0058592.0175782-.010742.0263673-.0156248v-1.0673828c0-1.1025391.8974609-2 2-2h.7221069c-1.081543-.4705811-2.2352905-.7924805-3.409668-.9262695-.4298095-.0488282-.8644409-.0737305-1.2922973-.0737305-6.0736084 0-11.0151978 4.9345703-11.0151978 11v.7226563l-1.8622437 3.762207c-.1856079.309082-.1904907.6938477-.0126953 1.0078125.1777345.3134765.5098878.5073242.8703004.5073242h2.0058594v2h1.0800781c.7783203 0 1.5097656-.3032227 2.0605469-.8535156.1953125-.1953125.5117188-.1953125.7070313 0s.1953125.512207 0 .7070313c-.7402345.7392577-1.7226563 1.1464843-2.7675782 1.1464843h-1.079773c.0015869 1.6571045 1.345459 3 3.0032959 3h1.0041504c.5524292 0 1.0002441.4476929 1.0002441 1v4h11.0140381l.0010986-6.9829102c0-.0053711.0029297-.0117188.0029907-.0170898z"/><path d="m29.4964027 14.4003906-1.4663086-.8398438c.0161133-.1879882.0239258-.3745116.0239258-.5605468s-.0078125-.3725586-.0239258-.5605469l1.4663086-.8398438c.2314453-.1323242.4003906-.3515625.4692383-.6088867s.0322266-.5317383-.1015625-.762207l-2.0180664-3.4638672c-.2773438-.4750977-.8857422-.637207-1.3608398-.3647461l-1.4790039.8466797c-.3154297-.2138672-.6533203-.4052734-1.0063477-.5693359v-1.6772461c0-.5522461-.4477539-1-1-1h-4c-.5522461 0-1 .4477539-1 1v1.6772461c-.3530273.1640625-.690918.3554688-1.0063477.5693359l-1.479003-.8466797c-.4760742-.2714844-1.0830078-.1103516-1.3608398.3647461l-2.0180664 3.4638672c-.1337891.2304688-.1704102.5048828-.1015625.762207s.237793.4765625.4692383.6088867l1.4663086.8398438c-.0161133.1879883-.0239258.3745117-.0239258.5605469s.0078125.3725586.0239258.5605469l-1.4663086.8398438c-.2314453.1323242-.4003906.3515625-.4692383.6088867s-.0322266.5317383.1015625.762207l2.0180664 3.4638672c.2773438.4746094.8842773.6367188 1.3608398.3647461l1.479003-.8461914c.3154297.2138672.6533203.4047852 1.0063477.5688477v1.677246c0 .5522461.4477539 1 1 1h4c.5522461 0 1-.4477539 1-1v-1.6772461c.3530273-.1640625.690918-.3549805 1.0063477-.5688477l1.4790039.8461914c.4775391.2729492 1.0844727.1103516 1.3608398-.3647461l2.0180664-3.4638672c.1337891-.2304688.1704102-.5048828.1015625-.762207s-.2377929-.4765625-.4692383-.6088867zm-8.496582 2.5996094c-2.2091064 0-4-1.7908936-4-4 0-2.2091675 1.7908936-4 4-4 2.2091675 0 4 1.7908325 4 4 0 2.2091064-1.7908325 4-4 4z"/><circle cx="21" cy="13" r="3"/>
      </g></svg>`
    },
    {
      id:2,
      name:"OBSERVATION.MAIN.CARDS.HAZARD",
      icon:`
      <?xml version="1.0" encoding="iso-8859-1"?>
      <!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
      <!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
      <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
         width="988.6px" height="988.6px" viewBox="0 0 988.6 988.6" style="enable-background:new 0 0 988.6 988.6;" xml:space="preserve"
        >
      <g>
        <g>
          <path d="M556.8,602.8c-3.8-6.6-12-9.3-19-6.3c-13.3,5.8-28,8.899-43.399,8.899c-15.301,0-29.9-3.199-43.2-8.899
            c-7-3-15.2-0.4-19,6.3l-106,184.7c-4.3,7.399-1.5,16.899,6.1,20.8c25.7,13.2,85.3,38.9,161.2,39.3l0,0c0,0,0.7,0,0.9,0
            c0.3,0,0.899,0,0.899,0l0,0c75.3-0.399,135.3-26.1,161.2-39.3c7.6-3.9,10.5-13.4,6.2-20.8L556.8,602.8z"/>
          <path d="M385.5,483.8c3.6-30.6,19.7-57.8,43-75.5c6.1-4.6,7.8-13,3.9-19.5L325,205c-4.3-7.4-14-9.7-21.1-5
            c-24.3,15.8-76.4,55-114.2,120.2l0,0c-0.3,0.5-0.3,0.5-0.4,0.7c-0.1,0.2-0.4,0.7-0.4,0.7l0,0c-37.3,65.9-44.8,130.7-46.1,159.601
            c-0.4,8.6,6.4,15.699,15,15.699h213C378.2,497,384.6,491.3,385.5,483.8z"/>
          <path d="M799.8,321.6c0,0-0.3-0.5-0.399-0.7c-0.101-0.2-0.101-0.2-0.4-0.7l0,0C761.2,255,709.1,215.8,684.8,200
            c-7.2-4.7-16.8-2.4-21.1,5L556.2,388.8c-3.8,6.6-2.101,14.9,3.899,19.5c23.301,17.7,39.4,44.9,43,75.5
            C604,491.3,610.4,497,618,497h213c8.6,0,15.4-7.101,15-15.7C844.6,452.399,837.2,387.6,799.8,321.6L799.8,321.6z"/>
          <path d="M421,496.5c0.2,26.8,14.8,50.1,36.4,62.7c10.8,6.3,23.5,10,36.899,10c13.5,0,26.2-3.7,37.101-10
            c21.5-12.601,36-35.9,36.3-62.7c0-0.2,0-0.4,0-0.601c0-27.199-14.8-50.899-36.7-63.5c-10.8-6.199-23.3-9.8-36.6-9.8
            c-13.301,0-25.801,3.601-36.601,9.8c-21.899,12.7-36.7,36.4-36.7,63.5C421,496.1,421,496.3,421,496.5z"/>
          <path d="M843.8,144.8c-45.3-45.4-98.2-81-157.1-105.9C625.7,13.1,561,0,494.3,0S362.9,13.1,301.9,38.9
            C243,63.8,190.2,99.4,144.8,144.8c-45.4,45.4-81,98.2-105.9,157.1C13.1,362.8,0,427.6,0,494.3s13.1,131.4,38.9,192.4
            C63.8,745.6,99.4,798.399,144.8,843.8c45.4,45.4,98.2,81,157.101,105.9C362.9,975.5,427.6,988.6,494.3,988.6
            s131.4-13.1,192.4-38.899c58.899-24.9,111.8-60.5,157.1-105.9c45.4-45.4,81-98.2,105.9-157.1c25.8-61,38.899-125.7,38.899-192.4
            s-13.1-131.5-38.8-192.4C924.9,243,889.2,190.2,843.8,144.8z M862.3,649.7c-20.1,47.5-48.899,90.199-85.6,126.899
            s-79.4,65.5-126.9,85.601C600.6,883,548.3,893.6,494.4,893.6c-53.9,0-106.2-10.6-155.4-31.399
            c-47.5-20.101-90.2-48.9-126.9-85.601c-36.7-36.7-65.5-79.399-85.6-126.899c-20.8-49.2-31.4-101.5-31.4-155.4
            s10.6-106.2,31.4-155.4c20.1-47.5,48.9-90.2,85.6-126.9c36.7-36.7,79.4-65.5,126.9-85.6C388.2,105.6,440.5,95,494.4,95
            C548.3,95,600.6,105.6,649.8,126.4c47.5,20.1,90.2,48.9,126.9,85.6c36.7,36.7,65.5,79.4,85.6,126.9
            c20.8,49.2,31.4,101.5,31.4,155.4S883.1,600.5,862.3,649.7z"/>
        </g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      </svg>
      `
    },
    {
      id:3,
      name:"OBSERVATION.MAIN.CARDS.OFFICE_SAFETY",
      icon:`
      <?xml version="1.0" encoding="UTF-8" standalone="no"?>
      <!-- Created with Inkscape (http://www.inkscape.org/) -->
      
      <svg
         version="1.1"
         id="svg3669"
         xml:space="preserve"
         width="682.66669"
         height="682.66669"
         viewBox="0 0 682.66669 682.66669"
         xmlns="http://www.w3.org/2000/svg"
         xmlns:svg="http://www.w3.org/2000/svg"><defs
           id="defs3673"><clipPath
             clipPathUnits="userSpaceOnUse"
             id="clipPath3683"><path
               d="M 0,512 H 512 V 0 H 0 Z"
               id="path3681" /></clipPath></defs><g
           id="g3675"
           transform="matrix(1.3333333,0,0,-1.3333333,0,682.66667)"><g
             id="g3677"><g
               id="g3679"
               clip-path="url(#clipPath3683)"><g
                 id="g3685"
                 transform="translate(356.8154,269.8418)"><path
                   d="m 0,0 v -40.927 c 0,-36.746 -34.523,-82.818 -76.735,-98.488 -9.268,-3.42 -17.453,-5.145 -25.638,-5.145 -8.185,0 -16.37,1.725 -25.637,5.145 -42.212,15.67 -76.707,61.742 -76.707,98.488 V 0"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3687" /></g><g
                 id="g3689"
                 transform="translate(137.1318,269.8418)"><path
                   d="m 0,0 c -8.595,0 -15.698,-6.957 -15.698,-15.552 v -9.851 c 0,-15.962 15.23,-24.937 31.512,-25.872"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3691" /></g><g
                 id="g3693"
                 transform="translate(371.8125,269.8418)"><path
                   d="m 0,0 c 8.594,0 15.64,-6.957 15.64,-15.552 v -9.851 c 0,-15.962 -15.231,-24.937 -31.514,-25.872"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3695" /></g><g
                 id="g3697"
                 transform="translate(239.3584,269.8418)"><path
                   d="m 0,0 h -102.227 c -8.886,0 -16.166,7.279 -16.166,16.195 0,8.888 7.28,16.167 16.166,16.167 h 234.681 c 8.916,0 16.165,-7.279 16.165,-16.167 C 148.619,7.279 141.37,0 132.454,0 H 34.407"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3699" /></g><g
                 id="g3701"
                 transform="translate(224.5664,302.2041)"><path
                   d="m 0,0 9.618,111.818 c 0.409,4.824 3.976,8.799 8.799,8.799 h 11.459 11.459 c 4.853,0 8.391,-3.975 8.8,-8.799 L 59.664,0"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3703" /></g><g
                 id="g3705"
                 transform="translate(140.5518,302.2041)"><path
                   d="m 0,0 c 0,55.748 40.575,102.463 93.662,112.11 m 40.458,0 C 187.236,102.492 227.84,55.748 227.84,0"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3707" /></g><g
                 id="g3709"
                 transform="translate(399.7002,143.6113)"><path
                   d="m 0,0 c 37.594,0 68.054,-30.461 68.054,-68.056 0,-37.594 -30.46,-68.055 -68.054,-68.055 -37.593,0 -68.083,30.461 -68.083,68.055 C -68.083,-30.461 -37.593,0 0,0 Z"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3711" /></g><g
                 id="g3713"
                 transform="translate(375.583,69.9141)"><path
                   d="M 0,0 20.347,-14.968 54.929,20.756"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3715" /></g><g
                 id="g3717"
                 transform="translate(432.6162,213.918)"><path
                   d="m 0,0 c -6.168,-13.944 -13.009,-27.596 -20.199,-41.044 -4.21,-7.805 -10.261,-18.008 -17.687,-29.437 m -62.967,-81.095 c -26.164,-27.509 -54.051,-48.557 -77.292,-48.557 -54.431,0 -134.646,115.736 -157.915,159.089 -36.95,68.846 -54.314,125.968 -52.122,198.964 0.702,23.095 -0.175,23.504 22.948,28.386 60.366,12.746 123.918,49.55 173.029,96.208 11.342,10.757 16.809,10.757 28.151,0 49.081,-46.658 112.663,-83.462 172.999,-96.208 23.153,-4.882 22.276,-5.291 22.978,-28.386 C 33.296,111.789 26.865,72.032 12.629,31.631"
                   style="fill:none;stroke:#000000;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
                   id="path3719" /></g></g></g></g></svg>
      `
    },
    {
      id:4,
      name:"OBSERVATION.MAIN.CARDS.INCIDENT",
      icon:`
      <svg id="Layer_1" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg"><path d="m152.875 138.5h-47c-4.418 0-8 3.582-8 8v47c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47c0-4.418-3.582-8-8-8zm-8 47h-31v-31h31zm52-38.999c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm0 46.998c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm-44 52.001h-47c-4.418 0-8 3.582-8 8v47c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47c0-4.418-3.582-8-8-8zm-8 47h-31v-31h31zm52-39c0-4.418 3.582-8 8-8h119.5c4.418 0 8 3.582 8 8s-3.582 8-8 8h-119.5c-4.418 0-8-3.582-8-8zm72.251 46.999c0 4.418-3.582 8-8 8h-56.251c-4.418 0-8-3.582-8-8s3.582-8 8-8h56.251c4.419 0 8 3.582 8 8zm-116.251 52h-47c-4.418 0-8 3.582-8 8v47.001c0 4.418 3.582 8 8 8h47c4.418 0 8-3.582 8-8v-47.001c0-4.418-3.582-8-8-8zm-8 47.001h-31v-31.001h31zm124.251-39c0 4.418-3.582 8-8 8h-56.251c-4.418 0-8-3.582-8-8s3.582-8 8-8h56.251c4.419 0 8 3.582 8 8zm-19.5 46.999c0 4.418-3.582 8-8 8h-36.751c-4.418 0-8-3.582-8-8s3.582-8 8-8h36.751c4.418 0 8 3.582 8 8zm223.299 38.318-75.8-131.291v-234.527c0-15.438-12.561-27.999-28-27.999h-69v-18c0-4.418-3.582-8-8-8h-38.149c-6.375-15.581-21.553-26-38.851-26s-32.476 10.419-38.851 26h-38.149c-4.418 0-8 3.582-8 8v18h-69c-15.439 0-28 12.561-28 27.999v368.001c0 15.439 12.561 28 28 28h171.976c.917 4.803 2.644 9.509 5.201 13.938 7.975 13.815 22.26 22.062 38.212 22.062h158.201c15.951 0 30.235-8.247 38.211-22.062 7.975-13.813 7.975-30.307-.001-44.121zm-326.8-403.817h35.949c3.696 0 6.911-2.531 7.777-6.124 2.822-11.703 13.215-19.876 25.274-19.876s22.452 8.173 25.274 19.876c.866 3.593 4.081 6.124 7.777 6.124h35.949v36h-138zm92.178 403.817c-2.6 4.504-4.337 9.294-5.242 14.183h-171.936c-6.617 0-12-5.383-12-12v-368.001c0-6.616 5.383-11.999 12-11.999h69v18c0 4.418 3.582 8 8 8h154c4.418 0 8-3.582 8-8v-18h69c6.617 0 12 5.383 12 11.999v214.821c-7.294-5.184-16.107-8.07-25.511-8.07-15.951 0-30.235 8.247-38.21 22.061zm220.766 36.121c-5.084 8.805-14.188 14.062-24.355 14.062h-158.2c-10.167 0-19.271-5.257-24.354-14.062s-5.083-19.316 0-28.121l79.101-137.007c5.083-8.805 14.188-14.061 24.354-14.061s19.271 5.256 24.354 14.061l79.101 137.007c5.082 8.805 5.082 19.317-.001 28.121zm-103.455-36.363c-11.091 0-20.114 9.023-20.114 20.114s9.023 20.114 20.114 20.114 20.115-9.023 20.115-20.114-9.024-20.114-20.115-20.114zm0 24.229c-2.269 0-4.114-1.846-4.114-4.114s1.846-4.114 4.114-4.114c2.269 0 4.115 1.846 4.115 4.114s-1.846 4.114-4.115 4.114zm-.002-29.855h.003c9.441-.001 17.103-7.392 17.443-16.825l2.39-66.195c.175-4.848-1.512-9.256-4.881-12.749-3.368-3.491-7.712-5.337-12.563-5.337h-4.781c-4.852 0-9.196 1.846-12.564 5.337-3.369 3.494-5.057 7.902-4.88 12.749l2.39 66.194c.34 9.437 8 16.826 17.443 16.826zm-3.437-84.662c.341-.353.556-.444 1.048-.444h4.781c.492 0 .707.092 1.047.444.341.354.425.572.408 1.063l-2.39 66.195c-.028.787-.668 1.403-1.456 1.403h-.001c-.787 0-1.426-.615-1.455-1.403l-2.39-66.196c-.017-.49.067-.708.408-1.062z"/></svg>`
    },
    

  ]
  public subAuditList = [
    {
      id:1,
      name:"OBSERVATION.MAIN.CARDS.SUPPLIER_AUDIT",
      icon:`
      <svg height="512" viewBox="0 0 32 32" width="512" xmlns="http://www.w3.org/2000/svg"><g id="_x32_0"><path d="m19 23c-1.1025391 0-2-.8974609-2-2v-1.0673828c-.0087891-.0048828-.0175781-.0097656-.0273438-.0146484l-.9619141.5498047c-.3017578.1728516-.6445313.2636719-.9921875.2636719-.7089844 0-1.3710938-.3798828-1.7285156-.9912109l-2.0185547-3.4658203c-.2666016-.4589844-.3408203-1.0146484-.203125-1.5253906.1367188-.5146484.4697266-.9482422.9365234-1.2158203l.9316407-.5332033-.9306641-.5322266c-.4677734-.2685547-.8007813-.7021484-.9384766-1.2216797-.1367188-.5058594-.0625-1.0615234.203125-1.5195313l2.0185547-3.4648438c.359375-.6132813 1.0214844-.9931641 1.7294922-.9931641.3466797 0 .6894531.0908203.9912109.2626953l.9638672.5517578c.0087892-.0058592.0175782-.010742.0263673-.0156248v-1.0673828c0-1.1025391.8974609-2 2-2h.7221069c-1.081543-.4705811-2.2352905-.7924805-3.409668-.9262695-.4298095-.0488282-.8644409-.0737305-1.2922973-.0737305-6.0736084 0-11.0151978 4.9345703-11.0151978 11v.7226563l-1.8622437 3.762207c-.1856079.309082-.1904907.6938477-.0126953 1.0078125.1777345.3134765.5098878.5073242.8703004.5073242h2.0058594v2h1.0800781c.7783203 0 1.5097656-.3032227 2.0605469-.8535156.1953125-.1953125.5117188-.1953125.7070313 0s.1953125.512207 0 .7070313c-.7402345.7392577-1.7226563 1.1464843-2.7675782 1.1464843h-1.079773c.0015869 1.6571045 1.345459 3 3.0032959 3h1.0041504c.5524292 0 1.0002441.4476929 1.0002441 1v4h11.0140381l.0010986-6.9829102c0-.0053711.0029297-.0117188.0029907-.0170898z"/><path d="m29.4964027 14.4003906-1.4663086-.8398438c.0161133-.1879882.0239258-.3745116.0239258-.5605468s-.0078125-.3725586-.0239258-.5605469l1.4663086-.8398438c.2314453-.1323242.4003906-.3515625.4692383-.6088867s.0322266-.5317383-.1015625-.762207l-2.0180664-3.4638672c-.2773438-.4750977-.8857422-.637207-1.3608398-.3647461l-1.4790039.8466797c-.3154297-.2138672-.6533203-.4052734-1.0063477-.5693359v-1.6772461c0-.5522461-.4477539-1-1-1h-4c-.5522461 0-1 .4477539-1 1v1.6772461c-.3530273.1640625-.690918.3554688-1.0063477.5693359l-1.479003-.8466797c-.4760742-.2714844-1.0830078-.1103516-1.3608398.3647461l-2.0180664 3.4638672c-.1337891.2304688-.1704102.5048828-.1015625.762207s.237793.4765625.4692383.6088867l1.4663086.8398438c-.0161133.1879883-.0239258.3745117-.0239258.5605469s.0078125.3725586.0239258.5605469l-1.4663086.8398438c-.2314453.1323242-.4003906.3515625-.4692383.6088867s-.0322266.5317383.1015625.762207l2.0180664 3.4638672c.2773438.4746094.8842773.6367188 1.3608398.3647461l1.479003-.8461914c.3154297.2138672.6533203.4047852 1.0063477.5688477v1.677246c0 .5522461.4477539 1 1 1h4c.5522461 0 1-.4477539 1-1v-1.6772461c.3530273-.1640625.690918-.3549805 1.0063477-.5688477l1.4790039.8461914c.4775391.2729492 1.0844727.1103516 1.3608398-.3647461l2.0180664-3.4638672c.1337891-.2304688.1704102-.5048828.1015625-.762207s-.2377929-.4765625-.4692383-.6088867zm-8.496582 2.5996094c-2.2091064 0-4-1.7908936-4-4 0-2.2091675 1.7908936-4 4-4 2.2091675 0 4 1.7908325 4 4 0 2.2091064-1.7908325 4-4 4z"/><circle cx="21" cy="13" r="3"/></g></svg>`
    },
    {
      id:2,
      name:"OBSERVATION.MAIN.CARDS.CUSTOMER_AUDIT",
      icon:`
      <?xml version="1.0" encoding="iso-8859-1"?>
      <!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
      <!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
      <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
         width="988.6px" height="988.6px" viewBox="0 0 988.6 988.6" style="enable-background:new 0 0 988.6 988.6;" xml:space="preserve"
        >
      <g>
        <g>
          <path d="M556.8,602.8c-3.8-6.6-12-9.3-19-6.3c-13.3,5.8-28,8.899-43.399,8.899c-15.301,0-29.9-3.199-43.2-8.899
            c-7-3-15.2-0.4-19,6.3l-106,184.7c-4.3,7.399-1.5,16.899,6.1,20.8c25.7,13.2,85.3,38.9,161.2,39.3l0,0c0,0,0.7,0,0.9,0
            c0.3,0,0.899,0,0.899,0l0,0c75.3-0.399,135.3-26.1,161.2-39.3c7.6-3.9,10.5-13.4,6.2-20.8L556.8,602.8z"/>
          <path d="M385.5,483.8c3.6-30.6,19.7-57.8,43-75.5c6.1-4.6,7.8-13,3.9-19.5L325,205c-4.3-7.4-14-9.7-21.1-5
            c-24.3,15.8-76.4,55-114.2,120.2l0,0c-0.3,0.5-0.3,0.5-0.4,0.7c-0.1,0.2-0.4,0.7-0.4,0.7l0,0c-37.3,65.9-44.8,130.7-46.1,159.601
            c-0.4,8.6,6.4,15.699,15,15.699h213C378.2,497,384.6,491.3,385.5,483.8z"/>
          <path d="M799.8,321.6c0,0-0.3-0.5-0.399-0.7c-0.101-0.2-0.101-0.2-0.4-0.7l0,0C761.2,255,709.1,215.8,684.8,200
            c-7.2-4.7-16.8-2.4-21.1,5L556.2,388.8c-3.8,6.6-2.101,14.9,3.899,19.5c23.301,17.7,39.4,44.9,43,75.5
            C604,491.3,610.4,497,618,497h213c8.6,0,15.4-7.101,15-15.7C844.6,452.399,837.2,387.6,799.8,321.6L799.8,321.6z"/>
          <path d="M421,496.5c0.2,26.8,14.8,50.1,36.4,62.7c10.8,6.3,23.5,10,36.899,10c13.5,0,26.2-3.7,37.101-10
            c21.5-12.601,36-35.9,36.3-62.7c0-0.2,0-0.4,0-0.601c0-27.199-14.8-50.899-36.7-63.5c-10.8-6.199-23.3-9.8-36.6-9.8
            c-13.301,0-25.801,3.601-36.601,9.8c-21.899,12.7-36.7,36.4-36.7,63.5C421,496.1,421,496.3,421,496.5z"/>
          <path d="M843.8,144.8c-45.3-45.4-98.2-81-157.1-105.9C625.7,13.1,561,0,494.3,0S362.9,13.1,301.9,38.9
            C243,63.8,190.2,99.4,144.8,144.8c-45.4,45.4-81,98.2-105.9,157.1C13.1,362.8,0,427.6,0,494.3s13.1,131.4,38.9,192.4
            C63.8,745.6,99.4,798.399,144.8,843.8c45.4,45.4,98.2,81,157.101,105.9C362.9,975.5,427.6,988.6,494.3,988.6
            s131.4-13.1,192.4-38.899c58.899-24.9,111.8-60.5,157.1-105.9c45.4-45.4,81-98.2,105.9-157.1c25.8-61,38.899-125.7,38.899-192.4
            s-13.1-131.5-38.8-192.4C924.9,243,889.2,190.2,843.8,144.8z M862.3,649.7c-20.1,47.5-48.899,90.199-85.6,126.899
            s-79.4,65.5-126.9,85.601C600.6,883,548.3,893.6,494.4,893.6c-53.9,0-106.2-10.6-155.4-31.399
            c-47.5-20.101-90.2-48.9-126.9-85.601c-36.7-36.7-65.5-79.399-85.6-126.899c-20.8-49.2-31.4-101.5-31.4-155.4
            s10.6-106.2,31.4-155.4c20.1-47.5,48.9-90.2,85.6-126.9c36.7-36.7,79.4-65.5,126.9-85.6C388.2,105.6,440.5,95,494.4,95
            C548.3,95,600.6,105.6,649.8,126.4c47.5,20.1,90.2,48.9,126.9,85.6c36.7,36.7,65.5,79.4,85.6,126.9
            c20.8,49.2,31.4,101.5,31.4,155.4S883.1,600.5,862.3,649.7z"/>
        </g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      </svg>
      `
    },
   
    

  ]


  public homeCreateObservationList = [
    {
      id:1,
      name:"OBSERVATION.MAIN.CARDS.STEWARDSHIP",
      icon: `<svg id="Layer_1" enable-background="new 0 0 66 66" height="35" viewBox="0 0 66 66" width="35" xmlns="http://www.w3.org/2000/svg">
          <g style="fill:none;stroke:#000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10"><g><path d="m54.5 24.4-.1-16.4c0-1.5 1.1-2.8 2.6-3 .7-.1 1.5.1 2 .4.7.5 1.3 1.2 1.4 2.1l3.5 23.9c.2 1.7-.1 3.4-1.1 4.9l-7.6 11.4c-1.2 1.7-1.9 3.8-2 5.9"/><path d="m41.3 49.3 1.9-11.4c.1-1.3.4-2.6.9-3.8l2.1-5.4c1-2.7 3.6-4.3 6.3-4.3.8 0 1.6.1 2.5.5l-4.3 11.4"/><path d="m40.6 47.4h10v17.6h-10z" transform="matrix(.334 -.943 .943 .334 -22.584 80.388)"/></g><g><path d="m11.5 24.4.2-16.4c0-1.5-1.1-2.8-2.6-3-.8-.2-1.5 0-2 .4-.8.4-1.3 1.2-1.4 2.1l-3.6 23.9c-.2 1.7.1 3.4 1.1 4.9l7.6 11.4c1.2 1.7 1.8 3.8 2 5.9"/><path d="m24.6 49.3-1.9-11.4c-.1-1.3-.4-2.6-.9-3.8l-2-5.4c-1-2.7-3.6-4.3-6.3-4.3-.8 0-1.6.1-2.5.4l4.2 11.4"/><path d="m11.5 51.2h17.6v10h-17.6z" transform="matrix(.944 -.33 .33 .944 -17.434 9.867)"/></g><path d="m26.6 38.8c0-3.5 2.8-6.4 6.4-6.4 3.5 0 6.4 2.8 6.4 6.4z"/><path d="m42.6 19.2-3.3 6.7-6.3 1.6"/><path d="m33 32.5v-11.1"/><path d="m23.2 16.1 2.8 2.8 6.3 1.5"/><path d="m33 21.4v-19.1"/><path d="m27.6 7.7 5.5-5.5 5.2 5.2"/><path d="m48.1 9.4.6 2.7c.7 3-1.1 6-4.1 6.8l-2 .5-.6-2.8c-.7-3 1.1-6 4.1-6.8z"/><path d="m16.7 6.8-.4 1.8c-.7 3 1.1 6 4.1 6.8l2.8.7.4-1.8c.7-3-1.1-6-4.1-6.8z"/>
       </g></svg>`
    },
    {
      id:2,
      name:"OBSERVATION.MAIN.CARDS.QUALITY",
      icon: `<?xml version="1.0"?>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="35" height="35"><g id="Medal"><path d="M57.0244,57.6646,49.7959,38.6592a6.1888,6.1888,0,0,0,1.8252-1.666,6.0858,6.0858,0,0,0,.9756-5.086,4.1352,4.1352,0,0,1,1.3926-4.1884,6.1012,6.1012,0,0,0-.0118-9.4468A4.1349,4.1349,0,0,1,52.6,14.0811a6.0808,6.0808,0,0,0-.9785-5.0748,6.237,6.237,0,0,0-4.6484-2.5571,4.2529,4.2529,0,0,1-3.6358-2.5971A6.189,6.189,0,0,0,39.5107.312a6.2889,6.2889,0,0,0-5.2578.626,4.3222,4.3222,0,0,1-4.5068,0A6.2886,6.2886,0,0,0,24.4883.312a6.19,6.19,0,0,0-3.8252,3.54A4.2561,4.2561,0,0,1,17.0176,6.45a6.23,6.23,0,0,0-4.6387,2.5571,6.0858,6.0858,0,0,0-.9756,5.086,4.1355,4.1355,0,0,1-1.3926,4.1885,6.1011,6.1011,0,0,0,.0118,9.4467A4.1349,4.1349,0,0,1,11.4,31.9189a6.0808,6.0808,0,0,0,.9785,5.0748,6.1887,6.1887,0,0,0,1.8254,1.6649L6.9756,57.6646A.9994.9994,0,0,0,8.248,58.9609l7.8731-2.83,4.001,7.3471A.9994.9994,0,0,0,21,64c.0225,0,.0449-.001.0674-.0024a1,1,0,0,0,.8672-.6421l6.7675-17.784a6.2959,6.2959,0,0,0,1.045-.51,4.3226,4.3226,0,0,1,4.5058,0l.001.0005a6.2859,6.2859,0,0,0,1.0415.5026l6.77,17.79a1,1,0,0,0,.8672.6421c.0225.0014.0449.0024.0674.0024a.9994.9994,0,0,0,.8779-.522l4.001-7.3471,7.8731,2.83a.9994.9994,0,0,0,1.2724-1.2963ZM20.8389,60.6123l-3.3711-6.19a1.0005,1.0005,0,0,0-1.2158-.4629L9.6172,56.3438l6.4441-16.9422a6.38,6.38,0,0,0,.966.1492,4.2529,4.2529,0,0,1,3.6358,2.5971,6.189,6.189,0,0,0,3.8262,3.54,6.3213,6.3213,0,0,0,1.9124.3058ZM28.6953,43.36a4.286,4.286,0,0,1-3.5869.4262,4.193,4.193,0,0,1-2.5938-2.3955,6.2686,6.2686,0,0,0-5.3613-3.8359,4.2372,4.2372,0,0,1-3.1553-1.7359,4.0894,4.0894,0,0,1-.6552-3.42,6.1465,6.1465,0,0,0-2.0586-6.2221,4.1017,4.1017,0,0,1,.0117-6.3628A6.1458,6.1458,0,0,0,13.34,13.59a4.084,4.084,0,0,1,.6582-3.4091,4.2439,4.2439,0,0,1,3.1661-1.7364,6.2651,6.2651,0,0,0,5.3505-3.8354,4.1886,4.1886,0,0,1,2.5938-2.395,4.291,4.291,0,0,1,3.5879.4262,6.34,6.34,0,0,0,6.6084,0,4.29,4.29,0,0,1,3.5869-.4262,4.193,4.193,0,0,1,2.5938,2.3955,6.2686,6.2686,0,0,0,5.3613,3.8359,4.2376,4.2376,0,0,1,3.1553,1.7359,4.0891,4.0891,0,0,1,.6552,3.42,6.1465,6.1465,0,0,0,2.0586,6.2221,4.1017,4.1017,0,0,1-.0117,6.3628A6.1458,6.1458,0,0,0,50.66,32.41a4.0837,4.0837,0,0,1-.6582,3.4091,4.2435,4.2435,0,0,1-3.1661,1.7364,6.2651,6.2651,0,0,0-5.3505,3.8354,4.1886,4.1886,0,0,1-2.5938,2.395,4.2823,4.2823,0,0,1-3.5879-.4262A6.34,6.34,0,0,0,28.6953,43.36ZM47.748,53.959a1,1,0,0,0-1.2158.4629l-3.3711,6.19L37.5991,45.996a6.325,6.325,0,0,0,1.9116-.3075,6.1926,6.1926,0,0,0,3.8272-3.5406A4.2526,4.2526,0,0,1,46.9824,39.55a6.3229,6.3229,0,0,0,.9567-.1478l6.4437,16.9413Z"/><path d="M47,23A15,15,0,1,0,32,38,15.0167,15.0167,0,0,0,47,23ZM32,36A13,13,0,1,1,45,23,13.0147,13.0147,0,0,1,32,36Z"/><path d="M39.9531,19.6841l-4.876-.7085-2.1806-4.418a1.0409,1.0409,0,0,0-1.793,0l-2.1806,4.418-4.876.7085a1,1,0,0,0-.5547,1.706l3.5293,3.439-.833,4.856a1,1,0,0,0,1.45,1.0542L32,28.4468l4.3613,2.2925a1,1,0,0,0,1.45-1.0542l-.833-4.856,3.5293-3.439a1,1,0,0,0-.5547-1.706Zm-4.747,4.08a1.0012,1.0012,0,0,0-.2872.8852l.5791,3.377-3.0332-1.5943a.9991.9991,0,0,0-.93,0L28.502,28.0259l.5791-3.377a1.0012,1.0012,0,0,0-.2872-.8852L26.34,21.3721l3.3907-.4927a.999.999,0,0,0,.7529-.5474L32,17.26l1.5166,3.0722a.999.999,0,0,0,.7529.5474l3.3907.4927Z"/></g></svg>
      `
    },
    {
      id:3,
      name:"OBSERVATION.MAIN.CARDS.RELIABILITY",
      icon: `
      <?xml version="1.0" encoding="iso-8859-1"?>
      <!-- Generator: Adobe Illustrator 19.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
      <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
         viewBox="0 0 512.003 512.003" style="enable-background:new 0 0 512.003 512.003;" xml:space="preserve">
      <g>
        <g>
          <path d="M509.605,171.075l-99.3-99.301c-3.193-3.194-8.37-3.194-11.565,0l-49.65,49.65c-1.533,1.533-2.394,3.613-2.394,5.782
            c0,2.169,0.861,4.249,2.394,5.782l4.953,4.953l-11.382,11.38c-7.389,7.386-18.854,9.402-28.528,5.011
            c-9.07-4.117-19.153-6.292-29.161-6.292c-11.883,0-23.496,2.983-33.814,8.633c-4.303-1.06-8.719-1.603-13.179-1.603
            c-6.45,0-12.785,1.113-18.829,3.31c-9.651,3.506-19.996,1.333-27.003-5.672L171.71,132.27l2.434-2.434
            c1.533-1.533,2.394-3.613,2.394-5.782c0-2.169-0.861-4.249-2.394-5.782l-49.65-49.65c-3.195-3.194-8.371-3.194-11.565,0
            L2.395,179.156c-3.193,3.194-3.193,8.371,0,11.564l49.649,49.65c1.534,1.534,3.613,2.395,5.783,2.395s4.248-0.861,5.783-2.395
            l2.961-2.961l14.414,14.414c3.637,3.637,6.048,8.178,6.971,13.131c4.786,25.683,17.086,49.032,35.57,67.526l2.715,2.715
            c-5.214,5.491-8.082,12.645-8.082,20.245c0,7.861,3.062,15.252,8.62,20.811c5.738,5.738,13.273,8.606,20.811,8.606
            c0.491,0,0.98-0.013,1.471-0.038c-0.398,8.019,2.458,16.17,8.568,22.282c5.559,5.559,12.95,8.62,20.811,8.62
            c0.219,0,0.437-0.011,0.656-0.016c-0.168,7.749,2.691,15.552,8.591,21.453c5.559,5.56,12.95,8.62,20.812,8.62
            c7.861,0,15.251-3.062,20.811-8.62c0.468-0.468,0.909-0.952,1.34-1.442c2.895,1.009,5.957,1.546,9.052,1.546
            c7.353,0,14.261-2.865,19.441-8.062c2.757-2.756,4.849-5.998,6.211-9.529l0.837,0.837c5.359,5.359,12.398,8.039,19.437,8.039
            c7.039,0,14.078-2.68,19.437-8.039c2.848-2.848,4.988-6.211,6.344-9.878c4.797,3.489,10.476,5.236,16.158,5.236
            c7.039,0,14.082-2.679,19.446-8.036c5.191-5.191,8.05-12.097,8.05-19.445c0-2.22-0.266-4.397-0.773-6.502
            c5.237-1.064,10.049-3.635,13.91-7.501c5.191-5.191,8.05-12.094,8.05-19.437c0-5.785-1.782-11.292-5.073-15.91l6.56-6.56
            c18.699-18.708,31.052-42.35,35.725-68.371c0.783-4.357,2.941-8.404,6.243-11.707l24.398-24.398l4.289,4.289
            c1.597,1.597,3.69,2.395,5.783,2.395c2.092,0,4.186-0.798,5.783-2.395l49.65-49.65c1.533-1.533,2.394-3.613,2.394-5.782
            S511.138,172.609,509.605,171.075z M57.827,223.025l-38.086-38.086L118.71,85.97l38.087,38.086L57.827,223.025z M156.836,364.689
            c-5.097,5.096-13.392,5.098-18.493,0c-2.47-2.471-3.83-5.754-3.83-9.247c0-3.492,1.361-6.776,3.831-9.246
            c2.549-2.549,5.896-3.824,9.245-3.824c3.348,0,6.698,1.275,9.246,3.824C161.933,351.294,161.933,359.59,156.836,364.689z
             M187.684,395.537c-2.468,2.471-5.751,3.83-9.246,3.83c-3.492,0-6.776-1.361-9.245-3.83c-5.099-5.098-5.099-13.394,0-18.493
            c2.549-2.549,5.896-3.824,9.246-3.824c3.347,0,6.697,1.275,9.245,3.824C192.784,382.142,192.784,390.439,187.684,395.537z
             M217.742,425.594c-2.47,2.47-5.753,3.83-9.245,3.83c-3.493,0-6.777-1.361-9.246-3.83c-5.099-5.098-5.099-13.394,0-18.493
            c2.549-2.549,5.896-3.824,9.246-3.824c3.347,0,6.697,1.275,9.245,3.824C222.841,412.2,222.841,420.496,217.742,425.594z
             M356.63,362.822c-2.102,2.104-4.897,3.263-7.869,3.263s-5.767-1.159-7.873-3.268l-79.33-79.312
            c-3.196-3.193-8.372-3.192-11.565,0.002c-3.192,3.193-3.191,8.371,0.002,11.564l85.451,85.442c2.103,2.102,3.26,4.898,3.26,7.872
            c0,2.98-1.158,5.779-3.257,7.878c-4.347,4.343-11.416,4.344-15.756,0.003l-14.416-14.416c-0.08-0.083-0.158-0.167-0.241-0.249
            c-0.024-0.024-0.051-0.045-0.076-0.069l-66.267-66.267c-3.195-3.193-8.371-3.193-11.565,0c-3.194,3.193-3.194,8.371,0,11.564
            l66.48,66.479c2.032,2.083,3.151,4.839,3.151,7.763c0,2.974-1.159,5.77-3.261,7.872c-4.338,4.341-11.401,4.341-15.743,0
            l-72.085-72.086c-3.195-3.194-8.371-3.194-11.565,0c-3.194,3.193-3.194,8.371,0,11.564l53.434,53.435
            c0.015,0.015,0.027,0.032,0.043,0.046c2.101,2.097,3.257,4.888,3.257,7.859c0,2.973-1.158,5.769-3.269,7.88
            c-2.099,2.104-4.893,3.263-7.87,3.263c-0.719,0-1.422-0.074-2.11-0.204c1.323-8.913-1.436-18.32-8.282-25.167
            c-5.559-5.558-12.95-8.62-20.811-8.62c-0.219,0-0.437,0.011-0.656,0.016c0.168-7.749-2.69-15.552-8.591-21.453
            c-5.56-5.558-12.95-8.62-20.812-8.62c-0.492,0-0.981,0.012-1.469,0.036c0.393-8.014-2.463-16.158-8.57-22.266
            c-7.434-7.433-17.884-10.044-27.444-7.847l-5.864-5.864c-16.14-16.147-26.878-36.535-31.057-58.96
            c-1.531-8.213-5.502-15.717-11.483-21.699l-14.415-14.415l82.01-82.01l20.438,20.438c7.856,7.856,18.552,12.06,29.507,12.06
            c4.906,0,9.867-0.844,14.646-2.581c2.318-0.843,4.715-1.448,7.144-1.832l-50.632,50.633c-6.195,6.194-9.607,14.43-9.607,23.191
            c0,8.76,3.412,16.996,9.606,23.19c6.394,6.394,14.79,9.59,23.19,9.589c8.398,0,16.797-3.198,23.192-9.589l25.43-25.43l6.883,6.888
            c0.002,0.002,0.003,0.003,0.005,0.005l0.286,0.286l0.275,0.275c0.001,0.001,0.003,0.003,0.005,0.004l0.005,0.005
            c0.079,0.078,0.156,0.152,0.233,0.226l95.881,95.881c2.103,2.102,3.26,4.898,3.26,7.872
            C359.893,357.921,358.736,360.717,356.63,362.822z M408.137,240.834c-5.674,5.675-9.4,12.723-10.774,20.381
            c-4.08,22.72-14.867,43.364-31.193,59.698l-6.284,6.285l-51.731-51.731c1.124,0.083,2.253,0.138,3.39,0.138
            c5.238,0,10.598-0.918,15.934-3.101c4.18-1.71,6.182-6.485,4.472-10.664c-1.71-4.179-6.481-6.182-10.664-4.472
            c-21.046,8.611-46.278-15.12-49.087-17.855c-0.047-0.046-0.094-0.091-0.142-0.135l-0.29-0.29
            c-0.001-0.001-0.002-0.001-0.003-0.002l-0.253-0.252c-0.001-0.001-0.003-0.003-0.005-0.004l-6.884-6.889l7.806-7.807
            c3.195-3.194,3.195-8.371,0.001-11.565c-3.194-3.192-8.371-3.193-11.564,0l-13.57,13.57c-0.005,0.005-0.011,0.01-0.016,0.015
            c-0.005,0.005-0.01,0.011-0.015,0.016l-31.2,31.2c-6.412,6.411-16.842,6.409-23.252,0c-3.105-3.105-4.815-7.234-4.815-11.626
            c0-4.392,1.71-8.521,4.816-11.626l53.852-53.854c2.996-2.995,6.326-5.63,9.905-7.837c8.503-5.256,18.324-8.034,28.401-8.034
            c7.693,0,15.439,1.67,22.403,4.831c15.842,7.188,34.671,3.839,46.851-8.338l11.383-11.381l66.929,66.929L408.137,240.834z
             M454.172,214.944l-87.736-87.736l38.087-38.086l87.736,87.736L454.172,214.944z"/>
        </g>
      </g>
      <g>
        <g>
          <circle cx="462.41" cy="183.11" r="8.177"/>
        </g>
      </g>
      <g>
        <g>
          <circle cx="53.567" cy="191.189" r="8.177"/>
        </g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      <g>
      </g>
      </svg>
      `
    },
    

  ]

  public homeTitle = "What would you like to do today?"
  public homeObservationList = [
    {
      id:1,
      name:"OBSERVATION.MAIN.CARDS.OBSERVATION",
      icon: `
      <svg id="Capa_1" enable-background="new 0 0 511.797 511.797" height="35" viewBox="0 0 511.797 511.797" width="35" xmlns="http://www.w3.org/2000/svg">
        <g><path d="m394.839 205.338 116.53-43.304-60.213-162.034-116.53 43.304 7.337 19.743-231.311 85.957 6.389 17.192-49.966 18.568-11.06-29.761 14.325-5.323-10.45-28.121-56.773 21.096 10.45 28.121 14.325-5.323 11.06 29.761-38.524 14.316 32.762 88.163 116.611-43.334 6.389 17.192 54.799-20.364c.212 13.237 6.163 25.097 15.474 33.204l-85.354 216.399 27.907 11.008 32.77-83.081h108.371l32.77 83.081 27.907-11.008-85.354-216.398c9.481-8.256 15.492-20.401 15.492-33.931 0-10.956-3.94-21.007-10.472-28.819l97.003-36.047zm-153.868 45.122c0-8.271 6.729-15 15-15s15 6.729 15 15-6.729 15-15 15-15-6.729-15-15zm57.352 148.257h-84.705l40.744-103.297c.535.019 1.069.041 1.609.041s1.074-.022 1.609-.041zm135.162-360.146 39.313 105.792-60.288 22.403-39.313-105.791zm-294.133 187.667-88.49 32.883-11.862-31.92 88.49-32.883zm237.7-68.765-203.19 75.506-24.639-66.305 203.19-75.506z"/>
      </g></svg>`
    },
    {
      id:2,
      name:"OBSERVATION.MAIN.CARDS.FIELD_WALK",
      icon: `<svg id="Layer_3" enable-background="new 0 0 60 60" height="35" viewBox="0 0 60 60" width="35" xmlns="http://www.w3.org/2000/svg"><ellipse cx="34.131" cy="13.505" rx="4.916" ry="4.916" transform="matrix(.707 -.707 .707 .707 .447 28.09)"/>
       <path d="m17.8845215 50.1693115 3.6141968-.770813c3.2630615-.7023315 5.9951172-2.946228 7.305542-6.0122681l.8649902-2.0297852 2.5864258.8821411c1.7814331.5995483 3.1602783 2.0383301 3.6827393 3.845459l.9677734 3.2887573c.3511963 1.2247314 1.4645386 2.0383301 2.7064209 2.0383301.1113281 0 .2226563-.0085449.3425293-.0256958.8136597-.0941772 1.541626-.5395508 2.0040894-1.2161255.4624634-.6766357.5995483-1.5159302.3939819-2.312439l-1.0534668-3.9995728c-.6679688-2.5522461-2.2010498-4.8389282-4.2907715-6.4404907l-3.0917969-2.3638306 1.1819458-6.534668c2.7063599 1.6786499 6.1321411 1.7300415 8.9327393.0427856l.5480957-.3253784c1.1047974-.6680908 1.4645386-2.1154785.7965088-3.2202759-.6251831-1.0448608-1.9698486-1.4302979-3.0575562-.8907471-1.4216309.7023315-3.1688232.3168945-4.1536865-.9249268l-.4882202-.6166382c-1.0877075-1.3703003-2.6464233-2.2695313-4.3764038-2.5265503l-3.7084351-.5480957c-1.284668-.1884155-2.6035767-.0170898-3.7940674.513855l-.5995483.256958c-4.4363403 1.9613037-7.2197876 6.526123-6.920105 11.3650513.0343018.6166382.3340454 1.1904907.8136597 1.5844116.4796143.385376 1.0962524.5653076 1.7128906.4796143 1.0620117-.1541748 1.8670654-1.0448608 1.9098511-2.1239624.0770874-2.1582642 1.2247925-4.1195068 2.9975586-5.2672119l-3.2886963 15.7244266-6.2434692 2.4494019c-1.1477051.4453735-1.8927612 1.53302-1.8927612 2.7663574 0 .899231.4024658 1.7471313 1.1047974 2.3123779.7022704.5652466 1.6101073.7879028 2.4922484.5995483z"/>
      </svg>`
    },
    {
      id:3,
      name:"OBSERVATION.MAIN.CARDS.AUDIT",
      icon: `<?xml version="1.0" encoding="UTF-8"?>
       <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 512.002 512.002" style="enable-background:new 0 0 512.002 512.002;" xml:space="preserve" width="35" height="35">
    <g>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" d="&#10;&#9;&#9;M127.384,415.718l-73.874,78.75c-9.567,10.198-26.087,9.889-35.975,0c-9.888-9.889-10.204-26.403,0-35.975l79.017-74.123"/>
	
		<ellipse transform="matrix(0.7071 -0.7071 0.7071 0.7071 -170.0409 229.4915)" style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" cx="192" cy="320.003" rx="112.496" ry="112.496"/>
	
		<polyline style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" points="&#10;&#9;&#9;152.002,209.836 152.002,10 412.002,10 502.002,100 502.002,480 152.002,480 152.002,430.171 &#9;"/>
	
		<polyline style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" points="&#10;&#9;&#9;497.001,100 412.002,100 412.002,15 &#9;"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="207.002" y1="55" x2="367.002" y2="55"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="207.002" y1="100" x2="287.002" y2="100"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="207.002" y1="162.501" x2="457.002" y2="162.501"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="302.201" y1="207.5" x2="457.002" y2="207.5"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="347.888" y1="297.5" x2="457.002" y2="297.5"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="347.889" y1="342.5" x2="402.445" y2="342.5"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="334.331" y1="387.499" x2="457.002" y2="387.499"/>
	
		<line style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" x1="302.209" y1="432.499" x2="332.002" y2="432.499"/>
	
		<polyline style="fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#000000;stroke-width:20.0001;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.9256;" points="&#10;&#9;&#9;225.751,297.503 180.75,342.504 158.25,320.004 &#9;"/>
</g>
</svg>
`
    },
    

  ]
  
  //Workflow Component
  public workFlowList = [
    {
      id:"WFID12202021",
      name:"WORKFLOW.MAIN_WORKFLOW.CARDS.SCHEDULE_OVERDUE",
      createdAt:"05/19/2023"
    },
    {
      id:"WFID12202022",
      name:"WORKFLOW.MAIN_WORKFLOW.CARDS.OBSERVATION_POINTS",
      createdAt:"05/19/2023"
    },
    {
      id:"WFID12202023",
      name:"WORKFLOW.MAIN_WORKFLOW.CARDS.AUDIT_OVERDUE",
      createdAt:"05/19/2023"
    },
  ]
  vendorPage1: string = "/observations/quality-assessment";
  vendorPage2: string = "/observations/upload-doc";
  vendorPage3: string = "/observations/completed";

 
  appMenuCode = {
    dashboardMenu:"APP-OFWA_DASHBOARD",
    observation:"APP-OFWA_OBSERVATION_OBSERVATION",
    createObservation:"APP-OFWA_OBSERVATION_CREATE_OBSERVATION",
    observationView:"APP-OFWA_OBSERVATION_VIEW",
    metricsbyperson:"APP-OFWA_OBSERVATION_METICSBYPERSON",
    observationEdit:"APP-OFWA_OBSERVATION_EDIT",
    observationView3d:"APP-OFWA_OBSERVATION_VIEW_3D_ASSETS",
    observationCreateAction:"APP-OFWA_OBSERVATION_CREATE_ACTION",
    observationShare:"APP-OFWA_OBSERVATION_SHARE",
    observationDelete:"APP-OFWA_OBSERVATION_DELETE",

    homeMenu:"APP-OFWA_HOME_HOME",
    homeObservation:"APP-OFWA_HOME_OBSERVATION",
    homeFieldWalk:"APP-OFWA_HOME_FIELD_WALK",
    homeAudit:"APP-OFWA_HOME_AUDIT",

    fieldWalk:"APP-OFWA_FIELD_WALK_FIELD_WALK",
    fieldWalkCreate:"APP-OFWA_FIELD_WALK_CREATE_FIELDWALK",
    fieldWalkView:"APP-OFWA_FIELD_WALK_VIEW",
    fieldWalkEdit:"APP-OFWA_FIELD_WALK_EDIT",
    fieldWalk3dAssest: "APP-OFWA_FIELD_WALK_VIEW_3D_ASSETS",
    fieldWalkCreateAction:"APP-OFWA_FIELD_WALK_CREATE_ACTION",
    fieldWalkDelete:"APP-OFWA_FIELD_WALK_DELETE",
    fieldWalkShare:"APP-OFWA_FIELD_WALK_SHARE",
    fieldWalkHistory : "APP-OFWA_FIELD_WALK_HISTORY",

    auditMenu:"APP-OFWA_AUDIT_AUDIT",
    auditSendAudit:"APP-OFWA_AUDIT_SEND_AUDIT",
    auditViewAudit:"APP-OFWA_AUDIT_VIEW_AUDIT",  
    auditEditAudit:"APP-OFWA_AUDIT_EDIT_AUDIT", 
    auditScoreCard:"APP-OFWA_AUDIT_SCORECARD",
    auditCreateAction: "APP-OFWA_AUDIT_CREATE_ACTION",
    auditSummary: "APP-OFWA_AUDIT_SUMMARY",
  
    scheduleMenu:"APP-OFWA_SCHEDULE_CREATE_SCHEDULE",// "APP-OFWA_SCHEDULE_SCHEDULE_SCHEDULE",
   // scheduleCreate: "APP-OFWA_SCHEDULE_CREATE_SCHEDULE",
    scheduleCreateObservation:"APP-OFWA_SCHEDULE_CREATE_OBSERVATIONSCHEDULE",//"APP-OFWA_SCHEDULE_SCHEDULE_OBSERVATIONSCHEDULE",
    scheduleCreateFieldWalk:"APP-OFWA_SCHEDULE_CREATE_FIELD_WALK_SCHEDULE",// "APP-OFWA_SCHEDULE_SCHEDULE_FIELD_WALK_SCHEDULE",
    scheduleAuditSchedule:"APP-OFWA_SCHEDULE_CREATE_AUDITSCHEDULE",//"APP-OFWA_SCHEDULE_SCHEDULE_AUDITSCHEDULE",
    scheduleTracker:"APP-OFWA_SCHEDULE_TRACKER",
    scheduleView:"APP-OFWA_SCHEDULE_VIEW",
    scheduleEdit:"APP-OFWA_SCHEDULE_EDIT",

    configuration:"APP-OFWA_CONFIGURATION_CONFIGURATION",
    settings:"APP-OFWA_SETTINGS_SETTINGS",
    configurationList:"APP-OFWA_CONFIGURATION_CONFIGURATION_LIST",
    configurationQuestionList:"APP-OFWA_CONFIGURATION_QUESTION_LIST",
    configurationAddQuestion:"APP-OFWA_CONFIGURATION_ADD_QUESTION",

    actionMenu: "APP-OFWA_ACTION_ACTION",
    actionView:"APP-OFWA_ACTION_VIEW",
    actionEdit: "APP-OFWA_ACTION_EDIT",

    infieldMenu: "APP-OFWA_INFIELD_INFIELD",
    
    dateAndTime : "APP-OFWA_SETTINGS_DATE_AND_TIME",
    crafts : "APP-OFWA_SETTINGS_CRAFTS",
    contractors : "APP-OFWA_SETTINGS_CONTRACTORS",
    list: "APP-OFWA_LIST_LIST",
    processConfiguration : "APP-OFWA_CONFIGURATION_PROCESS_CONFIGURATION",
    checklistQuestions : "APP-OFWA_CONFIGURATION_CHECKLIST_QUESTIONS",
    fieldConfiguration : "APP-OFWA_CONFIGURATION_FIELD_CONFIGURATION",
    dashboardConfiguration: "APP-OFWA_CONFIGURATION_DASHBOARD_CONFIGURATION",
    columnConfiguration: "APP-OFWA_CONFIGURATION_COLUMN_CONFIGURATION",

    hazards : "APP-OFWA_LIST_HAZARDS_HAZARDS",

    hazardCreate : "APP-OFWA_LIST_HAZARDS_CREATE_HAZARD",
    hazardEdit : "APP-OFWA_LIST_HAZARDS_EDIT",
    hazardView : "APP-OFWA_LIST_HAZARDS_VIEW",
    hazardCreateAction : "APP-OFWA_LIST_HAZARDS_CREATE_ACTION",
    hazardDelete : "APP-OFWA_LIST_HAZARDS_DELETE",
    hazardHistory : "APP-OFWA_LIST_HAZARDS_HISTORY",
    observationHistory : "APP-OFWA_OBSERVATION_HISTORY"
    
  }


  constructor(private router: Router, private tokenService: TokenService, private httpClient: HttpClient, @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private authService: MsalService) { }

  handleError(error: HttpErrorResponse) {
    let errorMessage = 'Unknown error!';
    if (error.error instanceof ErrorEvent) {
      // Client-side errors
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side errors
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    return throwError(errorMessage);
  }

//   // Step 1
// const httpHeaders: HttpHeaders = new HttpHeaders({
//   Content-Type: 'Bearer JWT-token'
// });
// // Step 2
// this.http.post(url, body, { headers: httpHeaders });


  public get(url) {
    const httpHeaders: HttpHeaders = new HttpHeaders({
      'Accept': 'image/png',
      "responseType": "blob",
      'X-Cdp-App': "OFWA",
    });
    return this.httpClient.get(url,{ headers: httpHeaders });
  }

  getImage(url): Observable<Blob> {
    const httpHeaders: HttpHeaders = new HttpHeaders({
      'Accept': 'image/png',
      'X-Cdp-App': "OFWA",
    });
    return this.httpClient.get(url, {headers:httpHeaders,responseType: "blob"});
  }
  public getData(url: string) {
    return this.httpClient.get(url).pipe(retry(1), catchError(this.handleError));
  }
  public postData(postObj, url: string) {
    return this.httpClient.post(url, postObj).pipe(retry(1), catchError(this.handleError));
  }
  public patchData(postObj, url: string) {
    return this.httpClient.patch(url, postObj).pipe(retry(1), catchError(this.handleError));
  }
  public deleteData(postObj, url: string) {
    return this.httpClient.delete(url, postObj).pipe(retry(1), catchError(this.handleError));
  }

  public getJsonFile(filePath: string): Observable<any> {
    return this.httpClient.get(filePath);
  }

  public toLocaleString(str: any) {

    return parseInt(str).toLocaleString("en-US")
  }

  public login() {
    console.log("login-")
    var _this = this;
    _this.authService.acquireTokenSilent({
      scopes: _this.scopes,
    }).subscribe((response) => {
      localStorage.setItem('dataSourceTkn', response.accessToken);
      _this.tokenService.saveToken(response.accessToken);
      _this.tokenService.saveRefreshToken(response.accessToken);
      localStorage.setItem('dataSourceIDTkn', response.idToken);
      _this.tokenService.saveIDToken(response.idToken);
      _this.tokenService.saveIDRefreshToken(response.idToken);

      if (this.msalGuardConfig.authRequest) {
        this.authService.loginRedirect({ ...this.msalGuardConfig.authRequest } as RedirectRequest);
      } else {
        this.authService.loginRedirect();
      }
    });
  }

  goHome() {
    var _this = this;
    _this.router.navigate(["observations/observation"]);
  }


}
