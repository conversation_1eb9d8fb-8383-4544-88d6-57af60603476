import { ChangeDetectorRef, Component, <PERSON>Zone, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-audit-plan-scorecard',
  templateUrl: './audit-plan-scorecard.component.html',
  styleUrls: ['./audit-plan-scorecard.component.scss']
})
export class AuditPlanScorecardComponent implements OnInit {


  siteControl: FormControl = new FormControl("");
  filteredSiteOptions: Observable<any[]>;
  labels = {}

  unitControl: FormControl = new FormControl("");
  filteredUnitOptions: Observable<any[]>;

  searchControl: FormControl = new FormControl("");

  scoreCardArr: any = [];
  scoreArrayTotal: any = {
    satisfactory: 0,
    ofi: 0,
    unSatisfactory: 0
  };
  // summaryArr = [
  //   {
  //     summaryName: "No. Audit Questions(total)",
  //     score: "56"
  //   },
  //   {
  //     summaryName: "No. of Questions audited",
  //     score: "56"
  //   },
  //   {
  //     summaryName: "Unsatisfactory weightage for score",
  //     score: "40"
  //   },
  //   {
  //     summaryName: "OFI weightage for score",
  //     score: "8"
  //   },
  //   {
  //     summaryName: "Satisfactory weightage",
  //     score: "10"
  //   },
  // ]
  loaderFlag: boolean;
  auditData: any;
  observation: any;
  selectedSite: any;

  checklistCount:any = 0;
  auditChecklistCount: any =0;
  unSatisfactoryWeightage: number = 0;
  ofiWeightage: number = 0;
  satisfactoryWeightage: number = 0;
  scorePercent: string = "0";
  constructor(private dataService: DataService, private router: Router, private commonService: CommonService, private ngZone: NgZone, private languageService: LanguageService, private changeDetector: ChangeDetectorRef) {

    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'scorecard': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'scorecard'] || 'scorecard',
        'tableheaderSubcats': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSubcats'] || 'tableheaderSubcats',
        'tableheaderSatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatisfactory'] || 'tableheaderSatisfactory',
        'tableheaderUnsatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatisfactory'] || 'tableheaderUnsatisfactory',
        'tableheaderNofindings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNofindings'] || 'tableheaderNofindings',
        'tableheaderSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSummary'] || 'tableheaderSummary',
        'tableheaderNoauditq': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNoauditq'] || 'tableheaderNoauditq',
        'tableheaderNoqaudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNoqaudit'] || 'tableheaderNoqaudit',
        'tableheaderUnsatweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatweightscore'] || 'tableheaderUnsatweightscore',
        'tableheaderOfiweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfiweightscore'] || 'tableheaderOfiweightscore',
        'tableheaderSatweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatweightscore'] || 'tableheaderSatweightscore',
        'tableheaderOfi': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfi'] || 'tableheaderOfi',
      }
    }

    var _this = this;

  }

  ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'scorecard': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'scorecard'] || 'scorecard',
          'tableheaderSubcats': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSubcats'] || 'tableheaderSubcats',
          'tableheaderSatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatisfactory'] || 'tableheaderSatisfactory',
          'tableheaderUnsatisfactory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatisfactory'] || 'tableheaderUnsatisfactory',
          'tableheaderNofindings': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNofindings'] || 'tableheaderNofindings',
          'tableheaderSummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSummary'] || 'tableheaderSummary',
          'tableheaderNoauditq': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNoauditq'] || 'tableheaderNoauditq',
          'tableheaderNoqaudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderNoqaudit'] || 'tableheaderNoqaudit',
          'tableheaderUnsatweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderUnsatweightscore'] || 'tableheaderUnsatweightscore',
          'tableheaderOfiweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfiweightscore'] || 'tableheaderOfiweightscore',
          'tableheaderSatweightscore': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderSatweightscore'] || 'tableheaderSatweightscore',
          'tableheaderOfi': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderOfi'] || 'tableheaderOfi',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })
    if (history.state.externalId) {
      _this.loaderFlag = true;
      _this.getScheduleData(history.state.externalId)
    } else {
      _this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
    }
  }
  ngAfterViewInit(): void {


    

  }

  goAudit(){
    this.router.navigate(['observations/list'], { state: {  listType: "Audit" } });
  }

  getScheduleData(id) {
    var _this = this;

    _this.dataService.postData({ externalId: id }, _this.dataService.NODE_API + "/api/service/listAudit").subscribe(data => {
      _this.auditData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
      console.log(_this.auditData)
      _this.dataService.postData({ auditId: id,isSummaryRecord:false }, _this.dataService.NODE_API + "/api/service/listScoreCard").subscribe(scoredata => {
        var scoreTotalData = scoredata["data"]["list" + _this.commonService.configuration["typeScoreCard"]]["items"];
        // if(scoreTotalData.length>0){
        //   _this.scoreArrayTotal["satisfactory"] = scoreTotalData[0]["satisfactory"];
        //   _this.scoreArrayTotal["ofi"] = scoreTotalData[0]["opportunityForImprovement"];
        //   _this.scoreArrayTotal["unSatisfactory"] = scoreTotalData[0]["unsatisfactory"];
        // }
        
      });
      _this.dataService.postData({ schedules: [_this.auditData["refOFWASchedule"]["externalId"]] }, _this.dataService.NODE_API + "/api/service/listChecklist").subscribe(data1 => {
        _this.observation = data1["data"]["list" + _this.commonService.configuration["typeChecklist"]]["items"];
        _this.commonService.getProcessConfiguration(_this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"], function (data2) {
          var processList = _this.commonService.processList.filter(e => {
            return (e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.auditData["refOFWASchedule"].refOFWACategory["externalId"];
          })
          var site = _this.commonService.siteList.find(e => e.externalId == _this.auditData["refOFWASchedule"]["refOFWAProcess"]["refSite"]["externalId"])
          _this.selectedSite = site;
          _this.loaderFlag = false;

          _this.checklistCount =  _this.observation.length;
          _this.auditChecklistCount =  _this.observation.filter(e => e.isSatisfactory || e.isUnsatisfactory || e.isOfi).length;
          _this.unSatisfactoryWeightage =  _this.observation.filter(e => e.unSatisfactory).length;
          _this.ofiWeightage =  _this.observation.filter(e => e.ofi).length/4;
          _this.satisfactoryWeightage =  _this.observation.filter(e => e.satisfactory).length;
          if(_this.auditData["refOFWAScorecard"]){
            _this.unSatisfactoryWeightage = _this.auditData["refOFWAScorecard"].unsatisfactory
            _this.ofiWeightage =  _this.auditData["refOFWAScorecard"].opportunityForImprovement/4
            _this.satisfactoryWeightage = _this.auditData["refOFWAScorecard"].satisfactory
            _this.scorePercent = ((((_this.auditChecklistCount)- (_this.unSatisfactoryWeightage+_this.ofiWeightage))/_this.checklistCount)*100).toFixed(2);
          }
         
          _.each(processList, function (eData) {
            var subCatAllList = _this.observation.filter(e => e.refOFWASubCategory["externalId"] == eData.externalId);
            var satisfactoryCount = subCatAllList.filter(e => e.isSatisfactory).length;
            var ofiCount = subCatAllList.filter(e => e.isOfi).length;
            var unSatisfactoryCount = subCatAllList.filter(e => e.isUnsatisfactory).length;
            var scoreArray = {
              subCategory: eData.name,
              satisfactory: satisfactoryCount,
              ofi: ofiCount,
              unSatisfactory: unSatisfactoryCount
            }
            _this.scoreArrayTotal["satisfactory"] += satisfactoryCount;
            _this.scoreArrayTotal["ofi"] += ofiCount
            _this.scoreArrayTotal["unSatisfactory"] += unSatisfactoryCount
            _this.scoreCardArr.push(scoreArray)

            // _this.selectSubCategory(eData);
          })
        });
      })

    })
  }


  goPage(page) {
    this.router.navigate([page]);
  }

}
