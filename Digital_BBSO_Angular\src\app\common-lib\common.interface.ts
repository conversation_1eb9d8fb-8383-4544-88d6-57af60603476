export interface TableColumnsSchemaDtl {
    name: string;
    dataKey: string;
    position?: string;
    type?: string;
    width?: number;
    hasSort?: boolean;
    hasFilter?: boolean;
    dropdownFilterOptions?: Set<any>;
    preSelectedFilterOptions?: Array<string>;
    navigateURL?: string;
    actionLabel?: string;
    actionType?: string;
    owner?: any;
    isHTML?: boolean;
    isLineClamp?: boolean;
    openModal?: string | boolean;
  }