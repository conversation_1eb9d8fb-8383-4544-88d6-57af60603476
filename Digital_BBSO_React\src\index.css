
@font-face {
  font-family: 'Myriad-Regular';
  src: url('./Myriad Pro/Myriad Pro Regular/Myriad Pro Regular.ttf');
}

@font-face {
  font-family: 'Myriad-Black';
  src: url('./Myriad Pro/Myriad Pro Black/Myriad Pro Black.otf');
}

@font-face {
  font-family: 'Myriad-Bold';
  src: url('./Myriad Pro/Myriad Pro Bold/Myriad Pro Bold.ttf');
}

@font-face {
  font-family: 'Myriad-ExtraBold';
  src: url('./Myriad Pro/Myriad Pro Heavy/Myriad Pro Heavy.ttf');
}

@font-face {
  font-family: 'Myriad-Medium';
  src: url('./Myriad Pro/Myriad Pro Bold/Myriad Pro Bold.ttf');
}

@font-face {
  font-family: 'Myriad-SemiBold';
  src: url('./Myriad Pro/Myriad Pro Semibold/Myriad Pro Semibold.ttf');
}

body {
  margin: 0px;
  /* padding-left: 10px; */
  font-family: 'Myriad-Regular' !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.form.oi-ec050e-table{
  margin: 0px !important;
}

.oi-ec050e-table td, .oi-ec050e-table th{
    padding: 7px 10px 7px 10px !important;
}
.oi-ec050e-table tr>:first-of-type input {
  opacity: 1 !important;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}


.observablehq {
  /* height: 90vh !important; */
}
.oi-ec050e-table table {
  width: 100%;
  /* margin: 10px; */
}

form.oi-ec050e-table {
  margin: 0px !important;
  max-height: 1000vh !important;
}

.oi-ec050e-table thead tr td, .oi-ec050e-table thead tr th{
  /* text-align: left; */
}


.oi-ec050e-table thead tr th{
  font-size: 16px !important;
  font-weight: 600 !important;
  font-family: 'Myriad-SemiBold' !important;
  vertical-align: top;
  /* border-bottom: solid 5px #93c854 !important; */
  background-color: #F8F8F8;
}

.oi-ec050e-table td {
  font-size: 12px !important;
  font-family: 'Myriad-Regular' !important;
  max-width:350px
}
.oi-ec050e-table tr:not(:last-child) td, .oi-ec050e-table tr:not(:last-child) th {
  border-bottom: solid .5px #7C4298 !important;
}



svg {
  background-color: unset !important;
  color: black;
}
.tick {
  color: white;
}
/* ::-webkit-scrollbar {
  width: 5px;
} */
  /* ::-webkit-scrollbar {
    width: 13px;
    height: 13px;
  }
  ::-webkit-scrollbar-button {
    background: #888
  }
  ::-webkit-scrollbar-track-piece {
    background: #000
  }
  ::-webkit-scrollbar-thumb {
    background: #888
  }
  .mat-icon-button{
    margin: 2px !important;
    margin-left: -12px !important;
  } */

  .oi-ec050e-table thead th span {
    display: contents !important;
    color: rgb(0, 0, 0) !important;
    font-size: 16px !important;
    font: Roboto !important;
  }
  /* #b ~ .y {
    Style all .y that follow #b 
  }*/
  .selectedTwo ~ li {
    cursor: not-allowed;
    pointer-events: none;

  }

  .form.oi-ec050e-table {
    height: 100% !important;
  }
  
  .tableBottom{
    display: flex;
    flex-direction: row ;
    justify-content: space-between; 

  }
  .numRowsText{
    font-size: 12px !important;
    font-family: 'Myriad-Regular' !important;
  }
  .numberRows {
    /* display: flex;
    flex-direction: row ;
    justify-content: end;  */
    margin-right: 10px;
  }

  .not-allowed {
    opacity: 0.4;
    cursor: not-allowed;
  }
  .pointer {
    cursor: pointer;
  }