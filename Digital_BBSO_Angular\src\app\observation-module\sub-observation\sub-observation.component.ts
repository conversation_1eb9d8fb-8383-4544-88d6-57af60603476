import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-sub-observation',
  templateUrl: './sub-observation.component.html',
  styleUrls: ['./sub-observation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SubObservationComponent implements OnInit {

  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  processList = []
  selectedProcess: any;
  @Input() corePrinciple: any
  @Input() process: any;
  labels ={}

  defaultIcon :any;
  constructor(
    private cd: ChangeDetectorRef,
    private dataService: DataService,
    private sanitizer: DomSanitizer,
    private commonService: CommonService,
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
    public translationService: TranslationService,
    private languageService : LanguageService,
    private ngZone: NgZone
  ) {
    this.labels ={
      'commonCreatean': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonCreatean'] || 'commonCreatean',
      'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
        'choosetheobservationtype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'choosetheobservationtype'] || 'choosetheobservationtype',
        'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
    }
    this.defaultIcon = dataService.defaultIcon;
  }
  ngOnInit(): void {
    // this.getListObserve();
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels ={
          'commonCreatean': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonCreatean'] || 'commonCreatean',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
            'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
            'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
            'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
            'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
            'observationType': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observationType'] || 'observationType',
            'choosetheobservationtype': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'choosetheobservationtype'] || 'choosetheobservationtype',
            'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })

    var processList = _this.commonService.processList.filter(e => {
      return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == _this.process.externalId);
    })

    _this.processList = processList.sort(function (a, b) {
      if ((a.sequence ? a.sequence :0)  < (b.sequence ? b.sequence :0)) { return -1; }
      if ((a.sequence ? a.sequence :0) > (b.sequence ? b.sequence :0)) { return 1; }
      return 0;
    })
    this.cd.detectChanges();
  }

  siteSelected(region) {
  }
  unitSelected(region) {
  }
  selectProcess(item) {
    this.selectedProcess = item;
    this.goCreatePage();
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  goBack() {
    this.newItemEvent.emit({"type":"Cancel","processType":"Sub Process" });
  }
  goCreatePage() {

    var _this = this;
    console.log(this.process)

    _this.newItemEvent.emit({"type":"Next","processType":"Sub Process","selectedOFWAProcess":this.selectedProcess});

    // if (this.process.name == "Observation") {
    //   this.newItemEvent.emit({ "checkList": true, "process": this.selectedProcess });
    // } else if (this.process.name == "Audit") {
    //   this.router.navigateByUrl('observations/audit-list', {
    //     state: { "process": this.selectedProcess }
    //   });
    // } else {
    //   this.commonService.triggerToast({ type: 'error', title: "", msg: "Please choose observation type" });
    // }
   }
}

