// export default App;
import React, { useRef, useEffect, useState, useMemo } from "react";
import { Runtime, Inspector } from "@observablehq/runtime";
import notebook from "../assets/.innoart-table";
import { useSearchParams } from 'react-router-dom';
import { html } from "htl";
import Asset_JSON from '../assets/data/cognite_data.json';
import Popup from "reactjs-popup";
import format from 'date-fns/format'
import axios from "axios";
import { CogniteClient } from "@cognite/sdk";
import { PublicClientApplication } from "@azure/msal-browser";
import Pagination from "./pagination/Pagination";
import * as Constants from '../Constant'

let main;
let limitSet = 10;
let firstPageIndex = 0;
let currentPageNumber = 1;
let listData = [];
let pageInfo = [];
let allListData = [];
let colSummary = {
  "name": {
    "enable": "Y",
    "colorRange": []
  },
  "description": {
    "enable": "Y",
    "colorRange": []
  },
  "parentId": {
    "enable": "Y",
    "colorRange": []
  }
};
let displayedColumns = [
  "externalId",
  "name",
  "description",
  "changedBy",
  "changedDate",
  "createdTime"
]
var paginationCursor = [];
let site;
let unit;
let search;
function Assets() {
  const viewofSelectionRef = useRef();
  const [currentPage, setCurrentPage] = useState(1);
  const [dataCount, setDataCount] = useState(0);
  const [limit, setLimitCount] = useState(10);
  window.onmessage = function (e) {
    if (e.data.type && e.data.type == "Assets") {
      if (e.data.action == "Column") {
        displayedColumns = e.data.data;
        colFun();
      } else if (e.data.action == "Filter") {
        site = e.data.site;
        unit = e.data.unit;
        search = e.data.search;
        getData(e.data.site, e.data.unit, e.data.search);
      } else
        if (e.data.action == "Summary") {
          colSummary = e.data.data;
          colFun();
        } else
          if (e.data.action == "PageRows") {

            setCurrentPage(1);
            setLimitCount(parseInt(e.data.data))
            limitSet = parseInt(e.data.data);
            paginationCursor = [];
            getData(e.data.site, e.data.unit, e.data.search);


          }

    }

  };


  const [searchParams, setSearchParams] = useSearchParams();


  function action1(x, i) {
    return html`<div>
      <img src="./images/server.png" id="${i}" title="360 View" style="height:18px;margin-right:5px;cursor: pointer;" onClick=${dataView}>
      <img src="./images/alert.png" id="${i}" title="View threat" style="height:18px;margin-right:5px;cursor: pointer;" onClick=${allThreats}>
      <img src="./images/file.png" id="${i}" title="Create threat" style="height:18px;margin-right:5px;cursor: pointer;" onClick=${createThreat}>
      </div>
      `
  }

  function dataView(event) {
    window.parent.postMessage({ "type": "Assets", "action": "DataView", "data": listData[parseInt(event.target.id)] }, "*");
  }
  function allThreats(event) {
    window.parent.postMessage({ "type": "Assets", "action": "AllThreats", "data": listData[parseInt(event.target.id)] }, "*");
  }
  function createThreat(event) {
    window.parent.postMessage({ "type": "Assets", "action": "CreateThreat", "data": listData[parseInt(event.target.id)] }, "*");
  }
  function colFun() {
    const element = document.getElementById("summaryBarChart");
    if(element){
     element.remove();
    }
    document.querySelectorAll('.summaryBar').forEach(e => e.remove());
    main.redefine("configuration", {
      columns: displayedColumns,

      header: {
        "externalId": "externalId",
        "name": "Name",
        "description": "Description",
        'changedBy': "changedBy",
        "changedDate": "changedDate",
        "functionalLocation": "functionalLocation",
        "maintenancePlanningPlant": "maintenancePlanningPlant",
        "maintenancePlant": "maintenancePlant",
        "plannerGroup": "plannerGroup",
        "createdTime": "Action",
      },
      headerSummary: colSummary,
      format: {
        createdAt: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        lastUpdatedTime: x => format(new Date(x), 'MM/dd/yyyy hh:mm aa'),
        id: x => {
          return x.toString()
        },
        rootId: x => x.toString(),
        parentId: x => x.toString(),
        dataSetId: x => x.toString(),
        createdTime: (x, i) => action1(x, i)
      },

      align: {
        "externalId": "left",
        "name": "left",
        "description": "left",
        "changedBy": "left",
        "changedDate": "left",
        "functionalLocation": "left",
        "maintenancePlanningPlant": "left",
        "maintenancePlant": "left",
        "plannerGroup": "left",
        "createdTime": "left"


      },
      rows: 25,
      width: {
        "externalId": 300,
        "name": 300,
        "description": 300,
        'changedBy': 300,
        "changedDate": 300,
        "createdTime": 300

      },
      maxWidth: "100vw",
      layout: "auto",
    });
  }



  function pushObj(item) {
    if (!paginationCursor.find(({ id }) => id === item.id)) {
      paginationCursor.push(item);
    }
  }


  async function getData() {
    //Fetch Data Count
    allListData = [];
  }


  useEffect(() => {



    const runtime = new Runtime();
    main = runtime.module(notebook, name => {
      if (name === "viewof selection1") return new Inspector(viewofSelectionRef.current);
      if (name === "selection") {
        return {
          // pending() { console.log(`${name} is running…`); },
          fulfilled(value) {
            window.parent.postMessage({ "type": "Assets", "action": "Select", "data": [], "selected": value }, "*");
          },
          // rejected(error) { console.error(error); }
        };
      }
    });
    setDataCount(1);
    colFun();
    getData();
    // main.redefine("data", Asset_JSON);
    return () => runtime.dispose();
  }, []);

  useMemo(async () => {

    firstPageIndex = (currentPage - 1) * limit;
    const lastPageIndex = firstPageIndex + limit;
    var cursorSrting = pageInfo["endCursor"];

    console.log("pageInfo")
    console.log(pageInfo)
    if (currentPageNumber < currentPage || currentPageNumber > currentPage) {
      if (currentPageNumber < currentPage) {
        console.log("Next");
        currentPageNumber = currentPage;
        cursorSrting = pageInfo["endCursor"];
        console.log("cursorSrting ", cursorSrting)
        //Fetch Data
       
      } else if (currentPageNumber > currentPage) {
        var selectedPageData = allListData.find(o => o.pageNumber === currentPage);
        currentPageNumber = currentPage;
        listData = selectedPageData["pageList"];
        pageInfo = selectedPageData["pageInfo"];
        main.redefine("data", listData);
        colFun();
      }

    }


    // let pobj = paginationCursor.find(o => o.id === currentPage);
    // if (pobj != undefined) {
    //   assetFilter["cursor"] = pobj.cursorName
    //   var clientAuthent3 = await client.authenticate();
    //   const assetsCursor = clientAuthent3 ? await client.assets.list(assetFilter) : {}
    //   pushObj({
    //     id: currentPage + 1,
    //     cursorName: assetsCursor.nextCursor
    //   })
    //   var newData = [];
    //   assetsCursor.items.forEach(element => {
    //     let merged = { ...element, ...element.metadata };
    //     delete merged.metadata;
    //     merged["action"] = "";
    //     newData.push(merged);
    //   });

    //   main.redefine("data", newData);
    // colFun();
    // } else {
    //   if (currentPage == 1 && paginationCursor.length > 0) {
    //     delete assetFilter['cursor'];
    //     var clientAuthent4 = await client.authenticate();
    //     const assets = clientAuthent4 ? await client.assets.list(assetFilter) : {};

    //     var newData = [];
    //     assets.items.forEach(element => {
    //       let merged = { ...element, ...element.metadata };
    //       delete merged.metadata;
    //       merged["action"] = "";
    //       newData.push(merged);
    //     });
    //     // listData = newData;
    //     main.redefine("data", newData);
    //     colFun();
    //   }

    // }

    // main.redefine("data", listData);
  }, [currentPage]);



  return (
    <>
      <div ref={viewofSelectionRef} />
      <Pagination
        className="pagination-bar"
        assetsType='assets_cognite'
        currentPage={currentPage}
        totalCount={dataCount}
        pageSize={limit}
        onPageChange={page => setCurrentPage(page)}
      />

    </>
  );
}


export default Assets;