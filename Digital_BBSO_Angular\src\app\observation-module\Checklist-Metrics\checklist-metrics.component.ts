import { Component, OnInit, ViewChild,Input ,Output,EventEmitter, AfterViewInit} from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { TokenService } from 'src/app/services/token.service';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { thru } from 'cypress/types/lodash';
import { MatSelectChange } from '@angular/material/select';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ActionPopupComponent } from '../action-popup/action-popup.component';
import { TranslateService } from '@ngx-translate/core';
import { event } from 'jquery';
import { ChangeDetectorRef, NgZone } from '@angular/core';
import { ChangeDetectionStrategy } from '@angular/core';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-checklist-metrics',
  templateUrl: './checklist-metrics.component.html',
  styleUrls: ['./checklist-metrics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush // Use OnPush change detection strategy
})
export class checklistmetricsComponent implements OnInit, AfterViewInit {

  displayedColumns: string[] = ['categoryName', 'subCategoryName', 'description', 'isSafeCount', 'isUnSafeCount', 'isNotObservedCount'];
  dataSource = new MatTableDataSource<any>([]);
  labels = {}

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Input() listType: any = "Observation";
  
  
  observeType: FormControl = new FormControl("");
  createOb_obj: any;
  createHa_obj: any;
  siteControl: FormControl = new FormControl("");


  externalIdForSelectedItem: string | null = null;
  selectedCorePrinciple: string | null = null;
  showSubProcessDropdown: boolean = true;

  

  filterFlag = '';
  filteredSiteOptions: Observable<any[]>;
  siteList = [];
  filteredSiteList = [];
  @Input() process: any;

  processControl: FormControl = new FormControl();
  processList = [];
  filteredProcessList = [];

  corePrinciplesControl: FormControl = new FormControl();
  corePrinciplesList = [];
  filteredCorePrinciplesList = [];

  subProcessControl: FormControl = new FormControl();
  subProcessList = [];
  filteredSubProcessList = [];


  filteredUnitOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl('');
  unitList = [];
  filteredUnitList = [];

  groupedData: any[] = [];

  observedForm: FormGroup;


  selectedName: string | undefined;
  

  names1: string[] = []; // First dropdown options
names2: string[] = []; // Second dropdown options based on the first dropdown
names3: string[] = []; // Third dropdown options based on the second dropdown

selectedName1: string | null = null; // Selected value from the first dropdown
selectedName2: string | null = null; // Selected value from the second dropdown

selectedName3: string | null = null;
  searchControl: FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();



 

  filteredObserveTypeOptions: Observable<any[]>
  dropdownData: any[] = [];
  processedItems2: any;
  processedItems1: any;
  processedItems3: any;
  actionPopupResponse: any;
  observation: any;
  isEditValueSet: boolean;
  receivedData: any;
  constructor(private dataService: DataService, private router: Router, private commonService: CommonService,private tokenService: TokenService,public dialog: MatDialog, private translate:TranslateService, private changeDetectorRef: ChangeDetectorRef, private ngZone: NgZone, private languageService: LanguageService) {
    
    this.labels = {
      'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'] || 'checklistTitle',
      'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
      'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
        'formcontrolsSubcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategory'] || 'formcontrolsSubcategory',
        'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
        'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
        'isSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'isSafe'] || 'isSafe',
        'unSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unSafe'] || 'unSafe',
        'formcontrolsIsnotobserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsIsnotobserved'] || 'formcontrolsIsnotobserved',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
    }

  }

  ngOnInit(): void {
    this.commonService.data$.subscribe(data => {
      this.receivedData = data;
    });
    var _this = this
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'checklistTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'checklistTitle'] || 'checklistTitle',
          'corePrinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'corePrinciple'] || 'corePrinciple',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
            'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
            'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
            'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
            'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
            'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
            'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
            'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
            'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
            'commonfilterChoosesubprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesubprocess'] || 'commonfilterChoosesubprocess',
            'formcontrolsSubcategory': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSubcategory'] || 'formcontrolsSubcategory',
            'category': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'category'] || 'category',
            'formcontrolsBehaviourchecklist': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsBehaviourchecklist'] || 'formcontrolsBehaviourchecklist',
            'isSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'isSafe'] || 'isSafe',
            'unSafe': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'unSafe'] || 'unSafe',
            'formcontrolsIsnotobserved': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsIsnotobserved'] || 'formcontrolsIsnotobserved',
            'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
            'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
            'commonfilterChoosecoreprinciple': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosecoreprinciple'] || 'commonfilterChoosecoreprinciple',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetectorRef.detectChanges();
      })
      _this.changeDetectorRef.detectChanges();
    })

    // this.translate.use(this.commonService.selectedLanguage.toLowerCase());

    console.log(this.receivedData)
    var _this = this;
    console.log(_this.dataService.siteId)
    //this.fetchItems('')
   // 

  //  this.fetchAndGroupData1()
  //  this.fetchAndGroupData2('')
  //  this.fetchAndGroupData3('')

  if (_this.dataService.siteId) {
    // _this.commonService.loaderFlag = true;
    _this.loadCorePrinciples();
  } else {
    _this.router.navigate(['observations/list']);
  }

    _this.corePrinciplesControl.valueChanges.subscribe(process => {
      var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == process);
      })
      _this.processList = childrenProcess;
      _this.filteredProcessList = _this.processList.slice();

      var myProcess = _this.commonService.processList.find(e => {
        return (e.externalId == process);
      })
    })

    _this.processControl.valueChanges.subscribe((value: any) => {
      console.log('====>',value)
      var childrenProcess = _this.commonService.processList.filter(e => {
        return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == value);
      })
      _this.subProcessList = childrenProcess;
      _this.filteredSubProcessList = _this.subProcessList.slice();
    });

    _this.subProcessControl.valueChanges.subscribe(process => {
      console.log(process)
      _this.getData();
    })
  }
  ngAfterViewInit(): void {
    var _this = this;
    // _this.translate.use(this.commonService.selectedLanguage.toLowerCase());
    _this.translate.onLangChange.subscribe(() => {
      _this.dataSource.paginator = _this.paginator;
      console.log('pagination: ', _this.dataSource.paginator);
      _this.changeDetectorRef.detectChanges();
    })

    _this.languageService.language$.subscribe((language) => {
      _this.dataSource.paginator = _this.paginator;
      console.log('pagination: ', _this.dataSource.paginator);
      _this.changeDetectorRef.detectChanges();
      console.log('commonService label', _this.labels)
      _this.changeDetectorRef.detectChanges();
    })

    _this.dataSource.paginator = _this.paginator;
    console.log('pagination: ', _this.dataSource.paginator);
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
      _this.filteredSiteList = _this.siteList.slice();
    }
  }
  loadCorePrinciples() {
    var _this = this;
   
    var corePrinciplesList = _this.commonService.processList.filter(e => {
      return e.processType == "Core Principles" && ((e.refSite && e.refSite.externalId) == _this.dataService.siteId);
    })
    _this.corePrinciplesList = corePrinciplesList;
    _this.filteredCorePrinciplesList = _this.corePrinciplesList.slice();
    if(_this.corePrinciplesList.length == 0 ){
      _this.getSiteProcess();
    }
    _this.processList = [];
    _this.filteredProcessList = _this.processList.slice();
  }

  getSiteProcess(){
    var _this = this;
    _this.commonService.loaderFlag = true;
    var mySite = _this.commonService.siteList.find(e => e.externalId == _this.dataService.siteId);
      _this.commonService.getProcessConfiguration(_this.dataService.siteId, function (data) {
        _this.commonService.loaderFlag = false;
        if (mySite) {
          _this.loadCorePrinciples();
        }
      });
  }

  handleListType(listType: string): void {
    console.log('Handling listType:', listType); // Debugging statement
    if (listType === "Observation") {
      // Handle Observation list type
      console.log('ListType is Observation'); // Debugging statement
    } else if (listType === "Hazards") {
      // Handle Hazards list type
      console.log('ListType is Hazards'); // Debugging statement
    }
  }

  getData() {
    var _this = this;
    _this.commonService.loaderFlag = true;
    var selProcess = _this.commonService.processList.find(e => e.externalId == _this.processControl.value)
    _this.dataService.postData({
      limit: 1000,
      listType: selProcess.name,
      sites: [this.dataService.siteId],
      units: [],
      process: [_this.processControl.value],
      subProcessId:_this.subProcessControl.value
    }, _this.dataService.NODE_API + '/api/service/listObservation'
    ).subscribe((resData: any) => {
      var observationList = resData['data']['list' + _this.commonService.configuration['typeObservation']]['items'];
      _this.commonService.loaderFlag = false;
      const groupedItems: any = {};
      if (observationList.length > 0) {
        var myObservation = observationList.filter(e => e.refOFWAChecklist.items.length > 0);
        console.log("myObservation", myObservation)
        _.each(myObservation, function (eData) {
          _.each(eData.refOFWAChecklist.items, function (eChecklist) {
            const key = _this.getDescription(eChecklist);
            if (!groupedItems[key]) {
              groupedItems[key] = {
                description: key,
                subCategoryName: eChecklist.refOFWASubCategory ? eChecklist.refOFWASubCategory.name : '',
                categoryName: eChecklist.refOFWACategory ? eChecklist.refOFWACategory.name : '',
                isSafeCount: 0,
                isUnSafeCount: 0,
                isNotObservedCount: 0,
                items: []
              };
            }
            if (eChecklist.isSafe) groupedItems[key].isSafeCount++;
            if (eChecklist.isUnSafe) groupedItems[key].isUnSafeCount++;
            if (eChecklist.isNotObserved) groupedItems[key].isNotObservedCount++;
            groupedItems[key].items.push(eChecklist);
          })
        })
        console.log(groupedItems)
        _this.dataSource.data = Object.values(groupedItems);
        _this.dataSource.paginator = this.paginator;
      } else {
        _this.dataSource.data = [];
      }
    }),
    (error: any) => {
      _this.commonService.loaderFlag = false;
      console.error("Error fetching items", error);
    };
  }


getDescription(item: any): string {

  if (item.refOFWAQuestion && item.refOFWAQuestion.description) {
    return item.refOFWAQuestion.description;
  }
  else if (item.refOFWASubCategory && item.refOFWASubCategory.name) {
    return item.refOFWASubCategory.name;
  }
  else if (item.refOFWACategory && item.refOFWACategory.name) {
    return item.refOFWACategory.name;
  }
  else {
    return ""; 
  }
}



goAudit() {
   var _this=this;
   
   if(this.receivedData == "Observation"){
    this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
  }else{
    this.router.navigate(['observations/list'], { state: { listType: "Hazards" } });
  }

  //this.router.navigate(['observations/list'], { state: {  listType: "Observation" } });
  
}


  

}
