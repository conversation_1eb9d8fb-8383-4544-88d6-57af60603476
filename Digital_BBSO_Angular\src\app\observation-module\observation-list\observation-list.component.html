<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="row" class="overflow-sectiontest">
  <div fxFlex="100">
    <div class="audit-plan-unit-section">
      <!-- <app-common-filter [siteControl]="siteControl"></app-common-filter>
       -->
       <div style="margin-bottom: 5px;"></div>


      <div class="audit-head-icon" >

        <div class="icon-bg-box" (click)="checklistclick()" matTooltip="{{ labels['checklistTitle'] }}">
         <svg viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg" stroke="#083D5B"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9703 3.3437C13.0166 2.88543 10.9834 2.88543 9.02975 3.3437C6.20842 4.00549 4.0055 6.20841 3.3437 9.02975C2.88543 10.9834 2.88543 13.0166 3.3437 14.9703C4.0055 17.7916 6.20842 19.9945 9.02975 20.6563C10.9834 21.1146 13.0166 21.1146 14.9703 20.6563C17.7916 19.9945 19.9945 17.7916 20.6563 14.9703C21.1146 13.0166 21.1146 10.9834 20.6563 9.02975C19.9945 6.20842 17.7916 4.00549 14.9703 3.3437ZM10.9883 8.22523C11.2226 8.45955 11.2226 8.83945 10.9883 9.07376L9.07376 10.9883C8.96124 11.1009 8.80863 11.1641 8.6495 11.1641C8.49037 11.1641 8.33775 11.1009 8.22523 10.9883L7.26795 10.031C7.03363 9.79673 7.03363 9.41683 7.26795 9.18252C7.50226 8.9482 7.88216 8.9482 8.11647 9.18252L8.6495 9.71554L10.1398 8.22523C10.3741 7.99092 10.754 7.99092 10.9883 8.22523ZM12.3573 10.0854C12.3573 9.75406 12.6259 9.48543 12.9573 9.48543H16.3078C16.6392 9.48543 16.9078 9.75406 16.9078 10.0854C16.9078 10.4168 16.6392 10.6854 16.3078 10.6854H12.9573C12.6259 10.6854 12.3573 10.4168 12.3573 10.0854ZM10.9883 13.0117C11.2226 13.246 11.2226 13.6259 10.9883 13.8602L9.07376 15.7748C8.83945 16.0091 8.45955 16.0091 8.22523 15.7748L7.26795 14.8175C7.03363 14.5832 7.03363 14.2033 7.26795 13.969C7.50226 13.7346 7.88216 13.7346 8.11647 13.969L8.6495 14.502L10.1398 13.0117C10.3741 12.7774 10.754 12.7774 10.9883 13.0117ZM12.3573 14.8719C12.3573 14.5405 12.6259 14.2719 12.9573 14.2719H16.3078C16.6392 14.2719 16.9078 14.5405 16.9078 14.8719C16.9078 15.2032 16.6392 15.4719 16.3078 15.4719H12.9573C12.6259 15.4719 12.3573 15.2032 12.3573 14.8719Z"></path> </g></svg>
        </div>
     <div *ngIf="metricsbyperson" class="icon-bg-box" (click)="popupClick()" matTooltip="{{ labels['reacttableMetricsbyperson'] }}">
          <svg fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" xml:space="preserve" stroke="#083D5B"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M72,22H28c-3.3,0-6,2.7-6,6v44c0,3.3,2.7,6,6,6h44c3.3,0,6-2.7,6-6V28C78,24.7,75.3,22,72,22z M38,66 c0,1.1-0.9,2-2,2h-2c-1.1,0-2-0.9-2-2V55c0-1.1,0.9-2,2-2h2c1.1,0,2,0.9,2,2V66z M48,66c0,1.1-0.9,2-2,2h-2c-1.1,0-2-0.9-2-2V40 c0-1.1,0.9-2,2-2h2c1.1,0,2,0.9,2,2V66z M58,66c0,1.1-0.9,2-2,2h-2c-1.1,0-2-0.9-2-2V34c0-1.1,0.9-2,2-2h2c1.1,0,2,0.9,2,2V66z M68,66c0,1.1-0.9,2-2,2h-2c-1.1,0-2-0.9-2-2V47c0-1.1,0.9-2,2-2h2c1.1,0,2,0.9,2,2V66z"></path> </g> </g></svg>
        </div>
        <mat-dialog-content *ngIf="isPopupOpen">
          <div *ngIf="isPopupOpen" class="overlay"></div>
          <div *ngIf="isPopupOpen" class="popup">
            <div class="popup-header">
              <input type="text" placeholder="{{ labels['commonfilterSearch'] }}" class="search-box" [(ngModel)]="searchTerm" (input)="onSearchTermChange()" />
              <div class="behaviour-checklist-fotter">
                <common-lib-button [className]="'cancel cst-btn'" id="cst-btn" [text]="labels['buttonCancel']"
                    (buttonAction)="cancelClick()"></common-lib-button>
              </div>
            </div>
            <div class="popup-content">
              <table class="popup-table">
                <thead>
                  <tr>
                    <th>{{ labels['formcontrolsSno'] }}</th>
                    <th>{{ labels['observedBy'] }}</th>
                    <th>{{ listType === 'Hazards' ? ( labels['formcontrolsHazcount'] ) : ( labels['formcontrolsObscount'] ) }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of filteredItems; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.count }}</td>
                  </tr>
                  <tr *ngIf="filteredItems.length === 0">
                    <td colspan="3">{{ labels['commonfilterNoresults'] }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </mat-dialog-content>
        
        
        <div class="icon-bg-box" (click)="excelExport()" matTooltip="{{ labels['reacttableExport'] }}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" width="17" height="17" fill="white">
            <path
              d="m648-140 112-112v92h40v-160H640v40h92L620-168l28 28Zm-448 20q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v268q-19-9-39-15.5t-41-9.5v-243H200v560h242q3 22 9.5 42t15.5 38H200Zm0-120v40-560 243-3 280Zm80-40h163q3-21 9.5-41t14.5-39H280v80Zm0-160h244q32-30 71.5-50t84.5-27v-3H280v80Zm0-160h400v-80H280v80ZM720-40q-83 0-141.5-58.5T520-240q0-83 58.5-141.5T720-440q83 0 141.5 58.5T920-240q0 83-58.5 141.5T720-40Z" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="summaryClick()" matTooltip="{{ labels['reacttableColsummary'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-stroke-svg-icon"
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="icon-bg-box" (click)="settingClick()" matTooltip="{{ labels['reacttableColselection'] }}">
          <svg width="17" height="17" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="button-svg-blue" fill-rule="evenodd" clip-rule="evenodd"
              d="M2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2H19C19.7957 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V5C2 4.20435 2.31607 3.44129 2.87868 2.87868ZM13 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V5C20 4.73478 19.8946 4.48043 19.7071 4.29289C19.5196 4.10536 19.2652 4 19 4H13V20ZM11 4V20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H11Z" />
          </svg>
        </div>

      </div>
    </div>

    <div class="ovservation-list-section" style="margin-top: 10px;">
      <div class="observation-list-filter-wrapper">
        <commom-label *ngIf="listType=='Observation'" labelText="{{ labels['observation'] }}"
          [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>
        <commom-label *ngIf="listType=='Hazards'" labelText="{{ labels['cardsHazards'] }}"
          [tagName]="'h4'" [cstClassName]="'heading unit-heading'"></commom-label>

        <div class="observation-list-filter">
          <!-- <mat-form-field class="marginLeft-5 marginRight-5" appearance="outline" >
            <mat-select   [formControl]="processControl"  >
              <mat-option *ngFor="let item of process" value="{{item.name}}" >{{item.name}}</mat-option>
            </mat-select>
          </mat-form-field> -->

          <!-- <mat-form-field class="marginLeft-5 marginRight-5 width165" appearance="outline" >
            <mat-select   [formControl]="observationControl"  >
              <mat-option *ngFor="let item of observation" value="{{item.name}}" >{{item.name}}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="marginLeft-5 marginRight-5 width165"appearance="outline" >
            <mat-select   [formControl]="categoryControl"  >
              <mat-option *ngFor="let item of category" value="{{item.name}}" >{{item.name}}</mat-option>
            </mat-select>
          </mat-form-field> -->


          

          <!-- <mat-form-field class="marginLeft-5 marginRight-5 width165"appearance="outline" >
            <mat-select  [formControl]="auditTypeControl"  placeholder="{{ labels['formcontrolsAudittype'] }}" >
              <mat-option *ngFor="let item of typeList" [value]="item" >
                <span *ngIf="item.name != 'Field Walk'" >
                  {{ labels['cards'+item.name] }}
                </span>
                <span *ngIf="item.name == 'Field Walk'" >
                  {{ labels['fieldWalk'] }}
                </span>
              </mat-option>
            </mat-select>
          </mat-form-field> -->
          <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['filter'] }}"
      (buttonAction)="openFilterDialog()"></common-lib-button>
<ng-template #filterDialog>
  <div class="filter-popup">
    <div class="filter-header">
      <h2> {{ labels['filters'] }} </h2>
      <button mat-icon-button (click)="closeDialog()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="filter-columns">

      <mat-form-field  appearance="outline" class="filter-field dateRangePick">
      <mat-datepicker-toggle class="my-dateicon" matIconSuffix [for]="picker3"></mat-datepicker-toggle>
        <mat-date-range-input  style="margin-left: 15px;" [rangePicker]="picker3">
       
            <input matStartDate [formControl]="startDateControl"
            placeholder="{{ labels['startDate'] }}">
          <input matEndDate [formControl]="endDateControl" placeholder="{{ labels['endDate'] }}">
        </mat-date-range-input>

        
        <mat-date-range-picker #picker3></mat-date-range-picker>
      </mat-form-field>

      <mat-form-field  appearance="outline" class="filter-field">
        <mat-label>{{ labels['corePrinciple'] }}</mat-label>
        <mat-select   [formControl]="coreTypeControl"  placeholder="{{ labels['corePrinciple'] }}" >
          <mat-option *ngFor="let item of coreTypeList" [value]="item" >
            {{ labels['cards'+item.name] }}
          </mat-option>
        </mat-select>
        <button *ngIf="coreTypeControl.value" mat-icon-button matSuffix (click)="coreTypeControl.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['commonfilterChoosesubprocess'] }}</mat-label>
        <mat-select (click)="filterClick()" placeholder="{{ labels['commonfilterChoosesubprocess'] }}"
            [formControl]="subProcessControl" disableOptionCentering>
            <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults'] "  [placeholder]="labels['commonfilterSearch']" [displayMember]="'name'"
                [array]="subProcessList" (filteredReturn)="filteredSubProcessList =$event"></mat-select-filter>
            <mat-option *ngFor="let item of filteredSubProcessList" [value]="item.externalId">
                {{item.name }}
            </mat-option>
        </mat-select>
        <button *ngIf="subProcessControl.value" mat-icon-button matSuffix (click)="subProcessControl.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
    </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['commonfilterChooseunit'] }}</mat-label>
        <mat-select placeholder="{{ labels['commonfilterChooseunit'] }}" [formControl]="unitControl" multiple>
          <mat-select-filter [placeholder]="labels['commonfilterSearch']" [displayMember]="'description'" [array]="unitList"
            (filteredReturn)="filteredUnitList = $event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredUnitList" [value]="item.externalId">
            {{ item.description }}
          </mat-option>
        </mat-select>
        <button *ngIf="unitControl.value" mat-icon-button matSuffix (click)="unitControl.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['commonfilterChooselocation'] }}</mat-label>
        <mat-select placeholder="{{ labels['commonfilterChooselocation'] }}" [formControl]="locationObserve">
          <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'description'" [array]="reportingLocationList" (filteredReturn)="filteredReportingLocationList = $event"></mat-select-filter>
          <mat-option *ngFor="let item of filteredReportingLocationList" [value]="item.externalId">
            {{ item.description }}
          </mat-option>
        </mat-select>
        <button *ngIf="locationObserve.value" mat-icon-button matSuffix (click)="locationObserve.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseCategory'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseCategory'] }}" [formControl]="categoryOpt">
          <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'name'" [array]="workOrderNumberList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter> -->
          <mat-option *ngFor="let item of filteredCategoryList" [value]="item.externalId">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="categoryOpt.value" mat-icon-button matSuffix (click)="categoryOpt.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['operationalLearning'] }}</mat-label>
        <mat-select [formControl]="operationalLearningControl" placeholder="{{labels['operationalLearning']}}" (selectionChange)="onSelectOperationalLearning($event.value)">
          <mat-option [value]="true">{{labels['formcontrolsYes']}}</mat-option>
          <mat-option [value]="false">{{labels['formcontrolsNo']}}</mat-option>
          <mat-option [value]="'All'">{{labels['all']}}</mat-option>
        </mat-select>
        <button *ngIf="operationalLearningControl.value" mat-icon-button matSuffix (click)="operationalLearningControl.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseStatus'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseStatus'] }}" [formControl]="status" (selectionChange)="onStatusChange($event.value)">
          <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'name'" [array]="workOrderNumberList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter> -->
          <mat-option *ngFor="let item of filteredStatusList" [value]="item.name">
            {{ item.name2 }}
          </mat-option>
        </mat-select>
        <button *ngIf="status.value" mat-icon-button matSuffix (click)="status.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>      

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['commonfilterChoosebehalf'] }}</mat-label>
        <mat-select placeholder="{{ labels['commonfilterChoosebehalf'] }}" [formControl]="behalf">
          <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'name'" [array]="behalfList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter>
          <mat-option *ngFor="let item of filteredBehalfList" [value]="item.externalId">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="behalf.value" mat-icon-button matSuffix (click)="behalf.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseCreatedBy'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseCreatedBy'] }}" [formControl]="createdBy">
          <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'name'" [array]="workOrderNumberList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter> -->
          <mat-option *ngFor="let item of filteredCreatedByList" [value]="item.name">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="createdBy.value" mat-icon-button matSuffix (click)="createdBy.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseProjectName'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseProjectName'] }}" [formControl]="projectName">
          <!-- <mat-select-filter [noResultsMessage]="labels['commonfilterNoresults']" [placeholder]="labels['commonfilterSearch']"
            [displayMember]="'name'" [array]="projectNameList" (filteredReturn)="onBehalfChange($event)"></mat-select-filter> -->
          <mat-option *ngFor="let item of filteredprojectNameList" [value]="item.name">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="projectName.value" mat-icon-button matSuffix (click)="projectName.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <!-- <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseWorkOrderNumber'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseWorkOrderNumber'] }}" [formControl]="workOrderNumber">
          <mat-option *ngFor="let item of filteredworkOrderNumberList" [value]="item.name">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="workOrderNumber.value" mat-icon-button matSuffix (click)="workOrderNumber.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field> -->







      <!-- <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ labels['chooseSubCategory'] }}</mat-label>
        <mat-select placeholder="{{ labels['chooseSubCategory'] }}" [formControl]="subCategoryOpt">
          <mat-option *ngFor="let item of filteredSubCategoryList" [value]="item.externalId">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <button *ngIf="subCategoryOpt.value" mat-icon-button matSuffix (click)="subCategoryOpt.reset()" aria-label="Clear">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field> -->


    </div>
    <div class="filter-actions">
      <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonClear'] }}"
      (buttonAction)="clearFilters()"></common-lib-button>
      <!-- <button mat-button (click)="clearFilters()">Clear</button> -->
      <!-- <button mat-raised-button color="primary" (click)="applyFilters()">Apply</button> -->
      <common-lib-button 
      [className]="'cst-btn ' + (!apply ? 'disabled-btn' : '')"
      [disable]="!apply" text="{{ labels['buttonApply'] }}"
      (buttonAction)="applyFilters()"></common-lib-button>
    </div>
  </div>
</ng-template>

         

          <common-lib-button
            *ngIf="listType=='Observation' && createOb_obj && createOb_obj.featureAccessLevelCode !='NoAccess'"
            [className]="'cst-btn marginLeft-5'" text=" {{ labels['behaviourchecklistCreateanobervation'] }} "
            (buttonAction)="createObservation()" [icon]="'add'"></common-lib-button>
          <common-lib-button
            *ngIf="listType=='Hazards' && createHa_obj && createHa_obj.featureAccessLevelCode !='NoAccess'"
            [className]="'cst-btn marginLeft-5'" text="{{ labels['behaviourchecklistCreateanhazards'] }}"
            (buttonAction)="createObservation()" [icon]="'add'"></common-lib-button>


        </div>

      </div>
      <div fxLayout="row" fxLayoutAlign="end center" style="margin-right: 10px;">
        <div *ngIf="!expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
          style="cursor: pointer;float: right;" matTooltip="{{ labels['expand']}}">
          <mat-icon style="color: white;margin: -2px;" aria-hidden="false">first_page</mat-icon>
        </div>
        <div *ngIf="expandFlag && showhide" (click)="expandClick()" class="icon-box-light"
          style="cursor: pointer;float: right;" matTooltip="{{ labels['collapse'] }}">
          <mat-icon style="color: white;margin: -2px;" aria-hidden="false">last_page</mat-icon>
        </div>
      </div>
      <div fxLayout="row" style="margin-left: 5px;">
        <as-split direction="horizontal">
          <as-split-area style="height: 72vh;" [ngClass]="showhide ? 'split-border' : ''" [size]="x"
            [minSize]="minSize">


            <div class="marginTop d-flex">
              <iframe id="iFrameObservationList" class="iFrameTable" [src]="url| safe" title="description">
              </iframe>
            </div>

          </as-split-area>
          <as-split-area *ngIf="showhide==true" [size]="y" style="overflow: hidden;">
            <div fxLayout="row" class="threat-dialog-box" style="padding:10px;" fxLayoutAlign="space-between center">
              <div fxLayout="row" style="align-items: center;">

                <div class="semi-bold">
                  <!-- {{ 'LOG_INFO' | translate }} -->
                    <!-- {{ translationService.translate('logInfo') }} -->
                      {{ labels['logInfo'] }}
                </div>
              </div>
              <div>
                <mat-icon (click)="closeClick()" style="cursor: pointer;" aria-hidden="false"
                  aria-label="Example home icon">close</mat-icon>

              </div>


            </div>
            <div style="margin: 8px;">
              <mat-card style="margin: 8px;">
                <div fxLayout="row" fxLayoutAlign="space-between center"  style="font-weight: bold;">
                  <div fxFlex="40">
                    <span>
                      <!-- {{ 'TABLE_COLS.Name' | translate }} -->
                        {{ labels['tablecolsName'] }}
                    </span>
                  
                  </div>
                  <div fxFlex="20">
                    <span>
                      <!-- {{ 'Type' | translate }} -->
                        {{ labels['type'] }}
                    </span>
                  
                  </div>
                  <div fxFlex="40">
                    <span>
                      {{ labels['dateAndTime'] }}
                    </span>
                   
                  </div>

                </div>


              </mat-card>
              <mat-card  style="margin: 8px;" *ngFor="let item of logList">
                <div fxLayout="row" fxLayoutAlign="space-between center" >
                  <div fxFlex="40">
                    <span>
                      {{item.name }}
                    </span>
                   
                  </div>
                  <div fxFlex="20">
                    <span>
                      {{item.logType }}
                    </span>
                   
                  </div>
                  <div fxFlex="40">
                    <span>
                      {{item.createdTime | date : "medium" }}
                    </span>
                  
                  </div>

                </div>


              </mat-card>
            
            </div>
        

            <div *ngIf="errorSpinner == false">
              <br>
              <br>
              <br>
              <br>

              <div fxLayout="row" fxLayoutAlign="center center">
                <mat-spinner></mat-spinner>
              </div>

            </div>


          </as-split-area>
        </as-split>
      </div>


    </div>

  </div>
</div>
