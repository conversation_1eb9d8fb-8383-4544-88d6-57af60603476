<button mat-raised-button color="primary" [class]="className" [disabled]="disable" (click)="onClickButton()">
    <i *ngIf="icon == 'add'" class="plus">
        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15">
            <path id="Icon_awesome-plus-circle" data-name="Icon awesome-plus-circle" d="M8.063.563a7.5,7.5,0,1,0,7.5,7.5A7.5,7.5,0,0,0,8.063.563Zm4.355,8.347a.364.364,0,0,1-.363.363H9.272v2.782a.364.364,0,0,1-.363.363H7.216a.364.364,0,0,1-.363-.363V9.272H4.071a.364.364,0,0,1-.363-.363V7.216a.364.364,0,0,1,.363-.363H6.853V4.071a.364.364,0,0,1,.363-.363H8.909a.364.364,0,0,1,.363.363V6.853h2.782a.364.364,0,0,1,.363.363Z" transform="translate(-0.563 -0.563)" fill="#fff"/>
          </svg>
    </i>
     <mat-icon class="add-circle-icon"  *ngIf="icon == 'minus'">do_disturb_on</mat-icon>
                                  
    <span style="margin-left: 4px !important;"> 
        <!-- {{text | translate}} -->
         <!-- {{ translationService.translate(text) }} -->
           {{ text }}
    </span>
</button>
