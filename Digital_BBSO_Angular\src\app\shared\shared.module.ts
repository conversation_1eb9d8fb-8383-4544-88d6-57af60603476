
import { NgModule } from '@angular/core';

import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';

import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { SideBarComponent } from './sidebar/sidebar.component';

import { NgApexchartsModule } from 'ng-apexcharts';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { CommonLibModule } from '../common-lib/common-lib.module';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { SiteUnitSearchComponent } from '../common-code/site-unit-search/site-unit-search.component';
import { SiteUnitComponent } from '../common-code/site-unit/site-unit.component';
import { AppSubHeader } from './sub-header/sub-header.component';
import { CommonFilterComponent } from '../common-code/common-filter/common-filter.component';
import { UserInterDailogComponent } from './user-integration-dialog/userInter-dailog.component';


@NgModule({
  declarations: [
    HeaderComponent,
    SideBarComponent,
    AppSubHeader,
    SiteUnitSearchComponent,
    SiteUnitComponent,
    CommonFilterComponent,
    UserInterDailogComponent
  ],
  imports: [
    CommonModule,
    CommonLibModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    SelectDropDownModule,
    NgApexchartsModule,
    NgxMaterialTimepickerModule,
  ],
  exports:[ 
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CommonLibModule,
    FlexLayoutModule,
    SelectDropDownModule,
    HeaderComponent,
    SideBarComponent,
    AppSubHeader,
    SiteUnitSearchComponent,
    SiteUnitComponent,
    CommonFilterComponent,
    NgApexchartsModule,
    NgxMaterialTimepickerModule,
    UserInterDailogComponent
],
  providers: [ 
  ],
})
export class SharedModule { }

