<div *ngIf="loaderFlag" class="spinner-body">
  <mat-spinner class="spinner"></mat-spinner>
</div>
<div *ngIf="showWelcomeSupplier">
  <div>
    <h3>{{ labels['welcomeSAP'] }}</h3>
  </div>
  <div class="form-row" style="margin-top: 20px;"></div>
  <div class="form-field">
    <label>{{ labels['welcomeSAPDesc'] }}</label>
  </div>
  <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonStartAudit'] }}" (buttonAction)="closeWelcomeSupplier()"
    style="margin-left: 20px;"></common-lib-button>
</div>

<mat-horizontal-stepper linear="true" #stepper *ngIf="!showWelcomeSupplier">
  <mat-step>
    <form [formGroup]="companyForm">
      <ng-template matStepLabel>{{ labels['tableheaderGeneralinformation'] }}</ng-template>
      <ng-template matStepperIcon="edit">
        <mat-icon>done</mat-icon>
      </ng-template>
      <div fxLayout="row wrap" fxLayoutGap="20px">
        <!-- <form [formGroup]="companyForm"  class="company-form"> -->
        <div class="company-form">
          <!-- <div>
                <h3>{{ 'OBSERVATION.SCORECARD.TABLE_HEADER.GENERAL_INFORMATION' | translate }}</h3>
              </div>
               -->
          <div class="form-row" style="margin-top: 20px;">
            <div class="form-field">
              <label>{{ labels['tableheadingCompanyname'] }}</label>
              <input formControlName="companyName">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingTelephone'] }}</label>
              <input formControlName="telephone">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingHqstreetaddress'] }}</label>
              <input formControlName="streetAddress">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingMaincontactname']}}</label>
              <input formControlName="mainContactName">
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label>{{ labels['tableheadingCity'] }}</label>
              <input formControlName="city">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingMobile'] }}</label>
              <input formControlName="mobile">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingState']}}</label>
              <input formControlName="region">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingJobtitle']}}</label>
              <input formControlName="jobTitle">
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label>{{ labels['tableheadingPostalcode'] }}</label>
              <input formControlName="postalCode">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingEmail'] }}</label>
              <input formControlName="email">
            </div>
            <div class="form-field">
              <label>{{ labels['tableheadingCountry'] }}</label>
              <input formControlName="country">
            </div>
            <div class="form-field">
              <label>{{labels['tableheadingCorporatewebsite'] }}</label>
              <input formControlName="website">
            </div>
          </div>
          <!-- <div style="margin-top: 20px;"><h3>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.PROD_INFO' | translate }}</h3></div> -->


          <!-- <div class="form-row" style="margin-top: 30px;">
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.PRODUCT_FAMILY' | translate }}</label>
                  <input formControlName="productFamily">
                </div>
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.PRODUCT' | translate }}</label>
                  <input formControlName="product">
                </div>
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.LOCATION_MANUFACTURING_SITE' | translate }}</label>
                  <input formControlName="manufacturingSite">
                </div>
                <div class="form-field">
                  <label>{{ labels['tableheadingMaincontactname']}}</label>
                  <input formControlName="productContactName">
                </div>
              </div> -->

          <!-- <div class="form-row">
                <div class="form-field">
                  <label>{{ labels[''] }}</label>
                  <input formControlName="productPostalCode">
                </div>
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.JOB_TITLE' | translate }}</label>
                  <input formControlName="productJobTitle">
                </div>
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.COUNTRY' | translate }}</label>
                  <input formControlName="productCountry">
                </div>
                <div class="form-field">
                  <label>{{ 'OBSERVATION.SCORECARD.TABLE_HEADING.TELEPHONE' | translate }}</label>
                  <input formControlName="productTelephone">
                </div>
              </div> -->

          <div class="form-actions">
            <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonBack'] }}"
              (buttonAction)="onCancel()"></common-lib-button>
            <common-lib-button [className]="'cst-btn'" text="{{ labels['next'] }} "
              (buttonAction)="goNext(stepper)" style="margin-left: 20px;"></common-lib-button>
            <!-- (buttonAction)="onnext()"  -->
          </div>
          <!-- </form> -->
        </div>
      </div>

    </form>
  </mat-step>
  <mat-step>
    <ng-template matStepLabel>{{ labels['qualityassessmentTitle'] }}</ng-template>
    <ng-template matStepperIcon="edit">
      <mat-icon>done</mat-icon>
    </ng-template>
    <div fxLayout="row wrap" fxLayoutGap="20px" *ngIf="auditExternalId && stepTwo">
      <app-quality-assessment style="width: 100%;" (dataEmitter)="eventEmitClick($event)"  [stepper]="stepper" [auditId]="auditExternalId"></app-quality-assessment>
      <!-- <app-audit-plan-quality-assessment style="width: 100%;" (dataEmitter)="eventEmitClick($event)"  [stepper]="stepper" [auditId]="auditExternalId"></app-audit-plan-quality-assessment> -->
    </div>
  </mat-step>
  <mat-step>
    <ng-template matStepLabel>{{ labels['tableheadingEvidences'] }}</ng-template>
    <ng-template matStepperIcon="edit">
      <mat-icon>done</mat-icon>
    </ng-template>
    <div fxLayout="row wrap" fxLayoutGap="20px" *ngIf="auditExternalId && stepUpload">
      <!-- <app-audit-plan-quality-file-doc *ngIf="stepFile" style="width: 100%;" [stepper]="stepper" [auditData]="auditData"></app-audit-plan-quality-file-doc> -->
      <app-quality-upload-doc *ngIf="stepUpload" style="width: 100%;" [stepper]="stepper" [auditData]="auditData"></app-quality-upload-doc>
    </div>
  </mat-step>
</mat-horizontal-stepper>
