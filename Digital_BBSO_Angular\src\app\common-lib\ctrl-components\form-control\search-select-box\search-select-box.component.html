
  <mat-form-field [class]="className" class="search-select-box">
    <mat-select (selectionChange)="selectItem($event.value)" placeholder="Select Unit">
      <mat-select-filter [noResultsMessage]="'COMMONFILTER.NO_RESULTS' | translate " [array]="options" [displayMember]="'name'" (filteredReturn)="filteredVariables = $event"></mat-select-filter>
      <mat-option *ngFor="let option of filteredVariables;" [value]="option">
        {{option.name}}
      </mat-option>
    </mat-select>
  </mat-form-field>