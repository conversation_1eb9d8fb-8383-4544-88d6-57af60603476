import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, OnDestroy  } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable, asyncScheduler, map, startWith } from 'rxjs';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';
import * as _ from 'lodash';
import { UserInterDailogComponent } from 'src/app/shared/user-integration-dialog/userInter-dailog.component';
import { ObservationSendMailComponent } from '../observation-module/observation-send-mail/observation-send-mail.component';
import { LanguageService } from '../services/language.service';
import { NgZone } from '@angular/core';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-barchartdetails',
  templateUrl: './barchartdetails.component.html',
  styleUrls: ['./barchartdetails.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush // Use OnPush change detection strategy
})
export class BarchartdetailsComponent implements OnInit, OnDestroy

// {
//   datas: any
//   constructor(
//     public dialogRef: MatDialogRef<BarchartdetailsComponent>,
//     @Inject(MAT_DIALOG_DATA) public data: any,
//     public router: Router,
//     private dataService: DataService,
//     private commonService: CommonService,
//     private cdRef: ChangeDetectorRef,
//     private translate: TranslateService
//   ) {
//     var _this= this
//     this.datas= data
//     console.log("data",this.datas)
//     setTimeout(() => {
//       _this.triggerAltKey()
//     }, 10000);
    
//   }

//   ngOnDestroy(): void {
//     var _this= this
//     // _this.triggerAltKey(); // Trigger the Alt key when the component is destroyed
//   }
//   ngOnInit(): void {
//     var _this= this
//   }

//   onClose(): void {
//     this.dialogRef.close();
//   }

//   cancelClick(): void {
//     var _this= this
//     _this.dialogRef.close();
    
//     this.triggerAltKey()
//     console.log("close")
//     _this.cdRef.detectChanges()
//   }

//   triggerAltKey(): void {
//     console.log("triggerAltKey")
//     const altKeyEvent = new KeyboardEvent('keydown', {
//       key: 'Alt',
//       code: 'AltLeft',
//       keyCode: 18,
//       charCode: 18,
//       bubbles: true,
//       cancelable: true,
//       view: window
//     });

//     document.dispatchEvent(altKeyEvent);
//   }
// }

{

  x: any = 60;
  y: any = 40;
  minSize: any = 100;

  showhide: boolean = false;
  expandFlag: boolean = false;
  errorSpinner: boolean = false;
  labels = {}
  
  logList = [
  ]

  siteControl: FormControl = new FormControl("");

  site: any;

  // @Input() listType: any = "Observation";
listType: any;
  filterFlag = '';
  filteredSiteOptions: Observable<any[]>;
  siteList = [];
  filteredSiteList = [];
  url3: string = "";
  filteredUnitOptions: Observable<any[]>;

  unitControl: FormControl = new FormControl('');
  unitList = [];
  filteredUnitList = [];

  loaderFlag: boolean;

  searchControl: FormControl = new FormControl("");
  @Output() newItemEvent: EventEmitter<any> = new EventEmitter();

  processControl: FormControl = new FormControl("Observation");

  process = [
    {
      id: 1,
      name: "Observation",

    },
    {
      id: 2,
      name: "Field Walk",

    },
    {
      id: 3,
      name: "Audit",

    },


  ]
  filteredProcessOptions: Observable<any[]>


  observationControl: FormControl = new FormControl("Behaviour");
  observation = [
    {
      name: "Behaviour",
    },
    {
      name: "Hazards",
    },
    {
      name: "Incidents",
    },
  ]

  categoryControl: FormControl = new FormControl("PPE");
  category = [
    {
      name: "PPE"
    },
    {
      name: "Tools & Equipment"
    },
    {
      name: "Work Environment"
    },

  ]

  observeType: FormControl = new FormControl("");
  filteredObserveTypeOptions: Observable<any[]>

  // category: FormControl = new FormControl("");
  // filteredCategoryOptions: Observable<any[]>

  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });

  startDateControl: FormControl = new FormControl("");
  endDateControl: FormControl = new FormControl("");
  displayedColumns: any = [];

  allColumns = [
    { name: 'externalId', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name: 'date', displayName: "Date", key: "date", activeFlag: true, summary: false },
    { name: 'week', displayName: "Week", key: "week", activeFlag: false, summary: false },
    { name: 'crafts', displayName: "Craft", key: "craft", activeFlag: true, summary: false },
    { name: 'site', displayName: "Site", key: "site", activeFlag: true, summary: false },
    { name: 'unit', displayName: "Unit", key: "unit", activeFlag: true, summary: false },
    { name: 'shortDescription', displayName: "Short Description", key: "shortDescription", activeFlag: true, summary: false },
    { name: 'category', displayName: "Category", key: "category", activeFlag: true, summary: false },
    { name: 'subCategory', displayName: "Sub Category", key: 'subCategory', activeFlag: true, summary: false },
    { name: 'observationType', displayName: "Observation Type", key: "observationType", activeFlag: true, summary: false },
    { name: 'observationStatus', displayName: "Status", key: "status", activeFlag: true, summary: false },
    { name: 'description', displayName: "Description", key: "description", activeFlag: false, summary: false },
    { name: 'createdTime', displayName: "Created On", key: "createdOn", activeFlag: true, summary: false },
    { name: 'createdBy', displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: 'lastUpdatedTime', displayName: "Updated On", key: "updatedOn", activeFlag: false, summary: false }, 
    { name: 'modifiedBy', displayName: "Updated By", key: "updatedBy", activeFlag: false, summary: false },
    { name: 'actions', displayName: "Actions", key: "actions", activeFlag: true, summary: false },
  ];
  allColumnsfieldwalk = [

    { name: 'externalId', displayName: "Id", key: "id", activeFlag: true, summary: false },
    { name: 'date', displayName: "Date", key: "date", activeFlag: true, summary: false },
    { name: 'site', displayName: "Site", key: "site", activeFlag: true, summary: false },
    { name: 'unit', displayName: "Unit", key: "unit", activeFlag: true, summary: false },
    { name: 'createdOn', displayName: "Created On", key: "createdOn", activeFlag: true, summary: false },
    { name: 'createdBy', displayName: "Created By", key: "createdBy", activeFlag: true, summary: false },
    { name: 'lastUpdatedTime', displayName: "Updated On", key: "updatedOn", activeFlag: true, summary: false },
    { name: 'modifiedBy', displayName: "Updated By", key: "updatedBy", activeFlag: true, summary: false },
    { name: 'actions', displayName: "Actions", key: "actions", activeFlag: true, summary: false },
    ];
  url: string = "";
  initialDataFlag = 0;
  userAccessMenu;
  createOb_obj: any;
  createHa_obj: any;

  constructor(private cd: ChangeDetectorRef,public dialogRef: MatDialogRef<BarchartdetailsComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router, private commonService: CommonService, private tokenService: TokenService, private dataService: DataService, private dialog: MatDialog, private toastr: ToastrService,
    private ngZone:NgZone, private languageService: LanguageService
  ) {
      this.listType=data.listType
    this.url = this.dataService.React_API + "/observationList";
    this.url3= this.dataService.React_API + "/fieldWalk";
    this.labels = {
      'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
      'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
      'reacttableExport': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableExport'] || 'reacttableExport',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'] || 'observations',
      'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
      'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
      'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'] || 'expand',
      'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'] || 'collapse',
      'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'] || 'logInfo',
      'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'] || 'type',
      'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
    }
  }
  deleteOb_obj: any;
  selectedSite: any;

  ngOnInit(): void {
    var _this=this

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'reacttableColsummary': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColsummary'] || 'reacttableColsummary',
          'reacttableColselection': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'reacttableColselection'] || 'reacttableColselection',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'observations': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observations'] || 'observations',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'expand': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'expand'] || 'expand',
          'collapse': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'collapse'] || 'collapse',
          'logInfo': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'logInfo'] || 'logInfo',
          'type': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'type'] || 'type',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })

    _this.loaderFlag = true
    _this.cd.detectChanges()
    if (this.listType == "Observation" || this.listType == "Hazards"){
    var _this = this;

    var _this = this;
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
    //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
    if(_this.userAccessMenu){
      _this.getUserMenuConfig();
    }
  
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }

    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.siteControl.setValue(_this.dataService.siteId);
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    
    if (_this.commonService.filterListFetched) {
      _this.filterInit('Site');
      _this.filterInit('Unit');
    }
  
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        _this.filterInit(fiterType);
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.processLoad(() => { });
      // _this.siteChanged();
    });
    _this.unitControl.valueChanges.subscribe(value => {
   
      _this.processLoad(() => { });

    });

    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);


    // setTimeout(function () {
    //   console.log("applied")
    //   _this.applyObservationFilter();
    // }, 1500);

    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "ObservationList") {
        if (e.data.action == "FormView") {
          _this.router.navigateByUrl('observations/observation', {
            state: { "externalId": e.data.data.externalId,
                    "pageFrom":"Observation List",
                    "action":"View" }
          });
        _this.cancelClick();
        } else if (e.data.action == "FormDelete") {
          _this.deleteObservation(e.data.data.externalId,e.data.data.refSite.siteCode)
          // _this.router.navigateByUrl('observations', {
          //   state: { "externalId": e.data.data.externalId,
          //           "pageFrom":"Observation List",
          //           "action":"Delete" }
          // });
        }else if ( e.data.action == "FormEdit") {
          _this.router.navigateByUrl('observations/observation', {
            state: { "externalId": e.data.data.externalId,
                    "pageFrom":"Observation List",
                    "action":"Edit" }
          });
          _this.cancelClick();
        }  else if ( e.data.action == "LangError") {
          _this.applyObservationFilter(); 
        }
        else if ( e.data.action == "Loading") {
              _this.loaderFlag = false
              _this.cd.detectChanges();
        } 
        else if ( e.data.action == "observationHistory") {
       
        _this.showhide = true;
        console.log('observationHistory',_this.showhide);
        _this.getLogList(e.data.data.externalId);
        _this.cd.detectChanges();
       } else if( e.data.action == "sendMail"){
        //sendMail
       console.log('sendMail',e.data.data);
       _this.sendMailUser(e.data.data);
       }
         else if (e.data.action == "createAction") {
         // _this.goPage('action/create-action')
          _this.router.navigateByUrl('action/create-action', {
            state: { "data": e.data.data,
                    "pageFrom":e.data.processType,
                    "action":"Create Action" }
          });
          _this.cancelClick();
        }
        else if (e.data.action == "Infield") {
          window.open('https://cognite-infield.cogniteapp.com/observations', '_blank', 'noopener, noreferrer')
        }
      };

    }

    var _this = this;

    this.endDateControl.valueChanges.subscribe(async range => {
      _this.applyObservationFilter();
    });



    this.filteredProcessOptions = this.processControl.valueChanges.pipe(
      startWith('', asyncScheduler),
      map(value => this._metafilter(value || '')),
    );
  }
  if (this.listType =="Field Walk"){
    var _this = this;

    console.log(_this.listType)
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.userAccessMenu = _this.commonService.userIntegrationMenu;
      //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
      if(_this.userAccessMenu){
        _this.getUserMenuConfig();
      }
    }
    if (_this.commonService.processList.length > 0) {
      _this.initialDataFlag = _this.initialDataFlag + 1;
      _this.siteControl.setValue(_this.dataService.siteId);
    }
    if (_this.initialDataFlag > 1) {
      _this.siteControl.setValue(_this.dataService.siteId);
    }

    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1) {
          _this.siteControl.setValue(_this.dataService.siteId);
        }
      }
    })

    _this.siteControl.valueChanges.subscribe(value => {
      _this.dataService.siteId = value;
      _this.processLoad(() => { });
    });

    setTimeout(function () {
      _this.processLoad(() => { });
    }, 2000);


    // setTimeout(function () {
    //   _this.applyObservationFilter();
    // }, 1500);

      window.onmessage = function (e) {
        if (e.data.type && e.data.action && e.data.type == "FieldWalkList") {
          console.log(e.data.data)
          if (e.data.action == "FormView") {
            _this.router.navigateByUrl('observations/field-visit', {
              state: { 
                "process": e.data.data.refOFWAProcess["items"][0], 
                "fieldwalk": e.data.data,
                "pageFrom":"FieldWalk List",
                "action":"View" } 
            });
            _this.cancelClick()
          } else if (e.data.action == "FormEdit") {
            _this.router.navigateByUrl('observations/field-visit', {
              state: { "process": e.data.data.refOFWAProcess["items"][0], "fieldwalk": e.data.data ,
              "pageFrom":"FieldWalk List",
              "action":"Edit"}
            });
            _this.cancelClick()
          } else if (e.data.action == "FormDelete") {
            _this.deleteObservation(e.data.data.externalId,e.data.data.refSite.siteCode)
            // _this.router.navigateByUrl('observations', {
            //   state: { "externalId": e.data.data.externalId,
            //           "pageFrom":"Observation List",
            //           "action":"Delete" }
            // });
          }else if ( e.data.action == "Loading") {
            _this.loaderFlag = false
              _this.cd.detectChanges();
            console.log("LoaderFalse Loading1",_this.loaderFlag)
          } else if ( e.data.action == "observationHistory") {
             console.log("e.data.datahistrory",e.data.data)
            _this.showhide = true;
            console.log('observationHistory',_this.showhide);
            _this.getLogList(e.data.data.externalId);
            _this.cd.detectChanges();
           } else if( e.data.action == "sendMail"){
            //sendMail
           console.log('sendMail',e.data.data);
           _this.sendMailUser(e.data.data);
           }else if (e.data.action == "createAction") {
           // _this.goPage('action/create-action')
            _this.router.navigateByUrl('action/create-action', {
              state: { "data": e.data.data,
                      "pageFrom":_this.commonService.configuration["typeFieldWalk"],
                      "action":"Create Action" }
            });
            _this.cancelClick()
          }
        };


      }

    
    var _this = this;
    
    this.endDateControl.valueChanges.subscribe(async range => {
      _this.applyObservationFilter();
    });



  }
}
  sendMailUser(obData){
    var _this =this;
    
    const dialogRef =  _this.dialog.open(ObservationSendMailComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'mail-dialog', data: obData
   });
   dialogRef.afterClosed().subscribe(result => {
   })


  }
  getLogList(objectId){
    var _this =this;
    _this.logList =[]
    _this.dataService.postData({"objectId":objectId}, _this.dataService.NODE_API + "/api/service/listOFWALog")
    .subscribe(data => {
      var listLog = data['data']['list' + _this.commonService.configuration["typeOFWALog"]]['items'];
      var temp = []
      listLog.forEach(element => {
        var obj =    {
          externalId:element.objectExternalId,
          logType:element.logType,
          name:element.refUser?element.refUser.firstName+' '+element.refUser.lastName:'',
          panelOpenState:false,
          createdTime:element.dateTime,
       
        }
        temp.push(obj)
       
      });
      _this.logList = temp
      _this.errorSpinner = true;
      _this.cd.detectChanges();
  
                     
    })

  }
  closeClick(){
    var _this =this;
    _this.showhide = false;
  }
  expandClick() {
    if (this.expandFlag) {
      this.expandFlag = false;
      this.x = 60;
      this.y = 40;

    
    } else {
      this.expandFlag = true;
      this.x = 0;
      this.y = 100;


    }

  }

  
  cancelClick(): void {
    var _this= this
    _this.dialogRef.close();
    
    this.triggerAltKey()
    console.log("close")
    _this.cd.detectChanges()
  }

    triggerAltKey(): void {
    console.log("triggerAltKey")
    const altKeyEvent = new KeyboardEvent('keydown', {
      key: 'Alt',
      code: 'AltLeft',
      keyCode: 18,
      charCode: 18,
      bubbles: true,
      cancelable: true,
      view: window
    });

    document.dispatchEvent(altKeyEvent);
  }
  createFW_obj: any;

  getUserMenuConfig(){
    var _this = this
 if (this.listType == "Observation" || this.listType == "Hazards") {
  if(_this.siteControl.value != _this.dataService.siteId){
    _this.siteControl.setValue(_this.dataService.siteId)
    setTimeout(()=>{
      // _this.siteChanged();
    },1000)
  
  }
    if(_this.commonService.menuFeatureUserIn.length>0){
      // var homeMenu = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == this.dataService.appMenuCode.homeMenu);
      // if( !homeMenu || (homeMenu && homeMenu.featureAccessLevelCode == "NoAccess")){
      // _this.router.navigateByUrl('noaccess/no-access', {})

      // }
    _this.createOb_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.createObservation);
    _this.createHa_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardCreateAction);
    
      var obView;
      var obEdit;
      var obdelete;
      var ob3Dview;
      var obCreateAction;
      var obHistory;
      var obShare;
   
    if(_this.listType == "Observation"){
      obView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView);
      obEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationEdit);
      // obdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationDelete);
      obdelete = "a"
      ob3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView3d);
      obCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationCreateAction);
      obHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationHistory);
      obShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationShare);
      
    }else{
      obView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardView);
      obEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardEdit);
      // obdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardDelete);
      obdelete = "a"
      // var ob3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationView3d);
      obCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardCreateAction);
      obHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.hazardHistory);
      obShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.observationShare);
      
    }
    

    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
    iWindow?.postMessage({ "type": "ObservationList", "action": "AccessMenu", "data": {obView:obView,obEdit:obEdit,ob3Dview:ob3Dview,obShare:obShare,obCreateAction:obCreateAction, obdelete:obdelete,obHistory:obHistory} }, '*');
      
    }else{
      _this.createOb_obj =  {}
      _this.createHa_obj = {}
      var iframe = document.getElementById('iFrameObservationList');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "ObservationList", "action": "AccessMenu", "data": {obView:{},obEdit:{},ob3Dview:{},obShare:{},obCreateAction:{}, obdelete:{},obHistory:{}} }, '*');
  
    }
  
 }
 if (this.listType == "Field Walk") {
   
  if(_this.siteControl.value != _this.dataService.siteId){
    _this.siteControl.setValue(_this.dataService.siteId)
  }
    if(_this.commonService.menuFeatureUserIn.length>0){
    _this.createFW_obj = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkCreate);
    var FWView = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkView);
    var FWEdit = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkEdit);
    var FW3Dview = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalk3dAssest);
    var FWCreateAction = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkCreateAction);
    // var  FWdelete = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkDelete);
    var FWdelete= "a";
    var  FWHistory = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkHistory);
    var   FWShare = _this.commonService.menuFeatureUserIn.find(item => item.featureCode == _this.dataService.appMenuCode.fieldWalkShare);

     var iframe = document.getElementById('iFrameFieldWalk111');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FieldWalk", "action": "AccessMenu", "data": {FWView:FWView,FWEdit:FWEdit,FW3Dview:FW3Dview,FWCreateAction:FWCreateAction,FWdelete:FWdelete,FWHistory:FWHistory,FWShare:FWShare} }, '*');
    iWindow?.postMessage(
      {
        type: 'Language',
        action: 'Language',
        LanguageCode: `${this.commonService.selectedLanguage.toUpperCase()}`,
        idToken: this.tokenService.getIDToken(),
        labels: _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()], 
      },
      '*'
    );
      
    }else{
      _this.createFW_obj = {}
       var iframe = document.getElementById('iFrameFieldWalk111');
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "FieldWalk", "action": "AccessMenu", "data": {FWView:{},FWEdit:{},FW3Dview:{},FWCreateAction:{},FWdelete:{},FWHistory:{},FWShare:{}} }, '*');
        
    }
 }
    

  }

  deleteObservation(id:string, sitecode:string) {
    var _this = this
    console.log(_this.observation)

    const dialogRef =  _this.dialog.open(UserInterDailogComponent, {
      width: '427px',
      minWidth: '427px !important', panelClass: 'confirmation-dialog', data: {
        title:'createactionAreyousure',
       }
   });
   dialogRef.afterClosed().subscribe(result => {
    console.log('result',result)
      if(result == 'YES'){
        _this.commonService.loaderFlag = true;
        var site = _this.commonService.siteList.find(e => e.externalId == _this.observation["refSite"])
        _this.selectedSite = site;
var softdata={
"type": _this.commonService.configuration["typeObservation"],
      "siteCode": sitecode,
      "unitCode": _this.commonService.configuration["allUnitCode"],
      "items": [
        {
          "externalId": id,
          "isActive" : false,
        }
      ]
}
console.log(softdata)
  _this.dataService.postData(softdata, _this.dataService.NODE_API + "/api/service/createInstanceByProperties")
  .subscribe(data => {
    // console.log(data)
    this.toastr.success('', 'This observation has been deleted', {
      timeOut: 3000,
    });
    this.applyObservationFilter();
    _this.commonService.loaderFlag = false;
    _this.loaderFlag = false

  })
        _this.cd.detectChanges();
      }
  });

  

  }
  
  processLoad(cb) {
    var _this = this;
    _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
    _this.applyObservationFilter();
    cb();
  }


  goSupplier() {
    //  this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier');
    this.toastr.success('', 'Quality Assessment Questionnaire has been send to the supplier', {
      timeOut: 3000,
    });
  }
  ngAfterViewInit(): void {

    var _this = this;
    
    _this.cd.detectChanges()
  if (_this.listType == "Observation" || _this.listType == "Hazards") {
  
      var iframe = document.getElementById('iFrameObservationList');
      console.log("HIIIIIIIIIIIIII", iframe)
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "ObservationList", "action": "Column", "data": _this.displayedColumns }, '*');
      _this.getUserMenuConfig()
      // _this.loaderFlag = true;
  }
  if (_this.listType == "Field Walk") {
    var _this = this;
    _this.loaderFlag = true
    console.log("loaderFlag   1",_this.loaderFlag)
      var iframe = document.getElementById('iFrameFieldWalk111');
      console.log("HIIIIIIIIIIIIII", iframe)
      if (iframe == null) return;
      var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
      iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
      iWindow?.postMessage({ "type": "FieldWalk", "action": "Column", "data": _this.displayedColumns }, '*');
      _this.getUserMenuConfig()
  }

  }

  applyObservationFilter() {
    if ( this.listType == "Observation" || this.listType == "Hazards") {
      var _this = this;
    _this.loaderFlag = false
    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var startD;
    var endD;
    iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    if (_this.endDateControl.value) {
      var myStartDate = new Date(this.startDateControl.value);
      startD = myStartDate.getFullYear() + "-" + (("0" + (myStartDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myStartDate.getDate())).slice(-2));
      var myEndDate = new Date(this.endDateControl.value);
      endD = myEndDate.getFullYear() + "-" + (("0" + (myEndDate.getMonth() + 1)).slice(-2)) + "-" + (("0" + (myEndDate.getDate())).slice(-2));

    }
    var sites = []
    var units = []
    if (this.siteControl.value) {
      sites = [this.siteControl.value]
    }
    if (this.unitControl.value) {
      units = [this.unitControl.value]
    }
    iWindow?.postMessage({ "type": "ObservationList", "action": "Filter", "subCategory": "","listType":_this.listType, "date": { start: startD, end: endD }, sites: sites,units:units, LanguageCode: `${this.commonService.selectedLanguage}`,tabledata:this.data.tabledata }, '*');
    _this.getUserMenuConfig();
    }

    if (this.listType == "Field Walk") {
      var _this = this;
    var iframe = document.getElementById('iFrameFieldWalk111');
    console.log("HIIIIIIIIIIIIII", iframe)
    if (iframe == null) {
      return;
    };
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    var startD;
    var endD;
    iWindow?.postMessage({ "type": "AuthToken","user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    if(_this.endDateControl.value){
      var myStartDate = new Date(this.startDateControl.value);
      startD = myStartDate.getFullYear() +"-"+(("0" + (myStartDate.getMonth() + 1)).slice(-2))+"-"+(("0" + (myStartDate.getDate())).slice(-2));
      var myEndDate = new Date(this.endDateControl.value);
      endD = myEndDate.getFullYear() +"-"+(("0" + (myEndDate.getMonth() + 1)).slice(-2))+"-"+(("0" + (myEndDate.getDate())).slice(-2));
     
    }
    var sites = []
    if(this.siteControl.value){
      sites = [this.siteControl.value]
    }
    iWindow?.postMessage({ "type": "FieldWalk", "action": "Filter", "listType":_this.listType, "subCategory": "","date":{start:startD,end:endD},sites:sites, 'LanguageCode': `${this.commonService.selectedLanguage}`,tabledata:this.data.tabledata }, '*');
    _this.getUserMenuConfig()
    }
    
  }
  ngOnDestroy(): void {

  }
  settingClick() {
    if (this.listType == "Observation" || this.listType == "Hazards") {
      const dialogConfig = new MatDialogConfig();
      dialogConfig.data = { allColumns: this.allColumns };
      dialogConfig.disableClose = true;
      dialogConfig.autoFocus = true;
      dialogConfig.position = { top: "150px", right: '10px' }
  
      const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
      const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
      dialogRef.afterClosed().subscribe(result => {
        if (typeof result == "object") {
          this.setColumn(result);
        }
      });
    }
if (this.listType == "Field Walk") {
  const dialogConfig = new MatDialogConfig();
  dialogConfig.data = { allColumns: this.allColumnsfieldwalk };
  dialogConfig.disableClose = true;
  dialogConfig.autoFocus = true;
  dialogConfig.position = { top: "150px", right: '10px' }

  const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
  const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
  dialogRef.afterClosed().subscribe(result => {
    if (typeof result == "object") {
      this.setColumn(result);
    }
  });
  }
}
  setColumn(selColumn) {
    if (this.listType == "Observation" || this.listType == "Hazards") {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
  }
  if (this.listType == "Field Walk") {
    var _this = this;
    this.allColumnsfieldwalk = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumnsfieldwalk.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
  }

  }
  filterInit(fiterType) {
    var _this = this;

    if (fiterType == 'Site') {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.commonService.siteList.slice();
      console.log('_this.siteList', _this.siteList);
      _this.commonService.observeListSubject.next(true);
    }
    if (fiterType == 'Unit') {
      _this.unitList = _this.commonService.unitList;
      _this.filteredUnitList = _this.commonService.unitList.slice();
    }
    //  _this.userData = _this.commonService.userInfo
  }

  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]['value'] = '';

      _this.filteredSiteList = _this.siteList.slice();
      _this.filteredUnitList = _this.unitList.slice();
     
    }
  }
  // unitChanged() {
  //   var _this = this;

  //   var selectedUnit = _this.commonService.getSelectedValue(
  //     _this.unitControl.value
  //   );
  //   if (selectedUnit.length > 0) {
  //     let result = Array.isArray(selectedUnit);
  //     console.log('selectedUnit', selectedUnit);
  //     if (result == true) {
  //       var mySite = [];
  //       var finalSite = _this.commonService.siteList.filter(function (e) {
  //         var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
  //           if (selectedUnit.indexOf(f.externalId) > -1) {
  //             mySite.push(e['externalId']);
  //             return true;
  //           }
  //         });
  //         return siteFiltered.length > 0;
  //       });
  //       console.log('mySite', mySite);

  //       _this.filterFlag = 'Unit';
  //       _this.siteControl.setValue(mySite);
  //     } else {
  //       var mySit;

  //       var finalSite = _this.commonService.siteList.filter(function (e) {
  //         var siteFiltered = e['reportingUnits']['items'].filter(function (f) {
  //           if (f.externalId == selectedUnit) {
  //             console.log('f.externalId', f.externalId);
  //             mySit = e.externalId;
  //             return true;
  //           }
  //         });
  //         return siteFiltered.length > 0;
  //       });
  //       console.log('mySite', mySite);

  //       _this.filterFlag = 'Unit';
  //       _this.siteControl.setValue(mySit);
  //     }
  //   } else {
  //     selectedUnit = _this.unitList.map((eItem) => eItem.externalId);
  //   }
  // }
  // siteChanged() {
  //   var _this = this;
  //   var selectedSite = _this.commonService.getSelectedValue(
  //     _this.siteControl.value
  //   );
  //   _this.unitList = [];
  //   _this.filteredUnitList = _this.unitList.slice();

  //   if (selectedSite.length > 0) {
  //     var myCountries = [];
  //     var myRegions = [];
  //     _this.commonService.siteList.filter(function (e) {
  //       if (selectedSite.indexOf(e.externalId) > -1) {
  //         if (e['country']) {
  //           myCountries.push(e['country']['externalId']);
  //           if (e['country']['parent'])
  //             myRegions.push(e['country']['parent']['externalId']);
  //         }
  //       }
  //       return selectedSite.indexOf(e.externalId) > -1;
  //     });
  //     _this.filterFlag = 'Site';
  //   } else {
  //     selectedSite = _this.siteList.map((eItem) => eItem.externalId);
  //   }

  //   if (selectedSite.length > 0) {
  //     _this.commonService.siteList.filter(function (e) {
  //       if (selectedSite.indexOf(e.externalId) > -1) {
  //         if (
  //           e['reportingUnits']['items'] &&
  //           e['reportingUnits']['items'].length > 0
  //         ) {
  //           _this.unitList = _this.unitList.concat(
  //             _.orderBy(e['reportingUnits']['items'], ['name'], ['asc'])
  //           );
  //           _this.unitList = _.uniqBy(_this.unitList, 'externalId');
  //           _this.filteredUnitList = _this.unitList.slice();
  //         }
  //       }
  //       return selectedSite.indexOf(e.externalId) > -1;
  //     });
  //   } else {
  //     if (_this.siteList.length > 0) {
  //       _this.unitList = _this.commonService.unitList;
  //       _this.filteredUnitList = _this.unitList.slice();
  //     }
  //   }
  // }

  summaryClick() {
    if (this.listType == "Observation" || this.listType == "Hazards") {
    var myColumns = [];
    console.log("summaryClick", this.allColumns)
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });
  }
  if (this.listType == "Field Walk") {
    console.log("summaryClick", this.allColumnsfieldwalk)
    var myColumns = [];
    this.allColumnsfieldwalk.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });
  }
  }
  setSummary() {
 if (this.listType == "Observation" || this.listType == "Hazards") {
  var _this = this;
  var summaryCol = {};
  this.allColumns.forEach(function (eData) {
    if (eData.summary) {
      summaryCol[eData.name] = {
        "enable": "Y",
        "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
      };
    }
  });
  // this.columnSummarysubject.next(summaryCol);
  var iframe = document.getElementById('iFrameObservationList');
  if (iframe == null) return;
  var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
  iWindow?.postMessage({ "type": "AuthToken",  "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
  iWindow?.postMessage({ "type": "ObservationList", "action": "Summary", "data": summaryCol }, '*');
  _this.getUserMenuConfig();
 }
    
    if (this.listType == "Field Walk") {
      
    var _this = this;
    var summaryCol = {};
    this.allColumnsfieldwalk.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    // this.columnSummarysubject.next(summaryCol);
    var iframe = document.getElementById('iFrameFieldWalk111');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'), "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FieldWalk", "action": "Summary", "data": summaryCol }, '*');
    _this.getUserMenuConfig()
    }
  }

  excelExport(){
    var _this = this
    var iframe = document.getElementById('iFrameObservationList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "user": localStorage.getItem('user'),  "isSiteAdmin": _this.commonService.isSiteAdmin(), "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "ObservationList", "action": "Excel", "data": {} }, '*');

  }



  private _metafilter(value) {
    const filterValue: any = this._metanormalizeValue(value);
    return this.process.filter((street) => this._metanormalizeValue(street.name).includes(filterValue));
  }

  private _metanormalizeValue(value: any): any {
    return value.toLowerCase().replace(/\s/g, '');
  }

  processSelected(process) {
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  observeTypeSelected(type) {

  }
  categorySelected(cate) {

  }

  // createObservation() {
  //   var _this = this;
  //   console.log("kkkkkkk")
  //   // observations
  //   var processList = _this.commonService.processList.filter(e => {
  //     return e.processType == "Process" && e.name == "Observation" && e.refSite["externalId"] == _this.siteControl.value;
  //   })
  //   _this.router.navigateByUrl('observations/observation', {
  //     state: {
  //       "observation": processList[0],
  //       "pageFrom": "Observation List",
  //       "action": "Edit"
  //     }
  //   });
  // }
}
