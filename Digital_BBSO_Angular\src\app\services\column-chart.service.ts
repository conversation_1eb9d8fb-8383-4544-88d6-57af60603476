import { Injectable } from "@angular/core";
import {
    ApexAxisChartSeries,
    ApexChart, ApexDataLabels, ApexFill, ApexGrid, ApexLegend, ApexPlotOptions, ApexStroke,
    ApexTitleSubtitle, ApexTooltip, ApexXAxis, ApexYAxis, ChartType
} from "ng-apexcharts";
import { ChartOptions } from "src/app/modals/chart.modal";

@Injectable()

export class ColumnChartService {
    constructor() { }

    getConfigureColumnChartOptions(
        columnWidth?: string | number, dataLabelsEnabled?: boolean,
        chartHeight?: string | number, chartType?: ChartType, toolbar?: boolean,
        showStroke?: boolean, strokeWidth?: number | number[], strokeCurve?: any, strokeColors?: string[],
        fillColors?: any[], opacity?: number | number[], yTooltip?: boolean,
        legendShow?: boolean, legendshowForSingleSeries?: boolean, customLegendItems?: string[],
        legendMarkersFillColors?: string[], xCategories?: string[], isYLabelFortmated?: boolean
    ): ChartOptions {
        let chartOptions: ChartOptions, plotOptions: ApexPlotOptions, dataLabels: ApexDataLabels,
            chart: ApexChart, stroke: ApexStroke, fill: ApexFill, tooltip: ApexTooltip,
            legend: ApexLegend, yaxis: ApexYAxis, xaxis: ApexXAxis, series: ApexAxisChartSeries,
            title: ApexTitleSubtitle, grid: ApexGrid

        chartOptions = {
            plotOptions: {
                bar: {
                    columnWidth: columnWidth ? columnWidth : '100%'
                }
            },
            dataLabels: {
                enabled: dataLabelsEnabled ? dataLabelsEnabled : false
            },
            chart: {
                height: chartHeight ? chartHeight : 350,
                type: chartType ? chartType : 'bar',
                toolbar: {
                    show: toolbar ? toolbar : false
                }
            },
            stroke: {
                show: showStroke ? showStroke : false,
                width: strokeWidth ? strokeWidth : 0,
                colors: strokeColors && strokeColors.length ? strokeColors : [],
                curve: strokeCurve
            },
            fill: {
                colors: fillColors && fillColors.length ? fillColors : [],
                opacity: opacity ? opacity : 0
            },
            tooltip: {
                y: yTooltip ? {
                    formatter: function (val) {
                        return "$ " + val + " thousands";
                    }
                } : {}
            },
            legend: {
                show: legendShow ? legendShow : false,
                showForSingleSeries: legendshowForSingleSeries ? legendshowForSingleSeries : false,
                customLegendItems: customLegendItems && customLegendItems.length ? customLegendItems : [],
                markers: {
                    fillColors: legendMarkersFillColors && legendMarkersFillColors.length ? legendMarkersFillColors : []
                }
            },
            yaxis: {
                labels: {
                    style: {
                        fontSize: '12.65px',
                        fontFamily: 'Myriad-Regular',
                        fontWeight: 400,
                        colors: ["#080808"],
                        cssClass: 'apexcharts-yaxis-label',
                    },
                    formatter: function (val) {
                        if (isYLabelFortmated) {
                            return val + "";
                        }
                        return val + '';
                    }
                }
            },
            xaxis: {
                categories: xCategories && xCategories.length ? xCategories : [],
                labels: {
                    style: {
                        fontSize: '12.65px',
                        fontFamily: 'Myriad-Regular',
                        fontWeight: 400,
                        colors: ["#080808"],
                        cssClass: 'apexcharts-yaxis-label',
                    }
                }
            },
            series,
            colors: [],
            title,
            grid,
        }
        return chartOptions;
    }
}