import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { FormControl } from '@angular/forms';
import { CogniteAuthentication, CogniteClient } from '@cognite/sdk';
import { TokenService } from 'src/app/services/token.service';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-completed',
  templateUrl: './completed.component.html',
  styleUrls: ['./completed.component.scss']
})
export class CompletedComponent implements OnInit {


  labels = {}
  auditFlag:boolean=false;
  constructor(private tokenService: TokenService, private router: Router, private dataService: DataService, private commonService: CommonService, private languageService: LanguageService, private ngZone:NgZone, private cd:ChangeDetectorRef) {
    this.labels = {
        'toasterSomethingwentwrongmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSomethingwentwrongmail'] || 'toasterSomethingwentwrongmail',
        'toasterCompthankyou': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterCompthankyou'] || 'toasterCompthankyou',
    }
    var _this = this;
    console.log(this.router.getCurrentNavigation())
   
    if (this.router.getCurrentNavigation() && this.router.getCurrentNavigation().extras.state) {
      console.log(this.router.getCurrentNavigation().extras.state)
      this.auditFlag = true
    }
  }

  ngOnInit(): void {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'toasterSomethingwentwrongmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterSomethingwentwrongmail'] || 'toasterSomethingwentwrongmail',
          'toasterCompthankyou': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'toasterCompthankyou'] || 'toasterCompthankyou',
      }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      _this.cd.detectChanges();
    })
    
  }

  goPage(page) {
    this.router.navigate([page]);
  }

}
