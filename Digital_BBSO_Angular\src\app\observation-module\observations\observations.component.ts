import { After<PERSON>iewInit, ChangeDetectorRef, Component, Ng<PERSON><PERSON>, OnDestroy, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { Observable, Subject, asyncScheduler, map, startWith } from 'rxjs';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from 'src/app/services/TranslationService/translation.services';
import { LanguageService } from 'src/app/services/language.service';
import { environment } from 'src/environments/environment';
import _ from 'cypress/types/lodash';
@Component({
  selector: 'app-observations',
  templateUrl: './observations.component.html',
  styleUrls: ['./observations.component.scss']
})
export class ObservationsComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
 
 
 
  siteControl: FormControl = new FormControl("");
  siteList = [];
  filteredSiteList = [];
 
  searchControl: FormControl = new FormControl("");
 
 
  corePrincipleFlag: boolean = true;
  processFlag: boolean = false;
  subProcessFlag: boolean = false;
  checkListFlag: boolean = false;
  checkListFlag2: boolean = false;
  checkListFlag3: boolean = false;
  checkListFlag4: boolean = false;
 
  processSubject: Subject<any> = new Subject<any>();
  subProcessSubject: Subject<any> = new Subject<any>();
 
  selectedProcess: any;
  selectedCorePrinciple: any;
  selectedSubProcess: any;
  site: any;
  observation: any;
  siteSubject: Subject<any> = new Subject<any>();
  loaderFlag: boolean = true;
  userAccessMenu: any;
  scheduleDetails: any;
 
  processControl: FormControl = new FormControl();
  processList = [];
  filteredProcessList = [];
  id: boolean;
  labels = {};

  constructor(private sanitizer: DomSanitizer, private dataService: DataService, private commonService: CommonService, private route: ActivatedRoute, private router: Router, private translate: TranslateService, public translationService: TranslationService, private languageService: LanguageService, private changeDetector: ChangeDetectorRef, private ngZone: NgZone) {
 
    if (this.commonService.labelObject[this.commonService.selectedLanguage] && this.commonService.labelObject && this.commonService.selectedLanguage) {
      this.labels = {
        'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
        'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
        'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
        'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
        'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
        'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
        'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
        'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
        'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
        'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
        'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
        'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
        'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
        'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
        'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',
      }
    }
 
  }
  initialDataFlag = 0;
  ngOnInit(): void {
    var _this = this;

    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'site': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'site'] || 'site',
          'commonfilterChoosesite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChoosesite'] || 'commonfilterChoosesite',
          'process': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'process'] || 'process',
          'commonfilterChooseprocess': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterChooseprocess'] || 'commonfilterChooseprocess',
          'commonfilterNoresults': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterNoresults'] || 'commonfilterNoresults',
          'cardsStewardship': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsStewardship'] || 'cardsStewardship',
          'cardsQuality': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsQuality'] || 'cardsQuality',
          'cardsReliability': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsReliability'] || 'cardsReliability',
          'cardsEngagement': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsEngagement'] || 'cardsEngagement',
          'fieldWalk': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'fieldWalk'] || 'fieldWalk',
          'commonfilterSearch': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'commonfilterSearch'] || 'commonfilterSearch',
          'observation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'observation'] || 'observation',
          'cardsHazards': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'cardsHazards'] || 'cardsHazards',
          'audit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'audit'] || 'audit',
          'all': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'all'] || 'all',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      _this.changeDetector.detectChanges();
    })

   _this.loaderFlag = true;
    console.log(history.state);
    if (_this.commonService.siteList.length > 0) {
      _this.siteList = _this.commonService.siteList;
      _this.filteredSiteList = _this.siteList.slice();
      _this.initialDataFlag = _this.initialDataFlag + 1;
    }
 
 
    _this.userAccessMenu = _this.commonService.userIntegrationMenu;
    //  console.log('_this.userAccessMenu===>',_this.userAccessMenu)
    if(_this.userAccessMenu){
      _this.getUserMenuConfig();
    }
 
    _this.commonService.filterListSubject.subscribe((fiterType: any) => {
      if (fiterType) {
        if (fiterType == "Process") {
          _this.initialDataFlag = _this.initialDataFlag + 1;
         // _this.processLoad(() => { });
        }
        if (fiterType == "Site") {
          _this.siteList = _this.commonService.siteList;
          _this.filteredSiteList = _this.siteList.slice();
          _this.initialDataFlag = _this.initialDataFlag + 1
        }
        if (_this.initialDataFlag > 1 && !_this.siteControl.value) {
          console.log("222222222")
          _this.siteControl.setValue(_this.dataService.siteId);
        }
        if(fiterType == "userAccess"){
          _this.userAccessMenu = _this.commonService.userIntegrationMenu;
          console.log('_this.userAccessMenu===>',_this.userAccessMenu)
          _this.getUserMenuConfig();
        }
      }
    })
 
    _this.siteControl.valueChanges.subscribe(value => {
      // _this.processFlag = false;
      _this.dataService.siteId = value;
      _this.loaderFlag = true;
      _this.cancel();
      //_this.commonService.siteChangeFun(value)
    });
    _this.processControl.valueChanges.subscribe((value: any) => {
      if(value && value.length>0){
      _this.selectedProcess = _this.commonService.processList.find(e=>  e.externalId == value);
      }
    });
    _this.searchControl.valueChanges.subscribe((value: any) => {
      console.log("value")
      console.log(value)
    });
 
  //  setTimeout(function () {
     // _this.processLoad(() => { });
    //}, 2000);
  }
  getUserMenuConfig(){
    var _this = this
   
    if(_this.siteControl.value != _this.dataService.siteId){
      _this.siteControl.setValue(_this.dataService.siteId)
    }
  }
  ngOnDestroy(): void {
 
  }
 ngAfterViewInit(): void {
    const _this = this;
 
    // Subscribe to the observable list subject
    _this.commonService.observeListSubject.subscribe((obType: any) => {
        _this.processLoad(() => {});
    });
 
    console.log("history.state.externalId------>", history.state);
 
    // Handle observation list case
    if (history.state.externalId && history.state.pageFrom === "Observation List") {
        _this.loaderFlag = true;
        _this.dataService.postData({ externalId: history.state.externalId }, `${_this.dataService.NODE_API}/api/service/listObservationById`)
            .subscribe(data => {
                const resData = data["data"]["list" + _this.commonService.configuration["typeObservation"]]["items"][0];
                console.log("3333333", resData);
                _this.loaderFlag = false;
                _this.siteControl.setValue(resData["refSite"]["externalId"]);
 
                if (resData["refOFWAProcess"] && resData["refOFWAProcess"]["items"].length > 0) {
                    const subProcess = resData["refOFWAProcess"]["items"][0]["refOFWAProcess"]["refOFWAProcess"];
                    _this.observation = resData;
                    const process = subProcess["refOFWAProcess"];
                    _this.selectedProcess = process;
                    _this.selectedCorePrinciple = process["refOFWAProcess"];
                    _this.selectedSubProcess = subProcess;
                    _this.processFlag = false;
                    _this.corePrincipleFlag = false;
                    _this.subProcessFlag = false;
                    _this.checkListFlag = true;
                    _this.checkListFlag2 = false;
                    _this.checkListFlag3 = false;
                    _this.checkListFlag4 = false;
 
                } else if (resData["refProcess"]) {
                    _this.observation = resData;
                    _this.selectedProcess = resData["refProcess"];
                    _this.selectedCorePrinciple = resData["refCorePrinciple"];
                    _this.selectedSubProcess = resData["refSubProcess"];
                    _this.processFlag = false;
                    _this.corePrincipleFlag = false;
                    _this.subProcessFlag = false;
                    _this.checkListFlag = true;
                    _this.checkListFlag2 = false;
                    _this.checkListFlag3 = false;
                    _this.checkListFlag4 = false;
                }
            });
 
    // Handle audit list case
    } else if (history.state.externalId && history.state.pageFrom === "Audit List") {
        _this.loaderFlag = true;
        _this.dataService.postData({ externalId: history.state.externalId }, `${_this.dataService.NODE_API}/api/service/listAudit`)
            .subscribe(data => {
                const resData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
                console.log("3333333", resData);
                _this.loaderFlag = false;
                _this.siteControl.setValue(resData["refSite"]["externalId"]);
 
                if (["items"].length > 0) {
                    console.log("resData", resData);
                    console.log("dddd",resData["refProcess"])
                    _this.observation = resData;
                    _this.selectedProcess = resData["refProcess"];
                    _this.selectedCorePrinciple = resData["refCorePrinciple"];
                    _this.selectedSubProcess = resData["refSubProcess"];
                    _this.processFlag = false;
                    _this.corePrincipleFlag = false;
                    _this.subProcessFlag = false;
                    _this.checkListFlag2 = true;
                    _this.checkListFlag3 = false;
                    _this.checkListFlag4 = false;
                    _this.checkListFlag = false;
 
                } else if (resData["refProcess"]) {
                    _this.observation = resData;
                    _this.selectedProcess = resData["refProcess"];
                    _this.selectedCorePrinciple = resData["refCorePrinciple"];
                    _this.selectedSubProcess = resData["refSubProcess"];
                    _this.processFlag = false;
                    _this.corePrincipleFlag = false;
                    _this.subProcessFlag = false;
                    _this.checkListFlag2 = true;
                    _this.checkListFlag3 = false;
                    _this.checkListFlag4 = false;
                    _this.checkListFlag = false;
                }
            });
 
    // Handle observation state case
  } else if (history.state.externalId && history.state.pageFrom === "Audit List2") {
    _this.loaderFlag = true;
    _this.dataService.postData({ externalId: history.state.externalId }, `${_this.dataService.NODE_API}/api/service/listAudit`)
        .subscribe(data => {
            const resData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
            console.log("3333333", resData);
            _this.loaderFlag = false;
            _this.siteControl.setValue(resData["refSite"]["externalId"]);

            if (["items"].length > 0) {
                console.log("resData", resData);
                console.log("dddd",resData["refProcess"])
                _this.observation = resData;
                _this.selectedProcess = resData["refProcess"];
                _this.selectedCorePrinciple = resData["refCorePrinciple"];
                _this.selectedSubProcess = resData["refSubProcess"];
                _this.processFlag = false;
                _this.corePrincipleFlag = false;
                _this.subProcessFlag = false;
                _this.checkListFlag2 = false;
                _this.checkListFlag3 = true;
                _this.checkListFlag4 = false;
                _this.checkListFlag = false;

            } else if (resData["refProcess"]) {
                _this.observation = resData;
                _this.selectedProcess = resData["refProcess"];
                _this.selectedCorePrinciple = resData["refCorePrinciple"];
                _this.selectedSubProcess = resData["refSubProcess"];
                _this.processFlag = false;
                _this.corePrincipleFlag = false;
                _this.subProcessFlag = false;
                _this.checkListFlag2 = false;
                _this.checkListFlag3 = true;
                _this.checkListFlag4 = false;
                _this.checkListFlag = false;
            }
        });
}else if (history.state.externalId && history.state.pageFrom === "Audit List3") {
  _this.loaderFlag = true;
  _this.dataService.postData({ externalId: history.state.externalId }, `${_this.dataService.NODE_API}/api/service/listAudit`)
      .subscribe(data => {
          const resData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
          _this.loaderFlag = false;
          _this.siteControl.setValue(resData["refSite"]["externalId"]);

          if (["items"].length > 0) {
              _this.observation = resData;
              _this.selectedProcess = resData["refProcess"];
              _this.selectedCorePrinciple = resData["refCorePrinciple"];
              _this.selectedSubProcess = resData["refSubProcess"];
              _this.processFlag = false;
              _this.corePrincipleFlag = false;
              _this.subProcessFlag = false;
              _this.checkListFlag2 = false;
              _this.checkListFlag3 = false;
              _this.checkListFlag4 = true;
              _this.checkListFlag = false;

          } else if (resData["refProcess"]) {
              _this.observation = resData;
              _this.selectedProcess = resData["refProcess"];
              _this.selectedCorePrinciple = resData["refCorePrinciple"];
              _this.selectedSubProcess = resData["refSubProcess"];
              _this.processFlag = false;
              _this.corePrincipleFlag = false;
              _this.subProcessFlag = false;
              _this.checkListFlag2 = false;
              _this.checkListFlag3 = false;
              _this.checkListFlag4 = true;
              _this.checkListFlag = false;
          }
      });
}
  else if (history.state.observation) {
        console.log("155..............");
        _this.selectedProcess = history.state.observation;
        _this.processFlag = false;
        _this.corePrincipleFlag = false;
        _this.subProcessFlag = false;
        _this.checkListFlag = false;
        _this.checkListFlag2 = false;
        _this.checkListFlag3 = false;
        _this.checkListFlag4 = false;
 
    // Default case when no externalId or observation is provided
    } else {
        if (_this.commonService.processList.length > 0) {
            _this.initialDataFlag += 1;
        }
        if (_this.initialDataFlag > 1) {
            console.log("1111111");
            _this.siteControl.setValue(_this.dataService.siteId);
        }
    }
 
    // Handle schedule case
    if (history.state.action === "Schedule") {
        console.log('history.state', history.state.data);
        _this.scheduleDetails = history.state.data;
        _this.selectedProcess = _this.scheduleDetails.refOFWASchedule.refOFWAProcess;
        _this.selectedCorePrinciple = _this.scheduleDetails.refOFWASchedule.refOFWACorePrinciple;
        _this.selectedSubProcess = _this.scheduleDetails.refOFWASchedule.refOFWAObservationType;
        _this.processFlag = false;
        _this.corePrincipleFlag = false;
        _this.subProcessFlag = false;
        _this.checkListFlag = true;
        _this.checkListFlag2 = false;
        _this.checkListFlag3 = false;
        _this.checkListFlag4 = false;
    }
}
 
 
  // filterClick() {
  //   var _this = this;
  //   var filter = document.getElementsByClassName('mat-filter-input');
  //   if (filter.length > 0) {
  //     filter[0]["value"] = "";
  //     _this.filteredSiteList = _this.siteList.slice();
  //   }
  // }
 
 
  goPage(page) {
    this.router.navigate([page]);
  }
  getNextevent(event) {
    console.log("event ", event)
    console.log(history.state)
    var _this = this;
    if(event.selectedSubProcess)
    _this.id=event.selectedSubProcess
    if (event.type == "Next") {
      if(event.processType == "Core Principles"){
        _this.selectedCorePrinciple = event.selectedOFWAProcess;
        _this.processList = _this.commonService.processList.filter(e => {
          return ((e.refOFWAProcess && e.refOFWAProcess.externalId) == event.selectedOFWAProcess.externalId);
        })
        _this.processList.unshift({name:"All",externalId:"All"});
        _this.filteredProcessList = _this.processList.slice();
        _this.processFlag = true;
        _this.corePrincipleFlag = false;
        _this.subProcessFlag = false;
        _this.checkListFlag = false;
        _this.checkListFlag2 = false;
        _this.checkListFlag3 = false;
        _this.checkListFlag4 = false;
      }
      else if(event.processType == "Process"){
        _this.selectedSubProcess = event.selectedSubProcess;
        _this.selectedProcess = event.selectedProcess;
        _this.subProcessFlag = false;
        _this.processFlag = false;
        _this.corePrincipleFlag = false;
        _this.processControl.setValue("");
        _this.searchControl.setValue("");
        //_this.checkListFlag2 = false; 
        // _this.checkListFlag = true;
 
        // if (_this.selectedProcess.name == "Audit") {
        //   // this.router.navigate(['observations/list'], { state: { listType: "Observation" } });
        //   _this.router.navigateByUrl('observations/list', { state: { listType: "Audit" } })
        // } else if (_this.selectedProcess.name == "Field Walk" ) {
        //   _this.router.navigateByUrl('observations/field-visit', {
        //     state: { "corePrinciple": _this.selectedCorePrinciple, "process": _this.selectedProcess }
        //   })
        // }
        if (_this.selectedProcess.name == "Field Walk") {
          _this.router.navigateByUrl('observations/field-visit', {
            state: { "corePrinciple": _this.selectedCorePrinciple, "process": _this.selectedProcess, "subProcess": _this.selectedSubProcess }
          })
        }
        if (_this.selectedProcess.name == "Observation" || _this.selectedProcess.name == "Hazards" || _this.selectedProcess.name == "Hazard") {
          _this.checkListFlag = true;
          _this.checkListFlag2 = false;
          _this.checkListFlag3 = false;
          _this.checkListFlag4 = false;
        } else if (_this.selectedProcess.name == "Audit") {
          if (_this.selectedSubProcess.name == "Layered Process Audit") {
            _this.checkListFlag2 = true;
            _this.checkListFlag = false;
            _this.checkListFlag3 = false;
            _this.checkListFlag4 = false;
        }
        else if (_this.selectedSubProcess.name == "Life Critical Audit") {
          _this.checkListFlag2 = false;
          _this.checkListFlag3 = true;
          _this.checkListFlag = false;
          _this.checkListFlag4 = false;
        }
        else if (_this.selectedSubProcess.name == "Procedure Audit") {
          _this.checkListFlag2 = false;
          _this.checkListFlag3 = false;
          _this.checkListFlag = false;
          _this.checkListFlag4 = true;
        }
        else {
            _this.router.navigate(['observations/list'], { state: { "process": _this.selectedProcess, listType: "Audit" } });
        }
        }
 
      }
      else if(event.processType == "Sub Process"){
        _this.selectedSubProcess = event.selectedOFWAProcess;
        _this.subProcessFlag = false;
        _this.processFlag = false;
        _this.corePrincipleFlag = false;
        if (_this.selectedProcess.name == "Observation" || _this.selectedProcess.name == "Hazards" || _this.selectedProcess.name == "Hazard") {
          _this.checkListFlag = true;
          _this.checkListFlag2 = false;
          _this.checkListFlag3 = false;
          _this.checkListFlag4 = false;
        } else if (_this.selectedProcess.name == "Audit") {
          if (_this.selectedSubProcess.name == "Layered Process Audit") {
            _this.checkListFlag2 = true;
            _this.checkListFlag3 = false;
            _this.checkListFlag = false;
            _this.checkListFlag4 = false;
        }
        else if (_this.selectedSubProcess.name == "Life Critical Audit") {
          _this.checkListFlag2 = false;
          _this.checkListFlag3 = true;
          _this.checkListFlag = false;
          _this.checkListFlag4 = false;
      }
      else if (_this.selectedSubProcess.name == "Procedure Audit") {
        _this.checkListFlag2 = false;
        _this.checkListFlag3 = false;
        _this.checkListFlag = false;
        _this.checkListFlag4 = true;
      }
       else {
            _this.router.navigate(['observations/list'], { state: { "process": _this.selectedProcess, listType: "Audit" } });
        }
        } else {
          _this.commonService.triggerToast({ type: 'error', title: "", msg: this.commonService.toasterLabelObject['toasterPleaseChooseOBS'] });
        }
      }
     
 
    }
 
    if(event.type == "Cancel"){
      _this.processControl.setValue("");
      _this.searchControl.setValue("");
      if(event.processType == "Core Principles"){
        _this.observation = null;
        _this.selectedCorePrinciple = null;
        _this.corePrincipleFlag = true;
        _this.processFlag = false;
        _this.subProcessFlag = false;
        _this.checkListFlag = false;
        _this.checkListFlag2 = false;
      }
      else if(event.processType == "Process"){
        _this.selectedProcess = null;
        _this.processFlag = false;
        _this.corePrincipleFlag = true;
        _this.subProcessFlag = false;
        _this.checkListFlag = false;
        _this.checkListFlag2 = false;
      }
      else if(event.processType == "Sub Process"){
        _this.selectedSubProcess = null;
        _this.processFlag = true;
        _this.corePrincipleFlag = false;
        _this.subProcessFlag = false;
        _this.checkListFlag = false;
         _this.checkListFlag2 = false;
      }else if(event.processType == "Observation"){
        _this.processFlag = true;
        _this.checkListFlag = false;
        _this.checkListFlag2 = false;
      }
     
 
      setTimeout(function () {
        _this.processLoad(() => { });
      }, 900);
 
     
    }
 
 
    if (event.behaveSubmit == true) {
      this.processFlag = false;
      this.corePrincipleFlag = false;
      this.subProcessFlag = false;
      this.checkListFlag = false;
      this.checkListFlag2 = false;
    }
 
  }
 
  filterClick() {
    var _this = this;
    var filter = document.getElementsByClassName('mat-filter-input');
    if (filter.length > 0) {
      filter[0]["value"] = "";
      _this.filteredProcessList = _this.processList.slice();
      _this.filteredSiteList = _this.siteList.slice();
     
    }
  }
 
  cancel(){
    var _this = this;
    this.observation = undefined;
    this.selectedCorePrinciple = undefined;
    this.selectedProcess = undefined;
    this.selectedSubProcess = undefined;
    this.corePrincipleFlag = true;
    this.processFlag = false;
    this.subProcessFlag = false;
    this.checkListFlag = false;
     _this.processLoad(() => { });
  }
  processLoad(cb) {
    var _this = this;
    console.log('_this.siteControl.value',_this.siteControl.value)
   
    _this.commonService.getProcessConfiguration(_this.siteControl.value, function (data) {
      _this.site = _this.commonService.siteList.find(e => e.externalId == _this.siteControl.value);
      _this.siteSubject.next(_this.site);
      console.log("fffffff")
      _this.loaderFlag = false;
    });
   
    cb();
 
    // if (mySite) {
    //   // _this.processSubject.next(mySite);
    //   cb();
    // }else{
    //   cb();
    // }
  }
 
}
