
import { ViewportRuler } from "@angular/cdk/overlay";
import { ChangeDetectionStrategy, Component, Input, OnInit, ViewChild } from "@angular/core";
import {
  ApexAxisChartSeries, ApexChart, ApexDataLabels, ApexFill, ApexLegend,
  ApexMarkers,
  ApexPlotOptions, ApexStroke, ApexTitleSubtitle, ApexTooltip, ApexXAxis, ApexYAxis
} from "ng-apexcharts";



@Component({
  selector: 'common-angle-chart',
  templateUrl: './angle-chart.component.html',
  changeDetection:ChangeDetectionStrategy.OnPush,
})

export class AngleChartComponent implements OnInit {
  @Input() series: ApexAxisChartSeries = [];
  @Input() plotOptions: ApexPlotOptions;
  @Input() dataLabels: ApexDataLabels;
  @Input() chartOption: ApexChart;
  @Input() stroke: ApexStroke;
  @Input() xaxis: ApexXAxis;
  @Input() yaxis: ApexYAxis;
  @Input() fill: ApexFill;
  @Input() tooltip: ApexTooltip;
  @Input() legend: ApexLegend;
  @Input() title: ApexTitleSubtitle;
  @Input() markers: ApexMarkers;
  @Input() labels:string[] = [];
  @Input() colors:any[] = [];
  @Input() responsive:any[] = [];
  docReact: any;
  constructor(private viewportRuler: ViewportRuler) {
    this.docReact = this.viewportRuler.getViewportRect();

  }

  ngOnInit(): void {
    this.chartOption.height = this.chartOption.height && this.docReact.height >= 900 ? this.chartOption.height : this.docReact.height <= 750 ? 350 : 350;
  }



}