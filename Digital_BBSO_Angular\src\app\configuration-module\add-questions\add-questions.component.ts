import { ChangeDetectorRef, Component, Inject, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, map, startWith } from 'rxjs';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { environment } from 'src/environments/environment';
import { LanguageService } from 'src/app/services/language.service';

@Component({
  selector: 'app-add-questions',
  templateUrl: './add-questions.component.html',
  styleUrls: ['./add-questions.component.scss']
})
export class AddQuestionsComponent implements OnInit {

  public addQuestionForm: FormGroup;
  public optionLabels = ['Yes', 'No'];

  // categoryInfo: any;
  editInfo: any;
  viewInfo: any;
  buttonEnable: boolean = false;
  titleName: any;
  saveEdit: boolean = false;
  loaderFlag: boolean;
  formType:any;
  questionList: any;
  labels = {}
  
  constructor(private formBuilder: FormBuilder, private router: Router, private commonService: CommonService, private dataService: DataService,@Inject(MAT_DIALOG_DATA) private _data: any,
  public dialogRef: MatDialogRef<AddQuestionsComponent>, private translate: TranslateService,
  private languageService : LanguageService,
  private changeDetector: ChangeDetectorRef,
  private ngZone: NgZone
) {

    // this.categoryInfo = history.state;
    // console.log('categoryInfo', this.categoryInfo);
    // console.log('this.categoryInfo.externalId', this.categoryInfo.config.externalId)

    this.titleName = _data.title
    this.formType = _data.formType
    this.labels = {
      'addquestionTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addquestionTitle'] || 'addquestionTitle',
      'formcontrolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestion'] || 'formcontrolsQuestion',
      'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
      'formcontrolsQuestiondescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestiondescription'] || 'formcontrolsQuestiondescription',
      'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
      'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
    }


  }

  ngOnInit() {
    // this.addQuestionForm = this.formBuilder.group({
    //   addQues: this.formBuilder.array([this.createEmailFormGroup()])
    // });
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase().toUpperCase()])
        this.labels = {
          'addquestionTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'addquestionTitle'] || 'addquestionTitle',
          'formcontrolsQuestion': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestion'] || 'formcontrolsQuestion',
          'formcontrolsSequence': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsSequence'] || 'formcontrolsSequence',
          'formcontrolsQuestiondescription': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'formcontrolsQuestiondescription'] || 'formcontrolsQuestiondescription',
          'buttonCancel': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonCancel'] || 'buttonCancel',
          'buttonSave': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonSave'] || 'buttonSave',
        }
        console.log('commonService label', _this.labels)
        _this.changeDetector.detectChanges();
      })
      // _this.changeDetector.detectChanges();
    })
    this.getQuestions();

    if(this._data.formType == "Edit" || this._data.formType == "View"){
      this.addQuestionForm = this.formBuilder.group({
        name: [this._data.name, Validators.required],
        sequence: [this._data.sequence ? this._data.sequence : 1],
        description: [this._data.description]
      })
      if(this._data.formType == "View"){
        this.addQuestionForm.get('name').disable();
        this.addQuestionForm.get('description').disable();
      }
    }else{
      this.addQuestionForm = this.formBuilder.group({
        name: ['', Validators.required],
        sequence: [1],
        description: ['']
      })
    }
  }


  goPage(page) {
    this.router.navigate([page]);
  }

  getQuestions(){
    var _this = this;
    var postObj = {
      process:_this._data.process.externalId,
      category:_this._data.category ? _this._data.category.externalId:undefined,
      subCategory:_this._data.subCategory ? _this._data.subCategory.externalId:undefined
    }
    console.log(postObj)
    _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/listQuestionList").subscribe(data => {
      console.log(data)
      console.log(this.commonService.configuration)
      console.log(this.commonService.configuration["typeOFWAQuestion"])
      _this.questionList = data["data"]["list"+this.commonService.configuration["typeOFWAQuestion"]]["items"];
      console.log(_this.questionList)
    })
    
  }

  checkDuplicate(cb){
    var _this= this;
    var findName = _this.questionList.find(e => e.name.toLocaleLowerCase() ==  this.addQuestionForm.value.name.toLocaleLowerCase())
    var findDescription = _this.questionList.find(e => e.description.toLocaleLowerCase() ==  this.addQuestionForm.value.description.toLocaleLowerCase())
    console.log(_this.questionList)
    console.log(findName)
    console.log(findDescription)
    console.log(this._data)
    if(findName && (findName.name.toLocaleLowerCase() != this._data.name)){
      return cb(false);
    }else
    if(findDescription && (findDescription.name.toLocaleLowerCase() != this._data.description)){
      return  cb(false);
    }else{
      return  cb(true);
    }
  }
  saveQuestion() {
    var _this = this
    
    _this.checkDuplicate(function(no){

      console.log("nooooo",no)
      if(no){
        _this.loaderFlag = true;
    if (_this.saveEdit == false) {
      if (_this.addQuestionForm.valid && _this._data.process != undefined) {

        var myObj = {
          "name": _this.addQuestionForm.value.name,
          "sequence": _this.addQuestionForm.value.sequence,
          "description": _this.addQuestionForm.value.description,
          "refOFWASubProcess":{
            "externalId": _this._data.process.externalId,
            "space": _this._data.process.space
          }
        };
        if(_this._data.category){
          myObj["refOFWACategory"] = {
            "externalId": _this._data.category.externalId,
            "space": _this._data.category.space
          }
        }
        if(_this._data.subCategory){
          myObj["refOFWASubCategory"] = {
            "externalId": _this._data.subCategory.externalId,
            "space": _this._data.subCategory.space
          }
        }
        if(_this._data.externalId){
          myObj["externalId"] = _this._data.externalId;
        }
        console.log('myObj', myObj)


        var postObj = {
          "type": _this.commonService.configuration["typeOFWAQuestion"],
          "siteCode": _this.commonService.configuration["allSiteCode"],
          "unitCode": _this.commonService.configuration["allUnitCode"],
          "items": [
            myObj
          ]
        }
        console.log('postObj', postObj)
        _this.dataService.postData(postObj, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
          _this.commonService.triggerToast({ type: 'success', title: "", msg: _this.commonService.toasterLabelObject['toasterSavesuccess'] });
          _this.loaderFlag = false;
          _this.dialogRef.close({ data: data })
          // _this.router.navigateByUrl('configuration/question-list', {
          //   state: history.state.config
          // });
        })


      } else {
        _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
        _this.loaderFlag = false;
        _this.dialogRef.close()
      }

    }
  }else{
    _this.commonService.triggerToast({ type: 'error', title: "", msg: _this.commonService.toasterLabelObject['toasterAlreadyexists'] });
  }
})
    // if (this.saveEdit == true) {
    //   if (this.addQuestionForm.valid) {
    //     var editObj = {

    //       "name": this.addQuestionForm.value.name,
    //       "description": this.addQuestionForm.value.description,
    //       "externalId": this.editInfo.externalId,
    //     };

    //     var editMain = {
    //       "type": _this.commonService.configuration["typeOFWAQuestion"],
    //       "siteCode": _this.commonService.configuration["allSiteCode"],
    //       "unitCode": _this.commonService.configuration["allUnitCode"],
    //       "items": [
    //         editObj
    //       ]
    //     }
    //     _this.dataService.postData(editMain, _this.dataService.NODE_API + "/api/service/createInstanceByProperties").subscribe(data => {
    //       _this.commonService.triggerToast({ type: 'success', title: "", msg: "Saved successfully" });
    //       _this.loaderFlag = false;
    //       _this.router.navigateByUrl('configuration/question-list', {
    //         state: history.state.config
    //       });
    //     })
    //   } else {
    //     this.commonService.triggerToast({ type: 'error', title: "", msg: "Please fill the required details" });
    //     _this.loaderFlag = false;
    //   }

    // }

  }

  cancelClick(){
    var _this = this;
    // _this.router.navigateByUrl('configuration/question-list', {
    //   state: history.state.config
    // });
    _this.dialogRef.close()
  }

}
