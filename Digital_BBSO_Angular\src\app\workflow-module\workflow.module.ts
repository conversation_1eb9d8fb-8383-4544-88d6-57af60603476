import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
import { NgModule } from "@angular/core";
import { WorkflowRoutingModule } from "./workflow-routing.module";
import { MainWorkflowComponent } from "./main-workflow/main-workflow.component";
import { CreateWorkflowComponent } from "./create-workflow/create-workflow.component";


@NgModule({
    declarations: [
        MainWorkflowComponent,
        CreateWorkflowComponent
    ],
    imports: [
        CommonModule,
        WorkflowRoutingModule,
        SharedModule
    ],
    providers: [],
  })
  export class WorkflowModule { }