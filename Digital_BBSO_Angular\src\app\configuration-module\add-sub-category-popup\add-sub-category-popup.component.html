<div *ngIf="loaderFlag" style="left: 50px;" class="spinner-body">
    <mat-spinner class="spinner"></mat-spinner>
</div>
<div fxLayout="column" *ngIf="configureDataDialog">
    <div>
        <commom-label labelText="{{ labels[configureDataDialog.headername] }}" [tagName]="'h4'"
            [cstClassName]="'heading unit-heading mb-0'"></commom-label>
    </div>
    <div>
        <div class="marginTop">
            <div>
                <span class="subheading-1 regular">{{ labels[configureDataDialog.firstKey] }} </span>
            </div>
            <div>
                <mat-form-field appearance="outline" class="add_sub_drop">
                    <mat-select [formControl]="category" >
                        <mat-option *ngFor="let item of categoryOption" [value]="item.externalId">
                            {{ item.name === 'Observation' || item.name === 'Audit' ? (labels[item.name.toLowerCase()]) : item.name === 'Field Walk'? (labels['cardsFieldwalks']) : labels['cardsHazards'] }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <div class="marginTop">
            <div>
                <span class="subheading-1 regular">{{labels[configureDataDialog.secondKey] }}</span>
            </div>
            <div>
                <mat-form-field appearance="outline" class="add_sub_drop">
                    <input type="text" [formControl]="categoryInput" class="input"
                        placeholder="{{labels[configureDataDialog.secondPlaceholder] }}" aria-span="Search" matInput>
                </mat-form-field>
            </div>
        </div>
        <div class="marginTop" *ngIf="configureDataDialog.type == 'subProcess' ">
            <div>
                <span class="subheading-1 regular">{{labels['description']  }}</span>
            </div>
            <div>
                <mat-form-field appearance="outline" class="add_sub_drop">
                    <input type="text" [formControl]="descriptionInput" class="input"
                        placeholder="{{labels['description'] }}" aria-span="Search" matInput>
                </mat-form-field>
            </div>
        </div>
        <div class="marginTop">
            <div>
                <span class="subheading-1 regular">{{ labels['formcontrolsSequence'] }}</span>
            </div>
            <div>
                <mat-form-field appearance="outline" class="add_sub_drop">
                    <input type="number" [formControl]="sequenceControl" class="input"
                        placeholder="{{ labels['formcontrolsSequence'] }}" aria-span="Search" matInput>
                </mat-form-field>
            </div>
        </div>
        <div class="marginTop" *ngIf="configureDataDialog.type == 'category' || configureDataDialog.type == 'subCategory'">
            <div>
                <span class="subheading-1 regular">{{ labels['formcontrolsGuidelinedoc'] }}</span>
            </div>
            <div style="margin-top: 5px;" fxLayoutAlign="start center">
                <input hidden type="file" #uploader (change)="onFileChange($event)" />
                <span class="subheading-1 uploadLink cursor" (click)="uploader.click()">
                    {{ labels['chooseFile'] }}
                </span>
        
        
                <div>
                    <span style="margin-left: 10px;" *ngIf="selectedFile">
                         {{selectedFile.name}}</span>
                    <svg *ngIf="!selectedFile && (configureDataDialog && configureDataDialog.guidelineDocument && configureDataDialog.guidelineDocument.length>0)"
                        (click)="imageView()" xmlns="http://www.w3.org/2000/svg" version="1.1"
                        xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" width="20" height="20"
                        x="0" y="0" viewBox="0 0 8.467 8.467" style="margin-left: 30px;width: 25px;height: 25px;"
                        xml:space="preserve" class="">
                        <g>
                            <path
                                d="M2.357.53a.844.844 0 0 0-.858.832v5.742c0 .459.38.833.858.833h3.751c.478 0 .86-.374.86-.833V2.822l-2.06-.337a.75.75 0 0 1-.615-.919L4.56.53zm2.462.13-.25.978a.462.462 0 0 0 .385.568l1.733.281zm-.58 2.788a.4.4 0 0 1 .343.193c.119.16.128.367.084.577a2.59 2.59 0 0 1-.236.601c.18.3.384.606.537.838.129-.019.256-.031.376-.037.235-.01.446.006.616.097a.478.478 0 0 1 .227.595.44.44 0 0 1-.269.248.57.57 0 0 1-.362.008c-.26-.08-.478-.334-.688-.594l-1.396.325c-.173.358-.328.668-.567.814a.45.45 0 0 1-.232.065.461.461 0 0 1-.288-.107c-.183-.17-.171-.463 0-.656.204-.23.545-.272.9-.356.274-.36.588-.813.816-1.228-.013-.023-.028-.039-.04-.062a2.457 2.457 0 0 1-.25-.61c-.043-.194-.038-.395.092-.54a.471.471 0 0 1 .338-.171zm-.003.286H4.23a.14.14 0 0 0-.116.074c-.038.056-.061.139-.028.288.025.111.097.257.166.4.049-.116.116-.243.135-.337.036-.17.018-.285-.034-.351-.03-.04-.066-.075-.118-.074zm.032 1.36c-.156.265-.353.557-.542.843l.935-.227c-.117-.18-.26-.402-.393-.615zm1.145.808-.057.002a.716.716 0 0 0-.131.02c.15.154.313.342.414.373.075.027.245.003.28-.088.037-.099.006-.186-.097-.242a.936.936 0 0 0-.409-.065zm-2.383.55c-.227.038-.347.072-.431.152-.082.093-.073.207-.02.257a.139.139 0 0 0 .175.01c.065-.04.177-.257.276-.418z"
                                fill="#083d5b" data-original="#000000" class="" style="cursor: pointer;"></path>
                        </g>
                    </svg>
                </div>
            </div>
        </div>
        <div class="marginTop category-pop-up" fxLayout="row">
            <div fxFlex="100" fxLayoutAlign="center center">
                <common-lib-button [className]="'cancel cst-btn'" text="{{ labels['buttonCancel'] }}"
                    (buttonAction)="cancel()"></common-lib-button>
                <common-lib-button [className]="'cst-btn'" text="{{ labels['buttonSave'] }}"
                    (buttonAction)="savaData()"></common-lib-button>
            </div>
        </div>
    </div>
</div>
