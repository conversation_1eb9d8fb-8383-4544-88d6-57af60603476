import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { CircleProgressComponent, CircleProgressOptions } from "ng-circle-progress";

@Component({
    selector: 'ng-progress-bar',
    templateUrl: './progress-bar.component.html',
})

export class NgProgressBarComponent implements OnInit {
    @ViewChild('circleProgress') circleProgress: CircleProgressComponent;
    options = new CircleProgressOptions();
    @Input() className:string = '';
    @Input() backgroundGradient:boolean = false;
    @Input() backgroundColor:string = '';
    @Input() backgroundGradientStopColor:string = '';
    @Input() backgroundOpacity:number = 0;
    @Input() backgroundStroke:string = '';
    @Input() backgroundStrokeWidth:number = 0;
    @Input() backgroundPadding:number = 0;
    @Input() percent:number = 0;
    @Input() radius:number = 0;
    @Input() space:number = 0;
    @Input() maxPercent:number = 0;
    @Input() outerStrokeWidth:number = 0;
    @Input() outerStrokeColor:string = '';
    @Input() innerStrokeWidth:number = 0;
    @Input() innerStrokeColor:string = '';
    @Input() title:string | Array<String> = '';
    @Input() titleFormat:any;
    @Input() titleColor:string = '';
    @Input() titleFontSize:string = '';
    @Input() titleFontWeight:string = '';
    @Input() animation:boolean = true;
    @Input() animateTitle:boolean = false;
    @Input() animationDuration:number = 0;
    @Input() showTitle:boolean = true;
    @Input() showSubtitle:boolean = false;
    @Input() showUnits:boolean = false;
    @Input() showImage:boolean = false;
    @Input() showBackground:boolean = false;
    @Input() showInnerStroke:boolean = true;
    @Input() clockwise:boolean = true;
    @Input() responsive:boolean = false;
    @Input() startFromZero:boolean = false;
    @Input() outerStrokeGradientStopColor:string = '';
    @Input() outerStrokeGradient:boolean = true;
    @Input() lazy:boolean = true;
    
    constructor(
        private changeDetector: ChangeDetectorRef,
    ){
       
    }
    ngOnInit(): void {
        this.options = Object.assign(this.options, this.getConfiguration());
       
    }

     
    getConfiguration():Object{
        let configurations = {
            class: this.className,
            backgroundGradient:this.backgroundGradient,
            backgroundColor: this.backgroundColor,
            backgroundGradientStopColor: this.backgroundGradientStopColor,
            backgroundOpacity: this.backgroundOpacity,
            backgroundStroke: this.backgroundStroke,
            backgroundStrokeWidth: this.backgroundStrokeWidth,
            backgroundPadding: this.backgroundPadding,
            percent:this.percent,
            radius: this.radius,
            space: this.space,
            maxPercent: this.maxPercent,
            outerStrokeWidth: this.outerStrokeWidth,
            outerStrokeColor: this.outerStrokeColor,
            innerStrokeWidth: this.innerStrokeWidth,
            innerStrokeColor: this.innerStrokeColor,
            title: this.title,
            titleFormat: this.titleFormat,
            titleColor: this.titleColor,
            titleFontSize: this.titleFontSize,
            titleFontWeight: this.titleFontWeight,
            animation: this.animation,
            animateTitle: this.animateTitle,
            animationDuration: this.animationDuration,
            showTitle: this.showTitle,
            showSubtitle: this.showSubtitle,
            showUnits: this.showUnits,
            showImage: this.showImage,
            showBackground: this.showBackground,
            showInnerStroke: this.showInnerStroke,
            clockwise: this.clockwise,
            responsive: this.responsive,
            startFromZero: this.startFromZero,
            lazy: this.lazy,
      }
        return configurations;
    }
}