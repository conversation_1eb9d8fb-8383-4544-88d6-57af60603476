export class SelectedCountryData implements SelectedCountry {
  tabId: number;
  countryName: any[];
  constructor(params?) {
    this.tabId = params?.tabId;
    this.countryName = params?.countryName;
  }
}

export interface Country {
  name: string;
  img: string;
  hover: string;
}

export interface keyMatrix {
  name: string;
  value: string;
  icon: string
}

export interface Announcement {
  date: string;
  value: string;
}

export interface Recognition {
  img: string;
  value: string;
}

export interface SelectedCountry {
  tabId: number;
  countryName: any[];
}

export interface LinksData {
  name: string;
  date: string;
  icon: string
}

export interface Options {
  name: string;
  value: string;
}

export interface MaintenanceTabs {
  name: string;
}

export interface MaintenanceSchedule {
  sap_Cost: string;
  activity_ID:string;
  activity_Name: string;
  planned_Duration: string;
  planned_Labor_Units: string;
  remaining_Duration: string;
  start: string;
  finish: string;
}

export interface Attention {
  type: string;
  description:string;
  assignedBy: string;
  due: string;
  application: string;
  status: string;
}

export class RouterType {
  url: string;
  constructor(params?) {
    this.url = params ? params : '';
  }
}

//mock Data

export const CountryData: Country[] = [
  {
    name: "AMERICAS",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
              <image id="america_1_" data-name="america (1)" width="40" height="40" xlink:href="data:image/png;base64,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"/>
              </svg>`,
    hover: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
                <image id="america" width="40" height="40" xlink:href="data:image/png;base64,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"/>
               </svg> `
  },
  {
    name: "EUROPE",
    img: ` <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
               <image id="europe" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAQAAAD2e2DtAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAAAqo0jMgAAAAlwSFlzAAAAYAAAAGAA8GtCzwAAAAd0SU1FB+cFCAc6K0abF7kAAAwySURBVHja7Z1tjFxVGcd/s1shWgVrmmhRgtTuFFtLCa1fLIYCxdYiqNmIlioqJG0CRBs+gMHEjEYTjB8IASWA0QBJCfANgmK6pja08SW+hAI1VFIalRDStRvBhL5txw+7053dnZf7cp7zPGfu8ztfZmd2zjz3/v/nOc/ce+dccBzHcRzHcRzHcRzHcRzHcRzHcRzHcQaQmnYAA8RlrMv9nmd5STtsJxQNmrnbUdboBj2kvdcqziJ26VrADaCNsgXcAPqoWsANYAFFC7gBbKBmATeAFZQs4Aawg4oF3ACWULCAG8AW0S3gBrBGZAu4AewR1QJuAItEtIAbwCbRLOAGsEokC7gB7BLFAm4Ay0SwgBvANuIWcANYZxHPsUquezeAfRYzKte5G6DiuAEqjhug4rgBKo4boOK4ASqOG6DiuAEqjhug4izQDkCNT3BN7vfsY6922KGpqgFWM8bi3O/6/uAZoJpTQDH5B5IqGsDlb6N6BnD5Z1E1A7j8c6iWAWTlH0mw50qxmiMFVvFpb40evS/jZMneu7dTLJfaKdXJANLJ/3uCX6mHuUsw8kpQfvT3zgCS4180B1QjA8iXfpLjHzwHlCLM6O+VAaTHv2AOkMsANbaaWIc0xhc/6fEPyeWAGj+lyc/Vp5hwo797Bogx/oW/C4RmSv6mugXCyt/NAI9Gkb9Jk0fC7yKJ1FXjfm6ZfnwzsI3TAXq9lKtzvmOI2yMc9atzg/hntNjKjzgY7dMKMjP6Wy1MFngo2jjLlwHijX+hHCAtfygLHFSXv5MBYs3/rWa8DugsfwgLLFEXv7MB4o5/4zmgu/zlLbBVXfxOBog9/psEzwHhavT20q8TN/NQiU+7PORGByPG9/+5GD0e0Hv0l88CFiqAuRlAY/wL5IB48he3gI0KYK4B4s//rWasDsguf1EL2KgAZhtAa/w3CZoDytcA/eb+uRSpBSxWABrzfwtDdUC+0V80C9ioANozgOb4D5wD4suf1wJWKoB2A+jN/61moA6o8WCJDbgt8+dYqQBmDKA9/psEywHFa4Aa97OtxCdnP01jrwLQnP9bBKoDihogb+lXhvWRPicry9iiHQIAW0PkgGJOjin/hw1dFb+OO4GNBsY/wDAP8BuNDy5e+nUuqHpjpwIYyJZ/Cog5+sFiBTBQ5DVAbPntVQADRj4DxJd/iaEKYCDJY4D48sOVkT+vcmQ3gIb8XgGIk9UAOvJ7BSBONgNoye8VgDhZDKAlv1cAEehvAD35vQKIQD8DaMrvFUAE+hngVkX5vQKIQD8DaK6o5RVABLR/vt0LrwAiYNkA67UDqAJ2DeAVQBTsGsArgCjYNYBXAFGwa4D12gFUA6sG8AogElYN4BVAJKwawCuASFg1wHrtAKqCTQN4BRANmwY4m3HtEKqCTQMc5iq3QBxsGgD2s8EtEIF/WTUAvOBZQJh/soO6XQPAfreAGIfZQZ17OWbZAG4BGV5jOyPcy3GwWwO0cAuE5RDbqfMQp1pPWDdAiHJwnM/zGJPaG6LOS3yFkXbxob8BLIy+cuXgBJt4mhvnb3qleJGvcwlP5L9zQ5jFIMosEDHFxQXv/XGUNW29XMiDBpZ3it1e4Etl7t4kZYF8Bih2+5cjrJ7XT52n1CWJ137P5uLSy1ogrwHyZ4HZo3+G93JMXZgYbR/XlhdfzgL5DZDPAt3kB9ilLo502xtOfCkLFDFAdgv0kh++pS6QrPhXhRVfxgLFDJDNAr3lhwvURZIT/woJ8SUsUNQA/cvBTqXfXF5Sl0qiTWTY8jnkORDU5DZ+JuGsnPQ+LjDBJl7o28cz2hshwvvZ3Sf3lSZcFmiUiqPbRNAv+bdYpz5apVrWPaBugXIG6GyB7Bs/xJvqUlXcAmUNMN8C+Tb8EXWhKm6B8gaYXQ5mKf3auV5dJsmWeW8UOxtorxzMVvq1Y2PFbykWZy0Hi54OtmKBqesFJriav+R8p3CSVGcRu2xPBI1gcazO/+0X2K2epg1NBBoWCGeAIgzxX3V54lhgVb8dUQYrE0F+RjhHO4QoLGa09z+UvSQsVQsMegWQmfLXBKZpATfANCG+DDW5lVu1NyQnboBp7F8VLEGNS7RDsMIgGOAszsr5jhHO1Q7aCqkeD1vESlawlKWspM4C3uBlDnGIA7zM4b4XP6/VDt8O6RjgXJZNC76COu+b8+oSlpx5fJzXOTBtiBd5s0NfXgGcwbIB3sX504IvZSkXZr62/WyWspTPTf81cSYzHOIA7wBugDasGeB8RqhT5yJG+GiQ6Bax5ozgpzjMQV7hUu3NtIO+Ac5h+bTgdeosFN7aZSwL8VOJwUHbAKsZU70nQeXR/Rro8qujaQCX3wB6BnD549Bnkh9WCsvlj8Ua/shr3V/WyQAufzwW8jSf6f6yhgFc/ri8h2f4QrcX408BLn98hhnl7xzo/FJcXH4dhhnlcKcL5+MawOXXY4jr+Dd/m/t0TAO4/LoMcR1H+dPsJ+MZwOXXp8Ym3uIP7U/FMoDLb4MaG2myZ+aJOAZw+S1xBe9mrPVHDAO4/Na4jIXsmnoobwCX3yLr+CC/AnkDuPxW+STn8SxNWQO4/JZZwwhPSxpgFb91+U2zihHJk0GjLr95tgzCL4OcErgBKo4boOK4ASqOG6DiuAEqTj4DLOm35pSTGnkMcD572OO/rB0sshvgAnYzEmP9SScmWQ1QZy8fA6IsQerEI5sBLmI3Hznzl1tggMhigBXs5rxZz7gFBob+BljL83xo3rOLeE56IeIKMx7vns39DPApxvhAx1cWM9bHAiXuV1tpxtlQ+o7pgfg0b/Vci7rXrUmK3OvX28w+LXq77LytB+t5O3O4Ln9Y+eNZoCubeSd3yC5/OPljWaAL1+a4w/bcsF3+MPLHsUBHvsyJwqG7/OHkj2GBDmzlZOHwXf6w8stbYB7bmCy8CS5/ePmlLTCHHZwu3NURl7/gfut/SE1uaM3iDvWdUb2W9TavUlmgjTvVd0b1Wp67/IpYYOZw7d3cmTkUJyvj/IJeR9t2sj9Hbxdzg0yYNe5RHwuD2MTv4h1K/vvUd9UgtiTkrzHMw3xTO4wBpMgNrRWosZMt2kEMIONsyHk7eyVqNMt34swhkdEP/sMQCRKS3w0QnqTkdwOEJjH53QCh+UZa8rsBQjOqHUBe/FtAWCZZySvaQeTBM0BYhrlLO4R8eAYITWI5wDNAaBLLAdXLAPvYC1zGOrFPmGQFB7U3Mzva58xitwYAywpc+Jq9PaItanaqOgW8yuOCvW9lufYGZqWqBoAfcEqs74TqgOoawHMAkIIB/sOkUM8/FOsZhvmuWN9BsW6ACTayU6jvg2I9A9yQRg6wbYCpc2tys7XXAaYN0Dq1Kjdbex1g2ADtZ9Y9B4hh1QCzL6zwHCCGTQPMv67Gc4AQFg3Q6bIqzwFC2DNAt6vqPAeIYMsAkzzG2i5X1aWbA+qCvQdA++xcq03yZJ90GeYMXkOs50TPDWoLn038KR4VMkCYnru1U7brgFTEhzAjtSHWc/dmOgekIv4U5UdqQ6zn7s10DkhHfAgxUhtiPfdqhnNAOuJPUXakNsR67tUM54CUxIfyI7Uh1nPvZjYHpCT+FOVGakOs50RzQFriQ9mR2hDrOckcEOdI4CSP8XGuD/SLGcljgk8K7oWNnCXYe0HkDXCap1jJjUF/KiF39P4fYvthgms4IdZ7YYZEj4OHHfkzyB69l2CcK6yuHDDML4XmvD2Cp0HqnBKoAaAhsieyLAetxBCT3MR9In3vFvyFnOwVvWGZYJPdJeOGgCbf5h7tQHIjeRY/JMZXDVoAQJPbOZHYYtGvchsXFnjf3qhRml8wcsGZR99hgru1w8nFg9oB9MV08p+i/Wvgj7lDO5yBwnjyn2L2cYCfcAtVWzBCiiTkn38g6AG2c1o7qAEgEfk7HQl8mK8lUl/bJRn5Ox8K3slXOakdWMIkJH+3cwFPMMpx7dASJSn5u58MeoYvckw7uARJTP5eZwN/zWf5n3Z4iZGc/L1PB/+OzbytHWBCJCh/v+sBnudKjmqHmAhJyt//gpA/czXj2kEmQKLyZ7ki6K9czhvaYRonWfmzXRJ2gCt5XTtQwyQsP9Qy/l+dm3L3PcaY9ublZgMbcr8n3/1/HcdxHMdxHMdxHMdxHMdxHMdxHMdxHEee/wNBiqnEjw2GFAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMy0wNS0wOFQwNzo1ODo0MyswMDowMNIsJ84AAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjMtMDUtMDhUMDc6NTg6NDMrMDA6MDCjcZ9yAAAAKHRFWHRkYXRlOnRpbWVzdGFtcAAyMDIzLTA1LTA4VDA3OjU4OjQzKzAwOjAw9GS+rQAAAABJRU5ErkJggg=="/>
               </svg>`,
    hover: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
               <image id="europe_1_" data-name="europe (1)" width="40" height="40" xlink:href="data:image/png;base64,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"/>
               </svg>`
  },
  {
    name: "ASIA",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="45.178" height="45.178" viewBox="0 0 45.178 45.178">
              <image id="location" width="40" height="40" transform="translate(0 5.567) rotate(-8)" xlink:href="data:image/png;base64,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"/>
              </svg>`,
    hover: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="45.178" height="45.178" viewBox="0 0 45.178 45.178">
                <image id="location_1_" data-name="location (1)" width="40" height="40" transform="translate(0 5.567) rotate(-8)" xlink:href="data:image/png;base64,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"/>
               </svg>`
  },
]


export const AMERICAS: Country[] = [
  {
    name: "Bay City",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Bishop",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Boucherville",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Campo Bom",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Cangrejera",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Clear Lake",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Edmonton",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Enoree",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Evansville",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Florence",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Narrows",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Shelby",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Silao",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Suzano",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Wilmington",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Winona",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  }
]

export const EUROPE: Country[] = [
  {
    name: "Ferrara",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Frankfurt",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Geleen",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Kaiserslautern",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Lanaken",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Oberhausen",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Perstorp",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Utzenfeld",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Wehr",
    img: "../assets/img/bishop.svg",
    hover: ""
  }
]

export const ASIA: Country[] = [
  {
    name: "Nanjing",
    img: "../assets/img/bishop.svg",
    hover: ""
  },
  {
    name: "Singapore",
    img: "../assets/img/Boucherville.svg",
    hover: ""
  },
  {
    name: "Suzhou",
    img: "../assets/img/bishop.svg",
    hover: ""
  }
]

export const COUNTRY_DETAILS: Country[] = [
  {
    name: "CLEAR LAKE",
    img: "../assets/img/WhoWeAre_Header_image.png",
    hover: ""
  },
  {
    name: "FRANKFURT",
    img: "../assets/img/FRANKFURT.png",
    hover: ""
  },
]

export const LANDINGPAGETABS: Country[] = [
  {
    name: "Overview",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="12.727" viewBox="0 0 20 12.727">
        <defs>
          <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#01112f"/>
            <stop offset="1" stop-color="#556074"/>
          </linearGradient>
        </defs>
        <path id="overview_key_FILL0_wght400_GRAD0_opsz48" d="M40,308.727V296H52.727v12.727Zm1.364-1.364h10v-10h-10ZM55,308.727V296h1.364v12.727Zm3.636,0V296H60v12.727Zm-17.273-1.364v0Z" transform="translate(-40 -296)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "Announcements",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="17.6" viewBox="0 0 22 17.6">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#081735"/>
              <stop offset="1" stop-color="#5a6478"/>
            </linearGradient>
          </defs>
          <path id="campaign_FILL0_wght400_GRAD0_opsz48" d="M97.875,265.625v-1.65H102v1.65ZM99.25,273.6l-3.327-2.475.99-1.32,3.328,2.475Zm-2.255-13.832-.99-1.32L99.25,256l.99,1.32ZM83.575,272.5v-4.4H81.65A1.655,1.655,0,0,1,80,266.45v-3.3a1.655,1.655,0,0,1,1.65-1.65H86.6l5.5-3.3v13.2l-5.5-3.3H85.225v4.4ZM86.05,264.8Zm7.15,3.685v-7.37a5.024,5.024,0,0,1,1.2,1.609,4.977,4.977,0,0,1,0,4.152A5.024,5.024,0,0,1,93.2,268.485ZM81.65,263.15v3.3h5.39l3.41,2.035v-7.37l-3.41,2.035Z" transform="translate(-80 -256)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "KPI's",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#02122f"/>
              <stop offset="0.519" stop-color="#061632"/>
              <stop offset="1" stop-color="#556074"/>
            </linearGradient>
          </defs>
          <path id="monitoring_FILL0_wght400_GRAD0_opsz48" d="M120,236v-2.111l1.667-1.667V236Zm4.583,0v-6.556l1.667-1.667V236Zm4.583,0v-8.222l1.667,1.694V236Zm4.583,0v-6.528l1.667-1.667V236Zm4.583,0V225L140,223.333V236ZM120,229.444v-2.361l7.778-7.722,4.444,4.444L140,216v2.361l-7.778,7.806-4.444-4.444Z" transform="translate(-120 -216)" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
  {
    name: "Recognition",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#1e2c46"/>
              <stop offset="1" stop-color="#06090e"/>
            </linearGradient>
          </defs>
          <path id="social_leaderboard_FILL0_wght400_GRAD0_opsz48" d="M89.994,194.5a4.983,4.983,0,1,0-3.537-1.462A4.836,4.836,0,0,0,89.994,194.5ZM86.65,183.925a6.371,6.371,0,0,1,1.5-.662A6.514,6.514,0,0,1,89.825,183l-2.75-5.5h-3.65Zm6.7,0,3.225-6.425h-3.65l-2.075,4.175.75,1.5a6.536,6.536,0,0,1,.912.313A6.145,6.145,0,0,1,93.35,183.925Zm-8.525,9.5a6.676,6.676,0,0,1-.975-1.812,6.55,6.55,0,0,1,0-4.225,6.675,6.675,0,0,1,.975-1.812,3.978,3.978,0,0,0-2.375,1.337,4,4,0,0,0,0,5.175A3.979,3.979,0,0,0,84.825,193.425Zm10.35,0a3.979,3.979,0,0,0,2.375-1.337,4,4,0,0,0,0-5.175,3.978,3.978,0,0,0-2.375-1.337,6.675,6.675,0,0,1,.975,1.813,6.55,6.55,0,0,1,0,4.225A6.675,6.675,0,0,1,95.175,193.425ZM90,196a6.327,6.327,0,0,1-1.912-.287,6.947,6.947,0,0,1-1.687-.788,2.782,2.782,0,0,1-.45.063q-.225.012-.475.012a5.485,5.485,0,0,1-4.025-9.2,5.335,5.335,0,0,1,3.575-1.725L81,176h7l2,4,2-4h7l-4,8.025a5.389,5.389,0,0,1,3.563,1.75A5.5,5.5,0,0,1,94.5,195q-.225,0-.463-.012a2.808,2.808,0,0,1-.463-.062,7.008,7.008,0,0,1-1.675.788A6.207,6.207,0,0,1,90,196ZM90,189.5Zm-3.35-5.575L83.425,177.5Zm6.7,0,3.225-6.425Zm-5.2,8.325.7-2.275L87,188.65h2.275l.725-2.4.725,2.4H93l-1.85,1.325.7,2.275L90,190.85Z" transform="translate(-80 -176)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "Events",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="21.333" viewBox="0 0 20 21.333">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#081734"/>
              <stop offset="1" stop-color="#5a6477"/>
            </linearGradient>
          </defs>
          <path id="Icon_metro-calendar" data-name="Icon metro-calendar" d="M9.237,9.928H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,8H7.9v2.667H5.237Zm4,0H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm-4-4H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,0H7.9v2.667H5.237Zm14.667-12V3.261H17.237V1.928H7.9V3.261H5.237V1.928H2.571V23.261h20V1.928H19.9Zm1.333,20H3.9V7.261H21.237Z" transform="translate(-2.571 -1.928)" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
  {
    name: "Links",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="10" viewBox="0 0 20 10">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#01112f"/>
              <stop offset="1" stop-color="#556074"/>
            </linearGradient>
          </defs>
          <path id="link_FILL0_wght400_GRAD0_opsz48" d="M89.25,386H85a5,5,0,1,1,0-10h4.25v1.5H85a3.5,3.5,0,1,0,0,7h4.25Zm-3.125-4.25v-1.5h7.75v1.5ZM90.75,386v-1.5H95a3.5,3.5,0,1,0,0-7H90.75V376H95a5,5,0,1,1,0,10Z" transform="translate(-80 -376)" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
  {
    name: "Units",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
        <image id="manufacture_2_" data-name="manufacture (2)" width="20" height="20" xlink:href="data:image/png;base64,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"/>
      </svg>`,
    hover: ""
  },
  {
    name: "Maintenance Schedule",
    img: `<svg id="maintenance-svgrepo-com" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#061533"/>
              <stop offset="1" stop-color="#586276"/>
            </linearGradient>
          </defs>
          <rect id="Rectangle_814" data-name="Rectangle 814" width="20" height="20" fill="none"/>
          <path id="Path_11478" data-name="Path 11478" d="M10,2a8,8,0,1,0,8,8A8,8,0,0,0,10,2Zm4.6,11.672-1,1a.432.432,0,0,1-.608,0l-4-3.96a2.832,2.832,0,0,1-3.52-3.864L7.384,8.712l1.3-1.3L6.8,5.544A2.88,2.88,0,0,1,10,6.1a2.792,2.792,0,0,1,.656,3l3.952,3.96a.392.392,0,0,1-.008.608Z" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
]

export const KEYMATRIXDATA: keyMatrix[] = [
  {
    name: "Productivity",
    value: "100m",
    icon: ` <svg id="copy" xmlns="http://www.w3.org/2000/svg" width="24.022" height="30" viewBox="0 0 24.022 30">
        <g id="Group_20" data-name="Group 20">
          <path id="Path_17" data-name="Path 17" d="M65.039,85.4H51.032a2.383,2.383,0,0,0-2.382,2.382v20.1a2.383,2.383,0,0,0,2.382,2.382H65.039a2.383,2.383,0,0,0,2.382-2.382v-20.1A2.391,2.391,0,0,0,65.039,85.4Zm.71,22.474a.716.716,0,0,1-.716.716H51.026a.716.716,0,0,1-.716-.716V87.782a.716.716,0,0,1,.716-.716H65.033a.716.716,0,0,1,.716.716Z" transform="translate(-48.65 -80.262)" fill="#fff"/>
          <path id="Path_18" data-name="Path 18" d="M151.839,0H137.832a2.383,2.383,0,0,0-2.382,2.382.833.833,0,0,0,1.666,0,.716.716,0,0,1,.716-.716h14.007a.716.716,0,0,1,.716.716v20.1a.716.716,0,0,1-.716.716.833.833,0,0,0,0,1.666,2.383,2.383,0,0,0,2.382-2.382V2.382A2.383,2.383,0,0,0,151.839,0Z" transform="translate(-130.199)" fill="#fff"/>
        </g>
      </svg>`
  },
  {
    name: "Major Incidents",
    value: "05",
    icon: ` <svg id="copy" xmlns="http://www.w3.org/2000/svg" width="24.022" height="30" viewBox="0 0 24.022 30">
        <g id="Group_20" data-name="Group 20">
          <path id="Path_17" data-name="Path 17" d="M65.039,85.4H51.032a2.383,2.383,0,0,0-2.382,2.382v20.1a2.383,2.383,0,0,0,2.382,2.382H65.039a2.383,2.383,0,0,0,2.382-2.382v-20.1A2.391,2.391,0,0,0,65.039,85.4Zm.71,22.474a.716.716,0,0,1-.716.716H51.026a.716.716,0,0,1-.716-.716V87.782a.716.716,0,0,1,.716-.716H65.033a.716.716,0,0,1,.716.716Z" transform="translate(-48.65 -80.262)" fill="#fff"/>
          <path id="Path_18" data-name="Path 18" d="M151.839,0H137.832a2.383,2.383,0,0,0-2.382,2.382.833.833,0,0,0,1.666,0,.716.716,0,0,1,.716-.716h14.007a.716.716,0,0,1,.716.716v20.1a.716.716,0,0,1-.716.716.833.833,0,0,0,0,1.666,2.383,2.383,0,0,0,2.382-2.382V2.382A2.383,2.383,0,0,0,151.839,0Z" transform="translate(-130.199)" fill="#fff"/>
        </g>
      </svg>`
  },
  {
    name: "Profitablity",
    value: "89%",
    icon: ` <svg id="copy" xmlns="http://www.w3.org/2000/svg" width="24.022" height="30" viewBox="0 0 24.022 30">
        <g id="Group_20" data-name="Group 20">
          <path id="Path_17" data-name="Path 17" d="M65.039,85.4H51.032a2.383,2.383,0,0,0-2.382,2.382v20.1a2.383,2.383,0,0,0,2.382,2.382H65.039a2.383,2.383,0,0,0,2.382-2.382v-20.1A2.391,2.391,0,0,0,65.039,85.4Zm.71,22.474a.716.716,0,0,1-.716.716H51.026a.716.716,0,0,1-.716-.716V87.782a.716.716,0,0,1,.716-.716H65.033a.716.716,0,0,1,.716.716Z" transform="translate(-48.65 -80.262)" fill="#fff"/>
          <path id="Path_18" data-name="Path 18" d="M151.839,0H137.832a2.383,2.383,0,0,0-2.382,2.382.833.833,0,0,0,1.666,0,.716.716,0,0,1,.716-.716h14.007a.716.716,0,0,1,.716.716v20.1a.716.716,0,0,1-.716.716.833.833,0,0,0,0,1.666,2.383,2.383,0,0,0,2.382-2.382V2.382A2.383,2.383,0,0,0,151.839,0Z" transform="translate(-130.199)" fill="#fff"/>
        </g>
      </svg>`
  }
]

export const ANNOUNCEMENTS: Announcement[] = [
  {
    date: "April 5, 2023",
    value: "Celanese Announces Agreement with Glaukos Corporation for Sustained Release Glaucoma Treatment",
  },
  {
    date: "March 1, 2023",
    value: "Celanese Announces Collaboration with Alessa Therapeutics for the Advancement of Oncology Treatments",
  },
  {
    date: "February 28, 2023",
    value: "Celanese Announces Hytrel® Price Increases",
  },
  {
    date: "February 15, 2023",
    value: "Celanese Announces a Research Agreement with Johns Hopkins University to Advance Sustained Ocular Drug Delivery to the Suprachoroidal Space",
  },
  {
    date: "November 18, 2022",
    value: "Celanese Announces Engineered Materials Price Increases",
  },
  {
    date: "March 1, 2023",
    value: "Celanese Announces Collaboration with Alessa Therapeutics for the Advancement of Oncology Treatments",
  },
  {
    date: "March 1, 2023",
    value: "Celanese Announces Agreement with Glaukos Corporation for Sustained Release Glaucoma Treatment",
  },
  {
    date: "April 5, 2023",
    value: "Celanese Announces Agreement with Glaukos Corporation for Sustained Release Glaucoma Treatment",
  },

]


export const RECOGNITION: Recognition[] = [
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },
  {
    img: '../assets/img/avtar.png',
    value: 'Congrats Albert for scoring the high observation points for the month of April 2023'
  },

]

export const RECENTDOCUMENTS: LinksData[] = [
  {
    name: "Noise and Vibration Conference",
    date: "17 May 2023",
    icon: ` <svg xmlns="http://www.w3.org/2000/svg" width="15" height="17.143" viewBox="0 0 15 17.143">
    <path id="Icon_open-document" data-name="Icon open-document"
        d="M0,0V17.143H15V8.571H6.429V0ZM8.571,0V6.429H15ZM2.143,4.286H4.286V6.429H2.143Zm0,4.286H4.286v2.143H2.143Zm0,4.286h8.571V15H2.143Z"
        fill="#fff" />
    </svg>`
  },
  {
    name: "Monthly SQRE Report",
    date: "18 May 2023",
    icon: ` <svg xmlns="http://www.w3.org/2000/svg" width="15" height="17.143" viewBox="0 0 15 17.143">
    <path id="Icon_open-document" data-name="Icon open-document"
        d="M0,0V17.143H15V8.571H6.429V0ZM8.571,0V6.429H15ZM2.143,4.286H4.286V6.429H2.143Zm0,4.286H4.286v2.143H2.143Zm0,4.286h8.571V15H2.143Z"
        fill="#fff" />
    </svg>`
  },
  {
    name: "Injury Incidents Reports",
    date: "18 May 2023",
    icon: ` <svg xmlns="http://www.w3.org/2000/svg" width="15" height="17.143" viewBox="0 0 15 17.143">
    <path id="Icon_open-document" data-name="Icon open-document"
        d="M0,0V17.143H15V8.571H6.429V0ZM8.571,0V6.429H15ZM2.143,4.286H4.286V6.429H2.143Zm0,4.286H4.286v2.143H2.143Zm0,4.286h8.571V15H2.143Z"
        fill="#fff" />
    </svg>`
  }
]


export const DASHBOARDTABS: Country[] = [
  {
    name: "Overview",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="12.727" viewBox="0 0 20 12.727">
        <defs>
          <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#01112f"/>
            <stop offset="1" stop-color="#556074"/>
          </linearGradient>
        </defs>
        <path id="overview_key_FILL0_wght400_GRAD0_opsz48" d="M40,308.727V296H52.727v12.727Zm1.364-1.364h10v-10h-10ZM55,308.727V296h1.364v12.727Zm3.636,0V296H60v12.727Zm-17.273-1.364v0Z" transform="translate(-40 -296)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "Announcements",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="17.6" viewBox="0 0 22 17.6">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#081735"/>
              <stop offset="1" stop-color="#5a6478"/>
            </linearGradient>
          </defs>
          <path id="campaign_FILL0_wght400_GRAD0_opsz48" d="M97.875,265.625v-1.65H102v1.65ZM99.25,273.6l-3.327-2.475.99-1.32,3.328,2.475Zm-2.255-13.832-.99-1.32L99.25,256l.99,1.32ZM83.575,272.5v-4.4H81.65A1.655,1.655,0,0,1,80,266.45v-3.3a1.655,1.655,0,0,1,1.65-1.65H86.6l5.5-3.3v13.2l-5.5-3.3H85.225v4.4ZM86.05,264.8Zm7.15,3.685v-7.37a5.024,5.024,0,0,1,1.2,1.609,4.977,4.977,0,0,1,0,4.152A5.024,5.024,0,0,1,93.2,268.485ZM81.65,263.15v3.3h5.39l3.41,2.035v-7.37l-3.41,2.035Z" transform="translate(-80 -256)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "KPI's",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#02122f"/>
              <stop offset="0.519" stop-color="#061632"/>
              <stop offset="1" stop-color="#556074"/>
            </linearGradient>
          </defs>
          <path id="monitoring_FILL0_wght400_GRAD0_opsz48" d="M120,236v-2.111l1.667-1.667V236Zm4.583,0v-6.556l1.667-1.667V236Zm4.583,0v-8.222l1.667,1.694V236Zm4.583,0v-6.528l1.667-1.667V236Zm4.583,0V225L140,223.333V236ZM120,229.444v-2.361l7.778-7.722,4.444,4.444L140,216v2.361l-7.778,7.806-4.444-4.444Z" transform="translate(-120 -216)" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
  {
    name: "Recognition",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#1e2c46"/>
              <stop offset="1" stop-color="#06090e"/>
            </linearGradient>
          </defs>
          <path id="social_leaderboard_FILL0_wght400_GRAD0_opsz48" d="M89.994,194.5a4.983,4.983,0,1,0-3.537-1.462A4.836,4.836,0,0,0,89.994,194.5ZM86.65,183.925a6.371,6.371,0,0,1,1.5-.662A6.514,6.514,0,0,1,89.825,183l-2.75-5.5h-3.65Zm6.7,0,3.225-6.425h-3.65l-2.075,4.175.75,1.5a6.536,6.536,0,0,1,.912.313A6.145,6.145,0,0,1,93.35,183.925Zm-8.525,9.5a6.676,6.676,0,0,1-.975-1.812,6.55,6.55,0,0,1,0-4.225,6.675,6.675,0,0,1,.975-1.812,3.978,3.978,0,0,0-2.375,1.337,4,4,0,0,0,0,5.175A3.979,3.979,0,0,0,84.825,193.425Zm10.35,0a3.979,3.979,0,0,0,2.375-1.337,4,4,0,0,0,0-5.175,3.978,3.978,0,0,0-2.375-1.337,6.675,6.675,0,0,1,.975,1.813,6.55,6.55,0,0,1,0,4.225A6.675,6.675,0,0,1,95.175,193.425ZM90,196a6.327,6.327,0,0,1-1.912-.287,6.947,6.947,0,0,1-1.687-.788,2.782,2.782,0,0,1-.45.063q-.225.012-.475.012a5.485,5.485,0,0,1-4.025-9.2,5.335,5.335,0,0,1,3.575-1.725L81,176h7l2,4,2-4h7l-4,8.025a5.389,5.389,0,0,1,3.563,1.75A5.5,5.5,0,0,1,94.5,195q-.225,0-.463-.012a2.808,2.808,0,0,1-.463-.062,7.008,7.008,0,0,1-1.675.788A6.207,6.207,0,0,1,90,196ZM90,189.5Zm-3.35-5.575L83.425,177.5Zm6.7,0,3.225-6.425Zm-5.2,8.325.7-2.275L87,188.65h2.275l.725-2.4.725,2.4H93l-1.85,1.325.7,2.275L90,190.85Z" transform="translate(-80 -176)" fill="url(#linear-gradient)"/>
        </svg>`,
    hover: ""
  },
  {
    name: "Events",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="21.333" viewBox="0 0 20 21.333">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#081734"/>
              <stop offset="1" stop-color="#5a6477"/>
            </linearGradient>
          </defs>
          <path id="Icon_metro-calendar" data-name="Icon metro-calendar" d="M9.237,9.928H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,8H7.9v2.667H5.237Zm4,0H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm-4-4H11.9v2.667H9.237Zm4,0H15.9v2.667H13.237Zm4,0H19.9v2.667H17.237Zm-12,0H7.9v2.667H5.237Zm14.667-12V3.261H17.237V1.928H7.9V3.261H5.237V1.928H2.571V23.261h20V1.928H19.9Zm1.333,20H3.9V7.261H21.237Z" transform="translate(-2.571 -1.928)" fill="url(#linear-gradient)"/>
        </svg>
        `,
    hover: ""
  },
  {
    name: "Shared Dashboard",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18" viewBox="0 0 18 18">
    <defs>
      <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
        <stop offset="0" stop-color="#0e1d3a"/>
        <stop offset="1" stop-color="#5e687b"/>
      </linearGradient>
    </defs>
    <path id="Icon_material-dashboard" data-name="Icon material-dashboard" d="M4.5,14.5h8V4.5h-8Zm0,8h8v-6h-8Zm10,0h8v-10h-8Zm0-18v6h8v-6Z" transform="translate(-4.5 -4.5)" fill="url(#linear-gradient)"/>
  </svg>
        `,
    hover: ""
  },
  {
    name: "Links",
    img: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
        <image id="manufacture_2_" data-name="manufacture (2)" width="20" height="20" xlink:href="data:image/png;base64,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"/>
      </svg>`,
    hover: ""
  },
 
]

export const LINKOPTIONS: Options[] = [
  {
    name:"Recent Documents",
    value: "1"
  },
  {
    name:"Articles",
    value: "2"
  },
  {
    name:"Forms",
    value: "3"
  }
]

export const SUBSCRIBEOPTIONS: Options[] = [
  {
    name:"Clear Lake",
    value: "1"
  },
  {
    name:"All subscribed sites",
    value: "2"
  },
  {
    name:"Frankfurt",
    value: "3"
  }
]

export const MAINTENANCEPAGETABS: MaintenanceTabs[] = [
  {
    name: "1 Week",
  },
  {
    name: "2 Weeks",
  },
  {
    name: "3 Weeks",
  }
]

export const MAINTENANCESCHEDULEDATAWEEK1: MaintenanceSchedule[] = [
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0111",
    activity_Name: "*PM* PSV-2616, RECEIVE, VERIFY & TAG PRD",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "02/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "*PM* PSV-2616, TRANSPORT w/FORKLIFT TO",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "03/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "L/R Trk 9 Spt 2 N2 line/blind for welding",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "04/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "L/R Trk 9 Spt 2 N2 remove scaffolds",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "05/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "P-1267 modify scaffolds as required",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "06/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "P-1267 modify scaffolds as required",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "P-1267 modify scaffolds as required",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "P-1267 modify scaffolds as required",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
 
]

export const MAINTENANCESCHEDULEDATAWEEK2: MaintenanceSchedule[] = [
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0111",
    activity_Name: "P1265 remove the scaffold",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "02/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "PHA 650# Trap #2, Install New Trap",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "03/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "*PM* V-750, Pull Blinds/Close",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "04/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "V-264, HMT to provide manpower and equip",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "05/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "P-1267 modify scaffolds as required",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "06/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "UTL Stm. Traps, Spirax to Repair per Ste",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "HE-1175 V-1049, Pull Blinds/Close",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
 
]

export const MAINTENANCESCHEDULEDATAWEEK3: MaintenanceSchedule[] = [
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0111",
    activity_Name: "Col SD 1 Demo two scaffold",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "02/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "*PM* V-264, Check Pumps Alignment",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "03/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "Col PC 1 build scaffolds for OPS to acce",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "04/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "Col PC 1 Remove insulation on the supply",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "05/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "ISO-360011 sht 4 fabricate new spools",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "06/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "Field measurement Blowdown on LCV-5391",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
  {
    sap_Cost: "01/23/2023",
    activity_ID:"55491574-0110",
    activity_Name: "Unbolt Blowdown on LCV-5391",
    planned_Duration: "1h",
    planned_Labor_Units: "1h",
    remaining_Duration: "0h",
    start: "23-Jan-23 07:00 AM",
    finish: "23-Jan-23 08:00 AM",
  },
 
]

export const ATTENTION: Attention[] = [
  {
    type: "Threat",
    description:"To close the actions that has been raised for Water Pipe Corrosion",
    assignedBy: "Kristin",
    due: "Mar 22, 2023",
    application: "Threat Management",
    status: "Overdue",
  },
  {
    type: "Observation",
    description:"Your scheduled observation has not been completed",
    assignedBy: "Kristin",
    due: "Mar 22, 2023",
    application: "Threat Management",
    status: "Overdue",
  },
  {
    type: "Field Walk",
    description:"To close the actions that has been raised for Water Pipe Corrosion",
    assignedBy: "Kristin",
    due: "Mar 22, 2023",
    application: "Threat Management",
    status: "In progress",
  },
  {
    type: "Threat",
    description:"To close the actions that has been raised for Water Pipe Corrosion",
    assignedBy: "Kristin",
    due: "Mar 22, 2023",
    application: "Threat Management",
    status: "Overdue",
  },
 
 
]
export const WEEKDAYS: any[] = [
  {
    id: 0,
    name: "S",
    fullName: "Sunday"
  },
  {
    id: 1,
    name: "M",
    fullName: "Monday"
  },
  {
    id: 2,
    name: "T",
    fullName: "Tuesday"
  },
  {
    id: 3,
    name: "W",
    fullName: "Wednesday"
  },
  {
    id: 4,
    name: "T",
    fullName: "Thursday"
  },
  {
    id: 5,
    name: "F",
    fullName: "Friday"
  },
  {
    id: 6,
    name: "S",
    fullName: "Saturday"
  }
];

export const DAYCOUNT: Options[] = [
  {
    name: "First",
    value: "first"
  },
  {
    name: "Second",
    value: "second"
  },
  {
    name: "Third",
    value: "third"
  },
  {
    name: "Fourth",
    value: "fouth"
  },
  {
    name: "Last",
    value: "last"
  }
]

export const FULLWEEKDAYS: Options[] = [
  {
    name: "Sunday",
    value: "1"
  },
  {
    name: "Monday",
    value: "2"
  },
  {
    name: "Tuesday",
    value: "3"
  },
  {
    name: "Wednesday",
    value: "4"
  },
  {
    name: "Thursday",
    value: "5"
  },
  {
    name: "Friday",
    value: "6"
  },
  {
    name: "Sataurday",
    value: "7"
  },
]

export class OccurrenceData {
  view: string;
  startDate: string;
  endDate: string;
  days: string[];
  dayCount: number;
  datType: string;
  day: string;
  selectedItem: string;
   quarter: string;
  constructor(params?: any) {
    this.view = params.view;
    this.startDate = params.startDate;
    this.endDate = params.endDate;
    this.dayCount = params.dayCount;
    this.datType = params.datType;
    this.day = params.day;
    this.selectedItem = params.selectedItem;
  }
}

