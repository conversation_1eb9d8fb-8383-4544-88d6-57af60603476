import { FlatTreeControl } from '@angular/cdk/tree';
import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from 'src/app/services/common.service';
import { DataService } from 'src/app/services/data.service';
import _ from "lodash";
import { TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { MatStepper } from '@angular/material/stepper';
import { environment } from 'src/environments/environment';
import { LanguageService } from 'src/app/services/language.service';
interface ExampleFlatNode {
  expandable: boolean;
  bold: boolean;
  name: string;
  level: number;
}

@Component({
  selector: 'app-general-info',
  templateUrl: './general-info.component.html',
  styleUrls: ['./general-info.component.scss']
})
export class generalinfoComponent implements OnInit {

  loaderFlag: boolean;
  companyForm: FormGroup;
  showWelcomeSupplier: boolean;
  auditExternalId;
  auditData;
  stepTwo=false;
  stepUpload=false;
  stepFile=false;
  labels = {}
  constructor(private fb: FormBuilder,private route: ActivatedRoute,private commonService: CommonService, private router: Router,private dataService: DataService, private translate: TranslateService, private languageService: LanguageService, private ngZone:NgZone, private cd:ChangeDetectorRef) {
  this.labels = {
      'welcomeSAP': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'welcomeSAP'] || 'welcomeSAP',
      'welcomeSAPDesc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'welcomeSAPDesc'] || 'welcomeSAPDesc',
      'tableheaderGeneralinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderGeneralinformation'] || 'tableheaderGeneralinformation',
      'tableheadingCompanyname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCompanyname'] || 'tableheadingCompanyname',
      'tableheadingTelephone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingTelephone'] || 'tableheadingTelephone',
      'tableheadingHqstreetaddress': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingHqstreetaddress'] || 'tableheadingHqstreetaddress',
      'tableheadingMaincontactname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMaincontactname'] || 'tableheadingMaincontactname',
      'tableheadingMobile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMobile'] || 'tableheadingMobile',
      'tableheadingState': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingState'] || 'tableheadingState',
      'tableheadingJobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingJobtitle'] || 'tableheadingJobtitle',
      'tableheadingPostalcode': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPostalcode'] || 'tableheadingPostalcode',
      'tableheadingCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCountry'] || 'tableheadingCountry',
      'tableheadingEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEmail'] || 'tableheadingEmail',
      'tableheadingCorporatewebsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCorporatewebsite'] || 'tableheadingCorporatewebsite',
      'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
      'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
      'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
      'tableheadingCity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCity'] || 'tableheadingCity',
      'tableheadingEvidences': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEvidences'] || 'tableheadingEvidences',
      'buttonStartAudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonStartAudit'] || 'buttonStartAudit',
    }
  }
  id: string | null = null;
  ngOnInit() {
    var _this = this;
    _this.ngZone.run(() => {
      console.log('Out subscribe', _this.labels)
      _this.languageService.language$.subscribe((language) => {
        console.log('obs labels language', _this.commonService.selectedLanguage)
        console.log('obs labels', _this.commonService.labelObject[_this.commonService.selectedLanguage.toUpperCase()])
        this.labels = {
          'welcomeSAP': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'welcomeSAP'] || 'welcomeSAP',
          'welcomeSAPDesc': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'welcomeSAPDesc'] || 'welcomeSAPDesc',
          'tableheaderGeneralinformation': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheaderGeneralinformation'] || 'tableheaderGeneralinformation',
          'tableheadingCompanyname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCompanyname'] || 'tableheadingCompanyname',
          'tableheadingTelephone': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingTelephone'] || 'tableheadingTelephone',
          'tableheadingHqstreetaddress': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingHqstreetaddress'] || 'tableheadingHqstreetaddress',
          'tableheadingMaincontactname': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMaincontactname'] || 'tableheadingMaincontactname',
          'tableheadingMobile': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingMobile'] || 'tableheadingMobile',
          'tableheadingState': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingState'] || 'tableheadingState',
          'tableheadingJobtitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingJobtitle'] || 'tableheadingJobtitle',
          'tableheadingPostalcode': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingPostalcode'] || 'tableheadingPostalcode',
          'tableheadingCountry': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCountry'] || 'tableheadingCountry',
          'tableheadingEmail': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEmail'] || 'tableheadingEmail',
          'tableheadingCorporatewebsite': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCorporatewebsite'] || 'tableheadingCorporatewebsite',
          'buttonBack': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonBack'] || 'buttonBack',
          'next': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'next'] || 'next',
          'qualityassessmentTitle': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'qualityassessmentTitle'] || 'qualityassessmentTitle',
          'tableheadingCity': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingCity'] || 'tableheadingCity',
          'tableheadingEvidences': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'tableheadingEvidences'] || 'tableheadingEvidences',
          'buttonStartAudit': this.commonService.labelObject[this.commonService.selectedLanguage][environment.applicationId+'.'+this.commonService.selectedLanguage+'.stLabel.'+'buttonStartAudit'] || 'buttonStartAudit',
        }
        console.log('commonService label', _this.labels)
        _this.cd.detectChanges();
      })
      // _this.cd.detectChanges();
    })
    
    _this.route.queryParamMap.subscribe(params => {
      _this.id = params.get('id');})
    if(_this.id){  
      _this.loaderFlag = true;
    
  console.log("Captured ID:", _this.id);  ///api/service/listAudit
  _this.dataService.postData({ externalId: _this.id }, _this.dataService.NODE_API + "/api/service/listAudit").subscribe(data => {
    _this.auditData = data["data"]["list" + _this.commonService.configuration["typeAudit"]]["items"][0];
    console.log(_this.auditData)
    
    _this.setData(JSON.stringify(_this.auditData))
    _this.loaderFlag = false;
  })

;
}

    this.showWelcomeSupplier = true;
    this.companyForm = this.fb.group({
      companyName: [''],
      telephone: ['',Validators.required],
      streetAddress: [''],
      mainContactName: ['',Validators.required],
      city: [''],
      mobile: [''],
      region: [''],
      jobTitle: [''],
      postalCode: [''],
      email: [''],
      country: [''],
      website: ['',Validators.required],
      
    });
    // productFamily: ['',Validators.required],
    // product: ['',Validators.required],
    // manufacturingSite: ['',Validators.required],
    // productContactName: ['',Validators.required],
    // productPostalCode: [''],
    // productJobTitle: [''],
    // productCountry: [''],
    // productTelephone: ['',Validators.required],
    if (!this.id) {
      if (localStorage.getItem('emailsend')){
        this.setData(localStorage.getItem('emailsend'))
      }
      else{
        this.router.navigate(['/observations/completed'])
      }
      
      
    }
    


     
  }

  setData(jsonString){
    var _this = this;
    console.log(jsonString)
    localStorage.setItem('emailsend', jsonString);
    if (jsonString) {
      const forms = JSON.parse(jsonString);
      _this.auditExternalId = forms.externalId;
      _this.auditData = forms;
      console.log("forms----->", forms);
      console.log("forms----->", forms.refOFWASchedule.refSupplier.address.city);
      console.log("forms----->", forms.refOFWASchedule.refSupplier.address.postalCode);
      console.log("forms----->", forms.refOFWASchedule.refSupplier.address.StreetAddress1);
      console.log("forms----->", forms.refOFWASchedule.refSupplier.address.name);
      this.companyForm.patchValue({
       companyName: forms.refOFWASchedule.refSupplier.address.name || '',
       streetAddress: forms.refOFWASchedule.refSupplier.address.streetAddress1 || '',
       city: forms.refOFWASchedule.refSupplier.address.city || '',
       postalCode: forms.refOFWASchedule.refSupplier.address.postalCode || '',
       mobile: forms.refOFWASchedule.refSupplier.address.phoneNumber || '',
       region:forms.refGeoRegion.name || '',
       jobTitle: forms.refOFWASchedule.refSupplier.address.jobTitle || '',
       email: forms.refOFWASchedule.email || '',
       country: forms?.refOFWASchedule?.refSupplier?.address?.country?.name || '',
       // productJobTitle:forms.refOFWASchedule.refSupplier.address.jobTitle || '',
       // productPostalCode: forms.refOFWASchedule.refSupplier.address.postalCode || '',
       // productCountry: forms?.refOFWASchedule?.refSupplier?.address?.country?.name || '',

       
     });
    }
  }

  eventEmitClick(event){
    if(event == "audit-plan-file"){
      this.stepFile = true;
    }else{
      this.stepUpload = true;
    }
  }

  onSubmit() {
    var _this = this;
  
    if (this.companyForm.valid) {
     var  observedObj = this.companyForm.value;
  
     


    }
  }


  closeWelcomeSupplier(){
    this.showWelcomeSupplier = false;
  }
  
  
  goNext(stepper: MatStepper) {

    var _this=this;
    // if (_this.companyForm.invalid) {
    //   _this.companyForm.markAllAsTouched(); 
    //   _this.commonService.triggerToast({ type: 'error', title: "", msg:_this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
    //   return;
    // }else{
      _this.onnext();
    // }

    stepper.next();
    this.stepTwo = true;
  }
  goPrevious(stepper: MatStepper) {
    stepper.previous();
  }

  
  onnext() {

    var _this=this;
    // if (this.companyForm.invalid) {
    //   this.companyForm.markAllAsTouched(); 
    //   _this.commonService.triggerToast({ type: 'error', title: "", msg:_this.commonService.toasterLabelObject['toasterPleasefillreqfields'] });
    //   return;
    // }

    const jsonString = localStorage.getItem('emailsend');
    if (jsonString) {
      const audit = JSON.parse(jsonString);
      console.log("jsonObject", audit);
  
      // Replace window.open with router navigation
      // this.router.navigate(['/observations/quality-assessment'], { queryParams: { id: audit.externalId } });
  
      // var instanceNotification = [
      //   {
      //     application: this.commonService.applicationInfo.name,
      //     description: 'Supplier Notification',
      //     externalUsers: [audit.refOFWASchedule.email],
      //     severity: audit.refOFWASchedule.priority,
      //     properties: [
      //       {
      //         name: 'Email',
      //         value: audit.refOFWASchedule.performerAzureDirectoryUserID.externalId,
      //         type: 'text',
      //       },
      //       {
      //         name: 'Start',
      //         value: new Date(audit.refOFWASchedule.observationStartDate).toDateString(),
      //         type: 'text',
      //       },
      //       {
      //         name: 'End',
      //         value: new Date(audit.refOFWASchedule.observationEndDate).toDateString(),
      //         type: 'text',
      //       },
      //       {
      //         name: 'link',
      //         value: this.commonService.configuration["VendorUrl"] + "/observations/general-info?id=" + this.id, //audit.externalId,
      //         type: 'text',
      //       },
      //     ],
      //   },
      // ];
      // let notificationType = 'Supplier Audit Notification';
      // this.commonService.notification(instanceNotification, notificationType);
    }
  
    const form = this.companyForm.value;
    const jsonString1 = JSON.stringify(form);
    localStorage.setItem('form', jsonString1);
  }
  
  
  onCancel() {
    this.showWelcomeSupplier = true;
    // this.router.navigate(['observations/list'], { state: { listType: "Audit" } });
  }

}
