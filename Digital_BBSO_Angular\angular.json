{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "cache": {"enabled": false}, "schematicCollections": ["@cypress/schematic", "@schematics/angular"]}, "version": 1, "newProjectRoot": "projects", "projects": {"bbso": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/bbso", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/_variables.scss", "node_modules/leaflet/dist/leaflet.css", "./node_modules/ngx-toastr/toastr.css", "./node_modules/angular-calendar/css/angular-calendar.css", "src/styles.scss"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "bbso:build:production"}, "development": {"browserTarget": "bbso:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "bbso:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css", "src/styles.scss"], "scripts": []}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "bbso:serve"}, "configurations": {"production": {"devServerTarget": "bbso:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "ct": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "bbso:serve", "watch": true, "headless": false, "testingType": "component"}, "configurations": {"development": {"devServerTarget": "bbso:serve:development"}}}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "bbso:serve", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "bbso:serve:production"}}}}}}, "defaultProject": "bbso"}