import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { ColumnDialog } from 'src/app/diolog/column-dialog/column-dialog';
import { ColumnSummaryDialog } from 'src/app/diolog/column-summary-dialog/column-summary-dialog';
import { TokenService } from 'src/app/services/token.service';

@Component({
  selector: 'app-rule-list',
  templateUrl: './rule-list.component.html',
  styleUrls: ['./rule-list.component.scss']
})
export class RuleListComponent implements OnInit , AfterViewInit, OnDestroy {
  displayedColumns2: string[] = ['id', 'ruleName', 'createdOn', 'createdBy', 'updateOn', 'updateBy', 'actions'];
  dataSource = [
    {
      id: 'RUL0001', ruleName: 'Schedule',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },
    {
      id: 'RUL0002', ruleName: 'Observation Points',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },
    {
      id: 'RUL0003', ruleName: 'Schedule 1',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },
    {
      id: 'RUL0004', ruleName: 'Threat',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },
    {
      id: 'RUL0005', ruleName: 'Low Observation Points',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },
    {
      id: 'RUL0006', ruleName: 'Overdue',
      createdOn: '04/24/2023',
      createdBy: 'Williams',
      updateOn: '04/24/2023',
      updateBy: '04/24/2023',
      actions: ''
    },

  ];


  
  displayedColumns: any = [];

  allColumns = [

    { key: 'id', displayName: "ID", name: "id", activeFlag: true, summary: false },
    { key: 'ruleName', displayName: "Rule Name", name: "ruleName", activeFlag: true, summary: false },
    { key: 'createdOn', displayName: "Created On", name: "createdOn", activeFlag: true, summary: false },
    { key: 'createdBy', displayName: "Created By", name: "createdBy", activeFlag: true, summary: false },
    { key: 'updateOn', displayName: "Update On", name: "updateOn", activeFlag: false, summary: false },
    { key: 'updateBy', displayName: "Update By", name: "updateBy", activeFlag: false, summary: false },
    { key: 'actions', displayName: "Actions", name: "actions", activeFlag: true, summary: false },
  ];
  url: any ="";
  constructor(private router: Router,private dataService: DataService,private dialog: MatDialog,private tokenService:TokenService) {

    this.url =this.dataService.React_API+ "/ruleList";
     

   }


  ngOnInit(): void {
    var _this = this;
    window.onmessage = function (e) {
      if (e.data.type && e.data.action && e.data.type == "RuleList") {
        console.log(e.data)
        if (e.data.action == "FormView") {
        
          console.log('FormView-->')
         // _this.goPage('create-rule')
          _this.router.navigateByUrl('create-rule', {
            state:  {}
        });
        } else if (e.data.action == "FormEdit") {
        
          console.log('FormEdit-->')
         // _this.goPage('create-rule')
          _this.router.navigateByUrl('create-rule', {
            state:  {}
        });
      }
    };


  }
}
  ngAfterViewInit(): void {
    
    var _this=this;
    var iframe = document.getElementById('iFrameRuleList');
    if (iframe == null) return;
    var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
    iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
    iWindow?.postMessage({ "type": "FormConfig", "action": "Column", "data": this.displayedColumns }, '*');
  // //  iWindow?.postMessage({ "type": "BadActor", "action": "Summary", "data": data }, '*');

  }
  ngOnDestroy(): void {
    
  }
  settingClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: this.allColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }

    const dialogRef = this.dialog.open(ColumnDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
instance.dataEmitter.subscribe((data) => {
    this.setColumn(data); 
});
    dialogRef.afterClosed().subscribe(result => {
      if (typeof result == "object") {
        this.setColumn(result);
      }
    });
  }
  setColumn(selColumn) {
    var _this = this;
    this.allColumns = [];
    this.displayedColumns = [];
    var i = 0;
    selColumn.forEach(element => {
      i++;
      this.allColumns.push(element);
      if (element.activeFlag) {
        this.displayedColumns.push(element.name);
      }


    });

    console.log('_this.displayedColumns,',_this.displayedColumns,)
    setTimeout(function () {
      _this.ngAfterViewInit()
    }, 100);
    // setTimeout(function () {
    //   _this.emitEventToChild({
    //     columns: _this.displayedColumns,
    //     threatFlag: _this.addThreatFlag
    //   })
    // }, 100);

  }
  summaryClick() {
    var myColumns = [];
    this.allColumns.forEach(function (eData) {
      if (eData.activeFlag) { myColumns.push(eData); }
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { allColumns: myColumns };
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.position = { top: "150px", right: '10px' }
    dialogConfig.height = "auto"
    const dialogRef = this.dialog.open(ColumnSummaryDialog, dialogConfig);
    const instance = dialogRef.componentInstance;
    instance.dataEmitterSummary.subscribe((data) => {
      this.setSummary();
    });
    // dialogRef.afterClosed().subscribe(result => {
    //   if (typeof result == "object") {
    //     this.setSummary();
    //   }
    // });

  }
  setSummary() {

    var _this = this;
    var summaryCol = {};
    this.allColumns.forEach(function (eData) {
      if (eData.summary) {
        summaryCol[eData.name] = {
          "enable": "Y",
          "colorRange": eData["summaryColor"] ? eData["summaryColor"] : []
        };
      }
    });
    console.log('summaryCol',summaryCol)
   // this.columnSummarysubject.next(summaryCol);
   var iframe = document.getElementById('iFrameRuleList');
   if (iframe == null) return;
   var iWindow = (<HTMLIFrameElement>iframe).contentWindow;
   iWindow?.postMessage({ "type": "AuthToken", "data": _this.tokenService.getToken() }, '*');
   iWindow?.postMessage({ "type": "FormConfig", "action": "Summary", "data": summaryCol }, '*');
  }

  goPage(page) {
    this.router.navigate([page]);
  }
  siteSelected(region) {
  }
  processSelected(process) {

  }
}
